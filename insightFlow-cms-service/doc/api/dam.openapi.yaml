openapi: 3.1.0
info:
  title: CMS
  description: ''
  version: 1.0.0
tags:
  - name: Assets
  - name: Directories
  - name: Tags
  - name: Tasks
  - name: Recycle Bin
paths:
  /dam/assets/{assetId}:
    get:
      summary: 素材详情获取
      deprecated: false
      description: 获取指定素材详细信息
      tags:
        - Assets
      parameters:
        - name: assetId
          in: path
          description: Asset ID
          required: true
          example: 0
          schema:
            type: integer
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                allOf:
                  - &ref_0
                    $ref: '#/components/schemas/RespBody'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          id:
                            type: integer
                            description: 素材 id
                          name:
                            type: string
                            description: 素材名称
                          directoryId:
                            type: integer
                            description: 文件夹 id
                          directoryName:
                            type: string
                            description: 文件夹名
                          duration:
                            type: number
                            description: 素材时长
                          aspectRatio:
                            type: string
                            description: 画面比率
                            enum:
                              - '1:1'
                              - '16:9'
                              - '9:16'
                          thumbnailUrl:
                            type: string
                            description: 缩略图 url
                          ossUrl:
                            type: string
                            description: oss存储url
                          tags:
                            type: array
                            items: &ref_3
                              $ref: '#/components/schemas/TagValue'
                            description: 标签列表
                          createTime:
                            type: string
                            format: date-time
                            description: Creation time
          headers: {}
      security: []
    put:
      summary: 素材标签更新
      deprecated: false
      description: 素材标签更新
      tags:
        - Assets
      parameters:
        - name: assetId
          in: path
          description: Asset ID
          required: true
          example: 0
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                type: object
                properties:
                  id:
                    type: integer
                    title: 标签值ID
                  tagId:
                    type: integer
                    title: 标签ID
                  name:
                    type: string
                    title: 标签名
                  value:
                    type: string
                    title: 标签值
            examples: {}
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema: *ref_0
          headers: {}
      security: []
    delete:
      summary: 素材删除
      deprecated: false
      description: 素材删除（移动到回收站）
      tags:
        - Assets
      parameters:
        - name: assetId
          in: path
          description: Asset ID
          required: true
          example: 0
          schema:
            type: integer
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema: *ref_0
          headers: {}
      security: []
  /dam/assets:
    post:
      summary: 素材列表
      deprecated: false
      description: 获取素材列表
      tags:
        - Assets
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                directoryId:
                  type: string
                  description: 文件夹 ID
                keyword:
                  type: string
                tags:
                  type: array
                  items:
                    type: object
                    properties:
                      tag:
                        type: string
                        description: 标签
                      tagValues:
                        type: array
                        items:
                          type: string
                        description: 标签值
                    required:
                      - tag
                      - tagValues
                  description: 标签搜索
                pageNum:
                  type: integer
                  title: 页码
                  description: '页码, 默认: 1'
                pageSize:
                  type: integer
                  title: 每页大小
                  description: '每页大小, 默认: 10'
                sortOrder:
                  type: string
                  title: 排序方式(asc/desc)
                  description: ''
                sortField:
                  type: string
                  title: 排序字段
                  description: ''
            examples: {}
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                allOf:
                  - *ref_0
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          total:
                            type: integer
                          records:
                            type: array
                            items: &ref_1
                              $ref: '#/components/schemas/Asset'
                          current:
                            type: integer
                          size:
                            type: integer
          headers: {}
      security: []
  /dam/assets/copy:
    post:
      summary: 素材拷贝
      deprecated: false
      description: 拷贝素材副本
      tags:
        - Assets
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                assetIds:
                  type: array
                  items:
                    type: integer
                  description: Asset IDs to share
                targetDirectoryId:
                  type: integer
                  description: Target category ID
              required:
                - assetIds
                - targetDirectoryId
            examples: {}
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema: *ref_0
          headers: {}
      security: []
  /dam/assets/move:
    post:
      summary: 素材移动
      deprecated: false
      description: 移动素材
      tags:
        - Assets
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                assetIds:
                  type: array
                  items:
                    type: integer
                  description: Asset IDs to share
                targetDirectoryId:
                  type: integer
                  description: Target category ID
              required:
                - assetIds
                - targetDirectoryId
            examples: {}
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema: *ref_0
          headers: {}
      security: []
  /dam/assets/{assetId}/download:
    get:
      summary: 下载素材
      deprecated: false
      description: 素材 id 换
      tags:
        - Assets
      parameters:
        - name: assetId
          in: path
          description: Asset ID
          required: true
          example: 0
          schema:
            type: integer
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                allOf:
                  - *ref_0
                  - type: object
                    properties:
                      data:
                        type: string
                        title: 下载链接
          headers: {}
      security: []
  /dam/assets/search/semantic:
    post:
      summary: 素材语义检索
      deprecated: false
      description: 素材检索 - 目前仅根据镜头描述进行检索
      tags:
        - Assets
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                query:
                  type: string
                  description: 检索内容
                aspectRatio:
                  type: string
                  description: 画面比例
                pageNum:
                  type: integer
                  title: 页码
                  description: '页码, 默认: 1'
                pageSize:
                  type: integer
                  default: 20
                  title: 每页大小
                  description: '每页大小, 默认: 10'
              required:
                - query
                - aspectRatio
            examples: {}
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                allOf:
                  - *ref_0
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          total:
                            type: integer
                          current:
                            type: string
                          size:
                            type: string
                          records:
                            type: array
                            items: *ref_1
                        required:
                          - current
                          - size
          headers: {}
      security: []
  /dam/directories:
    post:
      summary: 文件夹创建
      deprecated: false
      description: 创建一个文件夹
      tags:
        - Directories
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: 文件夹名
                type:
                  type: integer
                  description: 文件夹类型 1-个人文件夹, 2-租户文件夹
                  enum:
                    - 1
                    - 2
              required:
                - name
                - type
            examples: {}
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                allOf:
                  - *ref_0
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          id:
                            type: integer
                            description: 文件夹 id
                          name:
                            type: string
                            description: 文件夹名称
                          type:
                            type: integer
                            description: 文件夹类型（1-个人文件夹，2-租户文件夹）
                            enum:
                              - 1
                              - 2
                          createTime:
                            type: string
                            format: date-time
                            description: 创建时间
                          isDeleted:
                            type: boolean
                            description: 是否删除
                        required:
                          - isDeleted
          headers: {}
      security: []
    get:
      summary: 文件夹列表
      deprecated: false
      description: 列举文件夹
      tags:
        - Directories
      parameters:
        - name: type
          in: query
          description: 文件夹类型
          required: false
          schema:
            type: integer
            enum:
              - 1
              - 2
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                allOf:
                  - *ref_0
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: integer
                              description: 文件夹 id
                            name:
                              type: string
                              description: 文件夹名称
                            type:
                              type: integer
                              description: 文件夹类型（1-个人文件夹，2-租户文件夹）
                              enum:
                                - 1
                                - 2
                            createTime:
                              type: string
                              format: date-time
                              description: 创建时间
                            updateTime:
                              type: string
                              format: date-time
                              description: 更新时间
          headers: {}
      security: []
  /dam/directories/{directoryId}:
    get:
      summary: 文件夹详情
      deprecated: false
      description: 获取指定文件夹详情
      tags:
        - Directories
      parameters:
        - name: directoryId
          in: path
          description: 文件夹 ID
          required: true
          example: 0
          schema:
            type: integer
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                allOf:
                  - *ref_0
                  - type: object
                    properties:
                      data: &ref_7
                        $ref: '#/components/schemas/Directory'
          headers: {}
      security: []
    put:
      summary: 文件夹更新
      deprecated: false
      description: 文件夹更新 - 更新文件夹名
      tags:
        - Directories
      parameters:
        - name: directoryId
          in: path
          description: 文件夹 ID
          required: true
          example: 0
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: 新文件夹名
              required:
                - name
            examples: {}
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema: *ref_0
          headers: {}
      security: []
    delete:
      summary: 文件夹删除
      deprecated: false
      description: 将文件夹移动到回收站 - 存储在文件夹中的所有文件同步删除
      tags:
        - Directories
      parameters:
        - name: directoryId
          in: path
          description: 文件夹 ID
          required: true
          example: 0
          schema:
            type: integer
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema: *ref_0
          headers: {}
      security: []
  /dam/directories/{directoryId}/download:
    get:
      summary: 下载文件夹
      deprecated: false
      description: 下载文件夹下的所有素材
      tags:
        - Directories
      parameters:
        - name: directoryId
          in: path
          description: 文件夹 ID
          required: true
          example: 0
          schema:
            type: integer
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                allOf:
                  - *ref_0
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          assets:
                            type: array
                            items:
                              type: object
                              properties:
                                assetId:
                                  type: string
                                ossUrl:
                                  type: string
                              required:
                                - assetId
                                - ossUrl
                        required:
                          - assets
                      totalNum:
                        type: string
                    required:
                      - totalNum
          headers: {}
      security: []
  /dam/directories/copy:
    post:
      summary: 文件夹拷贝
      deprecated: false
      description: 拷贝文件夹下所有素材到目标文件夹
      tags:
        - Directories
      parameters: []
      requestBody:
        content:
          application/x-msgpack:
            schema:
              type: object
              properties:
                sourceDirectoryId:
                  type: integer
                  description: 来源文件夹 ID
                targetDirectoryId:
                  type: integer
                  description: 目标文件夹 ID
              required:
                - targetDirectoryId
                - sourceDirectoryId
            examples: {}
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema: *ref_0
          headers: {}
      security: []
  /dam/custom-tags:
    post:
      summary: 创建自定义标签
      deprecated: false
      description: 创建自定义标签
      tags:
        - Tags
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              properties:
                name:
                  type: string
                  title: 标签名
                description:
                  type: string
                  title: 标签描述
                example:
                  type: string
                  title: 示例
              required:
                - name
                - description
                - example
              $ref: '#/components/schemas/CreateCustomTagRequest'
            examples: {}
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                allOf:
                  - *ref_0
                  - type: object
                    properties:
                      data: &ref_2
                        $ref: '#/components/schemas/Tag'
          headers: {}
      security: []
  /dam/tags:
    get:
      summary: 获取标签列表
      deprecated: false
      description: Get tag list
      tags:
        - Tags
      parameters:
        - name: type
          in: query
          description: '标签类型, 1: 公共标签, 2: 自定义标签'
          required: false
          schema:
            type: integer
        - name: suggestValues
          in: query
          description: 是否包含推荐搜索标签值
          required: false
          schema:
            type: boolean
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                allOf:
                  - *ref_0
                  - type: object
                    properties:
                      data:
                        type: array
                        items: *ref_2
          headers: {}
      security: []
  /dam/tags/{tagId}:
    put:
      summary: 更新标签
      deprecated: false
      description: 更新标签
      tags:
        - Tags
      parameters:
        - name: tagId
          in: path
          description: Tag ID
          required: true
          example: 0
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  title: 标签名
                description:
                  type: string
                  title: 描述
                example:
                  type: string
                  title: 示例
              required:
                - name
                - description
                - example
            examples: {}
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                allOf:
                  - *ref_0
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          id:
                            type: integer
                          name:
                            type: string
                          updated:
                            type: boolean
          headers: {}
      security: []
    delete:
      summary: 删除标签
      deprecated: false
      description: 删除标签
      tags:
        - Tags
      parameters:
        - name: tagId
          in: path
          description: 标签ID
          required: true
          example: 0
          schema:
            type: integer
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema: *ref_0
          headers: {}
      security: []
  /dam/asset-upload-tasks:
    post:
      summary: 创建素材上传任务
      deprecated: false
      description: 创建素材上传任务
      tags: []
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                directoryId:
                  type: integer
                type:
                  $ref: '#/components/schemas/AssetUploadTaskType'
                assets:
                  type: array
                  items:
                    type: object
                    properties:
                      name:
                        type: string
                      ossUrl:
                        type: string
                      start:
                        type: integer
                        title: 剪辑开始位
                      end:
                        type: integer
                        title: 剪辑结束位
                    required:
                      - name
                      - ossUrl
                      - start
                      - end
              required:
                - assets
                - directoryId
                - type
            examples: {}
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                allOf:
                  - *ref_0
                  - type: object
                    properties:
                      data:
                        type: integer
                        description: 任务 ID
                    required:
                      - data
          headers: {}
      security: []
    get:
      summary: 素材上传任务列表
      deprecated: false
      description: 获取素材上传任务列表
      tags:
        - Tasks
      parameters:
        - name: type
          in: query
          description: '任务类型，1: 素材上传任务, 2: AI分镜镜头入库, 空则为全部'
          required: false
          schema:
            type:
              - integer
              - 'null'
            enum:
              - 1
              - 2
        - name: pageNum
          in: query
          description: '页码, 默认: 1'
          required: false
          schema:
            type: integer
            default: 1
        - name: pageSize
          in: query
          description: '页码大小, 默认: 10'
          required: false
          schema:
            type: integer
            default: 20
        - name: sortField
          in: query
          description: 排序字段
          required: false
          schema:
            type: string
            default: create_time
        - name: sortOrder
          in: query
          description: 排序方式(asc/desc)
          required: false
          schema:
            type: string
            enum:
              - asc
              - desc
            default: desc
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/RespBody%20Copy'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          records:
                            type: array
                            items:
                              $ref: '#/components/schemas/AssetUploadTask'
                        required:
                          - records
          headers: {}
      security: []
  /dam/asset-upload-tasks/{taskId}/detail:
    get:
      summary: 素材上传任务详情
      deprecated: false
      description: 获取素材任务详情
      tags:
        - Tasks
      parameters:
        - name: taskId
          in: path
          description: 素材上传任务ID
          required: true
          example: 0
          schema:
            type: integer
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                allOf:
                  - *ref_0
                  - type: object
                    properties:
                      data: &ref_5
                        description: 关联素材ID
                        $ref: '#/components/schemas/AssetUploadTaskItemDetail'
          headers: {}
      security: []
  /dam/asset-upload-tasks/{taskId}/storage:
    post:
      summary: 素材上传任务素材入库
      deprecated: false
      description: 素材上传任务素材入库
      tags:
        - Tasks
      parameters:
        - name: taskId
          in: path
          description: Task ID
          required: true
          example: 0
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                assets:
                  type: array
                  items:
                    type: object
                    properties:
                      id:
                        type: integer
                        description: 素材 id
                      name:
                        type: string
                        description: 素材名称
                      tags:
                        type: array
                        items: *ref_3
                        description: 标签列表
                  title: 入库素材ID列表
              required:
                - assets
            examples: {}
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema: *ref_0
          headers: {}
      security: []
  /dam/recycle-bin:
    get:
      summary: 获取回收站列表
      deprecated: false
      description: 获取回收站列表
      tags:
        - Recycle Bin
      parameters:
        - name: type
          in: query
          description: '1: 个人, 2: 租户'
          required: false
          schema:
            type: integer
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                allOf:
                  - *ref_0
                  - type: array
                    items:
                      $ref: '#/components/schemas/RecycleBinObject'
          headers: {}
      security: []
  /dam/recycle-bin/recover:
    post:
      summary: 回收站对象恢复
      deprecated: false
      description: 回收站对象恢复
      tags:
        - Recycle Bin
        - Assets
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                type: object
                properties:
                  recycleBinId:
                    type: integer
                    title: 回收站对象ID
                  targetDirectoryId:
                    type:
                      - integer
                      - 'null'
                    title: 目标目录ID
                    description: 仅当恢复素材时需要传递
                required:
                  - recycleBinId
            examples: {}
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                allOf:
                  - *ref_0
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          type: object
                          properties:
                            recycleBinId:
                              type: integer
                              title: 回收站对象ID
                            isRecovered:
                              type: boolean
                              title: 是否已恢复
                              description: false时表示，原文件夹不存在
                          required:
                            - recycleBinId
                            - isRecovered
          headers: {}
      security: []
  /dam/recycle-bin/delete:
    post:
      summary: 回收站对象删除
      deprecated: false
      description: 回收站对象恢复
      tags:
        - Recycle Bin
        - Assets
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                recycleBinIds:
                  type:
                    - array
                    - 'null'
                  items:
                    type: integer
                  title: 回收站对象ID列表
            examples: {}
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema: *ref_0
          headers: {}
      security: []
  /dam/recycle-bin/empty:
    post:
      summary: 回收站清空
      deprecated: false
      description: 回收站清空
      tags:
        - Recycle Bin
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties: {}
            examples: {}
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema: *ref_0
          headers: {}
      security: []
components:
  schemas:
    RespBody:
      type: object
      properties:
        statusCode:
          type: integer
          description: 状态码
        message:
          type: string
          description: 消息
        data:
          type: object
          properties: {}
      title: 通用响应
    RespBody Copy:
      allOf:
        - *ref_0
        - type: object
          properties:
            data:
              type: object
              properties:
                current:
                  type: integer
                  title: 页码
                  description: '页码, 默认: 1'
                size:
                  type: integer
                  title: 每页大小
                total:
                  type: integer
                  title: 总数
                records:
                  type: array
                  items:
                    type: string
                  title: 记录
              required:
                - current
                - size
                - total
                - records
          required:
            - data
      title: 分页通用响应
    Asset:
      type: object
      properties:
        id:
          type: integer
          description: 素材 id
        name:
          type: string
          description: 素材名称
        directoryId:
          type: integer
          description: 文件夹 id
        directoryName:
          type: string
          description: 文件夹名
        duration:
          type: number
          description: 素材时长
        aspectRatio:
          type: string
          description: 画面比率
          enum:
            - '1:1'
            - '16:9'
            - '9:16'
        thumbnailUrl:
          type: string
          description: 缩略图 url
        ossUrl:
          type: string
          description: oss存储url
        tags:
          type: array
          items: *ref_3
          description: 标签列表
        createTime:
          type: string
          format: date-time
          description: Creation time
        updateTime:
          type: string
          format: date-time
          description: Last update time
      title: 素材
    TagType:
      type: integer
      enum:
        - 1
        - 2
      description: 标签类型
      title: 标签类型
    Tag:
      type: object
      properties:
        id:
          type: integer
          description: 标签ID
          title: 标签ID
        name:
          type: string
          description: 标签名
          title: 标签名
        type: &ref_4
          description: 标签类型 (1- 公共标签, 2-自定义标签)
          $ref: '#/components/schemas/TagType'
          title: 标签类型
        description:
          type: string
          title: 描述
        example:
          type: string
          title: 示例
        usedNum:
          type: integer
          description: 应用频次
          title: 应用频次
        createTime:
          type: string
          format: date-time
          description: 创建时间
          title: 创建时间
      title: 标签
    标签信息:
      type: object
      properties:
        id:
          type: integer
          description: 标签ID
          title: 标签ID
        name:
          type: string
          description: 标签名
          title: 标签名
        type: *ref_4
        description:
          type: string
          title: 描述
        example:
          type: string
          title: 示例
        values:
          type:
            - array
            - 'null'
          items:
            type: string
            description: 推荐标签值
          title: 推荐标签值列表
        usedNum:
          type: integer
          description: 应用频次
          title: 应用频次
        createTime:
          type: string
          format: date-time
          description: 创建时间
          title: 创建时间
      title: 标签信息
    TagValue:
      type: object
      properties:
        id:
          type: integer
          title: 标签值ID
        tagId:
          type: integer
          title: 标签ID
        name:
          type: string
          title: 标签名
        value:
          type: string
          title: 标签值
        createTime:
          type: string
          format: date-time
          title: 创建时间
        updateTime:
          type: string
          title: 更新时间
          format: date-time
      title: 标签值
    CreateCustomTagRequest:
      type: object
      properties:
        name:
          type: string
          title: 标签名
        description:
          type: string
          title: 标签描述
        example:
          type: string
          title: 示例
      required:
        - name
        - description
        - example
      title: 创建自定义标签请求
    AssetUploadTaskType:
      type:
        - integer
        - 'null'
      title: 素材上传任务类型
      enum:
        - 1
        - 2
    AssetUploadTaskStatus:
      type: integer
      title: 素材上传任务状态
      enum:
        - 1
        - 2
        - 3
        - 4
      description: '1: 等待 2: 进行中 3: 完成 4: 失败'
    AssetUploadTask:
      type: object
      properties:
        id:
          type: integer
          title: 任务ID
        userId:
          type: integer
          title: 用户ID
        failCount:
          type: integer
          description: 失败数量
          title: 失败数量
        successCount:
          type: integer
          description: 成功数量
          title: 成功数量
        totalCount:
          type: integer
          description: 素材数量
          title: 素材数量
        error:
          type:
            - string
            - 'null'
          description: 错误信息
          title: 错误信息
        status: &ref_6
          $ref: '#/components/schemas/AssetUploadTaskStatus'
        createTime:
          type: string
          format: date-time
        updateTime:
          type: string
          format: date-time
      title: 素材上传任务
      required:
        - userId
    AssetUploadTaskInfo:
      type: object
      properties:
        id:
          type: integer
          description: 任务ID
        name:
          type: string
          description: 任务名称
        sourceType:
          type: integer
          description: 来源类型（1.上传视频 2.千川视频 3.素材上传）
        taskStatus:
          type: integer
          description: 任务状态（1-待处理，2-处理中，3-完成，4-失败）
        taskType:
          type: integer
          description: 任务类型（0-套路复用 1-AI仿写 2-帖子打标 3-素材AI打标）
        errorMessage:
          type: string
          description: 错误原因
        taskArg:
          type: string
          description: 任务参数，JSON格式存储
        sourceVideoIds:
          type: string
          description: 原视频分析id列表
        resultFileIds:
          type: string
          description: 结果文档id列表
        extra:
          type: string
          description: 任务额外信息，json存储
        userId:
          type: integer
          description: 创建人id
        tenantId:
          type: integer
          description: 租户Id
        createTime:
          type: string
          format: date-time
          description: 创建时间
        updateTime:
          type: string
          format: date-time
          description: 更新时间
      title: 素材上传任务信息
    AssetUploadTaskDetail:
      type: object
      properties:
        id:
          type: integer
          description: 主键ID
        assets: *ref_5
        error:
          type: string
          description: 失败原因
        status: *ref_6
        createTime:
          type: string
          format: date-time
          description: 创建时间
        updateTime:
          type: string
          format: date-time
          description: 更新时间
      title: 素材上传任务详情
    RecycleBinObjectType:
      type: integer
      title: 垃圾桶对象类型
      enum:
        - 1
        - 2
      description: '1: 素材, 2: 文件夹'
    RecycleBinObject:
      type: object
      properties:
        id:
          type: integer
          description: 主键ID
        tenantId:
          type: integer
          description: 租户ID
        userId:
          type: integer
          description: 操作用户ID
        objectType:
          description: 对象类型：1-目录，2-素材
          $ref: '#/components/schemas/RecycleBinObjectType'
        directory: *ref_7
        asset: *ref_1
        recoverTime:
          type: string
          format: date-time
          description: 恢复时间
        createTime:
          type: string
          format: date-time
          description: 创建时间
      title: 垃圾桶对象
      required:
        - asset
        - directory
    AssetUploadTaskItemDetail:
      allOf:
        - *ref_1
        - type: object
          properties:
            status: *ref_6
          required:
            - status
      title: 素材上传任务素材详情
    Directory:
      type: object
      properties:
        id:
          type: integer
          description: 文件夹 id
        name:
          type: string
          description: 文件夹名称
        type:
          type: integer
          description: 文件夹类型（1-个人文件夹，2-租户文件夹）
          enum:
            - 1
            - 2
        createTime:
          type: string
          format: date-time
          description: 创建时间
        updateTime:
          type: string
          format: date-time
          description: 更新时间
        isDeleted:
          type: boolean
          description: 是否删除
      title: 文件夹
      required:
        - isDeleted
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
servers: []
security: []
