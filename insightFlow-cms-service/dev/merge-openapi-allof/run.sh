#!/bin/bash

set -e

# 检查目录
DIR="$(dirname "$0")"
cd "$DIR"

# 检查依赖
echo "检查依赖..."
if [ ! -d "node_modules" ]; then
  echo "正在安装依赖..."
  if command -v npm &> /dev/null; then
    npm install
  elif command -v pnpm &> /dev/null; then
    pnpm install
  else
    echo "错误: 未找到 npm 或 pnpm，请先安装 Node.js 和 npm"
    exit 1
  fi
fi

# 解析命令行选项
while [[ $# -gt 0 ]]; do
  case $1 in
    -o|--output)
      output_file="$2"
      shift 2
      ;;
    -f|--force)
      force_mode=true
      shift
      ;;
    -h|--help)
      echo "使用方法: $0 <openapi_file> [--output|-o <output_file>] [--force|-f] [--help|-h]"
      echo ""
      echo "选项:"
      echo "  --output, -o    指定输出文件路径"
      echo "  --force, -f     强制移除所有allOf结构（可能会破坏$ref引用）"
      echo "  --help, -h      显示此帮助信息"
      exit 0
      ;;
    *)
      if [ -z "$openapi_file" ]; then
        openapi_file="$1"
      else
        echo "未知参数: $1"
        exit 1
      fi
      shift
      ;;
  esac
done

# 如果无参数则报错
if [ -z "$openapi_file" ]; then
    echo "使用方法: $0 <openapi_file> [--output|-o <output_file>] [--force|-f] [--help|-h]"
    exit 1
fi

# 如果文件不存在则报错
if [ ! -f "$openapi_file" ]; then
    echo "文件 $openapi_file 不存在"
    exit 1
fi

# 构建命令参数
cmd_args="$openapi_file"
if [ ! -z "$output_file" ]; then
    cmd_args="$cmd_args --output $output_file"
fi
if [ "$force_mode" = true ]; then
    cmd_args="$cmd_args --force"
fi

# 执行合并
echo "开始处理文件: $openapi_file"
node "$(dirname "$0")/main.js" $cmd_args

# 检查输出文件大小
if [ ! -z "$output_file" ]; then
    output_size=$(wc -c < "$output_file")
    echo "输出文件大小: $output_size 字节"
    
    # 检查输出是否为空对象
    if [ "$output_size" -le 5 ]; then
        echo "警告: 输出文件内容可能为空或仅包含 '{}'，请检查日志以获取更多信息"
    fi
else
    # 确定默认输出文件路径
    ext=$(echo "$openapi_file" | rev | cut -d'.' -f1 | rev)
    base_name=$(basename "$openapi_file" ".$ext")
    dir_name=$(dirname "$openapi_file")
    default_output="$dir_name/${base_name}-merged.$ext"
    
    if [ -f "$default_output" ]; then
        output_size=$(wc -c < "$default_output")
        echo "输出文件大小: $output_size 字节"
        
        # 检查输出是否为空对象
        if [ "$output_size" -le 5 ]; then
            echo "警告: 输出文件内容可能为空或仅包含 '{}'，请检查日志以获取更多信息"
        fi
    fi
fi

# 输出执行完成信息
echo "处理完成！"