import merger from 'json-schema-merge-allof';
import {merge} from 'allof-merge';
import fs from 'fs';
import path from 'path';
import { bundle, dereference } from 'api-ref-bundler';
import yaml from 'js-yaml';

// 添加一个专门处理Schema合并的函数
function mergeSchemas(target, source) {
  if (!target || !source || typeof target !== 'object' || typeof source !== 'object') {
    return target || source;
  }
  
  // 检查是否为allOf数组转换的对象（带数字键的对象）
  const isNumericKeysObject = (obj) => {
    if (!obj || typeof obj !== 'object' || Array.isArray(obj)) return false;
    const keys = Object.keys(obj);
    return keys.length > 0 && keys.every(key => !isNaN(parseInt(key)));
  };
  
  // 如果是数字键对象（来自数组），将其转换为适当的结构
  if (isNumericKeysObject(target)) {
    console.log('检测到疑似allOf数组转换的对象，尝试提取合并...');
    
    // 尝试合并所有数字键下的对象
    const mergedObj = {};
    for (const key of Object.keys(target)) {
      const item = target[key];
      if (typeof item === 'object' && !Array.isArray(item)) {
        // 合并每个属性
        for (const [propKey, propValue] of Object.entries(item)) {
          if (!mergedObj.hasOwnProperty(propKey)) {
            mergedObj[propKey] = propValue;
          } else if (propKey === 'properties' && typeof mergedObj[propKey] === 'object' && 
                  typeof propValue === 'object') {
            // 合并properties
            mergedObj[propKey] = { ...mergedObj[propKey], ...propValue };
          } else if (typeof mergedObj[propKey] === 'object' && typeof propValue === 'object') {
            // 递归合并其他对象
            mergedObj[propKey] = mergeSchemas(mergedObj[propKey], propValue);
          }
        }
      }
    }
    
    // 合并源对象（如果不是数字键对象）
    if (!isNumericKeysObject(source)) {
      return mergeSchemas(mergedObj, source);
    }
    
    return mergedObj;
  }
  
  const result = { ...target };
  
  // 特殊处理schema的特定字段
  for (const [key, value] of Object.entries(source)) {
    if (!result.hasOwnProperty(key)) {
      // 如果目标中不存在该属性，直接添加
      result[key] = value;
    } else if (key === 'properties' && result[key] && typeof result[key] === 'object' && 
              typeof value === 'object') {
      // 特殊处理properties字段，合并两个对象的属性
      result[key] = { ...result[key], ...value };
    } else if (key === 'items' && result[key] && typeof result[key] === 'object' && 
              typeof value === 'object') {
      // 特殊处理items字段，递归合并
      result[key] = mergeSchemas(result[key], value);
    } else if (key === 'required' && Array.isArray(result[key]) && Array.isArray(value)) {
      // 合并required数组，去重
      result[key] = [...new Set([...result[key], ...value])];
    } else if (key === 'type' && result[key] !== value) {
      // 如果类型不匹配，保留原始类型，但记录警告
      console.warn(`类型冲突: ${result[key]} vs ${value}，保留 ${result[key]}`);
    } else if (key === 'enum' && Array.isArray(result[key]) && Array.isArray(value)) {
      // 合并enum数组，去重
      result[key] = [...new Set([...result[key], ...value])];
    } else if (typeof result[key] === 'object' && !Array.isArray(result[key]) && 
              typeof value === 'object' && !Array.isArray(value)) {
      // 递归合并嵌套对象
      result[key] = mergeSchemas(result[key], value);
    }
    // 其他情况保持原始值不变
  }
  
  return result;
}

// 显示帮助信息
function showHelp() {
  console.log(`
使用方法: node main.js <openapi文件路径> [选项]

选项:
  --help, -h        显示帮助信息
  --output, -o      指定输出文件路径 (默认为: <输入文件名>-merged.<扩展名>)
  --force, -f       强制移除所有allOf结构（可能会破坏$ref引用）
  
示例:
  node main.js ./openapi.json
  node main.js ./openapi.yaml -o ./merged-api.json
  node main.js ./openapi.yaml -o ./merged-api.json -f
  `);
  process.exit(0);
}

// 解析命令行参数
const args = process.argv.slice(2);

// 检查是否请求帮助
if (args.includes('--help') || args.includes('-h')) {
  showHelp();
}

// 获取命令行参数中的文件路径
const openApiFilePath = args[0];

if (!openApiFilePath) {
  console.error('请提供OpenAPI文件路径');
  console.log('使用 --help 或 -h 查看帮助信息');
  process.exit(1);
}

// 检查是否使用强制模式
const forceMode = args.includes('--force') || args.includes('-f');

// 获取输出文件路径
let outputPath;
const outputArgIndex = args.indexOf('--output') !== -1 ? args.indexOf('--output') : args.indexOf('-o');
if (outputArgIndex !== -1 && args.length > outputArgIndex + 1) {
  outputPath = args[outputArgIndex + 1];
} else {
  const ext = path.extname(openApiFilePath);
  const basename = path.basename(openApiFilePath, ext);
  const dirname = path.dirname(openApiFilePath);
  outputPath = path.join(dirname, `${basename}-merged${ext}`);
}

// 创建resolver函数，用于解析引用文件
const resolver = async (sourcePath) => {
  try {
    // 确定引用路径
    let resolvedPath;
    if (sourcePath.startsWith('http://') || sourcePath.startsWith('https://')) {
      // 处理URL引用 - 当前我们不支持远程URL，暂时跳过
      console.warn(`警告: 检测到URL引用 ${sourcePath}，但当前工具不支持远程URL解析`);
      console.warn('如需处理远程URL，请将其下载到本地并修改引用路径');
      // 返回一个空对象，避免阻断整个流程
      return {};
    } else {
      // 处理本地文件引用
      // 获取当前OpenAPI文件的完整绝对路径
      const absoluteApiPath = path.resolve(openApiFilePath);
      // 获取OpenAPI文件所在目录
      const basedir = path.dirname(absoluteApiPath);
      // 拼接引用路径
      resolvedPath = path.resolve(basedir, sourcePath);
      
      console.log(`OpenAPI文件路径: ${absoluteApiPath}`);
      console.log(`基础目录: ${basedir}`);
    }

    console.log(`解析引用: ${sourcePath} -> ${resolvedPath}`);

    // 检查文件是否存在
    if (!fs.existsSync(resolvedPath)) {
      console.error(`引用文件不存在: ${resolvedPath}`);
      
      // 特殊情况处理：如果引用的是自身文件，直接返回已解析的openApi对象
      if (path.resolve(resolvedPath) === path.resolve(openApiFilePath)) {
        console.log('检测到引用自身文件，使用已解析的OpenAPI对象');
        return openApi;
      }
      
      return {}; // 返回空对象，避免阻断整个流程
    }

    // 读取文件内容
    const data = fs.readFileSync(resolvedPath, 'utf8');
    console.log(`引用文件大小: ${data.length} 字节`);
    
    // 根据文件类型返回不同格式
    const ext = path.extname(sourcePath).toLowerCase();
    
    if (ext === '.md') {
      return data; // 返回原始文本
    } else if (ext === '.yaml' || ext === '.yml') {
      try {
        return yaml.load(data);
      } catch (yamlError) {
        console.error(`解析YAML文件失败: ${sourcePath}, 错误: ${yamlError.message}`);
        throw yamlError;
      }
    } else {
      // 默认尝试解析为JSON
      try {
        return JSON.parse(data);
      } catch (jsonError) {
        console.error(`解析JSON文件失败: ${sourcePath}, 错误: ${jsonError.message}`);
        console.error(`文件内容前100个字符: ${data.substring(0, 100)}`);
        throw jsonError;
      }
    }
  } catch (error) {
    console.error(`解析引用文件失败: ${sourcePath}, 错误: ${error.message}`);
    throw error;
  }
};

// 深度克隆对象
function deepClone(obj) {
  return JSON.parse(JSON.stringify(obj));
}

// 用于存储解析后的OpenAPI对象，以便处理自引用
let openApi = null;

// 检查对象是否包含allOf属性
function hasAllOf(obj) {
  if (!obj || typeof obj !== 'object') {
    return false;
  }
  
  if (obj.allOf) {
    return true;
  }
  
  if (Array.isArray(obj)) {
    return obj.some(hasAllOf);
  }
  
  return Object.values(obj).some(value => 
    typeof value === 'object' && value !== null && hasAllOf(value)
  );
}

// 处理引用并合并allOf
function processDocument(doc) {
  // 处理过的缓存，避免循环引用
  const processedCache = new Map();
  
  // 递归处理节点
  function processNode(node, context = '') {
    // 处理基本类型和null
    if (node === null || typeof node !== 'object') {
      return node;
    }
    
    // 处理数组
    if (Array.isArray(node)) {
      return node.map((item, index) => processNode(item, `${context}[${index}]`));
    }
    
    // 检查是否已处理过该节点以避免循环引用
    if (processedCache.has(node)) {
      return processedCache.get(node);
    }
    
    // 创建结果对象
    const result = {};
    processedCache.set(node, result);
    
    // 如果同时存在$ref和allOf，我们需要特殊处理
    if (node.$ref && node.allOf) {
      if (forceMode) {
        console.log(`警告: ${context} 同时包含 $ref 和 allOf，强制模式将删除$ref`);
        
        // 在强制模式下删除$ref属性，保留allOf
        const nodeWithoutRef = {...node};
        delete nodeWithoutRef.$ref;
        
        // 递归处理该对象，并将结果合并回result
        const processed = processNode(nodeWithoutRef, context);
        Object.assign(result, processed);
      } else {
        console.log(`警告: ${context} 同时包含 $ref 和 allOf，将合并allOf内容到$ref中`);
        
        // 保存$ref值
        const refValue = node.$ref;
        
        // 处理allOf数组的每个元素
        const processedItems = node.allOf.map((item, index) => 
          processNode(item, `${context}.allOf[${index}]`)
        );
        
        try {
          // 尝试使用json-schema-merge-allof库合并allOf项
          const mergedObject = merger({allOf: processedItems});
          
          // 检查合并结果是否为带数字键的对象
          const hasNumericKeys = Object.keys(mergedObject).some(key => !isNaN(parseInt(key)));
          
          // 如果出现带数字键的对象，尝试手动合并
          if (hasNumericKeys) {
            console.log(`检测到合并后的数字键对象，尝试手动合并...`);
            
            // 创建一个新的合并对象
            const manualMerged = {};
            
            // 首先添加所有非allOf属性
            for (const key in processedItems) {
              if (key !== 'allOf' && Object.prototype.hasOwnProperty.call(processedItems, key)) {
                manualMerged[key] = processedItems[key];
              }
            }
            
            // 合并所有allOf项
            for (const item of processedItems) {
              for (const [key, value] of Object.entries(item)) {
                if (!manualMerged.hasOwnProperty(key)) {
                  // 如果目标对象中不存在该键，直接添加
                  manualMerged[key] = value;
                } else if (key === 'properties' && typeof manualMerged[key] === 'object' && 
                         typeof value === 'object') {
                  // 合并properties
                  manualMerged[key] = { ...manualMerged[key], ...value };
                } else if (typeof manualMerged[key] === 'object' && typeof value === 'object' &&
                         !Array.isArray(manualMerged[key]) && !Array.isArray(value)) {
                  // 递归合并嵌套对象
                  manualMerged[key] = mergeSchemas(manualMerged[key], value);
                }
                // 其他情况保持原有值
              }
            }
            
            // 将手动合并结果复制到result中
            Object.assign(result, manualMerged);
          } else {
            // 将合并结果智能地复制到result中
            for (const [key, value] of Object.entries(mergedObject)) {
              if (!result.hasOwnProperty(key)) {
                result[key] = value;
              } else if (key === 'properties' || key === 'items' || 
                       key === 'required' || key === 'enum') {
                // 使用特殊函数合并这些特定字段
                result[key] = mergeSchemas(result[key], value);
              } else {
                // 其他情况直接覆盖
                result[key] = value;
              }
            }
          }
          
          // 确保删除allOf属性
          delete result.allOf;
        } catch (error) {
          console.warn(`合并 ${context}.allOf 时出错: ${error.message}，将保留$ref和其他属性`);
          
          // 复制所有属性，包括$ref，但不包括allOf
          for (const [key, value] of Object.entries(node)) {
            if (key !== 'allOf') {
              result[key] = processNode(value, `${context}.${key}`);
            }
          }
        }
      }
      
      return result;
    }
    
    // 处理普通对象
    for (const [key, value] of Object.entries(node)) {
      if (key === 'allOf' && Array.isArray(value) && value.length > 0) {
        // 处理allOf数组的每个元素
        const processedItems = value.map((item, index) => 
          processNode(item, `${context}.allOf[${index}]`)
        );
        
        try {
          // 尝试使用json-schema-merge-allof库合并
          const mergedObject = merger({allOf: processedItems});
          
          // 检查合并结果是否为带数字键的对象
          const hasNumericKeys = Object.keys(mergedObject).some(key => !isNaN(parseInt(key)));
          
          // 如果出现带数字键的对象，尝试手动合并
          if (hasNumericKeys) {
            console.log(`检测到合并后的数字键对象，尝试手动合并...`);
            
            // 创建一个新的合并对象
            const manualMerged = {};
            
            // 首先添加所有非allOf属性
            for (const key in processedItems) {
              if (key !== 'allOf' && Object.prototype.hasOwnProperty.call(processedItems, key)) {
                manualMerged[key] = processedItems[key];
              }
            }
            
            // 合并所有allOf项
            for (const item of processedItems) {
              for (const [key, value] of Object.entries(item)) {
                if (!manualMerged.hasOwnProperty(key)) {
                  // 如果目标对象中不存在该键，直接添加
                  manualMerged[key] = value;
                } else if (key === 'properties' && typeof manualMerged[key] === 'object' && 
                         typeof value === 'object') {
                  // 合并properties
                  manualMerged[key] = { ...manualMerged[key], ...value };
                } else if (typeof manualMerged[key] === 'object' && typeof value === 'object' &&
                         !Array.isArray(manualMerged[key]) && !Array.isArray(value)) {
                  // 递归合并嵌套对象
                  manualMerged[key] = mergeSchemas(manualMerged[key], value);
                }
                // 其他情况保持原有值
              }
            }
            
            // 将手动合并结果复制到result中
            Object.assign(result, manualMerged);
          } else {
            // 将合并结果智能地复制到result中
            for (const [key, value] of Object.entries(mergedObject)) {
              if (!result.hasOwnProperty(key)) {
                result[key] = value;
              } else if (key === 'properties' || key === 'items' || 
                       key === 'required' || key === 'enum') {
                // 使用特殊函数合并这些特定字段
                result[key] = mergeSchemas(result[key], value);
              } else {
                // 其他情况直接覆盖
                result[key] = value;
              }
            }
          }
          
          // 确保删除allOf属性
          delete result.allOf;
        } catch (firstError) {
          try {
            // 如果第一种方法失败，尝试使用allof-merge库
            console.log(`使用json-schema-merge-allof合并 ${context}.allOf 失败，尝试使用备用方法...`);
            const mergedWithBackup = merge(processedItems);
            
            // 检查合并结果是否为带数字键的对象
            const hasNumericKeys = Object.keys(mergedWithBackup).some(key => !isNaN(parseInt(key)));
            
            // 如果出现带数字键的对象，尝试手动合并
            if (hasNumericKeys) {
              console.log(`检测到备用合并后的数字键对象，尝试手动合并...`);
              
              // 创建一个新的合并对象
              const manualMerged = {};
              
              // 合并所有allOf项
              for (const item of processedItems) {
                for (const [key, value] of Object.entries(item)) {
                  if (!manualMerged.hasOwnProperty(key)) {
                    // 如果目标对象中不存在该键，直接添加
                    manualMerged[key] = processNode(value, `${context}.${key}`);
                  } else if (key === 'properties' && typeof manualMerged[key] === 'object' && 
                           typeof value === 'object') {
                    // 合并properties
                    manualMerged[key] = { ...manualMerged[key], ...processNode(value, `${context}.${key}`) };
                  } else if (typeof manualMerged[key] === 'object' && typeof value === 'object' &&
                           !Array.isArray(manualMerged[key]) && !Array.isArray(value)) {
                    // 递归合并嵌套对象
                    manualMerged[key] = mergeSchemas(manualMerged[key], processNode(value, `${context}.${key}`));
                  }
                  // 其他情况保持原有值
                }
              }
              
              // 将手动合并结果复制到result中
              Object.assign(result, manualMerged);
            } else {
              // 智能合并结果
              for (const [key, value] of Object.entries(mergedWithBackup)) {
                if (!result.hasOwnProperty(key)) {
                  result[key] = value;
                } else if (key === 'properties' || key === 'items' || 
                         key === 'required' || key === 'enum') {
                  // 使用特殊函数合并这些特定字段
                  result[key] = mergeSchemas(result[key], value);
                } else {
                  // 其他情况直接覆盖
                  result[key] = value;
                }
              }
            }
            
            // 确保删除allOf属性
            delete result.allOf;
          } catch (secondError) {
            console.warn(`合并 ${context}.allOf 时所有方法都失败: ${firstError.message}, ${secondError.message}，将保留原始结构`);
            
            // 如果所有合并尝试都失败，保留原始结构
            result.allOf = processedItems;
          }
        }
      } else {
        // 递归处理其他属性
        result[key] = processNode(value, `${context}.${key}`);
      }
    }
    
    return result;
  }
  
  // 递归处理整个文档
  return processNode(doc, 'root');
}

// 增强版的深度清理函数，确保移除所有allOf结构
function deepCleanupAllOf(obj, context = 'root') {
  // 处理基本类型、null和数组
  if (!obj || typeof obj !== 'object') {
    return obj;
  }
  
  // 处理数组
  if (Array.isArray(obj)) {
    return obj.map((item, index) => deepCleanupAllOf(item, `${context}[${index}]`));
  }
  
  // 创建结果对象，保留除allOf外的所有属性
  const result = {};
  
  // 特殊处理包含allOf的对象
  if (obj.allOf && Array.isArray(obj.allOf) && obj.allOf.length > 0) {
    try {
      // 首先尝试合并allOf
      console.log(`最终清理阶段尝试合并: ${context}.allOf`);
      
      // 处理allOf中的每一项，确保其中的嵌套allOf也被处理
      const processedAllOf = obj.allOf.map((item, index) => 
        deepCleanupAllOf(item, `${context}.allOf[${index}]`)
      );
      
      // 尝试使用json-schema-merge-allof库合并
      const mergedResult = merger({allOf: processedAllOf});
      
      // 递归处理合并结果中的所有属性，确保嵌套的allOf也被清理
      for (const key in mergedResult) {
        if (key !== 'allOf' && Object.prototype.hasOwnProperty.call(mergedResult, key)) {
          result[key] = deepCleanupAllOf(mergedResult[key], `${context}.${key}`);
        }
      }
      
      // 处理obj中除了allOf外的其他属性，并合并进结果
      for (const key in obj) {
        if (key !== 'allOf' && Object.prototype.hasOwnProperty.call(obj, key)) {
          if (result[key] === undefined) {
            // 如果结果中不存在该属性，直接添加
            result[key] = deepCleanupAllOf(obj[key], `${context}.${key}`);
          } else {
            // 如果结果中已存在该属性，尝试合并
            const objValue = deepCleanupAllOf(obj[key], `${context}.${key}`);
            
            if (typeof result[key] === 'object' && typeof objValue === 'object' && 
                !Array.isArray(result[key]) && !Array.isArray(objValue)) {
              // 如果两者都是对象，合并它们
              result[key] = {...result[key], ...objValue};
            }
            // 其他情况保持原样，allOf的结果优先
          }
        }
      }
    } catch (error) {
      console.warn(`最终清理阶段合并 ${context}.allOf 时出错: ${error.message}`);
      
      // 如果合并失败，保留除allOf外的所有属性
      for (const key in obj) {
        if (key !== 'allOf' && Object.prototype.hasOwnProperty.call(obj, key)) {
          result[key] = deepCleanupAllOf(obj[key], `${context}.${key}`);
        }
      }
    }
  } else {
    // 对于不包含allOf的对象，递归处理所有属性
    for (const key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        result[key] = deepCleanupAllOf(obj[key], `${context}.${key}`);
      }
    }
  }
  
  return result;
}

// 最终处理函数，确保所有allOf都被移除
function finalizeDocument(doc) {
  // 多次应用深度清理，直到没有allOf为止
  let result = deepClone(doc);
  let iteration = 0;
  const MAX_ITERATIONS = 5;
  
  while (hasAllOf(result) && iteration < MAX_ITERATIONS) {
    console.log(`迭代 ${iteration + 1}: 仍然检测到allOf结构，继续清理...`);
    result = deepCleanupAllOf(result);
    iteration++;
  }
  
  if (hasAllOf(result)) {
    console.warn(`警告: 经过 ${MAX_ITERATIONS} 次清理迭代后，文档中仍然存在allOf结构`);
    if (forceMode) {
      console.log('强制模式已启用，强制移除所有剩余的allOf属性...');
      // 强制移除所有剩余的allOf属性
      result = forceRemoveAllOf(result);
    }
  } else {
    console.log(`所有allOf结构已成功移除，共进行了 ${iteration} 次清理迭代`);
  }
  
  return result;
}

// 强制移除所有allOf属性
function forceRemoveAllOf(obj) {
  if (!obj || typeof obj !== 'object') {
    return obj;
  }
  
  if (Array.isArray(obj)) {
    return obj.map(forceRemoveAllOf);
  }
  
  const result = {};
  
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      if (key === 'allOf') {
        // 跳过所有allOf属性
        continue;
      }
      result[key] = forceRemoveAllOf(obj[key]);
    }
  }
  
  return result;
}

// 添加一个通用的错误处理函数
function handleError(message, error, exit = true) {
  console.error(`错误: ${message}`);
  if (error) {
    console.error(`原因: ${error.message}`);
    console.error(error.stack);
  }
  if (exit) {
    process.exit(1);
  }
}

// 添加一个检查对象中$ref引用的函数
function checkReferences(obj, context = 'root') {
  // 处理基本类型和null
  if (obj === null || typeof obj !== 'object') {
    return;
  }
  
  // 处理数组
  if (Array.isArray(obj)) {
    obj.forEach((item, index) => checkReferences(item, `${context}[${index}]`));
    return;
  }
  
  // 检查$ref属性
  if (obj.$ref && typeof obj.$ref === 'string') {
    console.log(`发现引用: ${context}.$ref = ${obj.$ref}`);
    
    // 检查是否为本地文件引用
    if (obj.$ref.startsWith('#/')) {
      // 内部引用，无需特殊处理
      console.log(`  - 内部引用，无需特殊处理`);
    } else if (obj.$ref.startsWith('http://') || obj.$ref.startsWith('https://')) {
      // 远程URL引用
      console.warn(`  - 警告: 远程URL引用可能无法正确解析`);
    } else {
      // 外部文件引用
      console.log(`  - 外部文件引用，需要特殊处理`);
    }
  }
  
  // 递归检查所有属性
  Object.entries(obj).forEach(([key, value]) => {
    if (value && typeof value === 'object') {
      checkReferences(value, `${context}.${key}`);
    }
  });
}

// 添加一个函数用于修正$ref引用路径
function fixReferences(obj, context = 'root') {
  // 处理基本类型和null
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }
  
  // 处理数组
  if (Array.isArray(obj)) {
    return obj.map((item, index) => fixReferences(item, `${context}[${index}]`));
  }
  
  // 创建结果对象
  const result = Array.isArray(obj) ? [] : {};
  
  // 如果是对象且有$ref属性
  if (obj.$ref && typeof obj.$ref === 'string') {
    // 检查是否为内部引用
    if (obj.$ref.startsWith('#/')) {
      // 内部引用，保持不变
      result.$ref = obj.$ref;
    } else if (obj.$ref.startsWith('http://') || obj.$ref.startsWith('https://')) {
      // 远程URL引用，将其转换为内部引用
      console.warn(`警告: ${context}.$ref 包含远程URL引用，转换为内部引用`);
      // 移除$ref属性
      result.$removed_ref = obj.$ref;
      
      // 复制其他属性
      for (const [key, value] of Object.entries(obj)) {
        if (key !== '$ref') {
          result[key] = fixReferences(value, `${context}.${key}`);
        }
      }
    } else {
      // 外部文件引用，修改为内部引用
      console.log(`修正外部引用: ${context}.$ref = ${obj.$ref}`);
      
      // 获取原始引用的基础目录
      const basedir = path.dirname(openApiFilePath);
      const refPathAbs = path.resolve(basedir, obj.$ref.split('#')[0]);
      
      // 检查是否为自引用
      if (path.resolve(refPathAbs) === path.resolve(openApiFilePath)) {
        // 如果是引用自身文件，转换为内部引用
        if (obj.$ref.includes('#')) {
          // 提取引用路径中的片段标识符
          const fragment = obj.$ref.split('#')[1];
          result.$ref = '#' + fragment;
          console.log(`  - 自引用转换为内部引用: ${result.$ref}`);
        } else {
          // 整个文件的引用，移除$ref
          console.log(`  - 移除整个文件的自引用`);
          result.$removed_ref = obj.$ref;
          
          // 复制其他属性
          for (const [key, value] of Object.entries(obj)) {
            if (key !== '$ref') {
              result[key] = fixReferences(value, `${context}.${key}`);
            }
          }
        }
      } else {
        // 其他外部引用，标记为已移除
        console.log(`  - 标记移除外部引用：${obj.$ref}`);
        result.$removed_ref = obj.$ref;
        
        // 复制其他属性
        for (const [key, value] of Object.entries(obj)) {
          if (key !== '$ref') {
            result[key] = fixReferences(value, `${context}.${key}`);
          }
        }
      }
    }
  } else {
    // 常规对象，递归处理所有属性
    for (const [key, value] of Object.entries(obj)) {
      result[key] = fixReferences(value, `${context}.${key}`);
    }
  }
  
  return result;
}

// 检查对象中是否有任何$ref属性
function hasReferences(obj) {
  // 处理基本类型和null
  if (obj === null || typeof obj !== 'object') {
    return false;
  }
  
  // 检查对象是否有$ref属性
  if (obj.$ref) {
    return true;
  }
  
  // 如果是数组，检查任何元素是否有$ref
  if (Array.isArray(obj)) {
    return obj.some(hasReferences);
  }
  
  // 递归检查所有属性
  return Object.values(obj).some(value => 
    typeof value === 'object' && value !== null && hasReferences(value)
  );
}

// 添加一个函数用于解析内部$ref引用
function resolveInternalRefs(doc) {
  // 引用解析缓存，避免重复解析和循环引用
  const refCache = new Map();

  // 获取JSON指针引用的内容
  function getRefContent(ref, rootDoc) {
    // 如果引用路径不是内部引用，返回null
    if (!ref.startsWith('#/')) {
      return null;
    }

    // 从缓存中获取，如果已解析过
    if (refCache.has(ref)) {
      return refCache.get(ref);
    }

    // 解析引用路径
    const pathParts = ref.substring(2).split('/');
    let current = rootDoc;

    // 遍历路径
    for (const part of pathParts) {
      // 解码JSON指针转义的部分
      const key = part.replace(/~1/g, '/').replace(/~0/g, '~');
      
      if (!current || current[key] === undefined) {
        console.warn(`无法解析引用: ${ref}, 路径部分 "${key}" 不存在`);
        return null;
      }
      
      current = current[key];
    }

    // 存入缓存
    refCache.set(ref, current);
    return current;
  }

  // 递归解析对象中的所有$ref
  function resolveRefs(obj, context = 'root') {
    // 处理基本类型和null
    if (!obj || typeof obj !== 'object') {
      return obj;
    }

    // 处理数组
    if (Array.isArray(obj)) {
      return obj.map((item, index) => resolveRefs(item, `${context}[${index}]`));
    }

    // 处理$ref属性
    if (obj.$ref && typeof obj.$ref === 'string' && obj.$ref.startsWith('#/')) {
      const refContent = getRefContent(obj.$ref, doc);
      
      if (refContent) {
        console.log(`解析内部引用: ${context}.$ref = ${obj.$ref}`);
        
        // 创建一个新对象，合并引用内容和原对象中除$ref外的其他属性
        const result = { ...resolveRefs(refContent, context) };
        
        // 复制原对象中除$ref外的其他属性
        for (const [key, value] of Object.entries(obj)) {
          if (key !== '$ref') {
            // 对于其他属性也进行递归解析
            result[key] = resolveRefs(value, `${context}.${key}`);
          }
        }
        
        return result;
      } else {
        console.warn(`无法解析引用: ${obj.$ref}, 将保留原始引用`);
        return { ...obj };
      }
    }

    // 处理普通对象
    const result = {};
    for (const [key, value] of Object.entries(obj)) {
      result[key] = resolveRefs(value, `${context}.${key}`);
    }

    return result;
  }

  // 开始递归解析
  return resolveRefs(doc);
}

// 添加函数用于迭代解析多层引用
function resolveAllInternalRefs(doc, maxIterations = 5) {
  let result = { ...doc };
  let iteration = 0;
  let hasRefs = true;
  
  // 检查对象中是否有内部$ref
  function hasInternalRefs(obj) {
    if (!obj || typeof obj !== 'object') {
      return false;
    }
    
    if (obj.$ref && typeof obj.$ref === 'string' && obj.$ref.startsWith('#/')) {
      return true;
    }
    
    if (Array.isArray(obj)) {
      return obj.some(hasInternalRefs);
    }
    
    return Object.values(obj).some(value => 
      typeof value === 'object' && value !== null && hasInternalRefs(value)
    );
  }
  
  // 迭代解析，直到没有更多内部引用或达到最大迭代次数
  while (hasRefs && iteration < maxIterations) {
    console.log(`解析内部引用迭代 ${iteration + 1}/${maxIterations}...`);
    result = resolveInternalRefs(result);
    hasRefs = hasInternalRefs(result);
    iteration++;
    
    if (!hasRefs) {
      console.log(`所有内部引用已解析完成，共进行了 ${iteration} 次迭代`);
      break;
    }
  }
  
  if (hasRefs) {
    console.warn(`警告: 经过 ${maxIterations} 次迭代后，文档中仍然存在内部引用`);
  }
  
  return result;
}

// 添加一个函数，直接展开所有引用，不再保留$ref属性
function expandAllRefs(doc) {
  console.log('开始展开所有$ref引用...');
  
  // 深度复制对象以进行修改
  const clonedDoc = deepClone(doc);
  
  // 展开所有内部引用
  let expanded = resolveAllInternalRefs(clonedDoc);
  
  // 查找并删除所有剩余的$ref属性
  function removeAllRefs(obj) {
    if (!obj || typeof obj !== 'object') {
      return obj;
    }
    
    if (Array.isArray(obj)) {
      return obj.map(removeAllRefs);
    }
    
    const result = {};
    
    for (const [key, value] of Object.entries(obj)) {
      if (key === '$ref') {
        // 跳过$ref属性
        console.log(`移除未解析的引用: ${value}`);
        continue;
      } else if (key === '$removed_ref') {
        // 也跳过我们之前标记的已移除引用
        continue;
      } else {
        // 递归处理其他属性
        result[key] = removeAllRefs(value);
      }
    }
    
    return result;
  }
  
  // 移除所有未能解析的$ref
  expanded = removeAllRefs(expanded);
  
  console.log('所有$ref引用展开完成');
  return expanded;
}

// 处理allOf数组合并时可能产生的带数字键对象
function fixNumericKeysObject(obj) {
  if (!obj || typeof obj !== 'object') {
    return obj;
  }
  
  // 检查是否为带数字键的对象
  const isNumericKeysObject = (obj) => {
    if (!obj || typeof obj !== 'object' || Array.isArray(obj)) return false;
    const keys = Object.keys(obj);
    return keys.length > 0 && keys.every(key => !isNaN(parseInt(key)));
  };
  
  // 递归处理数组
  if (Array.isArray(obj)) {
    return obj.map(item => fixNumericKeysObject(item));
  }
  
  // 处理对象
  const result = {};
  
  for (const [key, value] of Object.entries(obj)) {
    if (typeof value === 'object' && value !== null) {
      // 如果值是带数字键的对象，特殊处理
      if (isNumericKeysObject(value)) {
        console.log(`正在修复带数字键的对象: ${key}`);
        
        // 创建合并后的对象
        const mergedValue = {};
        
        // 合并所有数字键下的对象
        for (const numKey of Object.keys(value)) {
          const item = value[numKey];
          if (typeof item === 'object' && !Array.isArray(item)) {
            // 使用mergeSchemas合并
            Object.assign(mergedValue, item);
          }
        }
        
        // 存储修复后的对象
        result[key] = fixNumericKeysObject(mergedValue);
      } else {
        // 递归处理其他对象
        result[key] = fixNumericKeysObject(value);
      }
    } else {
      // 复制非对象值
      result[key] = value;
    }
  }
  
  return result;
}

// 添加函数用于修复responses的格式问题
function fixResponsesFormat(obj) {
  if (!obj || typeof obj !== 'object') {
    return obj;
  }
  
  // 处理数组
  if (Array.isArray(obj)) {
    return obj.map(item => fixResponsesFormat(item));
  }
  
  // 创建结果对象
  const result = {};
  
  // 复制所有属性并递归处理
  for (const [key, value] of Object.entries(obj)) {
    // 特殊处理responses属性
    if (key === 'responses' && typeof value === 'object' && !Array.isArray(value)) {
      // 检查是否是错误格式的responses（直接包含description/schema等字段而不是状态码）
      const hasDirectProperties = ['description', 'schema', 'headers'].some(prop => 
        Object.keys(value).includes(prop)
      );
      
      if (hasDirectProperties) {
        console.log('检测到错误格式的responses，尝试修复...');
        
        // 创建正确格式的responses，默认使用200作为状态码
        result[key] = {
          "200": value
        };
      } else {
        // 递归处理正确格式的responses，确保每个状态码内部也被修复
        const fixedResponses = {};
        
        for (const [statusCode, responseObj] of Object.entries(value)) {
          fixedResponses[statusCode] = fixResponsesFormat(responseObj);
        }
        
        result[key] = fixedResponses;
      }
    } else if (typeof value === 'object' && value !== null) {
      // 递归处理嵌套对象
      result[key] = fixResponsesFormat(value);
    } else {
      // 直接复制非对象属性
      result[key] = value;
    }
  }
  
  return result;
}

try {
  // 读取OpenAPI文件
  console.log(`读取文件: ${openApiFilePath}`);
  const openApiContent = fs.readFileSync(openApiFilePath, 'utf8');
  console.log(`文件内容大小: ${openApiContent.length} 字节`);
  
  try {
    // 解析文件内容到全局变量
    const ext = path.extname(openApiFilePath).toLowerCase();
    
    // 根据文件扩展名判断格式
    if (ext === '.yaml' || ext === '.yml') {
      console.log('检测到YAML格式文件，使用YAML解析器');
      openApi = yaml.load(openApiContent);
    } else {
      console.log('使用JSON解析器解析文件');
      openApi = JSON.parse(openApiContent);
    }
    
    if (!openApi || typeof openApi !== 'object') {
      handleError(`解析后的OpenAPI不是有效的对象，得到的类型: ${typeof openApi}`);
    }
    
    console.log(`解析后的OpenAPI对象结构:`);
    console.log(JSON.stringify({
      hasInfo: !!openApi.info,
      hasComponents: !!openApi.components,
      hasPaths: !!openApi.paths,
      schemaVersion: openApi.openapi || openApi.swagger
    }, null, 2));
    
    // 检查$ref引用
    console.log('检查OpenAPI中的$ref引用...');
    checkReferences(openApi);
    console.log('$ref引用检查完成');
    
    // 是否包含外部引用
    if (hasReferences(openApi)) {
      console.log('尝试修正外部引用...');
      openApi = fixReferences(openApi);
      console.log('外部引用修正完成');
    }

    // 在处理allOf之前，先解析内部$ref引用
    console.log('开始解析内部$ref引用...');
    openApi = resolveAllInternalRefs(openApi);
    console.log('内部$ref引用解析完成');

    // 首先解析所有的$ref引用
    console.log('开始解析$ref引用...');
    
    // 获取OpenAPI文件的绝对路径
    const absoluteApiPath = path.resolve(openApiFilePath);
    console.log(`使用绝对路径进行解析: ${absoluteApiPath}`);
    
    // 使用绝对路径调用bundle函数
    bundle(absoluteApiPath, resolver, { 
      ignoreSibling: false,
      ignoreRemote: true
    })
      .then(bundledOpenApi => {
        if (!bundledOpenApi || Object.keys(bundledOpenApi).length === 0) {
          handleError('解析$ref引用后得到空对象');
        }
        
        console.log('$ref引用解析完成');
        console.log(`解析后的bundledOpenApi对象结构:`);
        console.log(JSON.stringify({
          hasInfo: !!bundledOpenApi.info,
          hasComponents: !!bundledOpenApi.components,
          hasPaths: !!bundledOpenApi.paths,
          schemaVersion: bundledOpenApi.openapi || bundledOpenApi.swagger
        }, null, 2));
        
        // 再次解析内部$ref引用，确保所有引用都已解析
        console.log('解析bundle后的内部$ref引用...');
        bundledOpenApi = resolveAllInternalRefs(bundledOpenApi);
        console.log('bundle后内部$ref引用解析完成');
        
        // 完全展开所有引用，移除所有$ref属性
        console.log('开始完全展开所有引用...');
        bundledOpenApi = expandAllRefs(bundledOpenApi);
        console.log('完全展开所有引用完成');
        
        // 然后处理allOf合并
        console.log('开始合并allOf...');
        console.log(`运行模式: ${forceMode ? '强制模式' : '普通模式'}`);
        
        // 处理文档，解析引用并合并allOf
        const processed = processDocument(bundledOpenApi);
        if (!processed || Object.keys(processed).length === 0) {
          handleError('处理文档后得到空对象');
        }
        
        console.log(`处理后的processed对象结构:`);
        console.log(JSON.stringify({
          hasInfo: !!processed.info,
          hasComponents: !!processed.components,
          hasPaths: !!processed.paths,
          schemaVersion: processed.openapi || processed.swagger
        }, null, 2));
        
        // 最后确保所有allOf都被移除
        const finalResult = finalizeDocument(processed);
        if (!finalResult || Object.keys(finalResult).length === 0) {
          handleError('最终处理后得到空对象');
        }
        
        // 修复可能存在的带数字键的对象问题
        console.log('修复可能存在的带数字键对象问题...');
        const fixedResult = fixNumericKeysObject(finalResult);
        console.log('带数字键对象问题修复完成');
        
        // 修复responses格式问题
        console.log('修复可能存在的responses格式问题...');
        const formattedResult = fixResponsesFormat(fixedResult);
        console.log('responses格式修复完成');
        
        console.log(`最终结果的对象结构:`);
        console.log(JSON.stringify({
          hasInfo: !!formattedResult.info,
          hasComponents: !!formattedResult.components,
          hasPaths: !!formattedResult.paths,
          schemaVersion: formattedResult.openapi || formattedResult.swagger
        }, null, 2));
        
        console.log('allOf合并完成');
        
        // 保存处理后的文件
        const outputContent = JSON.stringify(formattedResult, null, 2);
        
        // 检查输出内容是否为空对象
        if (outputContent === '{}') {
          handleError('处理结果为空对象，可能存在问题。请检查输入文件和处理过程。', null, false);
          
          // 尝试保留原始文件
          console.log('将保存原始OpenAPI文件内容...');
          fs.writeFileSync(outputPath, openApiContent);
          console.log(`已保存原始文件内容至: ${outputPath}`);
        } else {
          fs.writeFileSync(outputPath, outputContent);
          console.log(`已成功合并allOf并保存至: ${outputPath}`);
        }
      })
      .catch(error => {
        console.error(`处理$ref引用过程中出错: ${error.message}`);
        console.error(error.stack);
        
        // 备用处理方式：直接使用修正后的OpenAPI对象
        console.log('使用备用处理方式...');
        
        // 首先展开所有引用
        console.log('开始展开所有引用...');
        openApi = expandAllRefs(openApi);
        console.log('所有引用展开完成');
        
        // 处理文档，解析引用并合并allOf
        const processed = processDocument(openApi);
        if (!processed || Object.keys(processed).length === 0) {
          handleError('处理文档后得到空对象');
        }
        
        console.log(`处理后的processed对象结构:`);
        console.log(JSON.stringify({
          hasInfo: !!processed.info,
          hasComponents: !!processed.components,
          hasPaths: !!processed.paths,
          schemaVersion: processed.openapi || processed.swagger
        }, null, 2));
        
        // 最后确保所有allOf都被移除
        const finalResult = finalizeDocument(processed);
        if (!finalResult || Object.keys(finalResult).length === 0) {
          handleError('最终处理后得到空对象');
        }
        
        // 修复可能存在的带数字键的对象问题
        console.log('修复可能存在的带数字键对象问题...');
        const fixedResult = fixNumericKeysObject(finalResult);
        console.log('带数字键对象问题修复完成');
        
        // 修复responses格式问题
        console.log('修复可能存在的responses格式问题...');
        const formattedResult = fixResponsesFormat(fixedResult);
        console.log('responses格式修复完成');
        
        console.log(`最终结果的对象结构:`);
        console.log(JSON.stringify({
          hasInfo: !!formattedResult.info,
          hasComponents: !!formattedResult.components,
          hasPaths: !!formattedResult.paths,
          schemaVersion: formattedResult.openapi || formattedResult.swagger
        }, null, 2));
        
        console.log('allOf合并完成');
        
        // 保存处理后的文件
        const outputContent = JSON.stringify(formattedResult, null, 2);
        
        // 检查输出内容是否为空对象
        if (outputContent === '{}') {
          console.error('处理结果为空对象，可能存在问题。请检查输入文件和处理过程。');
          
          // 尝试保留原始文件
          console.log('将保存原始OpenAPI文件内容...');
          fs.writeFileSync(outputPath, openApiContent);
          console.log(`已保存原始文件内容至: ${outputPath}`);
        } else {
          fs.writeFileSync(outputPath, outputContent);
          console.log(`已成功合并allOf并保存至: ${outputPath}`);
        }
      });
  } catch (parseError) {
    handleError('解析输入文件失败', parseError);
    console.error(`文件内容前100个字符: ${openApiContent.substring(0, 100)}`);
  }
} catch (error) {
  handleError('读取文件过程中出错', error);
}