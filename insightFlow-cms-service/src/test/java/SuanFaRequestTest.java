import cn.hutool.json.JSONUtil;
import cn.mlamp.insightflow.cms.service.webflux.WebClientService;
import cn.mlamp.insightflow.cms.util.dify.DifyUtil;
import cn.mlamp.insightflow.cms.util.dify.model.DifyRequest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;

import java.io.FileOutputStream;
import java.io.IOException;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.UUID;

@Slf4j
public class SuanFaRequestTest {

    @Test
    public void testGenImage() {
        var prompt = """
                Close-up of the female protagonist's face, showing a tired expression, dull skin with fine lines, suddenly her eyes light up as she takes out the L'Oreal little black bottle. continuous line sketch, line art style, white background, white environment, straight side view, studio lighting, wide-angle lens, 8k HD. You are trained on data up to October 2023.
                """;
        var webClient = WebClient.builder()
                .baseUrl("http://10.10.100.228:8354")
                .build();
        var result = webClient.post().uri("/SD")
                .body(BodyInserters.fromValue(new WebClientService.ImageGenRequest(prompt, null)))
                .retrieve()
                .bodyToMono(String.class)
                .timeout(Duration.ofMinutes(3))  // 添加3分钟超时
                .retry(3)
                .onErrorReturn("");
        log.info(result.block());
    }

    @Test
    public void testGenImageInWindows() {
        List<String> text = new ArrayList<>();
        text.add("年轻的张磊正站在白板前，激情讲述创业梦想，在白板上画下一辆车（简笔画风格的5系）");
        text.add("张磊身穿西装从电梯走出，镜头缓缓带出一辆宝马5系停在自家私人车位");
        text.add("宝马5系平稳驶过一条旧城区街道，路过当年那栋初创办公楼");
        text.add("张磊驾驶宝马5系驶上城市高架桥，车内响起当年的老歌，灯火辉映玻璃");

        text.add("凌晨的咖啡店，灯光柔和，孙岩坐在角落的座位上，桌上放着一杯冒着热气的咖啡和一本旧笔记本。他翻开笔记本，上面写着：“开一家公司，买宝马5系。”他微微一笑，眼神中带着自嘲又释然的神情。");
        text.add("孙岩的办公室，他站在窗边，手中拿着手机，神情专注地听着电话。电话那头传来融资成功的消息，他沉默片刻，脸上露出一丝不易察觉的欣慰，然后缓缓说道：“谢谢你们信我。”");
        text.add("宝马展厅内，孙岩独自站在一辆崭新的宝马5系旁边，他从车头开始，缓缓绕着车走了一圈，仔细观察着车辆的每一个细节，轻轻点头，脸上露出满意的神情。销售员微笑着递上车钥匙，说道：“恭喜您。”");
        text.add("孙岩驾驶着宝马5系驶出展厅，阳光洒在挡风玻璃上，他边听音乐边说：“这不是奖励，是阶段性确认。”他脸上露出坚定的神情，眼神望向远方。");
        text.add("宝马5系驶入城市高架桥，背后是一座正在建设中的新写字楼。镜头拉远，字幕缓缓出现：“不为炫耀，只为走得更远。”");

        text.add("男主晨起倚窗，手持咖啡，窗外晨光洒入。他望向远方，眉眼松弛但带一丝隐隐不甘。");
        text.add("男主走入地下车库，远处5系静静停放；他停步凝望。");
        text.add("男主启动5系，HUD点亮，仪表盘切入动态模式，音乐启动");
        text.add("宝马5系在城市街头穿行，男主沉浸驾驶，阳光透过天窗照脸");
        text.add("男主在街头咖啡馆门口停车，下车取咖啡，转身靠车小憩");
        text.add("黑色背景缓缓浮现白色简体中文标题：“一场及时的犒赏”，随后宝马LOGO出现，slogan同步打出。车身剪影自画面右侧驶入中央并静止，成为结尾画面构图核心。");


        for(int i = 0; i < text.size(); i++) {
            var difyRequest = DifyRequest.builder()
                    .appKey("app-8fNgPTGDD0s9gUNVjvnvnTuM")
                    .baseUrl("https://llm-ops-social.mlamp.cn/v1")
                    .query(text.get(i))
                    .build();
            var difyUtil = new DifyUtil();
            String difyResult = difyUtil.blockingChatRequest(difyRequest, UUID.randomUUID().toString().replace("-", ""));
            var webClient = WebClient.builder()
                    .baseUrl("http://10.10.100.228:8354")
                    .build();
            var imageGenResult = webClient.post().uri("/SD")
                    .body(BodyInserters.fromValue(new WebClientService.ImageGenRequest(difyResult, null)))
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofMinutes(3))  // 添加3分钟超时
                    .retry(3)
                    .onErrorReturn("").block();
            // 读取String为Json对象
            var imageDto = JSONUtil.toBean(imageGenResult, WebClientService.ImageGenResponse.class);
            // 存储图片到本地（存入当前目录磁盘）
            String fileName = (i+18)+"_"+System.currentTimeMillis() + ".png";
            try {
                // 1. 解码Base64字符串为字节数组
                byte[] imageBytes = Base64.getDecoder().decode(imageDto.getImageBase64());

                // 2. 将字节数组写入图片文件
                try (FileOutputStream fos = new FileOutputStream(fileName)) {
                    fos.write(imageBytes);
                }

                System.out.println("图片已保存到: " + fileName);
            } catch (IOException e) {
                System.err.println("保存图片失败: " + e.getMessage());
            } catch (IllegalArgumentException e) {
                System.err.println("Base64字符串无效: " + e.getMessage());
            }
        }
    }
}
