package cn.mlamp.insightflow.cms.service.dam;

import cn.mlamp.insightflow.cms.BaseSpringBootTest;
import cn.mlamp.insightflow.cms.constant.PermissionConstant;
import cn.mlamp.insightflow.cms.entity.dam.DamPublicTag;
import cn.mlamp.insightflow.cms.entity.dam.DamTag;
import cn.mlamp.insightflow.cms.enums.dam.DamTagTypeEnum;
import cn.mlamp.insightflow.cms.model.converter.dam.DamTagConverter;
import cn.mlamp.insightflow.cms.model.dto.dam.DamTagDTO;
import cn.mlamp.insightflow.cms.model.vo.dam.DamTagVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.shiro.authz.AuthorizationException;
import org.apache.shiro.subject.Subject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

class IDamTagServiceTest extends BaseSpringBootTest {

    @Autowired
    private IDamTagService damTagService;

    @MockitoBean
    private IDamPublicTagService publicTagService;

    @MockitoBean
    private DamTagConverter tagConverter;

    private MockedStatic<org.apache.shiro.SecurityUtils> mockedSecurityUtils;
    private Subject mockSubject;

    private static final Integer USER_ID = 1001;
    private static final String USER_NAME = "testUser";
    private static final Integer TENANT_ID = 2001;

    @BeforeEach
    void setUp() {
        // 模拟当前用户
        mockCurrentUser(USER_ID, USER_NAME, TENANT_ID);

        // 模拟Shiro的Subject
        mockedSecurityUtils = Mockito.mockStatic(org.apache.shiro.SecurityUtils.class);
        mockSubject = mock(Subject.class);
        mockedSecurityUtils.when(org.apache.shiro.SecurityUtils::getSubject).thenReturn(mockSubject);
    }

    @Test
    void testCreateCustomTag() {
        // 准备测试数据
        DamTagDTO tagDTO = new DamTagDTO();
        tagDTO.setName("测试标签");
        tagDTO.setDescription("测试描述");
        tagDTO.setExample("测试示例");

        DamTag tag = new DamTag();
        tag.setId(1);
        tag.setName("测试标签");
        tag.setType(DamTagTypeEnum.CUSTOM);
        tag.setDescription("测试描述");
        tag.setExample("测试示例");
        tag.setTenantId(TENANT_ID);
        tag.setUserId(USER_ID);

        DamTagVO expectedVO = new DamTagVO();
        expectedVO.setId(1);
        expectedVO.setName("测试标签");
        expectedVO.setType(DamTagTypeEnum.CUSTOM);
        expectedVO.setDescription("测试描述");
        expectedVO.setExample("测试示例");

        // 模拟方法行为
        when(tagConverter.toEntity(any(DamTagDTO.class))).thenReturn(tag);
        when(tagConverter.toVO(any(DamTag.class))).thenReturn(expectedVO);

        // 执行测试
        DamTagVO result = damTagService.createCustomTag(tagDTO, USER_ID, TENANT_ID);

        // 验证结果
        assertNotNull(result);
        assertEquals("测试标签", result.getName());
        assertEquals(DamTagTypeEnum.CUSTOM, result.getType());
        assertEquals("测试描述", result.getDescription());
        assertEquals("测试示例", result.getExample());
        assertEquals(0, result.getUsedNum());

        // 验证方法调用
        verify(tagConverter, times(1)).toEntity(any(DamTagDTO.class));
        verify(tagConverter, times(1)).toVO(any(DamTag.class));
    }

    @Test
    void testCreateCustomTag_duplicateTag() {
        // 预期会抛出异常的情况
        DamTagDTO tagDTO = new DamTagDTO();
        tagDTO.setName("已存在标签");
        tagDTO.setDescription("测试描述");
        tagDTO.setExample("测试示例");

        DamTag existingTag = new DamTag();
        existingTag.setId(1);
        existingTag.setName("已存在标签");
        existingTag.setType(DamTagTypeEnum.CUSTOM);
        existingTag.setTenantId(TENANT_ID);
        existingTag.setIsDeleted(false);

        // 这里我们使用了自定义的方法拦截，模拟调用list方法时返回一个非空列表
        IDamTagService spyService = spy(damTagService);
        doReturn(Collections.singletonList(existingTag)).when(spyService)
                .list(any(LambdaQueryWrapper.class));

        // 验证是否抛出预期异常
        Exception exception = assertThrows(IllegalArgumentException.class, () -> {
            spyService.createCustomTag(tagDTO, USER_ID, TENANT_ID);
        });

        assertEquals("标签已存在", exception.getMessage());
    }

    @Test
    void testGetTagList_allTags() {
        // 准备测试数据
        List<DamTag> tags = new ArrayList<>();
        DamTag tag1 = new DamTag();
        tag1.setId(1);
        tag1.setName("公共标签1");
        tag1.setType(DamTagTypeEnum.PUBLIC);
        tag1.setDescription("公共标签描述");
        tag1.setExample("公共标签示例");
        tag1.setTenantId(TENANT_ID);
        tag1.setIsDeleted(false);

        DamTag tag2 = new DamTag();
        tag2.setId(2);
        tag2.setName("自定义标签1");
        tag2.setType(DamTagTypeEnum.CUSTOM);
        tag2.setDescription("自定义标签描述");
        tag2.setExample("自定义标签示例");
        tag2.setTenantId(TENANT_ID);
        tag2.setIsDeleted(false);

        tags.add(tag1);
        tags.add(tag2);

        List<DamTagVO> tagVOs = new ArrayList<>();
        DamTagVO vo1 = new DamTagVO();
        vo1.setId(1);
        vo1.setName("公共标签1");
        vo1.setType(DamTagTypeEnum.PUBLIC);

        DamTagVO vo2 = new DamTagVO();
        vo2.setId(2);
        vo2.setName("自定义标签1");
        vo2.setType(DamTagTypeEnum.CUSTOM);

        tagVOs.add(vo1);
        tagVOs.add(vo2);

        // 模拟方法行为
        IDamTagService spyService = spy(damTagService);
        doReturn(tags).when(spyService).list(any(LambdaQueryWrapper.class));
        when(tagConverter.toVOs(any())).thenReturn(tagVOs);

        // 准备公共标签建议值
        DamPublicTag publicTag = new DamPublicTag();
        publicTag.setName("公共标签1");
        publicTag.setSuggestValues(Arrays.asList("值1", "值2", "值3"));
        when(publicTagService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.singletonList(publicTag));

        // 执行测试 - 获取所有标签且带推荐值
        List<DamTagVO> result = spyService.getTagList(null, true, USER_ID, TENANT_ID);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());

        // 验证方法调用
        verify(tagConverter, times(1)).toVOs(any());
        verify(publicTagService, times(1)).list(any(LambdaQueryWrapper.class));
    }

    @Test
    void testGetTagList_publicTagsOnly() {
        // 准备测试数据
        List<DamTag> tags = new ArrayList<>();
        DamTag tag1 = new DamTag();
        tag1.setId(1);
        tag1.setName("公共标签1");
        tag1.setType(DamTagTypeEnum.PUBLIC);
        tags.add(tag1);

        List<DamTagVO> tagVOs = new ArrayList<>();
        DamTagVO vo1 = new DamTagVO();
        vo1.setId(1);
        vo1.setName("公共标签1");
        vo1.setType(DamTagTypeEnum.PUBLIC);
        tagVOs.add(vo1);

        // 模拟方法行为
        IDamTagService spyService = spy(damTagService);
        doReturn(tags).when(spyService).list(any(LambdaQueryWrapper.class));
        when(tagConverter.toVOs(any())).thenReturn(tagVOs);

        // 执行测试 - 只获取公共标签
        List<DamTagVO> result = spyService.getTagList(DamTagTypeEnum.PUBLIC, false, USER_ID, TENANT_ID);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(DamTagTypeEnum.PUBLIC, result.get(0).getType());

        // 验证方法调用
        verify(tagConverter, times(1)).toVOs(any());
        verify(publicTagService, never()).list(any(LambdaQueryWrapper.class)); // 不获取推荐值时不会调用
    }

    @Test
    void testUpdateTag_customTag() {
        // 准备测试数据
        DamTagDTO tagDTO = new DamTagDTO();
        tagDTO.setName("更新后的自定义标签");
        tagDTO.setDescription("更新后的描述");
        tagDTO.setExample("更新后的示例");

        DamTag existingTag = new DamTag();
        existingTag.setId(2);
        existingTag.setName("自定义标签");
        existingTag.setType(DamTagTypeEnum.CUSTOM);
        existingTag.setDescription("原描述");
        existingTag.setExample("原示例");
        existingTag.setTenantId(TENANT_ID);
        existingTag.setIsDeleted(false);

        DamTagVO expectedVO = new DamTagVO();
        expectedVO.setId(2);
        expectedVO.setName("更新后的自定义标签");
        expectedVO.setType(DamTagTypeEnum.CUSTOM);
        expectedVO.setDescription("更新后的描述");
        expectedVO.setExample("更新后的示例");

        // 模拟方法行为
        IDamTagService spyService = spy(damTagService);
        doReturn(existingTag).when(spyService).getOne(any(LambdaQueryWrapper.class));
        doReturn(true).when(spyService).updateById(any(DamTag.class));
        when(tagConverter.toVO(any(DamTag.class))).thenReturn(expectedVO);

        // 执行测试
        DamTagVO result = spyService.updateTag(2, tagDTO, USER_ID, TENANT_ID);

        // 验证结果
        assertNotNull(result);
        assertEquals("更新后的自定义标签", result.getName());
        assertEquals("更新后的描述", result.getDescription());
        assertEquals("更新后的示例", result.getExample());

        // 验证原实体是否被更新
        assertEquals("更新后的自定义标签", existingTag.getName());
        assertEquals("更新后的描述", existingTag.getDescription());
        assertEquals("更新后的示例", existingTag.getExample());

        // 验证方法调用
        verify(spyService, times(1)).updateById(existingTag);
        verify(tagConverter, times(1)).toVO(existingTag);
    }

    @Test
    void testUpdateTag_publicTag_withPermission() {
        // 准备测试数据
        DamTagDTO tagDTO = new DamTagDTO();
        tagDTO.setName("更新后的公共标签");
        tagDTO.setDescription("更新后的描述");
        tagDTO.setExample("更新后的示例");

        DamTag existingTag = new DamTag();
        existingTag.setId(1);
        existingTag.setName("公共标签");
        existingTag.setType(DamTagTypeEnum.PUBLIC);
        existingTag.setDescription("原描述");
        existingTag.setExample("原示例");
        existingTag.setTenantId(TENANT_ID);
        existingTag.setIsDeleted(false);

        DamTagVO expectedVO = new DamTagVO();
        expectedVO.setId(1);
        expectedVO.setName("更新后的公共标签");
        expectedVO.setType(DamTagTypeEnum.PUBLIC);
        expectedVO.setDescription("更新后的描述");
        expectedVO.setExample("更新后的示例");

        // 模拟用户有权限
        when(mockSubject.isPermitted(PermissionConstant.DAM_ADMIN)).thenReturn(true);

        // 模拟方法行为
        IDamTagService spyService = spy(damTagService);
        doReturn(existingTag).when(spyService).getOne(any(LambdaQueryWrapper.class));
        doReturn(true).when(spyService).updateById(any(DamTag.class));
        when(tagConverter.toVO(any(DamTag.class))).thenReturn(expectedVO);

        // 执行测试
        DamTagVO result = spyService.updateTag(1, tagDTO, USER_ID, TENANT_ID);

        // 验证结果
        assertNotNull(result);
        assertEquals("更新后的公共标签", result.getName());

        // 验证原实体是否被更新
        assertEquals("更新后的公共标签", existingTag.getName());
        assertEquals("更新后的描述", existingTag.getDescription());
        assertEquals("更新后的示例", existingTag.getExample());

        // 验证方法调用
        verify(spyService, times(1)).updateById(existingTag);
        verify(tagConverter, times(1)).toVO(existingTag);
    }

    @Test
    void testUpdateTag_publicTag_withoutPermission() {
        // 准备测试数据
        DamTagDTO tagDTO = new DamTagDTO();
        tagDTO.setName("更新后的公共标签");
        tagDTO.setDescription("更新后的描述");
        tagDTO.setExample("更新后的示例");

        DamTag existingTag = new DamTag();
        existingTag.setId(1);
        existingTag.setName("公共标签");
        existingTag.setType(DamTagTypeEnum.PUBLIC);
        existingTag.setDescription("原描述");
        existingTag.setExample("原示例");
        existingTag.setTenantId(TENANT_ID);
        existingTag.setIsDeleted(false);

        // 模拟用户没有权限
        when(mockSubject.isPermitted(PermissionConstant.DAM_ADMIN)).thenReturn(false);
        doThrow(new AuthorizationException()).when(mockSubject).checkPermission(PermissionConstant.DAM_ADMIN);

        // 模拟方法行为
        IDamTagService spyService = spy(damTagService);
        doReturn(existingTag).when(spyService).getOne(any(LambdaQueryWrapper.class));

        // 验证是否抛出预期异常
        assertThrows(AuthorizationException.class, () -> {
            spyService.updateTag(1, tagDTO, USER_ID, TENANT_ID);
        });
    }

    @Test
    void testDeleteTag_customTag() {
        // 准备测试数据
        DamTag existingTag = new DamTag();
        existingTag.setId(2);
        existingTag.setName("自定义标签");
        existingTag.setType(DamTagTypeEnum.CUSTOM);
        existingTag.setTenantId(TENANT_ID);
        existingTag.setIsDeleted(false);

        // 模拟方法行为
        IDamTagService spyService = spy(damTagService);
        doReturn(existingTag).when(spyService).getOne(any(LambdaQueryWrapper.class));
        doReturn(true).when(spyService).updateById(any(DamTag.class));

        // 执行测试
        boolean result = spyService.deleteTag(2, USER_ID, TENANT_ID);

        // 验证结果
        assertFalse(result); // 自定义标签的删除应该返回false
        assertTrue(existingTag.getIsDeleted()); // 标签应该被标记为已删除

        // 验证方法调用
        verify(spyService, times(1)).updateById(existingTag);
    }

    @Test
    void testDeleteTag_publicTag_withPermission() {
        // 准备测试数据
        DamTag existingTag = new DamTag();
        existingTag.setId(1);
        existingTag.setName("公共标签");
        existingTag.setType(DamTagTypeEnum.PUBLIC);
        existingTag.setTenantId(TENANT_ID);
        existingTag.setIsDeleted(false);

        // 模拟用户有权限
        when(mockSubject.isPermitted(PermissionConstant.DAM_ADMIN)).thenReturn(true);

        // 模拟方法行为
        IDamTagService spyService = spy(damTagService);
        doReturn(existingTag).when(spyService).getOne(any(LambdaQueryWrapper.class));
        doReturn(true).when(spyService).updateById(any(DamTag.class));

        // 执行测试
        boolean result = spyService.deleteTag(1, USER_ID, TENANT_ID);

        // 验证结果
        assertTrue(result); // 公共标签成功删除应该返回true
        assertTrue(existingTag.getIsDeleted()); // 标签应该被标记为已删除

        // 验证方法调用
        verify(spyService, times(1)).updateById(existingTag);
    }

    @Test
    void testDeleteTag_publicTag_withoutPermission() {
        // 准备测试数据
        DamTag existingTag = new DamTag();
        existingTag.setId(1);
        existingTag.setName("公共标签");
        existingTag.setType(DamTagTypeEnum.PUBLIC);
        existingTag.setTenantId(TENANT_ID);
        existingTag.setIsDeleted(false);

        // 模拟用户没有权限
        when(mockSubject.isPermitted(PermissionConstant.DAM_ADMIN)).thenReturn(false);
        doThrow(new AuthorizationException()).when(mockSubject).checkPermission(PermissionConstant.DAM_ADMIN);

        // 模拟方法行为
        IDamTagService spyService = spy(damTagService);
        doReturn(existingTag).when(spyService).getOne(any(LambdaQueryWrapper.class));

        // 验证是否抛出预期异常
        assertThrows(AuthorizationException.class, () -> {
            spyService.deleteTag(1, USER_ID, TENANT_ID);
        });
    }

    @Test
    void testDeleteTag_tagNotFound() {
        // 模拟方法行为 - 标签不存在
        IDamTagService spyService = spy(damTagService);
        doReturn(null).when(spyService).getOne(any(LambdaQueryWrapper.class));

        // 验证是否抛出预期异常
        Exception exception = assertThrows(IllegalArgumentException.class, () -> {
            spyService.deleteTag(999, USER_ID, TENANT_ID);
        });

        assertEquals("标签未找到", exception.getMessage());
    }
}