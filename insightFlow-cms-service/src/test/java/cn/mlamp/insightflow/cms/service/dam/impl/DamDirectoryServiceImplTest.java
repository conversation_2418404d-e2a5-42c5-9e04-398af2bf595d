package cn.mlamp.insightflow.cms.service.dam.impl;

import cn.mlamp.insightflow.cms.BaseSpringBootTest;
import cn.mlamp.insightflow.cms.common.resp.RespCode;
import cn.mlamp.insightflow.cms.entity.dam.DamAsset;
import cn.mlamp.insightflow.cms.entity.dam.DamDirectory;
import cn.mlamp.insightflow.cms.entity.dam.DamRecycleBin;
import cn.mlamp.insightflow.cms.entity.dam.DamRecycleBinDirectoryAssetMapping;
import cn.mlamp.insightflow.cms.enums.dam.DamDirectoryTypeEnum;
import cn.mlamp.insightflow.cms.enums.dam.DamRecycleBinObjectTypeEnum;
import cn.mlamp.insightflow.cms.exception.BusinessException;
import cn.mlamp.insightflow.cms.model.converter.dam.DamDirectoryConverter;
import cn.mlamp.insightflow.cms.model.dto.dam.DamDirectoryDTO;
import cn.mlamp.insightflow.cms.model.vo.dam.DamDirectoryVO;
import cn.mlamp.insightflow.cms.service.IUserService;
import cn.mlamp.insightflow.cms.service.dam.IDamAssetService;
import cn.mlamp.insightflow.cms.service.dam.IDamDirectoryService;
import cn.mlamp.insightflow.cms.service.dam.IDamRecycleBinDirectoryAssetMappingService;
import cn.mlamp.insightflow.cms.service.dam.IDamRecycleBinService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.jupiter.params.provider.NullSource;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean;

import java.util.Comparator;
import java.util.List;
import java.util.Random;
import java.util.stream.Stream;

import static cn.mlamp.insightflow.cms.enums.dam.DamDirectoryTypeEnum.PERSONAL;
import static cn.mlamp.insightflow.cms.enums.dam.DamDirectoryTypeEnum.TENANT;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * DamDirectoryServiceImpl 测试
 * 注意: 依赖 db 的集成测试
 *
 * <AUTHOR> liuyuan
 **/
@SpringBootTest
@ActiveProfiles("local")
class DamDirectoryServiceImplTest extends BaseSpringBootTest {
    @MockitoSpyBean
    private DamDirectoryServiceImpl directoryService;

    @MockitoSpyBean
    private DamDirectoryConverter directoryConverter;

    @Autowired
    private IDamAssetService assetService;

    @Autowired
    private IDamRecycleBinService recycleBinService;

    @Autowired
    private IDamRecycleBinDirectoryAssetMappingService recycleBinDirectoryAssetMappingService;

    private static final Integer mockUserId = new Random().nextInt(10000);
    private static final Integer mockTenantId = new Random().nextInt(10000);
    private static final String messageNotFound = "not found";
    private static final String messageNotAdmin = "only tenant administrator could";

    @BeforeEach
    void setUp() {
        String mockUserName = "testUser";
        mockCurrentUser(mockUserId, mockUserName, mockTenantId);

//        PowerMockito.replace(PowerMockito.method(ServiceImpl.class, "getSqlSessionFactory"))
//                .with((proxy, method, args) -> sqlSessionFactory);

    }

    /**
     * 为文件夹名添加时间戳后缀，避免重名
     *
     * @param name 文件夹名
     * @return name with suffix
     */
    String dirNameWithSuffix(String name) {
        return String.format("%s_%s", name, System.currentTimeMillis());
    }

    @ParameterizedTest
    @DisplayName("测试创建")
    @EnumSource(DamDirectoryTypeEnum.class)
    void createDirectory(DamDirectoryTypeEnum type) {
        when(directoryService.isTenantAdmin()).thenReturn(TENANT.equals(type));

        DamDirectoryDTO directoryDTO = new DamDirectoryDTO();
        directoryDTO.setName(dirNameWithSuffix("文件夹"));
        directoryDTO.setType(type);

        DamDirectoryVO result = directoryService.createDirectory(directoryDTO, mockUserId, mockTenantId);
        assertNotNull(result);
        assertEquals(directoryDTO.getName(), result.getName());
        assertEquals(type, result.getType());

        DamDirectory resultInDb = directoryService.getById(result.getId());
        assertNotNull(resultInDb);
        assertEquals(resultInDb.getName(), result.getName());
        assertEquals(type, resultInDb.getType());

        verify(directoryConverter).toEntity(any(DamDirectoryDTO.class));
        verify(directoryConverter).toVO(any(DamDirectory.class));
        verify(directoryService).save(any(DamDirectory.class));

        assertTrue(directoryService.removeById(result.getId()));
    }

    @Test
    @DisplayName("测试创建 - 租户文件夹（非租户管理员）")
    void createDirectory_TenantNotAdmin() {
        when(directoryService.isTenantAdmin()).thenReturn(false);
        DamDirectoryDTO directoryDTO = new DamDirectoryDTO();
        directoryDTO.setName(dirNameWithSuffix("文件夹"));
        directoryDTO.setType(DamDirectoryTypeEnum.TENANT);

        BusinessException exception = assertThrows(BusinessException.class,
                () -> directoryService.createDirectory(directoryDTO, mockUserId, mockTenantId));

        assertEquals(RespCode.FORBIDDEN.getCode(), exception.getStateCode());
        assertTrue(exception.getMessage().contains("only tenant administrator could create a tenant folder"));

        List<DamDirectory> result = directoryService.list(
                new LambdaQueryWrapper<DamDirectory>()
                        .eq(DamDirectory::getName, directoryDTO.getName())
                        .eq(DamDirectory::getType, directoryDTO.getType())
                        .eq(DamDirectory::getUserId, mockUserId)
                        .eq(DamDirectory::getTenantId, mockTenantId)
        );
        assertTrue(result.isEmpty());
    }

    @ParameterizedTest
    @DisplayName("测试获取目录列表")
    @EnumSource(DamDirectoryTypeEnum.class)
    @NullSource()
    void getDirectoryList(DamDirectoryTypeEnum type) {
        // 准备测试数据
        List<DamDirectoryVO> testData = Stream.of(
                        prepare("个人文件夹-1", PERSONAL, mockUserId, mockTenantId),
                        prepare("个人文件夹-2", PERSONAL, mockUserId, mockTenantId),

                        prepare("租户文件夹-6", TENANT, mockUserId, mockTenantId),
                        prepare("租户文件夹-7", TENANT, mockUserId, mockTenantId),
                        prepare("租户文件夹-8", TENANT, 10001, mockTenantId)
                )
                .map(directoryConverter::toVO)
                .sorted(Comparator.comparing(DamDirectoryVO::getId))
                .toList();

        List<DamDirectoryVO> expectDirectoryVOS = type == null ? testData :
                testData.stream().filter(directory -> type.equals(directory.getType())).toList();

        List<DamDirectoryVO> result = directoryService.getDirectoryList(type, mockUserId, mockTenantId);
        result.sort(Comparator.comparing(DamDirectoryVO::getId));

        assertEquals(expectDirectoryVOS, result);

        verify(directoryService).list(any(LambdaQueryWrapper.class));
        verify(directoryConverter).toVOs(anyList());

        List<Integer> ids = testData.stream().map(DamDirectoryVO::getId).toList();
        assertTrue(directoryService.removeByIds(ids));
    }

    @ParameterizedTest
    @DisplayName("测试获取目录列表 - 权限不可见")
    @EnumSource(DamDirectoryTypeEnum.class)
    @NullSource()
    void getDirectoryList_NotFound(DamDirectoryTypeEnum type) {
        List<DamDirectory> testData = List.of(
                prepare("个人文件夹-3", PERSONAL, mockUserId, 20001),
                prepare("个人文件夹-4", PERSONAL, 10001, mockTenantId),
                prepare("个人文件夹-5", PERSONAL, 10001, 20001),
                prepare("租户文件夹-9", TENANT, mockUserId, 20001),
                prepare("租户文件夹-10", TENANT, 10001, 20001)
        );
        List<DamDirectoryVO> result = directoryService.getDirectoryList(type, mockUserId, mockTenantId);

        assertTrue(result.isEmpty());

        verify(directoryService).list(any(LambdaQueryWrapper.class));
        verify(directoryConverter).toVOs(anyList());

        List<Integer> ids = testData.stream().map(DamDirectory::getId).toList();
        assertTrue(directoryService.removeByIds(ids));
    }

    static Stream<Arguments> testDataFor_getDirectoryDetail() {
        return Stream.of(
                Arguments.of("个人文件夹-1", PERSONAL, mockUserId, mockTenantId),
                Arguments.of("租户文件夹-6", TENANT, mockUserId, mockTenantId),
                Arguments.of("租户文件夹-8", TENANT, 10001, mockTenantId)
        );
    }

    @ParameterizedTest
    @DisplayName("测试获取文件夹详情")
    @MethodSource("testDataFor_getDirectoryDetail")
    void getDirectoryDetail(String name, DamDirectoryTypeEnum type, Integer userId, Integer tenantId) {
        DamDirectoryVO expectDirectory = directoryConverter.toVO(prepare(name, type, userId, tenantId));

        DamDirectoryVO result = directoryService.getDirectoryDetail(expectDirectory.getId(), mockUserId, mockTenantId);
        assertNotNull(result);
        assertEquals(expectDirectory, result);

        verify(directoryService).getOne(any(LambdaQueryWrapper.class));

        assertTrue(directoryService.removeById(expectDirectory.getId()));
    }

    static Stream<Arguments> testDataFor_getDirectoryDetail_NotFound() {
        return Stream.of(
                Arguments.of("个人文件夹-3", PERSONAL, mockUserId, 20001),
                Arguments.of("个人文件夹-4", PERSONAL, 10001, mockTenantId),
                Arguments.of("个人文件夹-5", PERSONAL, 10001, 20001),
                Arguments.of("租户文件夹-9", TENANT, mockUserId, 20001),
                Arguments.of("租户文件夹-10", TENANT, 10001, 20001)
        );
    }

    @ParameterizedTest
    @DisplayName("测试获取文件夹详情 - 文件夹不可见")
    @MethodSource("testDataFor_getDirectoryDetail_NotFound")
    void getDirectoryDetail_NotFound(String name, DamDirectoryTypeEnum type, Integer userId, Integer tenantId) {
        DamDirectory expectDirectory = prepare(name, type, userId, tenantId);

        IDamDirectoryService spyService = Mockito.spy(directoryService);
        BusinessException exception = assertThrows(BusinessException.class,
                () -> spyService.getDirectoryDetail(expectDirectory.getId(), mockUserId, mockTenantId)
        );

        assertEquals(RespCode.NOT_FOUND.getCode(), exception.getStateCode());
        assertTrue(exception.getMessage().contains("directory is not found"));

        verify(directoryService).getOne(any(LambdaQueryWrapper.class));

        assertTrue(directoryService.removeById(expectDirectory.getId()));
    }

    static Stream<Arguments> testDataFor_updateDirectory() {
        return Stream.of(
                Arguments.of("个人文件夹-1", PERSONAL, mockUserId, mockTenantId),
                Arguments.of("租户文件夹-6", TENANT, mockUserId, mockTenantId),
                Arguments.of("租户文件夹-8", TENANT, 10001, mockTenantId)
        );
    }

    @ParameterizedTest
    @DisplayName("测试更新文件夹")
    @MethodSource("testDataFor_updateDirectory")
    void updateDirectory(String name, DamDirectoryTypeEnum type, Integer userId, Integer tenantId) {
        when(directoryService.isTenantAdmin()).thenReturn(TENANT.equals(type));
        DamDirectory expectDirectory = prepare(name, type, userId, tenantId);

        DamDirectoryDTO update = new DamDirectoryDTO();
        update.setName(name + "_new_name");

        assertTrue(directoryService.updateDirectory(expectDirectory.getId(), update, mockUserId, mockTenantId));

        DamDirectory result = directoryService.getById(expectDirectory.getId());
        assertNotNull(result);
        assertEquals(update.getName(), result.getName());
        assertEquals(type, result.getType());

        verify(directoryService).getOne(any(LambdaQueryWrapper.class));
        verify(directoryService).updateById(any(DamDirectory.class));

        assertTrue(directoryService.removeById(expectDirectory));
    }

    static Stream<Arguments> testDataFor_updateDirectory_Exception() {
        return Stream.of(
                Arguments.of("个人文件夹-3", PERSONAL, mockUserId, 20001, RespCode.NOT_FOUND, messageNotFound),
                Arguments.of("个人文件夹-4", PERSONAL, 10001, mockTenantId, RespCode.NOT_FOUND, messageNotFound),
                Arguments.of("个人文件夹-5", PERSONAL, 10001, 20001, RespCode.NOT_FOUND, messageNotFound),
                Arguments.of("租户文件夹-9", TENANT, mockUserId, 20001, RespCode.NOT_FOUND, messageNotFound),
                Arguments.of("租户文件夹-10", TENANT, 10001, 20001, RespCode.NOT_FOUND, messageNotFound),

                Arguments.of("租户文件夹-6", TENANT, mockUserId, mockTenantId, RespCode.FORBIDDEN, messageNotAdmin),
                Arguments.of("租户文件夹-8", TENANT, 10001, mockTenantId, RespCode.FORBIDDEN, messageNotAdmin)
        );
    }

    @ParameterizedTest
    @DisplayName("测试更新文件夹 - 异常")
    @MethodSource("testDataFor_updateDirectory_Exception")
    void updateDirectory_Exception(String name, DamDirectoryTypeEnum type,
                                   Integer userId, Integer tenantId,
                                   RespCode code, String message) {
        DamDirectory expectDirectory = prepare(name, type, userId, tenantId);

        DamDirectoryDTO update = new DamDirectoryDTO();
        update.setName(name + "_new_name");

        IDamDirectoryService spyService = Mockito.spy(directoryService);
        BusinessException exception = assertThrows(BusinessException.class,
                () -> spyService.updateDirectory(expectDirectory.getId(), update, mockUserId, mockTenantId)
        );

        assertEquals(code.getCode(), exception.getStateCode());
        assertTrue(exception.getMessage().contains(message));

        DamDirectory result = directoryService.getById(expectDirectory.getId());
        assertNotNull(result);
        assertEquals(name, result.getName());

        assertTrue(directoryService.removeById(expectDirectory));
    }

    static Stream<Arguments> testDataFor_deleteDirectory() {
        return Stream.of(
                Arguments.of("个人文件夹-1", PERSONAL, mockUserId, mockTenantId),
                Arguments.of("租户文件夹-6", TENANT, mockUserId, mockTenantId),
                Arguments.of("租户文件夹-8", TENANT, 10001, mockTenantId)
        );
    }

    @ParameterizedTest
    @DisplayName("测试删除文件夹")
    @MethodSource("testDataFor_deleteDirectory")
    void deleteDirectory(String name, DamDirectoryTypeEnum type, Integer userId, Integer tenantId) {
        when(directoryService.isTenantAdmin()).thenReturn(TENANT.equals(type));
        DamDirectory expectDirectory = prepare(name, type, userId, tenantId);
        DamAsset asset = prepareAsset("测试素材-" + name, expectDirectory.getId(), userId, tenantId);

        assertTrue(directoryService.deleteDirectory(expectDirectory.getId(), mockUserId, mockTenantId));

        DamDirectory result = directoryService.getById(expectDirectory.getId());
        assertNotNull(result);
        assertTrue(result.getIsDeleted());

        DamAsset assetResult = assetService.getById(asset.getId());
        assertNotNull(assetResult);
        assertTrue(assetResult.getIsDeleted());

        List<DamRecycleBin> recycleBins = recycleBinService.list(
                new LambdaQueryWrapper<DamRecycleBin>()
                        .eq(DamRecycleBin::getObjectType, DamRecycleBinObjectTypeEnum.DIRECTORY)
                        .eq(DamRecycleBin::getDirectoryType, type)
                        .eq(DamRecycleBin::getObjectId, expectDirectory.getId())
                        .eq(DamRecycleBin::getUserId, mockUserId)
                        .eq(DamRecycleBin::getTenantId, mockTenantId)
        );
        assertNotNull(recycleBins);
        assertEquals(1, recycleBins.size());

        List<DamRecycleBinDirectoryAssetMapping> mappings = recycleBinDirectoryAssetMappingService.list(
                new LambdaQueryWrapper<DamRecycleBinDirectoryAssetMapping>()
                        .eq(DamRecycleBinDirectoryAssetMapping::getAssetId, asset.getId())
                        .eq(DamRecycleBinDirectoryAssetMapping::getRecycleBinId, recycleBins.get(0).getId())
        );
        assertNotNull(mappings);
        assertEquals(1, mappings.size());

        verify(directoryService).getOne(any(LambdaQueryWrapper.class));
        verify(directoryService).updateById(any(DamDirectory.class));

        assertTrue(directoryService.removeById(expectDirectory));
        assertTrue(assetService.removeById(asset));
        assertTrue(recycleBinService.removeById(recycleBins.get(0)));
        assertTrue(recycleBinDirectoryAssetMappingService.removeById(mappings.get(0)));
    }

    static Stream<Arguments> testDataFor_deleteDirectory_Exception() {
        return Stream.of(
                Arguments.of("个人文件夹-3", PERSONAL, mockUserId, 20001, RespCode.NOT_FOUND, messageNotFound),
                Arguments.of("个人文件夹-4", PERSONAL, 10001, mockTenantId, RespCode.NOT_FOUND, messageNotFound),
                Arguments.of("个人文件夹-5", PERSONAL, 10001, 20001, RespCode.NOT_FOUND, messageNotFound),
                Arguments.of("租户文件夹-9", TENANT, mockUserId, 20001, RespCode.NOT_FOUND, messageNotFound),
                Arguments.of("租户文件夹-10", TENANT, 10001, 20001, RespCode.NOT_FOUND, messageNotFound),

                Arguments.of("租户文件夹-6", TENANT, mockUserId, mockTenantId, RespCode.FORBIDDEN, messageNotAdmin),
                Arguments.of("租户文件夹-8", TENANT, 10001, mockTenantId, RespCode.FORBIDDEN, messageNotAdmin)
        );
    }

    @ParameterizedTest
    @DisplayName("测试删除文件夹 - 异常")
    @MethodSource("testDataFor_deleteDirectory_Exception")
    void deleteDirectory_Exception(String name, DamDirectoryTypeEnum type,
                                   Integer userId, Integer tenantId,
                                   RespCode code, String message) {
        DamDirectory expectDirectory = prepare(name, type, userId, tenantId);
        DamAsset asset = prepareAsset("测试素材-" + name, expectDirectory.getId(), userId, tenantId);

        IDamDirectoryService spyService = Mockito.spy(directoryService);
        BusinessException exception = assertThrows(BusinessException.class,
                () -> spyService.deleteDirectory(expectDirectory.getId(), mockUserId, mockTenantId)
        );

        assertEquals(code.getCode(), exception.getStateCode());
        assertTrue(exception.getMessage().contains(message));

        DamDirectory result = directoryService.getById(expectDirectory.getId());
        assertNotNull(result);
        assertFalse(result.getIsDeleted());

        DamAsset assetResult = assetService.getById(asset.getId());
        assertNotNull(assetResult);
        assertFalse(assetResult.getIsDeleted());

        List<DamRecycleBin> recycleBin = recycleBinService.list(
                new LambdaQueryWrapper<DamRecycleBin>()
                        .eq(DamRecycleBin::getObjectType, DamRecycleBinObjectTypeEnum.DIRECTORY)
                        .eq(DamRecycleBin::getDirectoryType, type)
                        .eq(DamRecycleBin::getObjectId, expectDirectory.getId())
                        .eq(DamRecycleBin::getUserId, mockUserId)
                        .eq(DamRecycleBin::getTenantId, mockTenantId)
        );
        assertNotNull(recycleBin);
        assertTrue(recycleBin.isEmpty());

        verify(directoryService).getOne(any(LambdaQueryWrapper.class));

        assertTrue(directoryService.removeById(expectDirectory));
        assertTrue(assetService.removeById(asset));
    }

    /**
     * 构造 dir 数据
     *
     * @param name     name
     * @param type     type
     * @param userId   userId
     * @param tenantId tenantId
     * @return DamDirectory
     */
    DamDirectory prepare(String name,
                         DamDirectoryTypeEnum type,
                         Integer userId,
                         Integer tenantId) {
        DamDirectory directory = new DamDirectory();
        directory.setName(name);
        directory.setType(type);
        directory.setUserId(userId);
        directory.setTenantId(tenantId);
        directoryService.save(directory);
        return directoryService.getById(directory.getId());
    }

    /**
     * 准备 asset 数据
     *
     * @param name        name
     * @param directoryId type
     * @param userId      userId
     * @param tenantId    tenantId
     * @return DamAsset
     */
    DamAsset prepareAsset(String name,
                          Integer directoryId,
                          Integer userId,
                          Integer tenantId) {
        DamAsset asset = new DamAsset();
        asset.setName(name);
        asset.setTenantId(tenantId);
        asset.setUserId(userId);
        asset.setDirectoryId(directoryId);
        asset.setOssId("");
        assetService.save(asset);
        return assetService.getById(asset.getId());
    }
}