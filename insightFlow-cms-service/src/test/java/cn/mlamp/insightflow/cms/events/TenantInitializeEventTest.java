package cn.mlamp.insightflow.cms.events;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import cn.mlamp.insightflow.cms.entity.dam.DamPublicTag;
import cn.mlamp.insightflow.cms.entity.dam.DamTag;
import cn.mlamp.insightflow.cms.enums.dam.DamTagTypeEnum;
import cn.mlamp.insightflow.cms.listeners.TenantListener;
import cn.mlamp.insightflow.cms.service.dam.IDamPublicTagService;
import cn.mlamp.insightflow.cms.service.dam.IDamTagService;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@ExtendWith(MockitoExtension.class)
@Rollback
@SpringBootTest
public class TenantInitializeEventTest {

    @Mock
    private IDamTagService tagService;

    @Mock
    private IDamPublicTagService publicTagService;

    @InjectMocks
    private TenantListener tenantListener;

    private static final Integer TEST_TENANT_ID = 100;
    private static final Integer TEST_USER_ID = 200;

    @Test
    @DisplayName("测试租户初始化 - 当租户没有标签时应授权所有公共标签")
    public void testTenantInitialize_WithNoExistingTags() {
        // 准备测试数据
        List<DamTag> emptyTagList = new ArrayList<>();
        List<DamPublicTag> publicTags = createMockPublicTags();

        // 配置模拟行为
        when(tagService.list(any(LambdaQueryWrapper.class))).thenReturn(emptyTagList);
        when(publicTagService.list()).thenReturn(publicTags);

        // 执行测试
        TenantInitializeEvent event = new TenantInitializeEvent(this, TEST_TENANT_ID, TEST_USER_ID);
        tenantListener.onTenantInitialize(event);

        // 验证结果
        verify(tagService, times(1)).list(any(LambdaQueryWrapper.class));
        verify(publicTagService, times(1)).list();
        verify(tagService, times(1)).saveBatch(anyList());
    }

    @Test
    @DisplayName("测试租户初始化 - 当租户已有标签时不应再次授权")
    public void testTenantInitialize_WithExistingTags() {
        // 准备测试数据
        List<DamTag> existingTags = createMockExistingTags();

        // 配置模拟行为
        when(tagService.list(any(LambdaQueryWrapper.class))).thenReturn(existingTags);

        // 执行测试
        TenantInitializeEvent event = new TenantInitializeEvent(this, TEST_TENANT_ID, TEST_USER_ID);
        tenantListener.onTenantInitialize(event);

        // 验证结果
        verify(tagService, times(1)).list(any(LambdaQueryWrapper.class));
        verify(publicTagService, never()).list();
        verify(tagService, never()).saveBatch(anyList());
    }

    @Test
    @DisplayName("测试TenantInitializeEvent属性")
    public void testTenantInitializeEventProperties() {
        // 创建事件实例
        TenantInitializeEvent event = new TenantInitializeEvent(this, TEST_TENANT_ID, TEST_USER_ID);

        // 验证属性
        assertEquals(TEST_TENANT_ID, event.getTenantId());
        assertEquals(TEST_USER_ID, event.getUserId());
        assertEquals(this, event.getSource());
    }

    /**
     * 创建模拟的公共标签数据
     */
    private List<DamPublicTag> createMockPublicTags() {
        List<DamPublicTag> publicTags = new ArrayList<>();

        DamPublicTag tag1 = new DamPublicTag();
        tag1.setId(1);
        tag1.setName("公共标签1");
        tag1.setDescription("描述1");
        tag1.setExample("示例1");

        DamPublicTag tag2 = new DamPublicTag();
        tag2.setId(2);
        tag2.setName("公共标签2");
        tag2.setDescription("描述2");
        tag2.setExample("示例2");

        publicTags.add(tag1);
        publicTags.add(tag2);

        return publicTags;
    }

    /**
     * 创建模拟的已存在标签数据
     */
    private List<DamTag> createMockExistingTags() {
        List<DamTag> existingTags = new ArrayList<>();

        DamTag tag1 = new DamTag();
        tag1.setId(1);
        tag1.setTenantId(TEST_TENANT_ID);
        tag1.setUserId(TEST_USER_ID);
        tag1.setName("已存在标签1");
        tag1.setType(DamTagTypeEnum.PUBLIC);

        existingTags.add(tag1);

        return existingTags;
    }
}
