package cn.mlamp.insightflow.cms.task.dam;

import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.ArgumentCaptor;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import org.mockito.Captor;
import org.mockito.Mock;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import cn.mlamp.insightflow.cms.BaseSpringBootTest;
import cn.mlamp.insightflow.cms.common.file.IS3FlowService;
import cn.mlamp.insightflow.cms.common.redis.LockService;
import cn.mlamp.insightflow.cms.config.TaskConfig;
import cn.mlamp.insightflow.cms.config.properties.DamOSSProperties;
import cn.mlamp.insightflow.cms.entity.CmsTaskInfo;
import cn.mlamp.insightflow.cms.entity.dam.DamAsset;
import cn.mlamp.insightflow.cms.entity.dam.DamAssetUploadTaskDetail;
import cn.mlamp.insightflow.cms.enums.VideoTaskStatusEnum;
import cn.mlamp.insightflow.cms.enums.dam.DamTaskStatusEnum;
import cn.mlamp.insightflow.cms.model.dto.dam.DamAssetUploadTaskArg;
import cn.mlamp.insightflow.cms.service.ICmsTaskInfoService;
import cn.mlamp.insightflow.cms.service.dam.IDamAssetService;
import cn.mlamp.insightflow.cms.service.dam.IDamAssetUploadTaskDetailService;
import cn.mlamp.insightflow.cms.strategy.handle.VideoRecognition2Handle;
import cn.mlamp.insightflow.cms.util.VideoUtil;

public abstract class AssetUploadTaskBaseTest extends BaseSpringBootTest {
    protected static final Integer MOCK_USER_ID = 1001;
    protected static final Integer MOCK_TENANT_ID = 2001;

    protected static final Integer MOCK_DIRECTORY_ID = 1;
    protected static final Integer MOCK_TASK_ID = 1001;

    protected static final String MOCK_ASSET_OSS_ID= "src/test/video/dam/origin.mp4";

    @Mock
    protected LockService lockService;

    @Mock
    protected RLock lock;

    @Mock
    protected IDamAssetService assetService;

    @Mock
    protected ICmsTaskInfoService taskService;

    @Mock
    protected IDamAssetUploadTaskDetailService assetUploadTaskDetailService;

    @Mock
    protected IS3FlowService s3Service;

    @Mock
    protected TaskConfig taskConfig;

    @MockitoSpyBean
    protected DamOSSProperties damOSSProperties;

    @Mock
    protected ObjectMapper objectMapper;

//    @InjectMocks
    @MockitoSpyBean
    protected AssetUploadTask assetUploadTask;

    @Captor
    protected ArgumentCaptor<LambdaQueryWrapper<DamAsset>> assetQueryCaptor;

    @MockitoSpyBean
    @Autowired
    @Qualifier("assetUploadTaskThreadExecutor")
    protected ExecutorService assetUploadTaskThreadExecutor;

    @MockitoSpyBean
    protected VideoRecognition2Handle videoRecognition2Handle;

    @BeforeEach
    void setUp() {
        // 模拟当前用户
        mockCurrentUser(MOCK_USER_ID, "testUser", MOCK_TENANT_ID);
        when(taskConfig.isLocal()).thenReturn(false);
        mockAssetUploadTask();
    }

    @AfterEach
    void tearDown() {
        // 清理资源
    }

    @DynamicPropertySource
    static void setProperties(DynamicPropertyRegistry registry) {
        registry.add("analysis.video.ffmpeg", () -> "/opt/homebrew/bin/ffmpeg");
        registry.add("analysis.video.ffprobe", () -> "/opt/homebrew/bin/ffprobe");
    }

    protected void mockAssetUploadTask() {
        doNothing().when(assetUploadTask).errorTask(eq(MOCK_TASK_ID), any());
        doNothing().when(assetUploadTask).onSuccess(any());
        doNothing().when(assetUploadTask).onError(any(), any());
        doNothing().when(assetUploadTask).completeTask(any());
        doNothing().when(assetUploadTask).errorTask(any(), any());
    }

    protected void checkAssetUploadTaskSuccess() {
        verify(assetUploadTask, times(1)).onSuccess(any());
        verify(assetUploadTask, times(0)).onError(any(), any());
        verify(assetUploadTask, times(1)).completeTask(any());
        verify(assetUploadTask, times(0)).errorTask(any(), any());
    }

    protected List<DamAsset> mockAssets(int num) {
        final List<DamAsset> assets = new ArrayList<>();
        for (int i = 0; i < num; i++) {
            // 模拟素材
            DamAsset asset = new DamAsset();
            asset.setId(i);
            asset.setTenantId(MOCK_TENANT_ID);
            asset.setUserId(MOCK_USER_ID);
            asset.setName("测试素材" + i);
            asset.setDirectoryId(MOCK_DIRECTORY_ID);
            asset.setOssId(MOCK_ASSET_OSS_ID);
            asset.setDuration(Math.toIntExact(VideoUtil.getVideoLengthAsMs(MOCK_ASSET_OSS_ID)));
            asset.setCreateTime(new Date());
            asset.setUpdateTime(new Date());
            assets.add(asset);

            when(assetService.getById(asset.getId())).thenReturn(asset);
            when(s3Service.existsObj(asset.getOssId())).thenReturn(true);
        }
        mockAssetsDownload(assets);
        return assets;
    }

    protected void mockAssetsDownload(List<DamAsset> assets) {
        for (DamAsset asset : assets) {
            when(s3Service.download(asset.getOssId())).thenAnswer(invocation -> Files.newInputStream(Path.of(asset.getOssId())));
        }
    }

    private List<DamAssetUploadTaskArg.Tag> createTags() {
        List<DamAssetUploadTaskArg.Tag> tags = new ArrayList<>();
        final DamAssetUploadTaskArg.Tag tag1 = new DamAssetUploadTaskArg.Tag();
        tag1.setId(1);
        tag1.setDescription("描述1");
        tag1.setExample("示例1");
        tags.add(tag1);
        final DamAssetUploadTaskArg.Tag tag2 = new DamAssetUploadTaskArg.Tag();
        tag2.setId(2);
        tag2.setDescription("描述2");
        tag2.setExample("示例2");
        tags.add(tag2);
        return tags;
    }

    protected CmsTaskInfo mockTaskInfo() throws JsonProcessingException {
        final DamAssetUploadTaskArg taskArg = new DamAssetUploadTaskArg();
        taskArg.setTags(createTags());

        when(objectMapper.writeValueAsString(taskArg)).thenReturn("""
                {
                    "assetIds": [2001, 2002],
                    "tags": [
                        {
                            "tagId": 1,
                            "tagName": "测试标签1",
                            "description": "描述1",
                            "example": "示例1"
                        },
                        {
                            "tagId": 2,
                            "tagName": "测试标签2",
                            "description": "描述2",
                            "example": "示例2"
                        }
                    ]
                }
                """.strip());
        when(objectMapper.readValue(anyString(), eq(DamAssetUploadTaskArg.class))).thenReturn(taskArg);

        final String taskArgJson = objectMapper.writeValueAsString(taskArg);

        final CmsTaskInfo taskInfo = new CmsTaskInfo();
        taskInfo.setId(MOCK_TASK_ID);
        taskInfo.setTaskArg(taskArgJson);
        taskInfo.setTaskStatus(VideoTaskStatusEnum.QUEUING.getCode());
        taskInfo.setCreateTime(new Date());
        taskInfo.setUpdateTime(new Date());

        when(taskService.getById(MOCK_TASK_ID)).thenReturn(taskInfo);
        return taskInfo;
    }

    protected void mockTaskDetail(List<DamAsset> assets) {
        final List<DamAssetUploadTaskDetail> taskDetails = new ArrayList<>();
        for (int i = 0; i < assets.size(); i++) {
            final DamAsset asset = assets.get(i);

            final DamAssetUploadTaskDetail taskDetail = new DamAssetUploadTaskDetail();
            taskDetail.setId(i);
            taskDetail.setTaskId(MOCK_TASK_ID);
            taskDetail.setAssetId(asset.getId());
            taskDetail.setStatus(DamTaskStatusEnum.PENDING);
            taskDetail.setCreateTime(new Date());
            taskDetail.setUpdateTime(new Date());
            taskDetails.add(taskDetail);

            when(assetUploadTaskDetailService.getById(taskDetail.getId())).thenReturn(taskDetail);
        }

        when(assetUploadTaskDetailService.list(any(LambdaQueryWrapper.class))).thenReturn(taskDetails);
    }
    
}
