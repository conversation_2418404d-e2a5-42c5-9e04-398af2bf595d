package cn.mlamp.insightflow.cms.util;

import java.io.IOException;
import java.nio.file.Path;

import static org.junit.jupiter.api.Assertions.assertEquals;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

import lombok.extern.slf4j.Slf4j;

@Slf4j
class VideoUtilTest {

    private static final String VIDEO_PATH = "src/test/video/dam/origin.mp4";

    @BeforeAll
    static void beforeAll() {
        VideoUtil.setFfmpegPath("/opt/homebrew/bin/ffmpeg");
        VideoUtil.setFfprobePath("/opt/homebrew/bin/ffprobe");
    }

    @Test
    void testCutVideo() throws IOException {
        FunctionUtils.withTempDirectory(path -> {
            final Path cutPath = path.resolve("cut.avi");
            final String originFormat = VideoUtil.getVideoFormat(VIDEO_PATH);
            final String originAspectRatio = VideoUtil.getVideoAspectRatio(VIDEO_PATH);
            VideoUtil.cutVideoSegment(
                    VIDEO_PATH,
                    cutPath.toString(),
                    "avi",
                    0,
                    VideoUtil.getVideoLengthAsMs(VIDEO_PATH)
            );
            final String cutFormat = VideoUtil.getVideoFormat(cutPath.toString());
            final String aspectRatio = VideoUtil.getVideoAspectRatio(cutPath.toString());
            assertEquals("9:16", originAspectRatio);
            assertEquals(originAspectRatio, aspectRatio);
            assertEquals("mp4", originFormat);
            assertEquals("avi", cutFormat);
        });
    }

}