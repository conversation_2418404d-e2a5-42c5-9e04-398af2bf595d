package cn.mlamp.insightflow.cms.controller.dam;

import cn.mlamp.insightflow.cms.BaseSpringBootTest;
import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.common.resp.RespCode;
import cn.mlamp.insightflow.cms.enums.dam.DamDirectoryTypeEnum;
import cn.mlamp.insightflow.cms.enums.dam.DamRecycleBinObjectTypeEnum;
import cn.mlamp.insightflow.cms.model.dto.dam.DamRecycleBinDeleteDTO;
import cn.mlamp.insightflow.cms.model.dto.dam.DamRecycleBinRecoverDTO;
import cn.mlamp.insightflow.cms.model.vo.dam.DamRecycleBinRecoverItem;
import cn.mlamp.insightflow.cms.model.vo.dam.DamRecycleBinVO;
import cn.mlamp.insightflow.cms.service.dam.IDamRecycleBinService;
import com.fasterxml.jackson.core.type.TypeReference;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class DamRecycleBinControllerTest extends BaseSpringBootTest {

    private MockMvc mockMvc;

    @Mock
    private IDamRecycleBinService recycleBinService;

    @InjectMocks
    private DamRecycleBinController damRecycleBinController;

    private final Integer mockUserId = 1001;
    private final String mockUserName = "testUser";
    private final Integer mockTenantId = 2001;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(damRecycleBinController).build();
        mockCurrentUser(mockUserId, mockUserName, mockTenantId);
    }

    /**
     * 创建测试用回收站列表
     */
    private List<DamRecycleBinVO> createTestRecycleBinList() {
        List<DamRecycleBinVO> recycleBinList = new ArrayList<>();

        DamRecycleBinVO recycleBin1 = new DamRecycleBinVO();
        recycleBin1.setId(1);
        recycleBin1.setTenantId(mockTenantId);
        recycleBin1.setUserId(mockUserId);
        recycleBin1.setObjectType(DamRecycleBinObjectTypeEnum.DIRECTORY); // 目录
        recycleBin1.setCreateTime(new Date());

        DamRecycleBinVO recycleBin2 = new DamRecycleBinVO();
        recycleBin2.setId(2);
        recycleBin2.setTenantId(mockTenantId);
        recycleBin2.setUserId(mockUserId);
        recycleBin2.setObjectType(DamRecycleBinObjectTypeEnum.ASSET); // 素材
        recycleBin2.setCreateTime(new Date());

        recycleBinList.add(recycleBin1);
        recycleBinList.add(recycleBin2);

        return recycleBinList;
    }

    /**
     * 创建测试用恢复结果列表
     */
    private List<DamRecycleBinRecoverItem> createTestRecoverResults() {
        List<DamRecycleBinRecoverItem> results = new ArrayList<>();

        DamRecycleBinRecoverItem result1 = new DamRecycleBinRecoverItem();
        result1.setRecycleBinId(1);
        result1.setIsRecovered(true);

        DamRecycleBinRecoverItem result2 = new DamRecycleBinRecoverItem();
        result2.setRecycleBinId(2);
        result2.setIsRecovered(false);

        results.add(result1);
        results.add(result2);

        return results;
    }

    @Test
    @DisplayName("测试获取回收站列表")
    void testGetRecycleBinList() throws Exception {
        // 准备测试数据
        List<DamRecycleBinVO> expectedList = createTestRecycleBinList();

        // 模拟Service层行为
        when(recycleBinService.getRecycleBinList(eq(DamDirectoryTypeEnum.PERSONAL), eq(mockUserId), eq(mockTenantId)))
                .thenReturn(expectedList);

        // 执行Controller方法
        final MvcResult result = mockMvc.perform(get("/api/dam/recycle-bin")
                        .param("type", String.valueOf(DamDirectoryTypeEnum.PERSONAL.getCode()))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.statusCode", is(200)))
                .andExpect(jsonPath("$.message", is("请求成功")))
                .andExpect(jsonPath("$.data", hasSize(2)))
                .andExpect(jsonPath("$.data[0].id", is(1)))
                .andExpect(jsonPath("$.data[1].id", is(2)))
                .andReturn();

        RespBody<List<DamRecycleBinVO>> response = extractMvcResultAsRespBody(
                result,
                new TypeReference<RespBody<List<DamRecycleBinVO>>>() {
                }
        );

        // 验证结果
        assertNotNull(response);
        assertEquals(RespCode.OK.getCode(), response.getStatusCode());
        assertEquals(2, response.getData().size());

        // 验证Service方法被调用
        Mockito.verify(recycleBinService).getRecycleBinList(eq(DamDirectoryTypeEnum.PERSONAL), eq(mockUserId), eq(mockTenantId));
    }

    @Test
    @DisplayName("测试恢复回收站对象")
    void testRecoverObjects() throws Exception {
        // 准备测试数据
        List<DamRecycleBinRecoverDTO> recoverDTOList = new ArrayList<>();
        DamRecycleBinRecoverDTO dto1 = new DamRecycleBinRecoverDTO();
        dto1.setRecycleBinId(1);

        DamRecycleBinRecoverDTO dto2 = new DamRecycleBinRecoverDTO();
        dto2.setRecycleBinId(2);
        dto2.setTargetDirectoryId(100); // 对于素材类型，设置目标目录

        recoverDTOList.add(dto1);
        recoverDTOList.add(dto2);

        List<DamRecycleBinRecoverItem> serviceResults = createTestRecoverResults();

        // 模拟Service层行为
        when(recycleBinService.recoverObjects(anyList(), eq(mockUserId), eq(mockTenantId)))
                .thenReturn(serviceResults);

        // 执行Controller方法
        final MvcResult result = mockMvc.perform(post("/api/dam/recycle-bin/recover")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(recoverDTOList)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.statusCode", is(200)))
                .andExpect(jsonPath("$.message", is("请求成功")))
                .andExpect(jsonPath("$.data", hasSize(2)))
                .andExpect(jsonPath("$.data[0].recycleBinId", is(1)))
                .andExpect(jsonPath("$.data[0].isRecovered", is(true)))
                .andExpect(jsonPath("$.data[1].recycleBinId", is(2)))
                .andExpect(jsonPath("$.data[1].isRecovered", is(false)))
                .andReturn();

        RespBody<List<DamRecycleBinRecoverItem>> response = extractMvcResultAsRespBody(
                result,
                new TypeReference<RespBody<List<DamRecycleBinRecoverItem>>>() {
                }
        );

        // 验证结果
        assertNotNull(response);
        assertEquals(RespCode.OK.getCode(), response.getStatusCode());
        assertEquals(2, response.getData().size());
        assertEquals(1, response.getData().get(0).getRecycleBinId());
        assertEquals(true, response.getData().get(0).getIsRecovered());

        // 验证Service方法被调用
        Mockito.verify(recycleBinService).recoverObjects(anyList(), eq(mockUserId), eq(mockTenantId));
    }

    @Test
    @DisplayName("测试删除回收站对象-成功")
    void testDeleteObjects_Success() throws Exception {
        // 准备测试数据
        DamRecycleBinDeleteDTO deleteDTO = new DamRecycleBinDeleteDTO();
        deleteDTO.setRecycleBinIds(Arrays.asList(1, 2, 3));

        // 模拟Service层行为
        when(recycleBinService.deleteObjects(anyList(), eq(mockUserId), eq(mockTenantId)))
                .thenReturn(true);

        // 执行Controller方法
        final MvcResult result = mockMvc.perform(post("/api/dam/recycle-bin/delete")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(deleteDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.statusCode", is(200)))
                .andExpect(jsonPath("$.message", is("请求成功")))
                .andReturn();

        RespBody<Void> response = extractMvcResultAsRespBody(result, Void.class);

        // 验证结果
        assertNotNull(response);
        assertEquals(RespCode.OK.getCode(), response.getStatusCode());

        // 验证Service方法被调用
        Mockito.verify(recycleBinService).deleteObjects(eq(deleteDTO.getRecycleBinIds()), eq(mockUserId), eq(mockTenantId));
    }

    @Test
    @DisplayName("测试删除回收站对象-失败")
    void testDeleteObjects_Failure() throws Exception {
        // 准备测试数据
        DamRecycleBinDeleteDTO deleteDTO = new DamRecycleBinDeleteDTO();
        deleteDTO.setRecycleBinIds(Arrays.asList(1, 2, 3));

        // 模拟Service层行为
        when(recycleBinService.deleteObjects(anyList(), eq(mockUserId), eq(mockTenantId)))
                .thenReturn(false);

        // 执行Controller方法
        final MvcResult result = mockMvc.perform(post("/api/dam/recycle-bin/delete")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(deleteDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.statusCode", is(RespCode.FAIL.getCode())))
                .andExpect(jsonPath("$.message", is("删除回收站对象失败")))
                .andReturn();

        RespBody<Void> response = extractMvcResultAsRespBody(result, Void.class);

        // 验证结果
        assertNotNull(response);
        assertEquals(RespCode.FAIL.getCode(), response.getStatusCode());
        assertEquals("删除回收站对象失败", response.getMessage());

        // 验证Service方法被调用
        Mockito.verify(recycleBinService).deleteObjects(eq(deleteDTO.getRecycleBinIds()), eq(mockUserId), eq(mockTenantId));
    }

    @Test
    @DisplayName("测试清空回收站-成功")
    void testEmptyRecycleBin_Success() throws Exception {
        // 模拟Service层行为
        when(recycleBinService.emptyRecycleBin(eq(mockUserId), eq(mockTenantId)))
                .thenReturn(true);

        // 执行Controller方法
        final MvcResult result = mockMvc.perform(post("/api/dam/recycle-bin/empty")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.statusCode", is(200)))
                .andExpect(jsonPath("$.message", is("请求成功")))
                .andReturn();

        RespBody<Void> response = extractMvcResultAsRespBody(result, Void.class);

        // 验证结果
        assertNotNull(response);
        assertEquals(RespCode.OK.getCode(), response.getStatusCode());

        // 验证Service方法被调用
        Mockito.verify(recycleBinService).emptyRecycleBin(eq(mockUserId), eq(mockTenantId));
    }

    @Test
    @DisplayName("测试清空回收站-失败")
    void testEmptyRecycleBin_Failure() throws Exception {
        // 模拟Service层行为
        when(recycleBinService.emptyRecycleBin(eq(mockUserId), eq(mockTenantId)))
                .thenReturn(false);

        // 执行Controller方法
        final MvcResult result = mockMvc.perform(post("/api/dam/recycle-bin/empty")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.statusCode", is(RespCode.FAIL.getCode())))
                .andExpect(jsonPath("$.message", is("清空回收站失败")))
                .andReturn();

        RespBody<Void> response = extractMvcResultAsRespBody(result, Void.class);

        // 验证结果
        assertNotNull(response);
        assertEquals(RespCode.FAIL.getCode(), response.getStatusCode());
        assertEquals("清空回收站失败", response.getMessage());

        // 验证Service方法被调用
        Mockito.verify(recycleBinService).emptyRecycleBin(eq(mockUserId), eq(mockTenantId));
    }
} 