package cn.mlamp.insightflow.cms.controller.dam;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.isNull;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import static org.mockito.Mockito.when;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import com.fasterxml.jackson.core.type.TypeReference;

import cn.mlamp.insightflow.cms.BaseSpringBootTest;
import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.common.resp.RespCode;
import cn.mlamp.insightflow.cms.enums.dam.DamTagTypeEnum;
import cn.mlamp.insightflow.cms.model.dto.dam.DamTagDTO;
import cn.mlamp.insightflow.cms.model.vo.dam.DamTagVO;
import cn.mlamp.insightflow.cms.service.dam.IDamTagService;

class DamTagControllerTest extends BaseSpringBootTest {

    private MockMvc mockMvc;

    @Mock
    private IDamTagService tagService;

    @InjectMocks
    private DamTagController damTagController;

    private final Integer mockUserId = 1001;
    private final String mockUserName = "testUser";
    private final Integer mockTenantId = 2001;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(damTagController).build();
        mockCurrentUser(mockUserId, mockUserName, mockTenantId);
    }

    /**
     * 创建测试用的DamTagDTO对象
     */
    private DamTagDTO createTestTagDTO() {
        DamTagDTO tagDTO = new DamTagDTO();
        tagDTO.setName("测试标签");
        tagDTO.setDescription("这是一个测试标签描述");
        tagDTO.setExample("测试示例");
        return tagDTO;
    }

    /**
     * 创建测试用的DamTagVO对象
     */
    private DamTagVO createTestTagVO(Integer id) {
        DamTagVO tagVO = new DamTagVO();
        tagVO.setId(id);
        tagVO.setName("测试标签");
        tagVO.setType(DamTagTypeEnum.CUSTOM); // 自定义标签
        tagVO.setDescription("这是一个测试标签描述");
        tagVO.setExample("测试示例");
        tagVO.setUsedNum(0);
        tagVO.setValues(Arrays.asList("值1", "值2", "值3"));
        tagVO.setCreateTime(new Date());
        return tagVO;
    }

    @Test
    @DisplayName("测试创建自定义标签")
    void testCreateCustomTag() throws Exception {
        // 准备测试数据
        DamTagDTO tagDTO = createTestTagDTO();
        DamTagVO expectedTagVO = createTestTagVO(1);

        // 模拟Service层行为
        when(tagService.createCustomTag(any(DamTagDTO.class), eq(mockUserId), eq(mockTenantId)))
                .thenReturn(expectedTagVO);

        // 执行Controller方法
        final MvcResult result = mockMvc.perform(post("/api/dam/custom-tags")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(tagDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.statusCode", is(RespCode.OK.getCode())))
                .andExpect(jsonPath("$.message", is("请求成功")))
                .andExpect(jsonPath("$.data.id", is(expectedTagVO.getId())))
                .andExpect(jsonPath("$.data.name", is(expectedTagVO.getName())))
                .andExpect(jsonPath("$.data.type", is(expectedTagVO.getType().getCode())))
                .andReturn();// 验证Service方法被调用

        RespBody<DamTagVO> response = extractMvcResultAsRespBody(result, DamTagVO.class);

        // 验证结果
        assertNotNull(response);
        assertEquals(200, response.getStatusCode());
        assertNotNull(response.getData());
        assertEquals(expectedTagVO.getId(), response.getData().getId());
        assertEquals(expectedTagVO.getName(), response.getData().getName());
        assertEquals(expectedTagVO.getType(), response.getData().getType());

        // 验证Service方法被调用
        Mockito.verify(tagService).createCustomTag(any(DamTagDTO.class), eq(mockUserId), eq(mockTenantId));
    }

    @Test
    @DisplayName("测试获取标签列表")
    void testGetTagList() throws Exception {
        // 准备测试数据
        List<DamTagVO> expectedTagList = Arrays.asList(
                createTestTagVO(1),
                createTestTagVO(2));

        // 模拟Service层行为
        when(tagService.getTagList(eq(DamTagTypeEnum.CUSTOM), eq(true), eq(mockUserId), eq(mockTenantId)))
                .thenReturn(expectedTagList);

        // 执行Controller方法
        final MvcResult result = mockMvc.perform(get("/api/dam/tags")
                .param("type", String.valueOf(DamTagTypeEnum.CUSTOM.getCode()))
                .param("suggestValues", "true")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.statusCode", is(200)))
                .andExpect(jsonPath("$.message", is("请求成功")))
                .andExpect(jsonPath("$.data", hasSize(2)))
                .andExpect(jsonPath("$.data[0].id", is(1)))
                .andExpect(jsonPath("$.data[1].id", is(2)))
                .andReturn();

        RespBody<List<DamTagVO>> response = extractMvcResultAsRespBody(result,
                new TypeReference<RespBody<List<DamTagVO>>>() {
                });

        // 验证结果
        assertNotNull(response);
        assertEquals(200, response.getStatusCode());
        assertEquals("请求成功", response.getMessage());

        // 验证Service方法被调用
        Mockito.verify(tagService).getTagList(eq(DamTagTypeEnum.CUSTOM), eq(true), eq(mockUserId), eq(mockTenantId));
    }

    @Test
    @DisplayName("测试获取标签列表-无类型参数")
    void testGetTagListWithoutType() throws Exception {
        // 准备测试数据
        List<DamTagVO> expectedTagList = Arrays.asList(
                createTestTagVO(1),
                createTestTagVO(2),
                createTestTagVO(3));

        // 模拟Service层行为
        when(tagService.getTagList(isNull(), eq(false), eq(mockUserId), eq(mockTenantId)))
                .thenReturn(expectedTagList);

        // 执行Controller方法
        final MvcResult result = mockMvc.perform(get("/api/dam/tags")
                .param("onlyMine", "false")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.statusCode", is(200)))
                .andExpect(jsonPath("$.data", hasSize(3)))
                .andReturn();

        RespBody<List<DamTagVO>> response = extractMvcResultAsRespBody(result,
                new TypeReference<RespBody<List<DamTagVO>>>() {
                });

        // 验证结果
        assertNotNull(response);
        assertEquals(200, response.getStatusCode());

        // 验证Service方法被调用
        Mockito.verify(tagService).getTagList(isNull(), eq(false), eq(mockUserId), eq(mockTenantId));
    }

    @Test
    @DisplayName("测试更新标签")
    void testUpdateTag() throws Exception {
        // 准备测试数据
        Integer tagId = 1;
        DamTagDTO tagDTO = createTestTagDTO();
        DamTagVO expectedTagVO = createTestTagVO(tagId);
        expectedTagVO.setName("更新后的标签名");
        tagDTO.setName("更新后的标签名");

        // 模拟Service层行为
        when(tagService.updateTag(eq(tagId), any(DamTagDTO.class), eq(mockUserId), eq(mockTenantId)))
                .thenReturn(expectedTagVO);

        // 执行Controller方法
        final MvcResult result = mockMvc.perform(put("/api/dam/tags/{id}", tagId)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(tagDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.statusCode", is(200)))
                .andExpect(jsonPath("$.message", is("请求成功")))
                .andExpect(jsonPath("$.data.name", is("更新后的标签名")))
                .andReturn();

        RespBody<DamTagVO> response = extractMvcResultAsRespBody(result, DamTagVO.class);

        // 验证结果
        assertNotNull(response);
        assertEquals(RespCode.OK.getCode(), response.getStatusCode());
        assertEquals("请求成功", response.getMessage());
        assertEquals("更新后的标签名", response.getData().getName());

        // 验证Service方法被调用
        Mockito.verify(tagService).updateTag(eq(tagId), any(DamTagDTO.class), eq(mockUserId), eq(mockTenantId));
    }

    @Test
    @DisplayName("测试删除标签-成功")
    void testDeleteTag_Success() throws Exception {
        // 准备测试数据
        Integer tagId = 1;

        // 模拟Service层行为
        when(tagService.deleteTag(eq(tagId), eq(mockUserId), eq(mockTenantId)))
                .thenReturn(true);

        // 执行Controller方法
        final MvcResult result = mockMvc.perform(delete("/api/dam/tags/{id}", tagId)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.statusCode", is(200)))
                .andExpect(jsonPath("$.message", is("请求成功")))
                .andReturn();

        RespBody<Void> response = extractMvcResultAsRespBody(result, Void.class);

        // 验证结果
        assertNotNull(response);
        assertEquals(RespCode.OK.getCode(), response.getStatusCode());
        assertEquals("请求成功", response.getMessage());

        // 验证Service方法被调用
        Mockito.verify(tagService).deleteTag(eq(tagId), eq(mockUserId), eq(mockTenantId));
    }

    @Test
    @DisplayName("测试删除标签-失败")
    void testDeleteTag_Failure() throws Exception {
        // 准备测试数据
        Integer tagId = 1;

        // 模拟Service层行为
        when(tagService.deleteTag(eq(tagId), eq(mockUserId), eq(mockTenantId)))
                .thenReturn(false);

        // 执行Controller方法
        final MvcResult result = mockMvc.perform(delete("/api/dam/tags/{id}", tagId)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.statusCode", is(RespCode.FAIL.getCode())))
                .andExpect(jsonPath("$.message", is("删除标签失败")))
                .andReturn();

        RespBody<Void> response = extractMvcResultAsRespBody(result, Void.class);

        // 验证结果
        assertNotNull(response);
        assertEquals(RespCode.FAIL.getCode(), response.getStatusCode());
        assertEquals("删除标签失败", response.getMessage());

        // 验证Service方法被调用
        Mockito.verify(tagService).deleteTag(eq(tagId), eq(mockUserId), eq(mockTenantId));
    }
}
