package cn.mlamp.insightflow.cms.task.dam;


import java.util.List;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;

import cn.mlamp.insightflow.cms.entity.CmsTaskInfo;
import cn.mlamp.insightflow.cms.entity.dam.DamAsset;
import cn.mlamp.insightflow.cms.model.dto.dam.DamAssetUploadTaskArg;
import cn.mlamp.insightflow.cms.util.FunctionUtils;
import cn.mlamp.insightflow.cms.util.VideoUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
class AssetUploadTaskTest extends AssetUploadTaskBaseTest {

    @SuppressWarnings("unchecked")
    @Test
    @DisplayName("测试运行素材上传任务 - 任务不存在")
    void testRunWithTaskNotFound() throws JsonMappingException, JsonProcessingException {
        // 模拟任务不存在
        when(taskService.getById(MOCK_TASK_ID)).thenReturn(null);

        // 执行方法
        assetUploadTask.run(MOCK_TASK_ID);

        // 验证结果 - 应该直接返回，不进行后续处理
        verify(taskService, times(1)).getById(MOCK_TASK_ID);
        verify(objectMapper, never()).readValue(anyString(), eq(DamAssetUploadTaskArg.class));
        verify(assetService, never()).list(any(LambdaQueryWrapper.class));
    }

    @SuppressWarnings("unchecked")
    @Test
    @DisplayName("测试运行素材上传任务 - 解析任务参数异常")
    void testRunWithJsonProcessingException() throws Exception {
        // 模拟任务存在
        CmsTaskInfo taskInfo = new CmsTaskInfo();
        taskInfo.setId(MOCK_TASK_ID);
        taskInfo.setTaskArg("{\"assetIds\":[2001,2002]}");

        when(taskService.getById(MOCK_TASK_ID)).thenReturn(taskInfo);

        // 模拟解析任务参数时抛出异常
        when(objectMapper.readValue(anyString(), eq(DamAssetUploadTaskArg.class)))
                .thenThrow(new JsonProcessingException("测试异常") {
                });

        // 执行方法
        assetUploadTask.run(MOCK_TASK_ID);

        // 验证结果 - 应该捕获异常并记录错误日志
        verify(taskService, times(1)).getById(MOCK_TASK_ID);
        verify(objectMapper, times(1)).readValue(anyString(), eq(DamAssetUploadTaskArg.class));
        verify(assetService, never()).list(any(LambdaQueryWrapper.class));
    }

    @Test
    @DisplayName("测试定时任务调度 - 本地环境")
    void testScheduleInLocalEnv() {
        // 模拟本地环境
        when(taskConfig.isLocal()).thenReturn(true);

        // 执行方法
        assetUploadTask.schedule();

        // 验证结果 - 在本地环境应提前返回
        verify(taskConfig, times(1)).isLocal();
        verify(lockService, never()).getLock(anyString());
    }

    @SuppressWarnings("unchecked")
    @Test
    @DisplayName("测试运行素材上传任务 - 实际处理")
    void testRunWithActualProcess() throws JsonProcessingException {
        // 模拟非本地环境
        when(taskConfig.isLocal()).thenReturn(false);

        // 模拟相关资源
        List<DamAsset> assets = mockAssets(2);
        mockTaskInfo();
        mockTaskDetail(assets);

        when(lockService.getLock(anyString())).thenReturn(lock);
        when(lock.tryLock()).thenReturn(true);

        // 执行方法
        assetUploadTask.run(MOCK_TASK_ID);

        verify(lockService, times(1)).getLock(anyString());
    }
    
    // 测试多种视频格式任务
    @Test
    @DisplayName("测试多种视频格式任务")
    void testRunWithMultipleFormat() throws JsonProcessingException {
        // 模拟非本地环境
        when(taskConfig.isLocal()).thenReturn(false);

        // 模拟相关资源
        List<DamAsset> assets = mockAssets(4);

        mockTaskInfo();
        mockTaskDetail(assets);

        when(lockService.getLock(anyString())).thenReturn(lock);
        when(lock.tryLock()).thenReturn(true);

        FunctionUtils.withTempDirectory(tempDir -> {
            final List<String> formats = List.of("mp4", "mov", "avi", "flv");
            for (int i = 0; i < formats.size(); i++) {
                final String format = formats.get(i);
                final DamAsset asset = assets.get(i);

                String videoPath = tempDir.resolve("origin." + format).toString();
                VideoUtil.cutVideoSegment(
                    MOCK_ASSET_OSS_ID,
                    videoPath,
                    format,
                    0,
                    VideoUtil.getVideoLengthAsMs(MOCK_ASSET_OSS_ID)
                );

                asset.setOssId(videoPath);
            }
            assetUploadTask.run(MOCK_TASK_ID);
        });

        // 执行方法
        verify(lockService, times(1)).getLock(anyString());
        checkAssetUploadTaskSuccess();
    }

}
