package cn.mlamp.insightflow.cms;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.web.servlet.MvcResult;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.config.UserContext;

/**
 * 基础测试类，支持mock用户
 */
@MockitoSettings(strictness = Strictness.LENIENT)
@ExtendWith(MockitoExtension.class)
@SpringBootTest(
    webEnvironment = SpringBootTest.WebEnvironment.MOCK,
    classes = {
        CmsApplication.class
    }
)
public class BaseSpringBootTest {

    @Autowired
    protected ObjectMapper objectMapper;

    private MockedStatic<UserContext> mockedUserContext;

    /**
     * 在每个测试方法执行前设置UserContext的mock
     */
    @BeforeEach
    public void setupMockUser() {
        mockedUserContext = Mockito.mockStatic(UserContext.class);
    }

    /**
     * 模拟当前登录用户
     *
     * @param userId   用户ID
     * @param userName 用户名
     * @param tenantId 租户ID
     */
    protected void mockCurrentUser(Integer userId, String userName, Integer tenantId) {
        // 直接模拟UserContext静态方法的返回值
        mockedUserContext.when(UserContext::getUserId).thenReturn(userId);
        mockedUserContext.when(UserContext::getUserName).thenReturn(userName);
        mockedUserContext.when(UserContext::getTenantId).thenReturn(tenantId);
    }

    protected <T> RespBody<T> extractMvcResultAsRespBody(MvcResult result, Class<T> cls) throws Exception {
        String jsonResult = result.getResponse().getContentAsString();
        return objectMapper.readValue(jsonResult, objectMapper.getTypeFactory().constructParametricType(RespBody.class, cls));
    }

    protected <T> RespBody<T> extractMvcResultAsRespBody(MvcResult result, TypeReference<RespBody<T>> typeReference) throws Exception {
        String jsonResult = result.getResponse().getContentAsString();
        return objectMapper.readValue(jsonResult, typeReference);
    }

    /**
     * 清理mock资源
     */
    @AfterEach
    public void closeMockUser() {
        if (mockedUserContext != null) {
            mockedUserContext.close();
        }
    }

    /**
     * 简单测试示例，展示如何使用mockCurrentUser
     */
    protected void exampleTest() {
        // 模拟用户
        mockCurrentUser(1001, "testUser", 2001);

        // 使用UserContext
        Integer currentUserId = UserContext.getUserId();
        String currentUserName = UserContext.getUserName();
        Integer currentTenantId = UserContext.getTenantId();

        // 验证mock的值
        assert currentUserId.equals(1001);
        assert currentUserName.equals("testUser");
        assert currentTenantId.equals(2001);
    }
}
