package cn.mlamp.insightflow.cms.config;

import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Profile;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 测试环境配置类
 */
@TestConfiguration
@Profile("local")
public class TestConfig implements WebMvcConfigurer {
    
    /**
     * 对拦截器进行配置，在测试环境下可以选择性禁用某些拦截器
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 在测试环境下可以有选择地禁用某些拦截器
        // 这里不做特殊处理，使用默认配置
    }
    
    /**
     * 可以添加一些测试专用的Bean
     */
    @Bean
    public TestUtilityBean testUtilityBean() {
        return new TestUtilityBean();
    }
    
    /**
     * 测试工具类，可用于测试中的辅助操作
     */
    public static class TestUtilityBean {
        public boolean isTestEnvironment() {
            return true;
        }
    }
} 