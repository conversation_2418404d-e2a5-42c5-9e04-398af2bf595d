package cn.mlamp.insightflow.cms;

import static org.junit.jupiter.api.Assertions.assertEquals;
import org.junit.jupiter.api.Test;

import cn.mlamp.insightflow.cms.config.UserContext;

/**
 * 用户上下文测试类，演示如何使用BaseSpringBootTest
 */
public class UserContextTest extends BaseSpringBootTest {

    @Test
    public void testMockUser() {
        // 模拟用户
        Integer userId = 1002;
        String userName = "testUser123";
        Integer tenantId = 2002;

        mockCurrentUser(userId, userName, tenantId);

        // 验证UserContext返回的值是否正确
        assertEquals(userId, UserContext.getUserId());
        assertEquals(userName, UserContext.getUserName());
        assertEquals(tenantId, UserContext.getTenantId());
    }
}