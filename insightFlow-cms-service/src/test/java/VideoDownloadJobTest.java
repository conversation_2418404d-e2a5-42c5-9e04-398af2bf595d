import cn.mlamp.insightflow.cms.entity.CmsPullTaskDedupedData;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class VideoDownloadJobTest {

    @Test
    public void testParseDataToMap() {
        CmsPullTaskDedupedData cmsPullTaskDedupedData1 = new CmsPullTaskDedupedData();
        cmsPullTaskDedupedData1.setEsId("1");
        cmsPullTaskDedupedData1.setDownloadDate("2022-01-01");

        CmsPullTaskDedupedData cmsPullTaskDedupedData2 = new CmsPullTaskDedupedData();
        cmsPullTaskDedupedData2.setEsId("2");
        cmsPullTaskDedupedData2.setDownloadDate("2022-01-01");

        CmsPullTaskDedupedData cmsPullTaskDedupedData3 = new CmsPullTaskDedupedData();
        cmsPullTaskDedupedData3.setEsId("3");
        cmsPullTaskDedupedData3.setDownloadDate("2022-01-02");

        List<CmsPullTaskDedupedData> list = List.of(cmsPullTaskDedupedData1, cmsPullTaskDedupedData2, cmsPullTaskDedupedData3);

        Map<String, List<String>> downloadDataEsIdsMap = list.stream()
                .collect(Collectors.groupingBy(CmsPullTaskDedupedData::getDownloadDate,
                        Collectors.mapping(CmsPullTaskDedupedData::getEsId, Collectors.toList())));

        System.out.println(downloadDataEsIdsMap);
    }
}
