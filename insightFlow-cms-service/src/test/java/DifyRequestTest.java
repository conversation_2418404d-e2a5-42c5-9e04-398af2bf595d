import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.mlamp.insightflow.cms.model.dto.DifyAiImitateRequestDTO;
import cn.mlamp.insightflow.cms.model.dto.DifyProductSummaryResponseDTO;
import cn.mlamp.insightflow.cms.model.dto.DifyScriptGenRequestDTO;
import cn.mlamp.insightflow.cms.model.dto.DifyScriptGenResponseDTO;
import cn.mlamp.insightflow.cms.model.query.VideoScriptGenRequest;
import cn.mlamp.insightflow.cms.util.JsonUtil;
import cn.mlamp.insightflow.cms.util.dify.DifyUtil;
import cn.mlamp.insightflow.cms.util.dify.model.DifyRequest;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Test;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class DifyRequestTest {

    @Test
    @SneakyThrows
    public void testImagePrompt() {
        var query = "女主角面部特写，疲惫表情，皮肤暗沉有细纹，突然眼睛一亮拿出欧莱雅小黑瓶";
        var difyRequest = DifyRequest.builder()
                .appKey("app-t9Emy5DGySRKC2jKZUzpM8M3")
                .baseUrl("https://llm-ops-social.mlamp.cn/v1")
                .query(query)
                .build();
        var difyUtil = new DifyUtil();
        var result = difyUtil.blockingChatRequest(difyRequest, "11111_11111");
        log.info(result.toString());
    }

    @Test
    @SneakyThrows
    public void testAiImitate() {
        var inputJson = """
                        {
                            "content": {
                                "brand": "小米",
                                "product": "REDMI K80 Pro",
                                "duration": 20,
                                "lensNum": 3,
                                "sellingPoint": "操作丝滑，配置高",
                                "scene": "户外",
                                "peopleNum": 1,
                                "festival": "618大促"
                            },
                            "storyboardIds": [
                                "2205",
                                "2206",
                                "2207",
                                "2208",
                                "2209"
                            ],
                            "decodeResult": {
                                "产品信息": [
                                    "手机",
                                    "OPPOFindX8s",
                                    "轻薄小直屏",
                                    "拍照&&自拍",
                                    "白色",
                                    "真实场景使用"
                                ],
                                "情感表达": [
                                    "平静",
                                    "日常生活"
                                ],
                                "用户相关": [
                                    "一人",
                                    "个人",
                                    "OPPOFindX8s"
                                ],
                                "视频制作": [
                                    "返校上课",
                                    "校园&&教室&&宿舍",
                                    "直接展示产品",
                                    "穿着休闲的音乐生",
                                    "快速切换",
                                    "情景展示",
                                    "清晰自然",
                                    "vlog",
                                    "亮度正常",
                                    "无贴图",
                                    "日常记录"
                                ]
                            },
                            "sourceId": "1237"
                        }
                """;
        VideoScriptGenRequest videoScriptGenRequest = JSONUtil.toBean(inputJson, VideoScriptGenRequest.class);
        var actionJson = """
                [
                    {
                        "pic": "https://mos-ex.intra.mlamp.cn/ai-pc-cms/video-decode/videos/douyin/pic/0_c8e0734722e737ae1a42f58f14c6525b/0.jpg",
                        "光影与色彩要求": "明亮色调，自然光线充足",
                        "出现演员": "女演员",
                        "分镜开始时间戳": "230",
                        "分镜结束时间戳": "4720",
                        "台词": "要去要要知道温度温度，",
                        "台词情绪": "轻松愉快",
                        "品牌植入": "",
                        "布景要求": "室内环境，现代家居风格",
                        "摄影器材": "稳定器、摄像机",
                        "服装造型": "灰色短上衣+牛仔裤",
                        "演员动作": "伸展双臂，整理头发",
                        "演员表情": "自然、轻松",
                        "背景音乐/音效": "轻快的背景音乐",
                        "视频内容策略": [
                            "情景引入",
                            "适用场景"
                        ],
                        "运镜方式": "固定",
                        "道具清单": "无",
                        "镜头参考": "0",
                        "镜头描述": "女演员在室内伸展身体",
                        "镜头类型": "中景"
                    },
                    {
                        "pic": "https://mos-ex.intra.mlamp.cn/ai-pc-cms/video-decode/videos/douyin/pic/0_c8e0734722e737ae1a42f58f14c6525b/5000.jpg",
                        "光影与色彩要求": "冷色调，突出产品质感",
                        "出现演员": "女演员",
                        "分镜开始时间戳": "4720",
                        "分镜结束时间戳": "13095",
                        "台词": "温度温度就因为为走位走位走位走位走耶。",
                        "台词情绪": "自信",
                        "品牌植入": "OPPOFindX8s",
                        "布景要求": "室内环境，简约风格",
                        "摄影器材": "微距镜头、稳定器",
                        "服装造型": "灰色短上衣+牛仔裤",
                        "演员动作": "手持手机展示",
                        "演员表情": "专注",
                        "背景音乐/音效": "科技感音效",
                        "视频内容策略": [
                            "产品卖点"
                        ],
                        "运镜方式": "缓慢推进",
                        "道具清单": "OPPOFindX8s手机",
                        "镜头参考": "5000",
                        "镜头描述": "展示手机产品",
                        "镜头类型": "特写"
                    }
                ]
                """;
//        log.info(JSONUtil.toJsonStr(DifyAiImitateRequestDTO.buildDifyParams(videoScriptGenRequest, actionJson)));
        var difyRequest = DifyRequest.builder()
                .appKey("app-QEEKD2FKeaJwADa4zXjsMB6X")
                .baseUrl("https://llm-ops-social.mlamp.cn/v1")
                .observationId(UUID.randomUUID().toString().replace("-", ""))
                .inputsParams(DifyAiImitateRequestDTO.buildDifyParams(videoScriptGenRequest, actionJson))
                .build();
        var difyUtil = new DifyUtil();
        var result = difyUtil.executeAndMergeStreaming(difyRequest);
        log.info(result.toString());
        var textStr = result.get("text").toString();
        var scenes = DifyScriptGenResponseDTO.parseScriptByText(textStr);
        log.info(JSONUtil.toJsonStr(scenes));
    }

    @Test
    public void testParasJson() {
        String scene1 = """
                {"布景要求":"室内环境，现代家居风格","品牌植入":"","台词情绪":"轻松愉快","分镜结束时间戳":"4720","镜头参考":"0","服装造型":"灰色短上衣+牛仔裤","演员表情":"自然、轻松","镜头类型":"中景","pic":"https://mos-ex.intra.mlamp.cn/ai-pc-cms/video-decode/videos/douyin/pic/0_c8e0734722e737ae1a42f58f14c6525b/0.jpg","道具清单":"无","光影与色彩要求":"明亮色调，自然光线充足","摄影器材":"稳定器、摄像机","台词":"要去要要知道温度温度，","镜头描述":"女演员在室内伸展身体","出现演员":"女演员","分镜开始时间戳":"230","视频内容策略":["情景引入","适用场景"],"演员动作":"伸展双臂，整理头发","运镜方式":"固定","背景音乐/音效":"轻快的背景音乐"}
                """;
        String scene2 = """
                {"布景要求":"室内环境，简约风格","品牌植入":"OPPOFindX8s","台词情绪":"自信","分镜结束时间戳":"13095","镜头参考":"5000","服装造型":"灰色短上衣+牛仔裤","演员表情":"专注","镜头类型":"特写","pic":"https://mos-ex.intra.mlamp.cn/ai-pc-cms/video-decode/videos/douyin/pic/0_c8e0734722e737ae1a42f58f14c6525b/5000.jpg","道具清单":"OPPOFindX8s手机","光影与色彩要求":"冷色调，突出产品质感","摄影器材":"微距镜头、稳定器","台词":"温度温度就因为为走位走位走位走位走耶。","镜头描述":"展示手机产品","出现演员":"女演员","分镜开始时间戳":"4720","视频内容策略":["产品卖点"],"演员动作":"手持手机展示","运镜方式":"缓慢推进","背景音乐/音效":"科技感音效"}
                """;

        // 解析 scene1 和 scene2 为 JSONObject
        JSONObject jsonScene1 = JSONUtil.parseObj(scene1);
        JSONObject jsonScene2 = JSONUtil.parseObj(scene2);

// 封装成 JSON 数组
        String jsonArray = JSONUtil.toJsonStr(new JSONObject[]{jsonScene1, jsonScene2});
        log.info(jsonArray);
    }


    @Test
    public void testAiImitateJson() {
        var result = """
                #### 分镜一

                - **镜头编号**: 1
                - **镜头描述**: 女演员在室内舒适地伸展身体，阳光透过窗户洒在她身上。
                - **品牌植入**: REDMI K80 Pro
                - **镜头类型**: 中景
                - **运镜方式**: 固定
                - **时长**: 4秒
                - **出现演员**: 女演员
                - **服化道**: 灰色短上衣 + 牛仔裤
                - **演员动作**: 伸展双臂，整理头发
                - **演员表情**: 自然、轻松
                - **台词**: "早晨的阳光，总能让我充满活力。”
                - **台词情绪**: 轻松愉悦
                - **服装造型建议**: 休闲舒适，日常风格
                - **布景要求**: 室内环境，现代家居风格
                - **背景音乐/音效**: 轻快的背景音乐
                - **光影与色彩要求**: 明亮色调，自然光线充足
                - **摄影器材**: 稳定器、摄像机
                - **视频内容策略**: 开篇黄金三秒产生情感共鸣，展示日常生活中使用REDMI K80 Pro的场景。
                - **绘画提示词**: woman stretching arms indoors, natural light, modern home decor, relaxed mood, wide shot, 8k HD continuous line sketch, line art style,white environment, studio lighting, black and white ,wide, 8k HD.

                #### 分镜二

                - **镜头编号**: 2
                - **镜头描述**: 女演员专注地展示REDMI K80 Pro手机，近距离拍摄手机细节。
                - **品牌植入**: REDMI K80 Pro
                - **镜头类型**: 特写
                - **运镜方式**: 缓慢推进
                - **时长**: 8秒
                - **出现演员**: 女演员
                - **服化道**: 灰色短上衣 + 牛仔裤
                - **演员动作**: 手持手机展示
                - **演员表情**: 专注
                - **台词**: "体验前所未有的丝滑操作，REDMI K80 Pro，您的智能助手。”
                - **台词情绪**: 自信专业
                - **服装造型建议**: 简约时尚
                - **布景要求**: 室内环境，简约风格
                - **背景音乐/音效**: 科技感音效
                - **光影与色彩要求**: 冷色调，突出产品质感
                - **摄影器材**: 微距镜头、稳定器
                - **视频内容策略**: 中段营销策略强调产品卖点，结尾逼单话术吸引用户购买。
                - **绘画提示词**: woman holding smartphone, close-up shot, sleek design, technology focus, cold color tone, 8k HD continuous line sketch, line art style,white environment, studio lighting, black and white ,wide, 8k HD.}
                                """;

        var result1 = """
                镜头编号：1
                                
                镜头描述：女演员在室内舒适自在地伸展身体，展现轻松愉悦的生活状态。
                                
                品牌植入：REDMI K80 Pro
                                
                镜头类型：中景
                                
                运镜方式：固定
                                
                时长：4秒
                                
                出现演员：女演员
                                
                服化道：灰色短上衣+牛仔裤
                                
                演员动作：伸展双臂，整理头发
                                
                演员表情：自然、轻松
                                
                台词：“拥有REDMI K80 Pro的生活，就是如此舒适自在。”
                                
                台词情绪：轻松愉快
                                
                服装造型建议：灰色短上衣搭配牛仔裤，简约时尚
                                
                布景要求：室内环境，现代家居风格
                                
                背景音乐/音效：轻快的背景音乐
                                
                光影与色彩要求：明亮色调，自然光线充足
                                
                摄影器材：稳定器、摄像机
                                
                视频内容策略：开篇黄金三秒产生情感共鸣，中段营销策略展示舒适生活，结尾逼单话术“让生活更舒适，选择REDMI K80 Pro。”
                                
                绘画提示词：woman stretching arms, modern indoor setting, relaxed expression, casual clothing, natural light, bright colors, comfortable atmosphere, continuous line sketch, line art style, white environment, studio lighting, black and white, wide, 8k HD.
                                
                ---
                                
                镜头编号：2
                                
                镜头描述：女演员手持REDMI K80 Pro手机，专注展示其高端配置和质感。
                                
                品牌植入：REDMI K80 Pro
                                
                镜头类型：特写
                                
                运镜方式：缓慢推进
                                
                时长：6秒
                                
                出现演员：女演员
                                
                服化道：灰色短上衣+牛仔裤
                                
                演员动作：手持手机展示
                                
                演员表情：专注
                                
                台词：“REDMI K80 Pro，性能卓越，质感非凡。”
                                
                台词情绪：自信
                                
                服装造型建议：灰色短上衣搭配牛仔裤，简约时尚
                                
                布景要求：室内环境，简约风格
                                
                背景音乐/音效：科技感音效
                                
                光影与色彩要求：冷色调，突出产品质感
                                
                摄影器材：微距镜头、稳定器
                                
                视频内容策略：开篇黄金三秒制造高级感，中段营销策略突出产品卖点，结尾逼单话术“体验卓越配置，选择REDMI K80 Pro。”
                                
                绘画提示词：close-up of smartphone, woman holding phone, focused expression, sleek design, cold tones, high-quality texture, continuous line sketch, line art style, white environment, studio lighting, black and white, wide, 8k HD.}
                                
                """;
        // 替换原来的正则表达式解析方法
        List<DifyScriptGenResponseDTO.Scene> scenes = DifyScriptGenResponseDTO.parseScriptByText(result1);
        log.info(JsonUtil.encode(scenes));
    }

    @Test
    @SneakyThrows
    public void testGenScript() {
        var videoScriptGenRequest = new VideoScriptGenRequest();
        var content = new VideoScriptGenRequest.ScriptInputContent();
        content.setBrand("小米");
        content.setProduct("小米10");
        content.setDuration(10);
        content.setLensNum(3);
        content.setPeopleNum(2);
        videoScriptGenRequest.setContent(content);
        Map<String, List<String>> decodeResult = new HashMap<>();
        decodeResult.put("画面套路", List.of("品牌展示", "手机展示"));
        decodeResult.put("台词套路", List.of("xxx系统操作太丝滑了"));
        videoScriptGenRequest.setDecodeResult(decodeResult);
        var difyRequest = DifyRequest.builder()
                .appKey("app-g1FdvpaoYHFeFrPg856cM68C")
                .baseUrl("https://llm-ops-social.mlamp.cn/v1")
                .inputsParams(DifyScriptGenRequestDTO.buildDifyParams(videoScriptGenRequest))
                .build();
        var difyUtil = new DifyUtil();
        var result = difyUtil.executeAndMergeStreaming(difyRequest);
        log.info(result.toString());
    }

    @Test
    public void splitResultJson() {
        var result1 = """
                # 欧莱雅小黑瓶精华抖音爆款短视频脚本
                                
                镜头编号：1
                镜头描述：女主角面部特写，疲惫表情，皮肤暗沉有细纹，突然眼睛一亮拿出欧莱雅小黑瓶
                品牌植入：欧莱雅小黑瓶精华特写
                镜头类型：特写
                运镜方式：从脸部拉近到产品
                时长：3秒
                出现演员：年轻女性（25-30岁）
                演员动作：摸脸皱眉后拿出小黑瓶
                演员表情：从痛苦到惊喜
                台词："熬夜加班肌肤救星终于被我找到了！"
                台词情绪：焦急转惊喜
                服装造型建议：居家简约风，白色吊带
                道具清单：欧莱雅小黑瓶精华
                布景要求：简约明亮卧室
                背景音乐/音效：轻快节奏音乐，惊喜音效
                光影与色彩要求：暖色调，突出产品黑色瓶身
                摄影器材：手持稳定器+手机
                视频内容策略字段：情绪营销
                                
                镜头编号：2
                镜头描述：手部特写，展示精华质地，滴落在手背上迅速吸收
                品牌植入：精华液特写
                镜头类型：微距特写
                运镜方式：固定拍摄
                时长：3秒
                出现演员：女主手部
                演员动作：滴落精华并涂抹
                演员表情：无
                台词："S.F.E发酵精粹+二列酵母，一滴见效秒吸收！"
                台词情绪：惊喜专业
                服装造型建议：修剪整齐的指甲
                道具清单：欧莱雅小黑瓶精华
                布景要求：纯色背景
                背景音乐/音效：液体滴落音效
                光影与色彩要求：高亮度突出产品质地通透感
                摄影器材：微距镜头
                视频内容策略字段：产品功效
                                
                镜头编号：3
                镜头描述：女主脸部对比，屏幕分割为"使用前"和"使用后"
                品牌植入：使用过程中展示欧莱雅logo
                镜头类型：对比镜头
                运镜方式：屏幕分割效果
                时长：4秒
                出现演员：女主角
                演员动作：左侧疲惫表情，右侧明亮笑容
                演员表情：从疲惫到焕发光彩
                台词："熬夜三天肌肤问题，一瓶全解决！"
                台词情绪：惊讶满足
                服装造型建议：同一套服装，对比明显的妆容效果
                道具清单：欧莱雅小黑瓶精华
                布景要求：同一背景
                背景音乐/音效：转场音效+惊叹声
                光影与色彩要求：前后对比鲜明，后者更明亮
                摄影器材：定焦镜头
                视频内容策略字段：产品卖点
                                
                镜头编号：4
                镜头描述：动画展示精华核心功效，四个功效点飞入画面
                品牌植入：动画中融入欧莱雅标志
                镜头类型：动画特效
                运镜方式：飞入动效
                时长：4秒
                出现演员：无
                演员动作：无
                演员表情：无
                台词："保湿、维稳、淡纹、收缩毛孔，敏感肌也能用！"
                台词情绪：专业自信
                服装造型建议：无
                道具清单：无
                布景要求：简约背景
                背景音乐/音效：科技感音效
                光影与色彩要求：品牌色调搭配
                摄影器材：后期动画制作
                视频内容策略字段：适用人群
                                
                镜头编号：5
                镜头描述：女主示范正确使用方法，特写皮肤吸收精华瞬间
                品牌植入：全程手持产品
                镜头类型：教程展示
                运镜方式：跟随手部移动
                时长：5秒
                出现演员：女主角
                演员动作：取3-4滴精华轻拍面部
                演员表情：专注愉悦
                台词："水乳之后取3-4滴，90%的人不知道它还能这样用..."
                台词情绪：神秘分享
                服装造型建议：简约白色上衣
                道具清单：欧莱雅小黑瓶精华、化妆棉
                布景要求：明亮卫生间或梳妆台
                背景音乐/音效：轻柔背景音乐
                光影与色彩要求：自然光照明
                摄影器材：手持稳定器
                视频内容策略字段：产品卖点
                                
                镜头编号：6
                镜头描述：女主对镜自拍，皮肤状态极佳，右下角出现产品和购买二维码
                品牌植入：产品包装完整展示
                镜头类型：自拍+产品展示
                运镜方式：固定
                时长：3秒
                出现演员：女主角
                演员动作：对镜微笑，比心
                演员表情：满足自信
                台词："熬夜也能水润无暇，点赞+评论告诉我你最在意的肌肤问题！#抗老精华 #熬夜肌拯救"
                台词情绪：邀请分享
                服装造型建议：精致妆容
                道具清单：欧莱雅小黑瓶精华
                布景要求：明亮自然光环境
                背景音乐/音效：品牌音乐结尾
                光影与色彩要求：高清通透
                摄影器材：自拍镜头
                视频内容策略字段：行动号召
                """;

        var result2 = """
                # 欧莱雅复颜精纯维C紧致精华液抖音脚本
                                
                镜头编号：1
                镜头描述：特写女主角对着镜子苦恼地检查皮肤，镜头突然变暗，聚光灯打在她粗大毛孔和细纹上
                品牌植入：无（铺垫痛点）
                镜头类型：特写
                运镜方式：从全脸到痛点放大
                时长：3秒
                出现演员：25-35岁女性主角
                演员动作：双手撑开脸部皮肤，目光焦虑地检查毛孔和细纹
                演员表情：懊恼、苦恼
                台词："熬夜后皮肤就像这块粗糙的瓷砖，毛孔大得能塞下一支笔！"
                台词情绪：夸张的懊恼
                服装造型建议：简约家居服，头发微乱但整体干净
                道具清单：放大镜、化妆镜
                布景要求：简约卧室梳妆台
                背景音效：紧张的检查声+失落音效
                光影与色彩要求：侧光打在脸部，强调肤质问题，色调偏冷
                摄影器材：微距镜头
                视频内容策略字段：情绪营销
                                
                镜头编号：2
                镜头描述：女主角突然眼前一亮，黄金光效从天而降，欧莱雅复颜精纯维C精华瓶身飞入画面，旋转360度
                品牌植入：欧莱雅复颜精纯维C紧致精华液特写
                镜头类型：产品特写
                运镜方式：动态旋转+特效
                时长：2秒
                出现演员：同上
                演员动作：双手接住从天而降的精华液，表情由苦恼转为惊喜
                演员表情：眼睛睁大，惊喜
                台词："我的救星来了！欧莱雅复颜维C精华，12%原型VC，抗氧化能力是普通VC的700倍！"
                台词情绪：惊喜、发现宝藏
                服装造型建议：同上
                道具清单：欧莱雅复颜精纯维C紧致精华液
                布景要求：同上，但光线突变明亮
                背景音效：发现宝物音效+品牌提示音
                光影与色彩要求：金色光效围绕产品，整体画面明亮温暖
                摄影器材：产品环绕拍摄装置
                视频内容策略字段：产品功效
                                
                镜头编号：3
                镜头描述：分屏对比效果，左侧是使用前粗大毛孔特写，右侧是精华液滴在皮肤上的微观吸收过程
                品牌植入：精华液滴落特写
                镜头类型：对比+功效展示
                运镜方式：分屏+微观拉近
                时长：3秒
                出现演员：同上
                演员动作：左手指向左侧问题皮肤，右手展示精华液吸收过程
                演员表情：专业讲解状态
                台词："熬夜、压力、污染，毛孔越来越大，但你知道吗？90%的女生都不知道这招！" [#护肤真相]
                台词情绪：分享秘密的兴奋
                服装造型建议：同上
                道具清单：欧莱雅精华液、放大镜
                布景要求：简约纯色背景
                背景音效：科技感扫描音效
                光影与色彩要求：左侧冷色调，右侧暖色调，形成鲜明对比
                摄影器材：微距镜头+分屏技术
                视频内容策略字段：产品卖点
                                
                镜头编号：4
                镜头描述：女主角演示精华液使用方法，滴管特写，3滴精华均匀涂抹，动画显示维C分子渗透皮肤过程
                品牌植入：精华液使用过程全程展示
                镜头类型：教学+动画
                运镜方式：从特写到全脸
                时长：4秒
                出现演员：同上
                演员动作：展示正确滴取量和涂抹手法
                演员表情：专注、享受
                台词："只需三滴，轻薄质地秒吸收！700倍抗氧科技瞬间修护毛孔，四周焕发瓷光肌！试了才知道效果有多惊人！"
                台词情绪：惊喜、分享
                服装造型建议：简约但精致的家居服
                道具清单：欧莱雅精华液、计时器道具
                布景要求：明亮干净的卫生间
                背景音效：轻柔的吸收声+科技感效果音
                光影与色彩要求：柔和自然光，强调产品的金色与肌肤的自然色
                摄影器材：专业微距镜头
                视频内容策略字段：产品卖点
                                
                镜头编号：5
                镜头描述：四分屏前后对比，显示使用1周/2周/4周的效果变化，毛孔逐渐缩小，肤质逐渐光滑透亮
                品牌植入：画面右下角产品logo持续展示
                镜头类型：效果对比
                运镜方式：四分屏转场
                时长：4秒
                出现演员：同上
                演员动作：依次指向不同阶段的效果，最后惊喜抚摸自己光滑的脸
                演员表情：从期待到惊喜到满足
                台词："一周细致毛孔，两周淡化细纹，四周瓷光焕现！无论你是干皮、油皮还是混合皮，都能速效visible改变！" [#黑科技护肤]
                台词情绪：惊喜、满足
                服装造型建议：同一风格但四种不同服饰，体现时间变化
                道具清单：欧莱雅精华液、日历标记
                布景要求：相同角度的拍摄环境，保证对比的准确性
                背景音效：逐渐升级的惊喜音效
                光影与色彩要求：光线逐渐明亮，最后一格特别明亮突出
                摄影器材：高清定格摄影
                视频内容策略字段：产品功效
                                
                镜头编号：6
                镜头描述：女主角手持精华液，灿烂微笑，屏幕飞出闪光特效和动态购买按钮
                品牌植入：产品特写+品牌logo
                镜头类型：行动号召
                运镜方式：从特写到中景
                时长：3秒
                出现演员：同上
                演员动作：向观众展示产品，然后指向屏幕下方
                演员表情：满足、亲和力十足
                台词："春节特惠，点击左下角链接，立即拥有700倍抗氧科技！前100名还送限量版旅行装！"
                台词情绪：热情邀请
                服装造型建议：明亮色彩的休闲装，展现自信与活力
                道具清单：欧莱雅精华液、礼盒
                布景要求：明亮简约背景，突出人物和产品
                背景音效：品牌音效+欢快结束音
                光影与色彩要求：明亮温暖，突出产品金色包装
                摄影器材：标准镜头
                视频内容策略字段：行动号召
                """;
        var result3 = """
                # 小米10抖音爆款短视频分镜脚本
                                
                ---
                                
                镜头编号：1
                镜头描述：特写镜头，演员夸张表情+手机卡顿，突然小米10从天而降，光效闪亮
                品牌植入：小米10正面清晰入镜，LOGO突出
                镜头类型：特写+慢动作
                运镜方式：垂直下拉+突然静止
                时长：3秒
                出现演员：年轻男性(25-30岁)
                演员动作：先抓狂挠头，随后惊喜接住从天而降的小米10
                演员表情：从懊恼转为惊喜
                台词："卡成PPT的手机真让人崩溃！（接住小米10后）等等...这是什么黑科技？"
                台词情绪：从烦躁到惊喜
                服装造型建议：简约时尚衬衫，科技感眼镜
                道具清单：旧手机一部，小米10一部
                布景要求：简约现代办公室/家庭场景，背景虚化
                背景音乐/音效：电子节拍+科技感"嗖"声
                光影与色彩要求：冷色调转暖色调，接住手机时光效闪亮
                摄影器材：手持稳定器+高速摄影
                视频内容策略字段：情景引入
                                
                ---
                                
                镜头编号：2
                镜头描述：小米10系统操作特写，突出骁龙865处理器+90Hz刷新率带来的流畅体验
                品牌植入：小米10正面全屏展示，系统操作画面
                镜头类型：特写+分屏对比
                运镜方式：俯拍+横向推进
                时长：4秒
                出现演员：同上男性的手指
                演员动作：快速切换多个应用，滑动操作流畅无卡顿
                演员表情：（仅手部出镜）
                台词："骁龙865+LPDDR5内存这组合也太丝滑了！90Hz刷新率，应用秒开不是梦！"
                台词情绪：惊叹+兴奋
                服装造型建议：手部干净整洁
                道具清单：小米10一部
                布景要求：纯色背景，增加科技感图形元素
                背景音乐/音效：流畅的滑动音效+科技背景音
                光影与色彩要求：明亮自然光，突出屏幕色彩
                摄影器材：微距镜头
                视频内容策略字段：产品卖点
                                
                ---
                                
                镜头编号：3
                镜头描述：1亿像素相机拍摄对比展示，从全景到细节放大，展示超清画质
                品牌植入：小米10拍照界面+成片展示
                镜头类型：跟踪+变焦
                运镜方式：从远到近+缩放特效
                时长：5秒
                出现演员：同一男性
                演员动作：拍摄城市夜景，放大照片展示惊人细节
                演员表情：震惊+满足
                台词："1亿像素8K电影相机，放大100倍细节依然清晰！这画质也太震撼了吧！"
                台词情绪：惊讶+赞叹
                服装造型建议：简约时尚，深色系提升科技感
                道具清单：小米10一部，城市夜景背景
                布景要求：城市天台或阳台，能拍摄远景
                背景音乐/音效：相机快门声+惊叹音效
                光影与色彩要求：夜景模式，城市灯光与手机相互辉映
                摄影器材：跟踪摄影+虚拟特效
                视频内容策略字段：产品功效
                                
                ---
                                
                镜头编号：4
                镜头描述：游戏特写，5G网络速度+超大VC液冷散热展示
                品牌植入：小米10游戏画面+散热系统动画
                镜头类型：分屏+科技动画
                运镜方式：旋转+特效切换
                时长：4秒
                出现演员：同一男性
                演员动作：沉浸式玩游戏，手机无发热
                演员表情：专注+惊喜
                台词："别人的手机玩游戏烫到煎鸡蛋，小米10超大VC液冷散热玩一小时依然冰凉，你们猜第三个秘密功能是什么？"
                台词情绪：神秘+炫耀
                服装造型建议：休闲游戏风格
                道具清单：小米10一部，游戏周边
                布景要求：简约卧室/游戏空间
                背景音乐/音效：游戏音效+神秘悬念声
                光影与色彩要求：炫彩灯光，游戏氛围
                摄影器材：手持云台+特写镜头
                视频内容策略字段：情绪营销
                                
                ---
                                
                镜头编号：5
                镜头描述：产品360°旋转展示，各种场景快速切换，突出全能属性
                品牌植入：小米10完整展示，包装盒亮相
                镜头类型：环绕+蒙太奇
                运镜方式：旋转+快速切换
                时长：4秒
                出现演员：同一男性
                演员动作：手持小米10做出邀请姿势
                演员表情：自信+邀请
                台词："小米10，科技实力派！左滑解锁更多玩法，评论区分享你最想尝试的功能！"
                台词情绪：热情+号召
                服装造型建议：科技感十足的时尚装扮
                道具清单：小米10完整包装
                布景要求：简约科技感场景，米家生态产品环绕
                背景音乐/音效：小米品牌音效+节奏感十足的结尾
                光影与色彩要求：明亮科技感，米家橙色点缀
                摄影器材：轨道摄影+环绕云台
                视频内容策略字段：行动号召
                """;

//        final Pattern SCENE_PATTERN = Pattern.compile(
//                "(?:^|\\n|\\r|\\r\\n|[-]{3,})\\s*镜头编号：(\\d+)\\s*镜头描述：([^\\n]*)\\s*品牌植入：([^\\n]*)\\s*镜头类型：([^\\n]*)\\s*运镜方式：([^\\n]*)\\s*时长：(\\d+)秒\\s*" +
//                        "出现演员：([^\\n]*)\\s*演员动作：([^\\n]*)\\s*演员表情：([^\\n]*)\\s*台词：\"([^\"]*)\"\\s*台词情绪：([^\\n]*)\\s*" +
//                        "服装造型建议：([^\\n]*)\\s*道具清单：([^\\n]*)\\s*布景要求：([^\\n]*)\\s*背景音乐/音效：([^\\n]*)\\s*" +
//                        "光影与色彩要求：([^\\n]*)\\s*摄影器材：([^\\n]*)\\s*视频内容策略字段：([^\\n]*)",
//                Pattern.MULTILINE
//        );
//        DifyScriptGenResponseDTO dto1 = getDifyVideoScriptDTO(SCENE_PATTERN, result1);
//        log.info("{}", JsonUtil.encode(dto1));
//        DifyScriptGenResponseDTO dto2 = getDifyVideoScriptDTO(SCENE_PATTERN, result2);
//        log.info("{}", JsonUtil.encode(dto2));
//        DifyScriptGenResponseDTO dto3 = getDifyVideoScriptDTO(SCENE_PATTERN, result3);
//        log.info("{}", JsonUtil.encode(dto3));

//        // 替换原来的正则表达式解析方法
//        List<DifyScriptGenResponseDTO.Scene> scenes1 = parseScriptWithoutRegex(result1);
//        List<DifyScriptGenResponseDTO.Scene> scenes2 = parseScriptWithoutRegex(result2);
//        List<DifyScriptGenResponseDTO.Scene> scenes3 = parseScriptWithoutRegex(result3);
//
//        DifyScriptGenResponseDTO dto1 = new DifyScriptGenResponseDTO();
//        dto1.setScenes(scenes1);
//        log.info("{}", JsonUtil.encode(dto1));
//
//        DifyScriptGenResponseDTO dto2 = new DifyScriptGenResponseDTO();
//        dto2.setScenes(scenes2);
//        log.info("{}", JsonUtil.encode(dto2));
//
//        DifyScriptGenResponseDTO dto3 = new DifyScriptGenResponseDTO();
//        dto3.setScenes(scenes3);
//        log.info("{}", JsonUtil.encode(dto3));

        List<String> scenes1 = parseScriptToScenes(result1);
        log.info(JsonUtil.encode(scenes1));
        List<String> scenes2 = parseScriptToScenes(result2);
        log.info(JsonUtil.encode(scenes2));
        List<String> scenes3 = parseScriptToScenes(result3);
        log.info(JsonUtil.encode(scenes3));
    }

    public List<DifyScriptGenResponseDTO.Scene> parseScriptWithoutRegex(String scriptContent) {
        List<DifyScriptGenResponseDTO.Scene> scenes = new ArrayList<>();
        DifyScriptGenResponseDTO.Scene currentScene = null;
        int index = 1;
        boolean multiLine = false;
        boolean hasFirst = false;
        String[] lines = scriptContent.split("\\r?\\n");
        for (String line : lines) {
            if (multiLine) {
                break;
            }
            line = line.trim();

            // 处理分隔线
            if (line.startsWith("---") || line.isEmpty()) {
                if (currentScene != null) {
                    scenes.add(currentScene);
                    currentScene = null;
                }
                continue;
            }

            // 处理多方案
            if (line.startsWith("## 方案")) {
                if (hasFirst) {
                    multiLine = true;
                }
                hasFirst = true;
                continue;
            }

            // 处理镜头编号
            if (line.startsWith("镜头编号：") || line.startsWith("## 镜头")) {
                if (currentScene != null) {
                    scenes.add(currentScene);
                }
                currentScene = new DifyScriptGenResponseDTO.Scene();
                var indexStr = line.substring(line.indexOf("：") + 1).trim();
                if (!indexStr.isEmpty()) {
                    index = Integer.parseInt(indexStr);
                    currentScene.setSceneNumber(index);
                } else {
                    currentScene.setSceneNumber(index++);
                }
                continue;
            }

            // 处理其他字段
            if (currentScene != null) {
                if (line.startsWith("镜头描述：")) {
                    var sceneDescription = line.substring(5).trim();
                    currentScene.setSceneDescription(sceneDescription);
                    currentScene.getDetailMap().put("镜头描述", sceneDescription);
                } else if (line.startsWith("品牌植入：")) {
                    var brandIntegration = line.substring(5).trim();
                    currentScene.setBrandIntegration(brandIntegration);
                    currentScene.getDetailMap().put("品牌植入", brandIntegration);
                } else if (line.startsWith("镜头类型：")) {
                    var cameraType = line.substring(5).trim();
                    currentScene.setCameraType(cameraType);
                    currentScene.getDetailMap().put("镜头类型", cameraType);
                } else if (line.startsWith("运镜方式：")) {
                    var cameraView = line.substring(5).trim();
                    currentScene.setCameraView(cameraView);
                    currentScene.getDetailMap().put("运镜方式", cameraView);
                } else if (line.startsWith("时长：")) {
                    String durationStr = line.substring(3).trim();
                    int duration = 1;
                    if (durationStr.contains("ms") || durationStr.contains("毫秒")) {
                        durationStr = durationStr.replace("ms", "").replace("毫秒", "");
                    }
                    if (durationStr.contains("s") || durationStr.contains("秒")) {
                        durationStr = durationStr.replace("s", "").replace("秒", "");
                        float durationF = Float.parseFloat(durationStr);
                        duration = (int) durationF;
                    }
                    currentScene.setSceneLength(duration);
//                    currentScene.getDetailMap().put("时长", duration);  // 不需要时长信息
                } else if (line.startsWith("出现演员：")) {
                    var actors = line.substring(5).trim();
                    currentScene.setActors(actors);
                    currentScene.getDetailMap().put("出现演员", actors);
                } else if (line.startsWith("服化道：")) {
                    var costumes = line.substring(4).trim();
                    currentScene.setCostumes(costumes);
                    currentScene.getDetailMap().put("服化道", costumes);
                } else if (line.startsWith("演员动作：")) {
                    var actions = line.substring(5).trim();
                    currentScene.setActorActions(actions);
                    currentScene.getDetailMap().put("演员动作", actions);
                } else if (line.startsWith("演员表情：")) {
                    var expressions = line.substring(5).trim();
                    currentScene.setActorExpressions(expressions);
                    currentScene.getDetailMap().put("演员表情", expressions);
                } else if (line.startsWith("台词：")) {
                    String dialogue = line.substring(3).trim();
                    if (dialogue.startsWith("\"") && dialogue.endsWith("\"")) {
                        dialogue = dialogue.substring(1, dialogue.length() - 1);
                    }
                    currentScene.setDialogue(dialogue);
                    currentScene.getDetailMap().put("台词", dialogue);
                } else if (line.startsWith("台词情绪：")) {
                    var emotion = line.substring(5).trim();
                    currentScene.setDialogueEmotion(emotion);
                    currentScene.getDetailMap().put("台词情绪", emotion);
                } else if (line.startsWith("服装造型：") || line.startsWith("服装造型建议：")) {
                    var costume = line.substring(line.indexOf("：") + 1).trim();
                    currentScene.setCostumeSuggestions(costume);
                    currentScene.getDetailMap().put("服装造型建议", costume);
                } else if (line.startsWith("道具清单：")) {
                    var props = line.substring(5).trim();
                    currentScene.setProps(props);
                    currentScene.getDetailMap().put("道具清单", props);
                } else if (line.startsWith("布景要求：")) {
                    var requirements = line.substring(5).trim();
                    currentScene.setSetRequirements(requirements);
                    currentScene.getDetailMap().put("布景要求", requirements);
                } else if (line.startsWith("背景音乐/音效：") || line.startsWith("背景音乐：") || line.startsWith("背景音效：")) {
                    var bgm = line.substring(line.indexOf("：") + 1).trim();
                    currentScene.setBgm(bgm);
                    currentScene.getDetailMap().put("背景音乐", bgm);
                } else if (line.startsWith("光影与色彩要求：")) {
                    var lightColor = line.substring(8).trim();
                    currentScene.setLightColor(lightColor);
                    currentScene.getDetailMap().put("光影与色彩要求", lightColor);
                } else if (line.startsWith("摄影器材：")) {
                    var equipment = line.substring(5).trim();
                    currentScene.setEquipment(equipment);
                    currentScene.getDetailMap().put("摄影器材", equipment);
                } else if (line.startsWith("视频内容策略字段：") || line.startsWith("视频内容策略：")) {
                    var strategy = line.substring(line.indexOf("：") + 1).trim();
                    currentScene.setContentStrategy(strategy);
                    currentScene.getDetailMap().put("视频内容策略", strategy);
                }
            }
        }

        // 添加最后一个场景
        if (currentScene != null) {
            scenes.add(currentScene);
        }

        return scenes;
    }

    @NotNull
    private static DifyScriptGenResponseDTO getDifyVideoScriptDTO(Pattern SCENE_PATTERN, String result3) {
        DifyScriptGenResponseDTO dto = new DifyScriptGenResponseDTO();
        List<DifyScriptGenResponseDTO.Scene> scenes = new ArrayList<>();

        Matcher matcher = SCENE_PATTERN.matcher(result3);
        while (matcher.find()) {
            DifyScriptGenResponseDTO.Scene scene = new DifyScriptGenResponseDTO.Scene();
            scene.setSceneNumber(Integer.parseInt(matcher.group(1)));
            scene.setSceneDescription(matcher.group(2));
            scene.setBrandIntegration(matcher.group(3));
            scene.setCameraType(matcher.group(4));
            scene.setCameraView(matcher.group(5));
            scene.setSceneLength(Integer.parseInt(matcher.group(6)));
            scene.setActors(matcher.group(7));
            scene.setActorActions(matcher.group(8));
            scene.setActorExpressions(matcher.group(9));
            scene.setDialogue(matcher.group(10));
            scene.setDialogueEmotion(matcher.group(11));
            scene.setCostumeSuggestions(matcher.group(12));
            scene.setProps(matcher.group(13));
            scene.setSetRequirements(matcher.group(14));
            scene.setBgm(matcher.group(15));
            scene.setLightColor(matcher.group(16));
            scene.setEquipment(matcher.group(17));
            scene.setContentStrategy(matcher.group(18));

            scenes.add(scene);
        }

        dto.setScenes(scenes);
        return dto;
    }

    public static List<String> parseScriptToScenes(String script) {
        List<String> scenes = new ArrayList<>();
        Pattern pattern = Pattern.compile("镜头编号：\\d+[\\s\\S]*?(?=(镜头编号：|$))");
        pattern.matcher(script)
                .results()
                .forEach(match -> {
                    String scene = match.group().trim();
                    // 移除"镜头编号：xxx"部分
                    scene = scene.replaceFirst("镜头编号：\\d+\\s*", "");
                    scenes.add(scene);
                });
        return scenes;
    }

    @Test
    public void parseArray() {
        var arrayStr = """
                输出格式：
                [1, 3]
                """;
        Pattern pattern = Pattern.compile("\\[(.*?)\\]");
        Matcher matcher = pattern.matcher(arrayStr);
        if (matcher.find()) {
            String contentInsideBrackets = matcher.group(); // 获取包含[]的内容
            System.out.println(contentInsideBrackets);
        }
    }

    @Test
    public void splitProductInfo() {
        var infoStr = """
                商品名：户外随行tritan上学女大容量茶隔吸管杯
                商品品牌：COOKER KING/炊大皇
                卖点：1. 材质为耐高温的tritan，安全可靠；2. 大容量650ml，适合户外随行；3. 带茶隔和吸管设计，使用便捷；4. 适用人群为成人，适合任何场景使用。
                """;

        var product = DifyProductSummaryResponseDTO.buildByText(infoStr);
        System.out.println(product);
    }

    @SneakyThrows
    @Test
    public void testDifyProductSummary() {
        var productStr = """
                {"title":"COOKER KING/炊大皇户外随行tritan上学女大容量茶隔吸管杯耐高温","tags":[{"message":[{"desc":"COOKER KING/炊大皇"}],"name":"品牌"},{"message":[{"desc":"简约清新"}],"name":"风格"},{"message":[{"desc":"tritan"}],"name":"材质"},{"message":[{"desc":"650ml"}],"name":"容量"},{"message":[{"desc":"带茶隔"},{"desc":"带吸管"},{"desc":"吸管"},{"desc":"吸管杯"}],"name":"杯子样式"},{"message":[{"desc":"2岁以上"}],"name":"适用对象"},{"message":[{"desc":"小清新"}],"name":"流行元素"},{"message":[{"desc":"任何场景"}],"name":"适用场景"},{"message":[{"desc":"吸管款"}],"name":"款式"},{"message":[{"desc":"旅行杯"}],"name":"水杯种类"},{"message":[{"desc":"成人"}],"name":"适用人群"}],"shopName":"炊大皇COOKER KING水具旗舰店"}
                """;
        Map<String, Object> input = Map.of("json", productStr);
        var difyRequest = DifyRequest.builder()
                .appKey("app-1OyXih78Vl1JRMGmX5yoJLKB")
                .baseUrl("https://llm-ops-social.mlamp.cn/v1")
                .inputsParams(input)
                .observationId(UUID.randomUUID().toString().replace("-", ""))
                .build();
        log.info("产品信息摘要中");
        DifyUtil difyUtil = new DifyUtil();
        var difyResult = difyUtil.executeAndMergeStreaming(difyRequest);
        if (!difyResult.containsKey("text")) {
            throw new Exception("调用dify的产品信息摘要失败");
        }
        log.info(difyResult.get("text").toString());
    }

}
