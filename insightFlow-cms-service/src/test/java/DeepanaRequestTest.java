import cn.hutool.json.JSONUtil;
import cn.mlamp.insightflow.cms.model.dto.DeepanaDySkuResponseDTO;
import cn.mlamp.insightflow.cms.model.dto.DeepanaResponseDataDTO;
import cn.mlamp.insightflow.cms.model.dto.DeepanaTikTokVideoDTO;
import cn.mlamp.insightflow.cms.model.dto.DeepanaTiktokRequest;
import cn.mlamp.insightflow.cms.util.DeepanaSignUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.Date;
import java.util.Map;

@Slf4j
public class DeepanaRequestTest {
    private static final String DEEPANA_GET_TIKTOK_VIDEO_INFO = "/get_json";
    String request = """
            {
                "resource_id": "dfsdfsfsdf",\s
                "video_url": "https://www.douyin.com/video/7482383345079340348",\s
                "requestId": "123456",\s
                "appId": "aaaa",\s
                "time": "1742994088639",\s
                "sign": "048c59f5e80a8248e3fccc056df0a64b",\s
                "upload_date": "2022-10-22"
            }
            """;

    String response = """
                        {
                          "CollectCount": 3985,
                          "CommentCount": 3444,
                          "Content": "回去能给钓友吹一年牛皮… #路亚 #钓鱼#路亚野钓 #钓鱼人 #户外",
                          "FollowerCount": 37577,
                          "IsDeleted": false,
                          "LikeCount": 80240,
                          "NickName": "野钓-鱼鹰",
                          "PublishedAt": **********,
                          "RepostCount": 86930,
                          "VideoDuration": 18700,
                          "kwHeadImage": "https://p3-pc-sign.douyinpic.com/image-cut-tos-priv/89813ba9798bd79dfe8f57cff8a9d149~tplv-dy-resize-origshort-autoq-75:330.jpeg?lk3s\\u003d138a59ce\\u0026x-expires\\u003d**********\\u0026x-signature\\u003djjZS3EihKO%2FwU4slejF79tnzKNg%3D\\u0026from\\u003d327834062\\u0026s\\u003dPackSourceEnum_AWEME_DETAIL\\u0026se\\u003dfalse\\u0026sc\\u003dcover\\u0026biz_tag\\u003dpcweb_cover\\u0026l\\u003d202503281413522DC2B4429B4B9B10A22B",
                          "kwProfileImageUrl": "https://p3-pc.douyinpic.com/aweme/100x100/aweme-avatar/tos-cn-avt-0015_17eedf5a2d39b070430d9dd954b00b49.jpeg?from\\u003d327834062",
                          "kwUserUrl": "https://www.douyin.com/user/MS4wLjABAAAAr277bLM3i8RoOUTiI88lipBSrfQ12cTXsEhmyyfyCeKJc7BT-Yql7j2c0eTnEyTS",
                          "kwVideoUrl": "https://www.douyin.com/aweme/v1/play/?video_id\\u003dv1e00fgi0000cva23cvog65jehbnmhi0\\u0026line\\u003d0\\u0026file_id\\u003d74bf5be6c46346d2ac5998d007fcd565\\u0026sign\\u003d633ce7785b047c477ef0302018a1a466\\u0026is_play_url\\u003d1\\u0026source\\u003dPackSourceEnum_AWEME_DETAIL",
                          "video_url": "https://www.douyin.com/aweme/v1/play/?video_id\\u003dv1e00fgi0000cva23cvog65jehbnmhi0\\u0026line\\u003d0\\u0026file_id\\u003d74bf5be6c46346d2ac5998d007fcd565\\u0026sign\\u003d633ce7785b047c477ef0302018a1a466\\u0026is_play_url\\u003d1\\u0026source\\u003dPackSourceEnum_AWEME_DETAIL"
                        }
            """;

    @Test
    public void parseUnixTime() {
        long unixTime = **********;
        Date date = new Date(unixTime * 1000);
        System.out.println(date);
    }

    @Test
    public void testDownloadJson() {
        var url = "https://www.douyin.com/video/7488367442469719308";
        var json = getTikTokVideoInfo(url, "7488367442469719308", new Date()).block();
        System.out.println(json);
    }


    public Mono<DeepanaResponseDataDTO<String>> getTikTokVideoInfo(String url, String esId, Date documentCreateDate) {
        DeepanaSignUtil.setAppKey("d62c83b1bd204b23826e9c7b310bc332");
        DeepanaSignUtil.setAppId("insightflow-cms");
        var deepanaWebClient = WebClient.builder()
                .baseUrl("https://deepana.hsk.top")
                .build();

        return deepanaWebClient.post().uri(DEEPANA_GET_TIKTOK_VIDEO_INFO)
                .body(BodyInserters.fromValue(new DeepanaTiktokRequest(url, esId, documentCreateDate)))
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<DeepanaResponseDataDTO<String>>() {
                })
                .retry(3)
                .onErrorReturn(new DeepanaResponseDataDTO<>());
    }

    @Test
    public void testLoadDySku() {
        var url = "https://v.douyin.com/kQBjEHcj3dw/";
        var result = getTikTokSkuInfo(url).block();
        log.info(JSONUtil.toJsonStr(result));
    }

    public Mono<DeepanaDySkuResponseDTO> getTikTokSkuInfo(String url) {
        DeepanaSignUtil.setAppKey("d62c83b1bd204b23826e9c7b310bc332");
        DeepanaSignUtil.setAppId("insightflow-cms");

        String requestId = DeepanaSignUtil.getRequestId();
        String time = DeepanaSignUtil.getTime();

        // 创建 MultiValueMap 实例
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();

        // 添加查询参数
        params.add("appId", DeepanaSignUtil.getAppId());
        params.add("requestId", requestId);
        params.add("sign", DeepanaSignUtil.sign(requestId, time));
        params.add("time", time);
        params.add("short_url", url);


        var deepanaWebClient = WebClient.builder()
                .baseUrl("http://dysku.akuanyun.com")
                .build();

        return deepanaWebClient.get()
                .uri(uriBuilder -> uriBuilder.path("/")
                        .queryParams(params)
                        .build())
                .retrieve()
                .bodyToMono(DeepanaDySkuResponseDTO.class);
    }
}
