logging:
  level:
    root: INFO
spring:
  task:
    scheduling:
      enabled: true
  servlet:
    multipart:
      # 是否开启springMVC 多部分上传功能(默认开启)
      enabled: true
      # 上传单个文件大小(默认是1MB)
      max-file-size: 1024MB
      # 限制所有文件大小
      max-request-size: 1024MB
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *******************************************************************************************************
    username: ai_image
    password: kytzLNcXKT
    hikari:
      maximum-pool-size: 10
      connection-init-sql: "SELECT 1"
  data:
    redis:
      database: 6
      timeout: 30000
      password: gpqylk3p6l
      host: *************
      port: 8001
  ai:
    openai:
      base-url: https://ai-gateway.mininglamp.com/
      api-key: sk-zvy3nOiS59wWW5ul4896339f402f4dAfBc892b9b53425d96
      embedding:
        options:
          model: text-embedding-ada-002
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher

# 对象存储服务配置
s3:
  config:
    # 图片
    #    pic:
    #      aliasBucketName: ai-pc-test
    #      bucketName: ai-pc-test
    #      endpoint: https://oss-cn-beijing.aliyuncs.com
    #      accessKey: LTAI5tC3KdiCP5heNnTMr5sw
    #      region: oss-cn-beijing
    #      pathStyle: false
    #      secretKey: ******************************

    # 视频存储配置
    cms:
      aliasBucketName: ai-pc-cms2
      bucketName: ai-pc-cms2
      endpoint: https://oss-cn-beijing.aliyuncs.com
      accessKey: LTAI5tMoPYdd2FtfVtKR9qFT
      region: oss-cn-beijing
      pathStyle: false
      secretKey: ******************************

  # 文档对象存储服务配置
  document:
    external:
      service: true
    download:
      expireSecond: 900
    upload:
      expireSecond: 900

login:
  session:
    active: false

image-server:
  upload-address: http://************:8008/upload_image
  combine-address: http://************:8008/combine
  flux1-address: http://************:1607/generate/flux1
  sd3-address: http://************:1607/generate/sd3
  role-generation-address: http://************:8321/generate_image
  role-upload-address: http://************:8321/upload_image

analysis-server:
  cluster-address: http://*************:8103/cluster
  ascribe-address: http://*************:8112/attribution_analysis
  model-cluster-address: https://llm-ops-social.mlamp.cn/v1
  model-cluster-api: app-X1UtJyi42uHNzZyp4zzxY8Jw
  marking-address: http://************:8166/batch-api
  marking-username: suanyang
  marking-password: 2024@mlamp-say
  token-use-address: https://ai-gateway.mininglamp.com/api/token/usage
ascribe-authentication:
  token: Bearer Attribution_Analysis-4f3a2b1c9d8e7f6a5b4c3d2e1f0a9b8c
work-flow:
  show-address: https://insight-flow-test.mlamp.cn/share/

# 分享配置
share:
  base-url: https://insight-flow-cms.mlamp.cn

auth-center:
  anonymousPath:
    - "/ttc/login"
    - "/"
    - "/open-api/material-videos/batch"
    - "/open-api/material-videos/check-non-existing"
    - "/open-api/material-videos/check-exists"
    - "/ttc/user/register"
    - "/ttc/user/activate"
    - "/ttc/user/register/check_tenant_name_exist"
    - "/ttc/user/reset_password"
    - "/verification_code/send"
    - "/video-hotspot/list"
    - "/video-hotspot/tribe/list"
    - "/video/five/gold/list"
    - "/qianchuan-hotspot/list"
    - "/video/info/async/result"
    - "/open-api/share"
  baseDomain: mlamp.cn
  loginPath: /ttc/login
  logoutPath: /ttc/logout
  productKey: IFCMS
  secure: false
  serverUrl: https://ttc.cn.miaozhen.com/ttc/api
  serverOpenApiUrl: https://ttc.cn.miaozhen.com/ttc/open/api
  registerUrl: https://ttc.cn.miaozhen.com/ttc/open/api/tenant/register
  activateUrl: https://ttc.cn.miaozhen.com/ttc/open/api/tenant/activate
  checkTenantNameExistUrl: https://ttc.cn.miaozhen.com/ttc/open/api/tenant/check_tenant_name_exist
  sendVerificationCodeUrl: https://ttc.cn.miaozhen.com/ttc/open/api/verification_code/send
  resetPasswordUrl: https://ttc.cn.miaozhen.com/ttc/open/api/user/reset_password
  tenantListUrl: https://ttc.cn.miaozhen.com/ttc/open/api/tenant/list
  tenantDetailUrl: https://ttc.cn.miaozhen.com/ttc/open/api/tenant/detail
  domainFromHeader: false
  serverOpenApiClientId: IFCMS
  serverOpenApiSign: 9C7F4AD8DB5B2ADE26A4C935A6A3D8D5


es:
  client:
    es7:
      hosts: https://social-es.mlamp.cn
      path-prefix: "mz_social"
      api-key: "Y2EzU1JKUUI4UUs3cnVwWEtmRlo6dUtPRUhRRGFSU0NuakctYmRkd2d1Zw=="                       # 如果需要，提供你的 API 密钥
      connect-timeout: 5000
      socket-timeout: 300000
      connection-request-timeout: 10000
      max-conn-total: 30
      max-conn-per-route: 10
      keep-alive-strategy-minute: 3
      bulk-timeout: 120000

tcc:
  tenant-id: 8540
  client-id: "insight-flow"
  productKey: "insight-flow"
  url: "https://ttc.cn.miaozhen.com/ttc/"
  clientSecret: "597dc305-5a78-b97b-dead-25e82902dd64"
  resourceTypeKeyAgent: "agent"
  resourceTypeKeyPrompt: "prompt"

code:
  python:
    interpreter: /opt/miniconda3/bin/python


# 数据库配置（PostgreSQL）
database:
  postgres:
    driver-class-name: org.postgresql.Driver
    url: **************************************************
    username: cms
    password: cms

  mysql:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *******************************************************************************************************
    username: ai_image
    password: kytzLNcXKT
    hikari:
      maximum-pool-size: 10
      connection-init-sql: "SELECT 1"


video:
  download:
    baseUrl: https://deepana.hsk.top

analysis:
  video:
    ffmpeg: ffmpeg
    ffprobe: ffprobe
    url: http://*************:8354

dify:
  base-url: https://llm-ops-social.mlamp.cn/v1
  storyboard-recommend-key: app-m2So29LUuMni7DfRCgx4dVnW
  ai-write-key: app-Wk3n5ElB7Uf28tKlgFfcpivN
  image-prompt-translate-key: app-T4NuU7nsuck5flI2ZyUm53Ms
  gold-five-second-key: app-xqwGETE4HzvRTyPdtqrH9Vvm
  product-summary-key: app-JvcPbUCSIP8rRju7yMoknGXl
  storyboard-modify-key: app-55QSzz8VMr966l28RJU17x6S
  imageStyleAndDescKey: app-M3wI0s78fYCY6uJyopupUdCB
sign-check:
  app-secrets:
    qianchuan_app: G7xKpL1MnQvE9zRbSt3HyA2WeiUfCk8J

tenant-template:
  isForceCreateTenant: false
  expiredDay: 365
  resources: []
  permissions: [892, 905, 913, 914, 917, 918, 919]
  quotas: []
  userCountLimit: 100
  tenantTagList: []

# 管理员租户配置
admin:
  tenant-id: 5312

# 腾讯验证码
tencent:
  captcha:
    secret-id: AKIDjHafW8UaR2NSJwJQGqAE2qOfgRv84mEP #云API密钥-secretId
    secret-key: UigOp0vay5vVoqb6MM6ZmhMnxqSnrfDU #云API密钥-secretKey
    captcha-app-id: 195965402 #验证码应用ID。登录 验证码控制台，在验证列表的【密钥】列，即可查看到CaptchaAppId。
    app-secret-key: Rt7bqN79myiKrsaJzo4hz76uq #验证码应用密钥。登录 验证码控制台，在验证列表的【密钥】列，即可查看到AppSecretKey。AppSecretKey属于服务器端校验验证码票据的密钥，请妥善保密，请勿泄露给第三方。
    captchaType: 9 #固定填值：9。可在控制台配置不同验证码类型。


gold:
  five:
    second:
      video:
        limit: 1000  # 可以根据需要调整
      group:
        size:
          threshold: 2  # 可以根据需要调整

wechat:
  webhook:
    url: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=cef96e3f-8689-4b3f-8a93-e3c492b5fcfa
