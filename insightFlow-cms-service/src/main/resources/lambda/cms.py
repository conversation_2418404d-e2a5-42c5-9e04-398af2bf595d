import os
import subprocess
from oss2 import Auth, Bucket
import logging
import json
import tempfile

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger()

def handler(event, context):
    # 解析事件数据
    try:
        event_obj = json.loads(event)
    except Exception as e:
        logger.error(f"Failed to parse event: {str(e)}")
        return {"status": "error", "message": "Invalid event data"}

    # 获取OSS事件信息
    try:
        records = event_obj.get('events', [])
        if not records:
            logger.info("No events found in the payload")
            return {"status": "success", "message": "No events to process"}

        # 获取第一个事件（通常只有一个）
        oss_event = records[0]
        oss_info = oss_event.get('oss', {})
        bucket_info = oss_info.get('bucket', {})
        object_info = oss_info.get('object', {})

        bucket_name = bucket_info.get('name')
        object_key = object_info.get('key')
        region = 'cn-beijing'  # 固定为北京区域

        logger.info(f"Processing OSS event for bucket: {bucket_name}, key: {object_key}")

    except Exception as e:
        logger.error(f"Failed to parse OSS event: {str(e)}")
        return {"status": "error", "message": "Failed to parse OSS event"}

    # 检查文件是否符合处理条件
    if not (object_key.startswith('test/ceshi/') and object_key.endswith('.mp4')):
        logger.info(f"File {object_key} does not match the criteria, skipping")
        return {"status": "success", "message": "File does not match criteria"}

    # 使用固定密钥信息（请替换为您的实际密钥）
    access_key_id = 'LTAI5tMoPYdd2FtfVtKR9qFT'
    access_key_secret = '******************************'

    # 初始化OSS认证（修正为正确的参数数量）
    auth = Auth(access_key_id, access_key_secret)

    # 初始化OSS Bucket
    endpoint = 'https://oss-cn-beijing.aliyuncs.com'
    bucket = Bucket(auth, endpoint, 'ai-pc-cms2')  # 使用您指定的bucket名称

    try:
        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            # 下载原始MP4文件
            local_mp4_path = os.path.join(temp_dir, os.path.basename(object_key))
            logger.info(f"Downloading {object_key} to {local_mp4_path}")
            bucket.get_object_to_file(object_key, local_mp4_path)

            # 准备输出文件名和路径
            original_filename = os.path.splitext(os.path.basename(object_key))[0]
            compress_mp4_name = f"compress_{original_filename}.mp4"
            wav_name = f"{original_filename}.wav"

            # 输出目录结构
            output_dir = f"new/{original_filename}/"
            compress_mp4_key = f"{output_dir}{compress_mp4_name}"
            wav_key = f"{output_dir}{wav_name}"

            # 1. 压缩MP4文件
            compressed_mp4_path = os.path.join(temp_dir, compress_mp4_name)
            compress_cmd = [
                'ffmpeg', '-y', '-v', 'error', '-i', local_mp4_path,
                '-strict', 'experimental', '-crf', '35', '-vcodec', 'libx264',
                '-vf', "scale='if(gt(iw,ih),min(1080,iw),-1)':'if(gt(iw,ih),-1,min(1080,ih))'",
                compressed_mp4_path
            ]
            logger.info(f"Executing compress command: {' '.join(compress_cmd)}")
            subprocess.run(compress_cmd, check=True)

            # 2. 生成WAV文件
            wav_path = os.path.join(temp_dir, wav_name)
            wav_cmd = [
                'ffmpeg', '-y', '-v', 'error', '-i', local_mp4_path,
                '-f', 'wav', '-vn', '-acodec', 'pcm_s16le', '-ac', '1', '-ar', '16000',
                wav_path
            ]
            logger.info(f"Executing WAV conversion command: {' '.join(wav_cmd)}")
            subprocess.run(wav_cmd, check=True)

            # 上传处理后的文件到OSS
            logger.info(f"Uploading compressed MP4 to {compress_mp4_key}")
            bucket.put_object_from_file(compress_mp4_key, compressed_mp4_path)

            logger.info(f"Uploading WAV file to {wav_key}")
            bucket.put_object_from_file(wav_key, wav_path)

            logger.info("File processing completed successfully")
            return {
                "status": "success",
                "message": "Files processed and uploaded",
                "compressed_mp4": compress_mp4_key,
                "wav_file": wav_key
            }

    except subprocess.CalledProcessError as e:
        logger.error(f"FFmpeg command failed: {str(e)}")
        return {"status": "error", "message": f"FFmpeg processing failed: {str(e)}"}
    except Exception as e:
        logger.error(f"Error processing file: {str(e)}")
        return {"status": "error", "message": f"Processing failed: {str(e)}"}