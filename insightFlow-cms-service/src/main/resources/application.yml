spring:
  profiles:
    active: local
  main:
    allow-circular-references: true
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *********************************************************
    username: root
    password: samepage.ai
    hikari:
      maximum-pool-size: 10
      connection-init-sql: 'SELECT 1'
  data:
    redis:
      host: 127.0.0.1 # 通过docker容器管理外链
      port: 6379
      database: 0
  devtools:
    restart:
      enabled: false

server:
  port: 8080
  compression:
    min-response-size: 1024
    enabled: true
    mime-types: application/json,application/xml,text/html,text/plain,text/css,application/x-javascript

# 分享配置
share:
  base-url: http://localhost:8080

s3:
  # 文档对象存储服务配置
  document:
    download:
      expireSecond: 900
    upload:
      expireSecond: 900
    endpoint: http://minio:9001/
    bucketName: samepage-s3-paper
    accessKey: minioadmin
    secretKey: minioadmin

    region: myregion
  # 图片
  picture:
    endpoint: http://minio:9001/
    bucketName: samepage-s3-paper
    accessKey: minioadmin
    secretKey: minioadmin
    region: myregion


# application.yml
mybatis-plus:
  type-handlers-package: com.baomidou.mybatisplus.extension.handlers

springdoc:
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    #operations-sorter: order
  api-docs:
    path: /v3/api-docs
  group-configs:
    - group: 'default'
      display-name: '测试'
      paths-to-match: '/**'
      packages-to-scan: cn.mlamp.insightflow.cms
  default-flat-param-object: true

knife4j:
  enable: true
  setting:
    language: zh_cn
    swagger-model-name: 实体类列表

# 上传视频链接配置
upload-link:
  product-urls:
    - https://v.douyin.com/
  domains:
#    - id : xiaohongshu
#      name : 小红书
#      icon-key : xiaohongshu
#      urls :
#        - xiaohongshu.com
#        - xhslink.com
#        - xiaohs.com
    - id : tik-tok
      name : 抖音
      icon-key : tik-tok
      urls :
        - douyin.com/video
        - douyin.com/discover

third-web:
  deepana:
    base-url: https://deepana.hsk.top
  deepana-dy-sku:
    base-url: http://dysku.akuanyun.com
  suan-fa:
    base-url: http://*************:8354

dify:
  base-url: https://llm-ops-social.mlamp.cn/v1
  script-gen-key: app-g1FdvpaoYHFeFrPg856cM68C
  ai-write-key: app-XOCSC1YaSJyjKSdML0LXAucS
  image-prompt-translate-key: app-8fNgPTGDD0s9gUNVjvnvnTuM
