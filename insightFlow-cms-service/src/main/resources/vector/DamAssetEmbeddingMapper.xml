<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.mlamp.insightflow.cms.vector.DamAssetEmbeddingMapper">

    <resultMap id="BaseResultMap" type="cn.mlamp.insightflow.cms.entity.dam.DamAssetEmbedding">
        <id column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="user_id" property="userId"/>
        <result column="directory_id" property="directoryId"/>
        <result column="aspect_ratio" property="aspectRatio"/>
        <result column="content" property="content"/>
        <result column="embedding" property="embedding"
                jdbcType="OTHER"
                typeHandler="cn.mlamp.insightflow.cms.util.mybatis.pg.FloatArrayTypeHandler"/>
        <result column="metadata" property="metadata"
                typeHandler="cn.mlamp.insightflow.cms.util.mybatis.pg.JsonbTypeHandler"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <insert id="insert" parameterType="cn.mlamp.insightflow.cms.entity.dam.DamAssetEmbedding">
        INSERT INTO cms_asset_embeddings
        (id, tenant_id, user_id, directory_id, aspect_ratio, content, embedding, metadata, create_time, update_time, is_deleted)
        VALUES (#{id},
                #{tenantId},
                #{userId},
                #{directoryId},
                #{aspectRatio},
                #{content},
                #{embedding, jdbcType=ARRAY, typeHandler=cn.mlamp.insightflow.cms.util.mybatis.pg.FloatArrayTypeHandler},
                #{metadata}::jsonb,
                #{createTime},
                #{updateTime},
                #{isDeleted})
    </insert>

    <update id="updateByEntity" parameterType="cn.mlamp.insightflow.cms.entity.dam.DamAssetEmbedding">
        UPDATE cms_asset_embeddings
        SET is_deleted=0,
            <if test="tenantId != null">
                tenant_id=#{tenantId},
            </if>
            <if test="userId != null">
                user_id=#{userId},
            </if>
            <if test="directoryId != null">
                directory_id=#{directoryId},
            </if>
            <if test="metadata != null">
                metadata=#{metadata}::jsonb,
            </if>
            update_time=#{updateTime}
        WHERE id = #{id}
    </update>

    <update id="updateEmbedding">
        UPDATE cms_asset_embeddings
        SET embedding=#{embedding, jdbcType=ARRAY, typeHandler=cn.mlamp.insightflow.cms.util.mybatis.pg.FloatArrayTypeHandler},
            content=#{content},
            update_time=#{updateTime}
        WHERE id = #{id}
    </update>

    <select id="totalEmbeddingCount" resultType="long">
        SELECT COUNT(*)
        FROM cms_asset_embeddings
        WHERE is_deleted = 0
        AND aspect_ratio = #{aspectRatio}
        <if test="tenantId != null">
            AND tenant_id = #{tenantId}
        </if>
        <if test="userId != null">
            AND user_id = #{userId}
        </if>
        <if test="directoryIds != null and directoryIds.size() > 0">
            AND directory_id IN
            <foreach collection="directoryIds" item="dirId" open="(" separator="," close=")">
                #{dirId}
            </foreach>
        </if>
    </select>

    <select id="pageEmbedding" resultType="cn.mlamp.insightflow.cms.entity.dam.DamAssetEmbedding">
        SELECT
        id,
        tenant_id,
        user_id,
        directory_id,
        aspect_ratio,
        content,
        embedding &lt;-&gt; #{embedding}::vector AS distance,
        create_time,
        update_time,
        is_deleted
        FROM cms_asset_embeddings
        WHERE is_deleted = 0
        AND aspect_ratio = #{aspectRatio}
        <if test="tenantId != null">
            AND tenant_id = #{tenantId}
        </if>
        <if test="userId != null">
            AND user_id = #{userId}
        </if>
        <if test="directoryIds != null and directoryIds.size() > 0">
            AND directory_id IN
            <foreach collection="directoryIds" item="dirId" open="(" separator="," close=")">
                #{dirId}
            </foreach>
        </if>
        ORDER BY embedding &lt;-&gt; #{embedding}::vector
        LIMIT #{pageSize}
        OFFSET #{offset}
    </select>

    <select id="topKEmbedding" resultType="cn.mlamp.insightflow.cms.entity.dam.DamAssetEmbedding">
        SELECT id, tenant_id, user_id, directory_id, aspect_ratio, content, embedding &lt;-&gt; #{embedding}::vector as distance
        FROM cms_asset_embeddings
        WHERE is_deleted = 0
        AND aspect_ratio = #{aspectRatio}
        <if test="tenantId != null">
            AND tenant_id = #{tenantId}
        </if>
        <if test="userId != null">
            AND user_id = #{userId}
        </if>
        <if test="directoryIds != null and directoryIds.size() > 0">
            AND directory_id IN
            <foreach collection="directoryIds" item="dirId" open="(" separator="," close=")">
                #{dirId}
            </foreach>
        </if>
        <if test="threshold != null">
            AND embedding &lt;-&gt; #{embedding}::vector &lt;= #{threshold}
        </if>
        ORDER BY embedding &lt;-&gt; #{embedding}::vector
        LIMIT #{limit}
    </select>
</mapper>