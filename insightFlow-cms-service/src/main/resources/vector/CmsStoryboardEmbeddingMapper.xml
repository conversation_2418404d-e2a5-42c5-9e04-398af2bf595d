<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.mlamp.insightflow.cms.vector.CmsStoryboardEmbeddingMapper">

    <resultMap id="BaseResultMap" type="cn.mlamp.insightflow.cms.entity.CmsStoryboardEmbedding">
        <id column="id" property="id"/>
        <result column="task_id" property="taskId"/>
        <result column="content" property="content"/>
        <result column="embedding" property="embedding"
                jdbcType="OTHER"
                typeHandler="cn.mlamp.insightflow.cms.util.mybatis.pg.FloatArrayTypeHandler"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="user_id" property="userId"/>
    </resultMap>

    <insert id="insert" parameterType="cn.mlamp.insightflow.cms.entity.CmsStoryboardEmbedding">
        INSERT INTO cms_storyboard_embeddings
        (id, task_id, content, embedding, tenant_id, user_id)
        VALUES (#{id},
                #{taskId},
                #{content},
                #{embedding, jdbcType=ARRAY, typeHandler=cn.mlamp.insightflow.cms.util.mybatis.pg.FloatArrayTypeHandler},
                #{tenantId},
                #{userId})
    </insert>

    <update id="updateEmbedding">
        UPDATE cms_storyboard_embeddings
        SET embedding=#{embedding, jdbcType=ARRAY, typeHandler=cn.mlamp.insightflow.cms.util.mybatis.pg.FloatArrayTypeHandler},
            content=#{content}
        WHERE id = #{id}
    </update>

    <select id="totalEmbeddingCount" resultType="long">
        SELECT COUNT(*)
        FROM cms_storyboard_embeddings
        WHERE 1=1
        <if test="taskId != null">
            AND task_id = #{taskId}
        </if>
        <if test="tenantId != null">
            AND tenant_id = #{tenantId}
        </if>
        <if test="userId != null">
            AND user_id = #{userId}
        </if>
    </select>

    <select id="pageEmbedding" resultType="cn.mlamp.insightflow.cms.entity.CmsStoryboardEmbedding">
        SELECT
        id,
        task_id,
        content,
        embedding &lt;-&gt; #{embedding}::vector AS distance,
        tenant_id,
        user_id
        FROM cms_storyboard_embeddings
        WHERE 1=1
        <if test="taskId != null">
            AND task_id = #{taskId}
        </if>
        <if test="tenantId != null">
            AND tenant_id = #{tenantId}
        </if>
        <if test="userId != null">
            AND user_id = #{userId}
        </if>
        ORDER BY embedding &lt;-&gt; #{embedding}::vector
        LIMIT #{pageSize}
        OFFSET #{offset}
    </select>

    <select id="topKEmbedding" resultType="cn.mlamp.insightflow.cms.entity.CmsStoryboardEmbedding">
        SELECT id, task_id, content, embedding &lt;-&gt; #{embedding}::vector as distance, tenant_id, user_id
        FROM cms_storyboard_embeddings
        WHERE 1=1
        <if test="taskId != null">
            AND task_id = #{taskId}
        </if>
        <if test="tenantId != null">
            AND tenant_id = #{tenantId}
        </if>
        <if test="userId != null">
            AND user_id = #{userId}
        </if>
        <if test="threshold != null">
            AND embedding &lt;-&gt; #{embedding}::vector &lt;= #{threshold}
        </if>
        ORDER BY embedding &lt;-&gt; #{embedding}::vector
        LIMIT #{limit}
    </select>
</mapper>
