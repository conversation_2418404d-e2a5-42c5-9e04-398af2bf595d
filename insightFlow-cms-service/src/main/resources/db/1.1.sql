-- 千川素材视频数据表

CREATE TABLE `cms_qianchuan_material_video` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `ossid` varchar(128) NOT NULL COMMENT 'OSS ID',
  `video_id` varchar(64) NOT NULL COMMENT '视频ID',
  `consume_range` varchar(32) DEFAULT NULL COMMENT '消耗区间',
  `title` varchar(255) DEFAULT NULL COMMENT '标题',
  `brand` varchar(100) DEFAULT NULL COMMENT '品牌',
  `exposure` int(11) DEFAULT '0' COMMENT '曝光量',
  `shares` int(11) DEFAULT '0' COMMENT '分享数',
  `comments` int(11) DEFAULT '0' COMMENT '评论数',
  `likes` int(11) DEFAULT '0' COMMENT '点赞数',
  `clicks` int(11) DEFAULT '0' COMMENT '点击数',
  `industry` varchar(64) DEFAULT NULL COMMENT '行业',
  `ranking_type` varchar(32) DEFAULT NULL COMMENT '榜单类型（总榜/新兴榜）',
  `publish_time` timestamp NULL DEFAULT NULL COMMENT '发布时间',
  `author_name` varchar(100) DEFAULT NULL COMMENT '发布作者',
  `author_avatar` varchar(255) DEFAULT NULL COMMENT '发布作者头像',
  `cover_image` varchar(255) DEFAULT NULL COMMENT '视频首页头图',
  `duration` int(11) DEFAULT NULL COMMENT '视频时长（单位：秒）',
  `completion_rate` decimal(5,2) DEFAULT NULL COMMENT '完播率，百分比',
  `rating` float DEFAULT NULL COMMENT '创意评分',
  `kw_video_content` text COMMENT '视频语音识别',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` int(11) DEFAULT '0' COMMENT '逻辑删除标志（0-未删除，1-已删除）',
  `product_name` varchar(100) DEFAULT NULL COMMENT '产品名称',
  `celling_point` varchar(100) DEFAULT NULL COMMENT '卖点',
  `aiming_tribe` varchar(100) DEFAULT NULL COMMENT '受众人群',
  `highlight` int(11) DEFAULT NULL COMMENT '是否高光0：没有1：高光',
  `analysis_status` int(11) DEFAULT '0' COMMENT '0:待分析；1：分析中，2：分析成功，3：分析失败',
  `consume_range_weight` int(11) DEFAULT NULL COMMENT '用于排序的权重，单位为万，如70表示70w',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_video_id` (`video_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='千川素材视频数据表';

-- 视频黄金5秒方法论表

CREATE TABLE `cms_video_five_gold` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键 自增id',
  `industry` varchar(255) DEFAULT NULL COMMENT '行业分类',
  `cover_video_url` varchar(255) NOT NULL COMMENT '封面id',
  `video_num` int(11) DEFAULT NULL COMMENT '关联素材（视频）',
  `interact_count` int(11) DEFAULT NULL COMMENT '平均互动数',
  `like_count` int(11) DEFAULT NULL COMMENT '平均点赞数',
  `comment_count` int(11) DEFAULT NULL COMMENT '平均评论数',
  `originality_num` varchar(255) DEFAULT NULL COMMENT '创意分值',
  `dialogue_routine` varchar(255) DEFAULT NULL COMMENT '台词套路',
  `status` int(11) NOT NULL COMMENT '1：待处理；2：处理中，3：完成，4：失败',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '数据逻辑删除标记',
  `analysis_date` varchar(255) DEFAULT NULL COMMENT '数据曝光量',
  `exposure_count` int(11) DEFAULT NULL,
  `tag` varchar(255) DEFAULT NULL COMMENT '视频标签',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='视频黄金5秒方法论表';

-- 黄金3秒视频分析关系表
CREATE TABLE `cms_video_three_gold_relation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `video_three_gold_id` int(11) NOT NULL COMMENT '黄金3秒Id',
  `video_id` varchar(255) NOT NULL COMMENT '视频分析Id',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '数据逻辑删除标记',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=555 DEFAULT CHARSET=utf8mb4 COMMENT='黄金3秒视频分析关系表';


CREATE TABLE `cms_feedback` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键 自增id',
    `text` text COMMENT '反馈内容',
    `type` tinyint(2) unsigned DEFAULT '0' COMMENT '0:通用 1:视频分析2：AI仿写',
    `user_id` int(11) NOT NULL COMMENT '用户Id',
    `tenant_id` int(11) NOT NULL COMMENT '租户Id',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '数据逻辑删除标记',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='反馈意见表';


CREATE TABLE `cms_product_record` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键 自增id',
    `url` varchar(2048) DEFAULT NULL COMMENT '导入的URL',
    `title` varchar(255) DEFAULT NULL COMMENT '标题',
    `brand` varchar(255) DEFAULT NULL COMMENT '品牌',
    `product_name` varchar(255) DEFAULT NULL COMMENT '产品名称',
    `selling_point` varchar(255) DEFAULT NULL COMMENT '卖点',
    `duration` int(11) DEFAULT NULL COMMENT '时长',
    `lens_num` int(11) DEFAULT NULL COMMENT '镜头数量',
    `scene` varchar(255) DEFAULT NULL COMMENT '场景',
    `people_num` int(11) DEFAULT NULL COMMENT '人数',
    `festival` varchar(20) DEFAULT NULL COMMENT '节日',
    `user_id` int(11) NOT NULL COMMENT '用户Id',
    `tenant_id` int(11) NOT NULL COMMENT '租户Id',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '数据逻辑删除标记',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='商品记录表';


ALTER TABLE cms_video_result MODIFY COLUMN `data` TEXT COMMENT 'json格式';
ALTER TABLE cms_video_result ADD COLUMN user_id int(11) DEFAULT NULL COMMENT '用户id';
ALTER TABLE cms_video_result ADD COLUMN tenant_id int(11) DEFAULT NULL COMMENT '租户id';

ALTER TABLE `cms_pull_task_deduped_datas`
  ADD COLUMN `product_name` varchar(100) DEFAULT NULL COMMENT '产品名称',
  ADD COLUMN `celling_point` varchar(100) DEFAULT NULL COMMENT '卖点',
  ADD COLUMN `aiming_tribe` varchar(100) DEFAULT NULL COMMENT '受众人群',
  ADD COLUMN `highlight` int(11) DEFAULT NULL COMMENT '是否高光0：没有1：高光',
  ADD COLUMN `video_oss_id` varchar(100) DEFAULT NULL COMMENT '下载视频的OSSId',
  ADD COLUMN `video_head_pic_oss_id` varchar(255) DEFAULT NULL COMMENT '视频头图的OSSId';

-- 删除base64结果, 减少data长度为text
DELETE FROM `cms_task_detail` WHERE data_type = 1;
ALTER TABLE `cms_task_detail` CHANGE COLUMN `data` `data` TEXT NULL DEFAULT NULL COMMENT '结果数据' AFTER `data_type`;

-- task_info 表新增source_type字段
ALTER TABLE `cms_task_info` ADD COLUMN `source_type` tinyint(1) NULL DEFAULT 0 COMMENT '视频来源类型（0-用户，1-千川）' AFTER `id`;


ALTER TABLE cms_video_info ADD COLUMN `three_gold_type` VARCHAR(255) COMMENT '黄金3秒类型';
ALTER TABLE cms_video_info ADD COLUMN `industry` VARCHAR(255) COMMENT '行业';