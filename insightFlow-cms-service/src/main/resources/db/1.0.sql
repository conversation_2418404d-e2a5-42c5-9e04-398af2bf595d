# 数据拉取任务表
CREATE TABLE `cms_pull_tasks` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键 ID，自增',
  `start_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '任务开始时间',
  `end_time` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '任务结束时间，可为空',
  `status` varchar(200) NOT NULL DEFAULT 'pending' COMMENT '任务状态（pending, running, completed, failed）',
  `fail_message` mediumtext,
  `pull_params` text NOT NULL COMMENT '拉取参数，存储 JSON 或其他格式的参数',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '逻辑删除标志（0: 未删除, 1: 已删除）',
  `type` varchar(255) NOT NULL COMMENT '任务类型',
  PRIMARY KEY (`id`),
  UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


# 数据拉取原始数据表

CREATE TABLE `cms_pull_task_deduped_datas` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `task_id` int(11) NOT NULL,
  `es_id` varchar(255) NOT NULL,
  `type` int(11) NOT NULL COMMENT '类型 1:精选视频，2：圈层热点，3：视频解码',
  `kw_source` varchar(255) DEFAULT NULL COMMENT '来源',
  `source_type` int(11) NOT NULL DEFAULT '1' COMMENT '数据来源类型1 es，2 上传，3 链接',
  `user_id` int(11) DEFAULT NULL COMMENT '用户 ID',
  `tenant_id` int(11) DEFAULT NULL COMMENT '租户 ID',
  `kw_kb_industry` varchar(500) DEFAULT NULL COMMENT '行业分类',
  `kw_two_level_tribe_tag` varchar(500) DEFAULT NULL COMMENT '圈成二级标签',
  `text_content` text COMMENT '内容',
  `text_title` varchar(500) DEFAULT NULL COMMENT '标题',
  `kw_url` varchar(2000) DEFAULT NULL COMMENT '帖子链接',
  `date_published_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '发布时间',
  `bool_is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除',
  `long_repost_count` bigint(20) DEFAULT '0' COMMENT '转发数',
  `long_comment_count` bigint(20) DEFAULT '0' COMMENT '评论数',
  `long_interact_count` bigint(20) DEFAULT '0' COMMENT '互动数',
  `long_like_count` bigint(20) DEFAULT '0' COMMENT '点赞数',
  `text_nick_name` varchar(255) DEFAULT NULL COMMENT '用户昵称',
  `kw_profile_image_url` varchar(2000) DEFAULT NULL COMMENT '用户头像链接',
  `kw_user_url` varchar(2000) DEFAULT NULL COMMENT '用户主页链接',
  `long_video_duration` bigint(20) DEFAULT '0' COMMENT '视频时长',
  `kw_head_image` varchar(2000) DEFAULT NULL COMMENT '视频头图',
  `kw_video_url` varchar(2000) DEFAULT NULL COMMENT '视频链接',
  `kw_video_content` text COMMENT '视频语音识别',
  `kw_common_sentiment_plus` text COMMENT '通用情感 Plus 版',
  `kw_data_tag_plus` text COMMENT '数据标签 Plus 版',
  `long_view_count` bigint(20) DEFAULT '0' COMMENT '阅读数',
  `long_follower_count` bigint(20) DEFAULT '0' COMMENT '发帖用户粉丝数',
  `long_collect_count` bigint(20) DEFAULT '0' COMMENT '收藏数',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` int(11) DEFAULT '0' COMMENT '逻辑删除标志（0-未删除，1-已删除）',
  `rating` float DEFAULT NULL COMMENT '四有三好（创意分值）',
  `download_status` int(11) DEFAULT '0' COMMENT '下载状态0：待下载，1：下载中，2：成功，3，失败',
  `download_date` varchar(100) DEFAULT NULL COMMENT '下载日期',
  `db_unique_id` varchar(100) DEFAULT NULL COMMENT '视频分析任务Id',
  `analysis_status` int(11) DEFAULT '0' COMMENT '0:待分析；1：分析中，2：分析成功，3：分析失败',
  PRIMARY KEY (`id`),
  UNIQUE KEY `id` (`id`),
  UNIQUE KEY `type` (`type`,`es_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4;


# 数据拉取去重数据表
CREATE TABLE `cms_pull_task_raw_datas` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `task_id` int(11) NOT NULL,
  `es_id` varchar(255) NOT NULL COMMENT '唯一 ID（外部系统唯一标识）',
  `kw_kb_industry` varchar(500) DEFAULT NULL COMMENT '行业分类',
  `kw_two_level_tribe_tag` varchar(500) DEFAULT NULL COMMENT '圈成二级标签',
  `type` int(11) NOT NULL COMMENT '类型',
  `kw_source` varchar(255) DEFAULT NULL COMMENT '来源',
  `text_content` text COMMENT '内容',
  `text_title` varchar(500) DEFAULT NULL COMMENT '标题',
  `kw_url` varchar(2000) DEFAULT NULL COMMENT '帖子链接',
  `date_published_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '发布时间',
  `bool_is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除',
  `long_repost_count` bigint(20) DEFAULT '0' COMMENT '转发数',
  `long_comment_count` bigint(20) DEFAULT '0' COMMENT '评论数',
  `long_interact_count` bigint(20) DEFAULT '0' COMMENT '互动数',
  `long_like_count` bigint(20) DEFAULT '0' COMMENT '点赞数',
  `text_nick_name` varchar(255) DEFAULT NULL COMMENT '用户昵称',
  `kw_profile_image_url` varchar(2000) DEFAULT NULL COMMENT '用户头像链接',
  `kw_user_url` varchar(2000) DEFAULT NULL COMMENT '用户主页链接',
  `long_video_duration` bigint(20) DEFAULT '0' COMMENT '视频时长',
  `kw_head_image` varchar(2000) DEFAULT NULL COMMENT '视频头图',
  `kw_video_url` varchar(2000) DEFAULT NULL COMMENT '视频链接',
  `kw_video_content` text COMMENT '视频语音识别',
  `kw_common_sentiment_plus` text COMMENT '通用情感 Plus 版',
  `kw_data_tag_plus` text COMMENT '数据标签 Plus 版',
  `long_view_count` bigint(20) DEFAULT '0' COMMENT '阅读数',
  `long_follower_count` bigint(20) DEFAULT '0' COMMENT '发帖用户粉丝数',
  `long_collect_count` bigint(20) DEFAULT '0' COMMENT '收藏数',
  `raw_data` json DEFAULT NULL COMMENT '原始数据（存 JSON 格式）',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` int(11) DEFAULT '0' COMMENT '逻辑删除标志（0-未删除，1-已删除）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


# 安装扩展
CREATE EXTENSION IF NOT EXISTS vector;
CREATE EXTENSION IF NOT EXISTS hstore;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

# 向量存储表用户昵称
CREATE TABLE IF NOT EXISTS user_nike_name_vector_store (
    id uuid DEFAULT uuid_generate_v4() PRIMARY KEY,
	content text,
	metadata json,
	embedding vector(1536)
);
CREATE INDEX ON user_nike_name_vector_store USING HNSW (embedding vector_cosine_ops);

# 向量存储表内容
CREATE TABLE IF NOT EXISTS text_content_vector_store (
   id uuid DEFAULT uuid_generate_v4() PRIMARY KEY,
	content text,
	metadata json,
	embedding vector(1536)
);

CREATE INDEX ON text_content_vector_store USING HNSW (embedding vector_cosine_ops);


# 向量存储表视频内容识别
CREATE TABLE IF NOT EXISTS video_content_vector_store (
   id uuid DEFAULT uuid_generate_v4() PRIMARY KEY,
	content text,
	metadata json,
	embedding vector(1536)
);

CREATE INDEX ON video_content_vector_store USING HNSW (embedding vector_cosine_ops);

# 向量存储表综合表
CREATE TABLE IF NOT EXISTS combined_data_vector_store (
   id uuid DEFAULT uuid_generate_v4() PRIMARY KEY,
	content text,
	metadata json,
	embedding vector(1536)
);

CREATE INDEX ON combined_data_vector_store USING HNSW (embedding vector_cosine_ops);
