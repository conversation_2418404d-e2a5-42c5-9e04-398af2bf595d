CREATE TABLE cms_directory (
    `id` INT AUTO_INCREMENT COMMENT '主键ID',
    `tenant_id` INT NOT NULL COMMENT '租户ID',
    `user_id` INT NOT NULL COMMENT '创建用户ID',
    `name` VARCHAR(20) NOT NULL COMMENT '目录名称',
    `type` TINYINT NOT NULL COMMENT '目录类型：1-个人文件夹，2-租户文件夹',
    `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    PRIMARY KEY (id),
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_user_id (user_id),
    INDEX idx_type (type),
    INDEX idx_is_deleted (is_deleted)
) COMMENT = 'DAM素材目录表';


CREATE TABLE cms_asset (
    `id` INT AUTO_INCREMENT COMMENT '主键ID',
    `tenant_id` INT COMMENT '租户ID（租户素材）',
    `user_id` INT NOT NULL COMMENT '上传用户ID',
    `directory_id` INT NOT NULL COMMENT '所属目录ID',
    `name` VARCHAR(255) NOT NULL COMMENT '素材名称',
    `duration` INT COMMENT '视频时长（秒）',
    `aspect_ratio` VARCHAR(20) COMMENT '画面比率 16:9、9:16、1:1',
    `thumbnail_oss_id` VARCHAR(2000) COMMENT '缩略图URL',
    `ext`     VARCHAR(32) COMMENT '文件扩展名',
    `oss_id` VARCHAR(2000) COMMENT 'oss 存储 url',
    `storage_time` TIMESTAMP COMMENT '入库时间',
    `is_stored` TINYINT NOT NULL DEFAULT 0 COMMENT '是否入库，0-未入库，1-入库',
    `used_num` INT NOT NULL COMMENT '应用频次',
    `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    PRIMARY KEY (id),
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_user_id (user_id),
    INDEX idx_directory_id (directory_id),
    INDEX idx_is_deleted (is_deleted),
    INDEX idx_is_stored (is_stored)
) COMMENT = 'DAM素材表';

CREATE TABLE cms_public_tag (
    `id` INT AUTO_INCREMENT COMMENT '主键ID',
    `name` VARCHAR(40) NOT NULL COMMENT '标签名称',
    `description` VARCHAR(255) NOT NULL COMMENT '描述',
    `example`     VARCHAR(255) NOT NULL COMMENT '示例',
    `suggest_values` TEXT COMMENT '示例',
    `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    PRIMARY KEY (id),
    INDEX idx_name (name),
    INDEX idx_is_deleted (is_deleted)
) COMMENT = 'DAM公共标签表';

CREATE TABLE cms_tag (
    `id` INT AUTO_INCREMENT COMMENT '主键ID',
    `tenant_id` INT COMMENT '租户ID',
    `user_id`   INT COMMENT '创建者ID',
    `name` VARCHAR(40) NOT NULL COMMENT '标签名称',
    `type` TINYINT NOT NULL COMMENT '标签类型：1-公共标签, 2-自定义标签, 3-AI标签',
    `description` VARCHAR(255) NOT NULL COMMENT '描述',
    `example`     VARCHAR(255) NOT NULL COMMENT '示例',
    `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    PRIMARY KEY (id),
    INDEX idx_user_id (user_id),
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_name (name),
    INDEX idx_type (type),
    INDEX idx_is_deleted (is_deleted)
) COMMENT = 'DAM标签表';

CREATE TABLE cms_tag_value (
    `id` INT AUTO_INCREMENT COMMENT '主键ID',
    `tenant_id` INT NOT NULL COMMENT '租户ID',
    `tag_id`    INT NOT NULL COMMENT '标签ID',
    `asset_id`  INT NOT NULL COMMENT '素材 id',
    `value` VARCHAR(250) NOT NULL COMMENT '标签值',
    `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    PRIMARY KEY (id),
    INDEX idx_tag_id (tag_id),
    INDEX idx_asset_id (asset_id),
    INDEX idx_is_deleted (is_deleted)
) COMMENT = 'DAM标签值表';

CREATE TABLE cms_asset_upload_task (
    `id` INT AUTO_INCREMENT COMMENT 'id',
    `tenant_id` INT NOT NULL COMMENT '租户ID',
    `user_id` INT NOT NULL COMMENT '创建人ID',
    `type` TINYINT NOT NULL COMMENT '任务类型（1: 素材上传任务, 2: AI分镜镜头入库）',
    `task_arg` TEXT COMMENT '任务参数',
    `is_stored` TINYINT NOT NULL DEFAULT 0 COMMENT '是否入库，0-未入库，1-入库',
    `status` TINYINT NOT NULL COMMENT '状态：1-待处理，2-处理中，3-完成，4-失败',
    `error`  TEXT COMMENT '错误原因',
    `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '数据逻辑删除标记',
    PRIMARY KEY (id),
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_user_id (user_id),
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_is_deleted (is_deleted)
) COMMENT = '任务表';

CREATE TABLE cms_asset_upload_task_detail (
    `id` INT AUTO_INCREMENT COMMENT '主键ID',
    `task_id` INT NOT NULL COMMENT '任务 id',
    `asset_id` INT NOT NULL COMMENT '关联素材 id',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态：1-待处理，2-处理中，3-完成，4-失败',
    `result` TEXT COMMENT '素材打标结果',
    `error`  TEXT COMMENT '失败原因',
    `metrics` TEXT COMMENT '指标',
    `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',  -- 基本上等于完成时间/失败时间
    `is_deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',                 -- 理论上没有用
    PRIMARY KEY (id),
    INDEX idx_task_id (task_id),
    INDEX idx_asset_id (asset_id),
    INDEX idx_status (status),
    INDEX idx_is_deleted (is_deleted)
) COMMENT = 'DAM素材上传任务表-记录细节';

CREATE TABLE cms_recycle_bin (
    `id` INT AUTO_INCREMENT COMMENT '主键ID',
    `tenant_id` INT COMMENT '租户ID',
    `user_id` INT NOT NULL COMMENT '操作用户ID',
    `directory_type` TINYINT COMMENT '目录类型：1-个人文件夹，2-租户文件夹',
    `object_id` INT NOT NULL COMMENT '对象ID',
    `object_type` TINYINT NOT NULL COMMENT '对象类型：1-目录，2-素材',
    `recover_time` TIMESTAMP NULL COMMENT '恢复时间',
    `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `is_deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    PRIMARY KEY (id),
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_user_id (user_id),
    INDEX idx_directory_type (directory_type),
    INDEX idx_object_id (object_id),
    INDEX idx_object_type (object_type),
    INDEX idx_is_deleted (is_deleted)
) COMMENT = 'DAM回收站表';

CREATE TABLE cms_recycle_bin_directory_asset_mapping (
    `id` INT AUTO_INCREMENT COMMENT '主键ID',
    `recycle_bin_id` INT NOT NULL COMMENT '回收站ID',
    `asset_id` INT NOT NULL COMMENT '素材ID',
    `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `is_deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    PRIMARY KEY (id),
    INDEX idx_recycle_bin_id (recycle_bin_id),
    INDEX idx_asset_id (asset_id),
    INDEX idx_is_deleted (is_deleted)
) COMMENT = 'DAM回收站目录和素材映射表';

insert into cms_public_tag (name, description, example) values
('镜头描述', '对镜头画面展示内容的简要描述，控制在三十字左右', ''),
('品牌植入', '(可选)：是否出现品牌与品牌的展示效果', ''),
('出现演员', '(可选)：枚举项，可多选，选项为【男演员，女演员，宠物】', ''),
('演员动作', '演员在分镜中的行为描述，控制在二十字以内', ''),
('演员表情', '(可选)：演员在分镜中的情绪表达', ''),
('布景要求', '分镜所需的布景', ''),
('道具清单', '(可选)：分镜中使用的道具', ''),
('服装造型', '(可选)：分镜中演员的服装造型', ''),
('光影与色彩要求', '色调与色彩', ''),
('摄影器材', '拍摄分镜所需的摄影器材', ''),
('运镜方式', '单选，包括但不限于【固定，跟随移动，缓慢推进，缓慢推远，快速旋转，从左至右平移】', ''),
('镜头类型', '单选枚举项，选项为【特写，近景，中景，广角，全景】', ''),
('台词情绪', '(可选)：台词的情绪表达', ''),
('背景音乐/音效', '音乐的类型与构成元素', '');