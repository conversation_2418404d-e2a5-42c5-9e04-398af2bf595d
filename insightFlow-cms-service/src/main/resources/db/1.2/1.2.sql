CREATE TABLE `cms_task_share_report` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `task_id` int(11) NOT NULL COMMENT '任务id',
  `type` int(11) NOT NULL COMMENT '类型：1：视频合成任务',
  `authorize_code` varchar(255) DEFAULT NULL COMMENT '授权code',
  `authorize_time` int(11) DEFAULT NULL COMMENT '授权时长（单位分钟）',
  `content` text NOT NULL COMMENT '内容（json存储，前端定义）',
  `data` text COMMENT '数据（json存储，后端定义）',
  `user_id` int(11) DEFAULT NULL COMMENT '创建者id',
  `tenant_id` int(11) DEFAULT NULL COMMENT '租户id',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `expire_time` timestamp NULL DEFAULT NULL COMMENT '过期时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 0：未删 1：删除',
  PRIMARY KEY (`id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_authorize_code` (`authorize_code`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_expire_time` (`expire_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务分享报告表';

-- 用户查询历史记录表
CREATE TABLE `cms_search_history` (
  `id` int(11)  NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int(11) DEFAULT NULL COMMENT '用户ID',
  `tenant_id` int(11) DEFAULT NULL COMMENT '租户ID',
  `keyword` varchar(255) NOT NULL COMMENT '查询关键词',
  `search_type` varchar(50) DEFAULT NULL COMMENT '查询类型',
  `search_module` varchar(50) NOT NULL COMMENT '查询模块',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_keyword` (`keyword`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户查询历史记录表';


-- ALTER TABLE cms_task_detail MODIFY COLUMN data MEDIUMTEXT COMMENT '结果数据';
