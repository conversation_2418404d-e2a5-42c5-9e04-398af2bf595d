CREATE TABLE "public"."cms_asset_embeddings" (
    "id" int4 NOT NULL,
    "tenant_id" int4 NOT NULL,
    "user_id" int4 NOT NULL,
    "directory_id" int4 NOT NULL,
    "content" text COLLATE "pg_catalog"."default" NOT NULL,
    "embedding" "public"."vector" NOT NULL,
    "metadata" jsonb NOT NULL,
    "create_time" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "update_time" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "is_deleted" int2 NOT NULL DEFAULT 0,
    "aspect_ratio" varchar(16) COLLATE "pg_catalog"."default" NOT NULL DEFAULT '16:9'::character varying,
    CONSTRAINT "cms_asset_embeddings_pkey" PRIMARY KEY ("id")
);

ALTER TABLE "public"."cms_asset_embeddings"
OWNER TO "cms";

CREATE INDEX "idx_aspect_ratio" ON "public"."cms_asset_embeddings" USING btree (
    "aspect_ratio" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

CREATE INDEX "idx_directory_id" ON "public"."cms_asset_embeddings" USING btree (
    "directory_id" "pg_catalog"."int4_ops" ASC NULLS LAST
);

CREATE INDEX "idx_is_deleted" ON "public"."cms_asset_embeddings" USING btree (
    "is_deleted" "pg_catalog"."int2_ops" ASC NULLS LAST
);

CREATE INDEX "idx_tenant_id" ON "public"."cms_asset_embeddings" USING btree (
    "tenant_id" "pg_catalog"."int4_ops" ASC NULLS LAST
);

CREATE INDEX "idx_user_id" ON "public"."cms_asset_embeddings" USING btree (
    "user_id" "pg_catalog"."int4_ops" ASC NULLS LAST
);

COMMENT ON COLUMN "public"."cms_asset_embeddings"."id" IS '主键，公用素材id';

COMMENT ON COLUMN "public"."cms_asset_embeddings"."tenant_id" IS '租户ID';

COMMENT ON COLUMN "public"."cms_asset_embeddings"."user_id" IS '上传用户ID';

COMMENT ON COLUMN "public"."cms_asset_embeddings"."directory_id" IS '所属目录ID';

COMMENT ON COLUMN "public"."cms_asset_embeddings"."content" IS 'embedding内容，目前是镜头描述';

COMMENT ON COLUMN "public"."cms_asset_embeddings"."embedding" IS '素材的embedding向量';

COMMENT ON COLUMN "public"."cms_asset_embeddings"."metadata" IS '额外元数据，如标签信息等';

COMMENT ON COLUMN "public"."cms_asset_embeddings"."create_time" IS '创建时间';

COMMENT ON COLUMN "public"."cms_asset_embeddings"."update_time" IS '更新时间';

COMMENT ON COLUMN "public"."cms_asset_embeddings"."is_deleted" IS '逻辑删除：0-未删除，1-已删除';

COMMENT ON COLUMN "public"."cms_asset_embeddings"."aspect_ratio" IS '画面比列';


-- ----------------------------
-- Table structure for cms_storyboard_embeddings
-- ----------------------------

CREATE TABLE "public"."cms_storyboard_embeddings" (
                                                      "id" "pg_catalog"."int4" NOT NULL,
                                                      "task_id" "pg_catalog"."int4" NOT NULL,
                                                      "content" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
                                                      "embedding" "public"."vector" NOT NULL,
                                                      "tenant_id" "pg_catalog"."int4" NOT NULL,
                                                      "user_id" "pg_catalog"."int4" NOT NULL,
                                                      CONSTRAINT "cms_storyboard_desc_embeddings_pkey" PRIMARY KEY ("id")
);

ALTER TABLE "public"."cms_storyboard_embeddings"
    OWNER TO "cms";

CREATE INDEX "idx_task_id" ON "public"."cms_storyboard_embeddings" USING btree (
                                                                                "task_id" "pg_catalog"."int4_ops" ASC NULLS LAST
    );

COMMENT ON COLUMN "public"."cms_storyboard_embeddings"."id" IS '分镜id';

COMMENT ON COLUMN "public"."cms_storyboard_embeddings"."task_id" IS '脚本生成任务id';

COMMENT ON COLUMN "public"."cms_storyboard_embeddings"."content" IS '分镜镜头描述';

COMMENT ON COLUMN "public"."cms_storyboard_embeddings"."embedding" IS 'embedding向量';

COMMENT ON COLUMN "public"."cms_storyboard_embeddings"."tenant_id" IS '租户id';

COMMENT ON COLUMN "public"."cms_storyboard_embeddings"."user_id" IS '用户id';