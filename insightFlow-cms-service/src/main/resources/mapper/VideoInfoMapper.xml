<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.mlamp.insightflow.cms.mapper.VideoInfoMapper">

    <resultMap id="BaseResultMap" type="cn.mlamp.insightflow.cms.entity.CmsVideoInfo">
        <id property="id" column="id" jdbcType="INTEGER"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="esId" column="es_id" jdbcType="VARCHAR"/>
        <result property="fiveGoldId" column="five_gold_id" jdbcType="INTEGER"/>
        <result property="sourceFileId" column="source_file_id" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="rating" column="rating" jdbcType="INTEGER"/>
        <result property="userId" column="user_id" jdbcType="INTEGER"/>
        <result property="tenantId" column="tenant_id" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="isDeleted" column="is_deleted" jdbcType="TINYINT"/>
    </resultMap>

    <select id="pageVideoInfo" resultType="cn.mlamp.insightflow.cms.model.vo.VideoTaskListInfoVO">
        select vi.id as id,
        vi.es_id as esId,
        vi.status as status,
        vi.create_time as startTime,
        vi.update_time as endTime,
        vi.source_file_id as sourceId,
        di.id as taskId,
        di.doc_name as title,
        ptd.kw_head_image as headImageUrl,
        tud.tokens as points
        from cms_video_info vi
        left join cms_document_info di on di.id = vi.source_file_id
        left join cms_pull_task_deduped_datas ptd on ptd.es_id = vi.es_id
        left join cms_token_use_detail tud on tud.task_id = vi.id and tud.task_type = 1
        where vi.is_deleted = 0
        <if test="type != null and type != ''">
            AND vi.type = #{type}
        </if>
        <if test="tenantId != null and tenantId != ''">
            AND vi.tenant_id = #{tenantId}
        </if>
        <if test="userId != null and userId != ''">
            AND vi.user_id = #{userId}
        </if>
        order by vi.create_time desc
    </select>

</mapper>
