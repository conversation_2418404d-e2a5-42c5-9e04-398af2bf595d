<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.mlamp.insightflow.cms.mapper.UserMapper">

    <resultMap id="BaseResultMap" type="cn.mlamp.insightflow.cms.entity.CmsUser">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="pic" column="pic" jdbcType="VARCHAR"/>
            <result property="userName" column="user_name" jdbcType="VARCHAR"/>
            <result property="userId" column="user_id" jdbcType="INTEGER"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="isDeleted" column="is_deleted" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,pic,user_name,userId,
        create_time,update_time,
        is_deleted
    </sql>
</mapper>
