<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.mlamp.insightflow.cms.mapper.VideorResultMapper">

    <resultMap id="BaseResultMap" type="cn.mlamp.insightflow.cms.entity.CmsVideoResult">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="type" column="type" jdbcType="INTEGER"/>
            <result property="videoId" column="video_id" jdbcType="INTEGER"/>
            <result property="index" column="index" jdbcType="INTEGER"/>
            <result property="data" column="data" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="isDeleted" column="is_deleted" jdbcType="TINYINT"/>
    </resultMap>

</mapper>
