<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.mlamp.insightflow.cms.mapper.CmsProductRecordMapper">

    <resultMap id="BaseResultMap" type="cn.mlamp.insightflow.cms.entity.CmsProductRecord">
            <id property="id" column="id" />
            <result property="url" column="url" />
            <result property="title" column="title" />
            <result property="brand" column="brand" />
            <result property="productName" column="product_name" />
            <result property="sellingPoint" column="selling_point" />
            <result property="userId" column="user_id" />
            <result property="tenantId" column="tenant_id" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
            <result property="isDeleted" column="is_deleted" />
    </resultMap>

    <sql id="Base_Column_List">
        id,url,title,brand,product_name,selling_point,
        user_id,tenant_id,create_time,update_time,is_deleted
    </sql>
</mapper>
