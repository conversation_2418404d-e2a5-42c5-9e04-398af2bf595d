<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.mlamp.insightflow.cms.mapper.dam.DamPublicTagMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="cn.mlamp.insightflow.cms.entity.dam.DamPublicTag">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="description" property="description"/>
        <result column="example" property="example"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <!-- 基础查询SQL片段 -->
    <sql id="Base_Column_List">
        id, name, description, example, create_time, update_time, is_deleted
    </sql>

</mapper>
