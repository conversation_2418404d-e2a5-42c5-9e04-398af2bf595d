<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.mlamp.insightflow.cms.mapper.dam.DamRecycleBinMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="cn.mlamp.insightflow.cms.entity.dam.DamRecycleBin">
        <id column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="user_id" property="userId"/>
        <result column="object_id" property="objectId"/>
        <result column="object_type" property="objectType"/>
        <result column="recover_time" property="recoverTime"/>
        <result column="create_time" property="createTime"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <!-- 结果映射（带对象详情） -->
    <resultMap id="RecycleBinDetailMap" type="cn.mlamp.insightflow.cms.entity.dam.DamRecycleBin" extends="BaseResultMap">
        <result column="object_name" property="objectName"/>
        <result column="object_details" property="objectDetails"/>
    </resultMap>

    <!-- 获取回收站列表，包括对象详情 -->
    <select id="selectRecycleBinListWithDetails" resultMap="RecycleBinDetailMap">
        SELECT rb.*, 
               CASE 
                   WHEN rb.object_type = 1 THEN d.name
                   WHEN rb.object_type = 2 THEN a.name
               END as object_name,
               CASE 
                   WHEN rb.object_type = 1 THEN JSON_OBJECT('type', d.type)
                   WHEN rb.object_type = 2 THEN JSON_OBJECT('directoryId', a.directory_id, 'thumbnail', a.thumbnail)
               END as object_details
        FROM cms_recycle_bin rb
        LEFT JOIN cms_directory d ON rb.object_type = 1 AND rb.object_id = d.id
        LEFT JOIN cms_asset a ON rb.object_type = 2 AND rb.object_id = a.id
        WHERE rb.is_deleted = 0
        <if test="type != null">
            AND object_type = #{type}
        </if>
        <if test="tenantId != null">
            AND rb.tenant_id = #{tenantId}
        </if>
        <if test="userId != null">
            AND rb.user_id = #{userId}
        </if>
        ORDER BY rb.create_time DESC
    </select>

    <!-- 更新恢复时间 -->
    <update id="updateRecoverTime">
        UPDATE cms_recycle_bin
        SET recover_time = NOW(),
            is_deleted = 1
        WHERE id = #{id}
    </update>

    <!-- 批量删除回收站记录（物理删除） -->
    <delete id="batchDeletePhysically">
        DELETE FROM cms_recycle_bin
        WHERE id IN
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 清空回收站（物理删除全部） -->
    <delete id="emptyRecycleBin">
        DELETE FROM cms_recycle_bin
        WHERE is_deleted = 0
        <if test="tenantId != null">
            AND tenant_id = #{tenantId}
        </if>
        <if test="userId != null">
            AND user_id = #{userId}
        </if>
    </delete>

</mapper>
