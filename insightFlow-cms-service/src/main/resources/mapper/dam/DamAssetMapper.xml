<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.mlamp.insightflow.cms.mapper.dam.DamAssetMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="cn.mlamp.insightflow.cms.entity.dam.DamAsset">
        <id column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="user_id" property="userId"/>
        <result column="directory_id" property="directoryId"/>
        <result column="name" property="name"/>
        <result column="duration" property="duration"/>
        <result column="thumbnail" property="thumbnail"/>
        <result column="aspect_ratio" property="aspectRatio"/>
        <result column="oss_url" property="ossUrl"/>
        <result column="storage_time" property="storageTime"/>
        <result column="is_stored" property="isStored"/>
        <result column="used_num" property="usedNum"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <!-- 获取素材列表，包含标签信息 -->
    <select id="selectAssetList" resultMap="BaseResultMap">
        SELECT a.*
        FROM cms_asset a
        WHERE a.is_deleted = 0
        <if test="directoryId != null">
            AND a.directory_id = #{directoryId}
        </if>
        <if test="keyword != null and keyword != ''">
            AND a.name LIKE CONCAT('%', #{keyword}, '%')
        </if>
        <if test="tenantId != null">
            AND a.tenant_id = #{tenantId}
        </if>
        <if test="userId != null">
            AND a.user_id = #{userId}
        </if>
        ORDER BY a.create_time DESC
    </select>

</mapper>
