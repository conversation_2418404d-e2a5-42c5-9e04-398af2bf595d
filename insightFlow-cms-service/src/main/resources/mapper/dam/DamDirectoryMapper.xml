<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.mlamp.insightflow.cms.mapper.dam.DamDirectoryMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="cn.mlamp.insightflow.cms.entity.dam.DamDirectory">
        <id column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="user_id" property="userId"/>
        <result column="name" property="name"/>
        <result column="type" property="type"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <!-- 基础查询SQL片段 -->
    <sql id="Base_Column_List">
        id, tenant_id, user_id, name, type, create_time, update_time, is_deleted
    </sql>

    <!-- 查询个人可查看的目录对象列表：属于user当前租户下的个人文件夹，或者属于tenantId且type为租户的文件夹 -->
    <select id="getViewableDirectory" resultMap="BaseResultMap">
        SELECT *
        FROM cms_directory
        WHERE is_deleted = 0
          AND (
            (user_id = #{userId} AND type = '1' and tenant_id = #{tenantId})
            OR (tenant_id = #{tenantId} AND type = '2')
          )
    </select>

</mapper>
