<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.mlamp.insightflow.cms.mapper.dam.DamAssetUploadTaskDetailMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="cn.mlamp.insightflow.cms.entity.dam.DamAssetUploadTaskDetail">
        <id column="id" property="id"/>
        <result column="task_id" property="taskId"/>
        <result column="asset_id" property="assetId"/>
        <result column="status" property="status"/>
        <result column="result" property="result"/>
        <result column="error" property="error"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <!-- 根据任务ID获取任务详情 -->
    <select id="selectByTaskId" resultMap="BaseResultMap">
        SELECT *
        FROM cms_asset_upload_task_detail
        WHERE task_id = #{taskId}
        AND is_deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 批量插入任务详情 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO cms_asset_upload_task_detail (
            task_id, asset_id, status, create_time, update_time, is_deleted
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.taskId},
                #{item.assetId},
                #{item.status},
                NOW(),
                NOW(),
                0
            )
        </foreach>
    </insert>

    <!-- 更新任务状态 -->
    <update id="updateStatus">
        UPDATE cms_asset_upload_task_detail
        SET status = #{status},
            <if test="result != null">
                result = #{result},
            </if>
            <if test="error != null">
                error = #{error},
            </if>
            update_time = NOW()
        WHERE id = #{id}
    </update>

</mapper>
