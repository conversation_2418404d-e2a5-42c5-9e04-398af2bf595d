<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.mlamp.insightflow.cms.mapper.dam.DamTagMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="cn.mlamp.insightflow.cms.entity.dam.DamTag">
        <id column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="user_id" property="userId"/>
        <result column="name" property="name"/>
        <result column="type" property="type"/>
        <result column="description" property="description"/>
        <result column="example" property="example"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <!-- 结果映射（包含使用次数） -->
    <resultMap id="TagWithUsedNumMap" type="cn.mlamp.insightflow.cms.entity.dam.DamTag" extends="BaseResultMap">
        <result column="used_num" property="usedNum"/>
    </resultMap>

    <!-- 根据标签ID获取已使用次数 -->
    <select id="countUsageByTagId" resultType="int">
        SELECT COUNT(1)
        FROM cms_tag_value
        WHERE tag_id = #{tagId}
        AND is_deleted = 0
    </select>

    <!-- 查询标签列表，包含使用次数 -->
    <select id="selectTagListWithUsedNum" resultMap="TagWithUsedNumMap">
        SELECT t.*, COUNT(tv.id) as used_num
        FROM cms_tag t
        LEFT JOIN cms_tag_value tv ON t.id = tv.tag_id AND tv.is_deleted = 0
        WHERE t.is_deleted = 0
        <if test="type != null">
            AND t.type = #{type}
        </if>
        <if test="tenantId != null">
            AND t.tenant_id = #{tenantId}
        </if>
        <if test="userId != null">
            AND t.user_id = #{userId}
        </if>
        GROUP BY t.id
        ORDER BY t.create_time DESC
    </select>

</mapper>
