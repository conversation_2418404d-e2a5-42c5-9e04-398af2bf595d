<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.mlamp.insightflow.cms.mapper.dam.DamTagValueMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="cn.mlamp.insightflow.cms.entity.dam.DamTagValue">
        <id column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="tag_id" property="tagId"/>
        <result column="asset_id" property="assetId"/>
        <result column="value" property="value"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <!-- 根据素材ID查询标签值 -->
    <select id="selectTagValuesByAssetId" resultMap="BaseResultMap">
        SELECT tv.*
        FROM cms_tag_value tv
        WHERE tv.asset_id = #{assetId}
        AND tv.is_deleted = 0
        ORDER BY tv.create_time DESC
    </select>

    <!-- 根据标签ID获取已使用的标签值列表 -->
    <select id="selectValuesByTagId" resultType="java.lang.String">
        SELECT DISTINCT tv.value
        FROM cms_tag_value tv
        WHERE tv.tag_id = #{tagId}
        AND tv.is_deleted = 0
    </select>

    <!-- 批量保存标签值 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO cms_tag_value (
            tenant_id, tag_id, asset_id, value, create_time, update_time, is_deleted
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.tenantId},
                #{item.tagId},
                #{item.assetId},
                #{item.value},
                NOW(),
                NOW(),
                0
            )
        </foreach>
    </insert>

</mapper>
