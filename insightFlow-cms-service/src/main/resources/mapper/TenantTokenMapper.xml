<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.mlamp.insightflow.cms.mapper.TenantTokenMapper">

    <resultMap id="BaseResultMap" type="cn.mlamp.insightflow.cms.entity.TenantToken">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="tenantId" column="tenant_id"/>
            <result property="balance" column="balance"/>
            <result property="accumulatedRecharge" column="accumulated_recharge"/>
            <result property="accumulatedExpenses" column="accumulated_expenses"/>
            <result property="statisticalTime" column="statistical_time"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="isDeleted" column="is_deleted" jdbcType="TINYINT"/>
    </resultMap>


</mapper>
