<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.mlamp.insightflow.cms.mapper.VideoFiveGoldMapper">

    <resultMap id="BaseResultMap" type="cn.mlamp.insightflow.cms.entity.CmsVideoFiveGold">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="kwKbIndustry" column="kw_kb_industry" jdbcType="VARCHAR"/>
            <result property="coverVideoId" column="cover_video_id" jdbcType="INTEGER"/>
            <result property="timeSlot" column="time_slot" jdbcType="INTEGER"/>
            <result property="videoNum" column="video_num" jdbcType="INTEGER"/>
            <result property="interactCount" column="interact_count" jdbcType="INTEGER"/>
            <result property="likeCount" column="like_count" jdbcType="INTEGER"/>
            <result property="commentCount" column="comment_count" jdbcType="INTEGER"/>
            <result property="originalityNum" column="originality_num" jdbcType="VARCHAR"/>
            <result property="visualRoutine" column="visual_routine" jdbcType="VARCHAR"/>
            <result property="dialogueRoutine" column="dialogue_routine" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
            <result property="resultFileId" column="result_file_id" jdbcType="INTEGER"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="isDeleted" column="is_deleted" jdbcType="TINYINT"/>
    </resultMap>

</mapper>
