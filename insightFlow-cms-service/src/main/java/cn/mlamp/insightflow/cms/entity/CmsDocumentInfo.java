package cn.mlamp.insightflow.cms.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 文件表;
 *
 * <AUTHOR> husper
 * @date : 2025-3-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "cms_document_info", autoResultMap = true)
public class CmsDocumentInfo extends BaseEntity {

    /**
     * 文档名称（含后缀）
     */
    private String docName;
    /**
     * 1 .mp4 ；2 .avi
     */
    private String docType;
    /**
     * 来源类型 from_local,from_url
     */
    private String sourceType;
    /**
     * 来源url
     */
    private String link;
    /**
     * 虚拟esId给前端查询
     */
    private String esId;
    /**
     * 文档在minio的id
     */
    private String objId;
    /**
     * 文档存储桶名称
     */
    private String bucketName;
    /**
     * md5
     */
    private String md5;
    /**
     * 文档大小
     */
    private Long size;
    /**
     * 文档额外信息（json）
     */
    private String extra;
    /**
     * 状态 0：处理中（url爬取），1：等待中（等待解析），2：分析中，3：完成，4：失败
     */
    private String status;
    /**
     * 创建人id
     */
    private Integer userId;
    /**
     * 租户Id
     */
    private Integer tenantId;

}
