package cn.mlamp.insightflow.cms.config.properties;

import cn.hutool.core.util.StrUtil;
import cn.mlamp.insightflow.cms.config.S3FlowConfig;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties("dam.oss")
public class DamOSSProperties {

    private String basePath = "insight_platform/cms";

    @Autowired
    private S3FlowConfig s3FlowConfig;

    public String getAssetBasePath(Integer tenantId, Integer assetId) {
        // basePath移除最后的/
        basePath = basePath.replaceAll("/$", "");
        if (StrUtil.isBlank(basePath)) {
            return String.format("%s/%d/dam/assets/%d", s3FlowConfig.getProfile(), tenantId, assetId);
        }
        return String.format("%s/%s/%d/dam/assets/%d", s3FlowConfig.getProfile(), basePath, tenantId, assetId);
    }

}
