package cn.mlamp.insightflow.cms.model.dto;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.mlamp.insightflow.cms.util.DeepanaSignUtil;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;
import java.util.UUID;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DeepanaTiktokRequest {
    @JsonProperty("resource_id")
    public String resourceId;
    @JsonProperty("video_url")
    public String videoUrl;
    @JsonProperty("upload_date")
    public String uploadDate;

    //加密操作
    private String requestId;
    private String appId;
    private String time;
    private String sign;

    public DeepanaTiktokRequest(String videoUrl) {
        this.resourceId = UUID.randomUUID().toString().replace("-", "");
        this.videoUrl = videoUrl;

        // 加密处理
        this.requestId = DeepanaSignUtil.getRequestId();
        this.appId = DeepanaSignUtil.getAppId();
        this.time = DeepanaSignUtil.getTime();
        this.sign = DeepanaSignUtil.sign(requestId, time);
    }

    public DeepanaTiktokRequest(String videoUrl, String esId, Date documentCreatedAt) {
        this.resourceId = esId;
        this.videoUrl = videoUrl;
        // 加密处理
        this.requestId = DeepanaSignUtil.getRequestId();
        this.appId = DeepanaSignUtil.getAppId();
        this.time = DeepanaSignUtil.getTime();
        this.sign = DeepanaSignUtil.sign(requestId, time);
        this.uploadDate = DateUtil.format(documentCreatedAt, DatePattern.NORM_DATE_PATTERN);
    }
}
