package cn.mlamp.insightflow.cms.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class ConsumptionSummaryVO implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "累计充值", required = false)
    private Integer accumulatedRecharge;

    @Schema(description = "当前余额", required = false)
    private Integer balance;

    @Schema(description = "累计消耗", required = false)
    private Integer accumulatedExpenses;
}
