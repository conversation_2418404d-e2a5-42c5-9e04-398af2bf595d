package cn.mlamp.insightflow.cms.model.vo;

import cn.hutool.core.collection.CollectionUtil;
import cn.mlamp.insightflow.cms.model.dto.dam.DamTagValueDTO;
import cn.mlamp.insightflow.cms.model.vo.dam.DamAssetVO;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 分镜信息
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class VideoStoryBoardVO {

    @Schema(description = "分镜id", required = false)
    private Integer id;

    @Schema(description = "分镜图片ossId", required = false)
    private String objOssId;

    @Schema(description = "分镜图片url", required = false)
    private String url;

    @Schema(description = "镜头描述", required = false)
    private String sceneDescription;

    @Schema(description = "品牌植入", required = false)
    private String brandIntegration;

    @Schema(description = "镜头参考", required = false)
    private String referenceImage;

    @Schema(description = "镜头类型", required = false)
    private String cameraType;

    @Schema(description = "运镜方式", required = false)
    private String cameraView;

    @Schema(description = "时长", required = false)
    private Integer sceneLength;

    @Schema(description = "道具清单", required = false)
    private String props;

    @Schema(description = "布景要求", required = false)
    private String setRequirements;

    @Schema(description = "背景音乐/音效", required = false)
    private String bgm;

    @Schema(description = "光影与色彩要求", required = false)
    private String lightColor;

    @Schema(description = "台词", required = false)
    private String dialogue;              // 台词

    @Schema(description = "台词情绪", required = false)
    private String dialogueEmotion;       // 台词情绪

    @Schema(description = "内容策略", required = false)
    private String contentStrategy;

    @Schema(description = "出现演员", required = false)
    private String actors;                // 出现演员

    @Schema(description = "演员动作", required = false)
    private String actorActions;          // 演员动作

    @Schema(description = "演员表情", required = false)
    private String actorExpressions;      // 演员表情

    @Schema(description = "服装造型建议", required = false)
    private String costumeSuggestions;    // 服装造型建议

    @Schema(description = "摄影器材", required = false)
    private String equipment;             // 摄影器材

    @Schema(description = "服化道", required = false)
    private String costumes;              // 服化道

    @Schema(description = "推荐视频信息", required = false)
    private List<RecommendVideoInfoVO> recommendVideos; // 推荐视频信息

    @Schema(description = "信息map", required = false)
    private Map<String, String> detailMap; // 信息map字段


    public static Map<String, String> getInputArgMap() {
        // 装载参数信息，英语key，和对应的中文value
        Map<String, String> inputArgMap = new HashMap<>();
        inputArgMap.put("sceneDescription", "镜头描述");
        inputArgMap.put("brandIntegration", "品牌植入");
        inputArgMap.put("referenceImage", "分镜画面");
        inputArgMap.put("cameraType", "镜头类型");
        inputArgMap.put("cameraView", "运镜方式");
        inputArgMap.put("sceneLength", "时长");
        inputArgMap.put("props", "道具清单");
        inputArgMap.put("setRequirements", "布景要求");
        inputArgMap.put("bgm", "背景音乐/音效");
        inputArgMap.put("lightColor", "光影与色彩要求");
        inputArgMap.put("dialogue", "台词");
        inputArgMap.put("dialogueEmotion", "台词情绪");
        inputArgMap.put("contentStrategy", "视频内容策略");
        inputArgMap.put("actors", "出现演员");
        inputArgMap.put("actorActions", "演员动作");
        inputArgMap.put("actorExpressions", "演员表情");
        inputArgMap.put("costumeSuggestions", "服装造型建议");
        inputArgMap.put("equipment", "摄影器材");
        inputArgMap.put("costumes", "服化道");
        return inputArgMap;
    }

    public static Map<String, String> loadDetailMap(VideoStoryBoardVO vo) {
        // 装载信息，中文key，信息value
        Map<String, String> detailMap = new HashMap<>();
        if (vo.sceneDescription != null) {
            detailMap.put("镜头描述", vo.sceneDescription);
        }
        if (vo.brandIntegration != null) {
            detailMap.put("品牌植入", vo.brandIntegration);
        }
        if (vo.referenceImage != null) {
            detailMap.put("分镜画面", vo.referenceImage);
        }
        if (vo.cameraType != null) {
            detailMap.put("镜头类型", vo.cameraType);
        }
        if (vo.cameraView != null) {
            detailMap.put("运镜方式", vo.cameraView);
        }
        if (vo.sceneLength != null) {
            detailMap.put("时长", String.valueOf(vo.sceneLength));
        }
        if (vo.props != null) {
            detailMap.put("道具清单", vo.props);
        }
        if (vo.setRequirements != null) {
            detailMap.put("布景要求", vo.setRequirements);
        }
        if (vo.bgm != null) {
            detailMap.put("背景音乐/音效", vo.bgm);
        }
        if (vo.lightColor != null) {
            detailMap.put("光影与色彩要求", vo.lightColor);
        }
        if (vo.dialogue != null) {
            detailMap.put("台词", vo.dialogue);
        }
        if (vo.dialogueEmotion != null) {
            detailMap.put("台词情绪", vo.dialogueEmotion);
        }
        if (vo.contentStrategy != null) {
            detailMap.put("视频内容策略", vo.contentStrategy);
        }
        if (vo.actors != null) {
            detailMap.put("出现演员", vo.actors);
        }
        if (vo.actorActions != null) {
            detailMap.put("演员动作", vo.actorActions);
        }
        if (vo.actorExpressions != null) {
            detailMap.put("演员表情", vo.actorExpressions);
        }
        if (vo.costumeSuggestions != null) {
            detailMap.put("服装造型建议", vo.costumeSuggestions);
        }
        if (vo.equipment != null) {
            detailMap.put("摄影器材", vo.equipment);
        }
        if (vo.costumes != null) {
            detailMap.put("服化道", vo.costumes);
        }
        return detailMap;
    }

    @Data
    public static class RecommendVideoInfoVO {

        @Schema(description = "视频素材id", required = false)
        private Integer videoId;

        @Schema(description = "视频素材名称", required = false)
        private String name;

        @Schema(description = "标签列表")
        private List<DamTagValueDTO> tags = new ArrayList<>();

        @Schema(description = "视频素材ossId", required = false)
        private String objOssId;

        @Schema(description = "视频素材头图ossId", required = false)
        private String headObjOssId;

        @Schema(description = "视频素材url", required = false)
        private String videoUrl;

        @Schema(description = "视频素材头图", required = false)
        private String headUrl;
    }

    public static List<RecommendVideoInfoVO> mapperRecommendVideoByAssetVO(List<DamAssetVO> assets) {
        if (assets == null) return new ArrayList<>();
        return assets.stream().map(asset -> {
                    RecommendVideoInfoVO vo = new RecommendVideoInfoVO();
                    vo.setVideoId(asset.getId());
                    vo.setName(asset.getName());
                    if (CollectionUtil.isNotEmpty(asset.getTags())) {
                        vo.setTags(asset.getTags());
                    }
                    vo.setObjOssId(asset.getOssId());
                    vo.setHeadObjOssId(asset.getThumbnailOssId());
                    vo.setVideoUrl(asset.getOssUrl());
                    vo.setHeadUrl(asset.getThumbnailUrl());
                    return vo;
                })
                .collect(Collectors.toList());
    }
}
