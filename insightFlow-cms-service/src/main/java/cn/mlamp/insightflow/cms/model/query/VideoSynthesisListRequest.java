package cn.mlamp.insightflow.cms.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 视频合成任务列表查询请求
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
public class VideoSynthesisListRequest extends PageRequest {

    @Schema(description = "任务状态", required = false)
    private Integer taskStatus;

    @Schema(description = "任务名称关键词", required = false)
    private String keyword;
}
