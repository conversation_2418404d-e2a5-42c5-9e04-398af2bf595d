package cn.mlamp.insightflow.cms.service.impl;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import cn.mlamp.insightflow.cms.entity.QianchuanMaterialVideo;
import cn.mlamp.insightflow.cms.mapper.QianchuanMaterialVideoMapper;
import cn.mlamp.insightflow.cms.model.dto.QianchuanMaterialVideoDTO;
import cn.mlamp.insightflow.cms.model.dto.VideoExistCheckRequestDTO;
import cn.mlamp.insightflow.cms.service.QianchuanMaterialVideoService;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class QianchuanMaterialVideoServiceImpl extends ServiceImpl<QianchuanMaterialVideoMapper, QianchuanMaterialVideo>
        implements QianchuanMaterialVideoService {

    private final QianchuanMaterialVideoMapper videoMapper;

    @Override
    public Map<String, Object> batchInsertOrUpdate(List<QianchuanMaterialVideoDTO> videoList) {
        List<Map<String, Object>> failedList = new ArrayList<>();

        // 提取所有的视频 ID
        List<String> videoIds = videoList.stream().map(QianchuanMaterialVideoDTO::getVideoId)
                .collect(Collectors.toList());

        // 使用 LambdaQueryWrapper 执行批量查询现有数据
        LambdaQueryWrapper<QianchuanMaterialVideo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(QianchuanMaterialVideo::getVideoId, videoIds); // 批量查询视频 ID

        List<QianchuanMaterialVideo> existingVideos = videoMapper.selectList(queryWrapper);

        // 创建映射关系，视频 ID 到已存在的视频实体
        Map<String, QianchuanMaterialVideo> existingMap = existingVideos.stream()
                .collect(Collectors.toMap(QianchuanMaterialVideo::getVideoId, video -> video));

        // 插入和更新操作
        for (QianchuanMaterialVideoDTO dto : videoList) {
            QianchuanMaterialVideo videoEntity = toEntity(dto); // 将 DTO 转换为实体类

            // 设置 consume_range_weight 字段的值
            setConsumeRangeWeight(videoEntity);

            try {
                if (existingMap.containsKey(dto.getVideoId())) {
                    // 如果视频已存在，执行更新
                    LambdaUpdateWrapper<QianchuanMaterialVideo> updateWrapper = new LambdaUpdateWrapper<>();
                    updateWrapper.eq(QianchuanMaterialVideo::getVideoId, dto.getVideoId()); // 条件
                    videoMapper.update(videoEntity, updateWrapper); // 执行更新
                } else {
                    // 如果视频不存在，执行插入
                    videoMapper.insert(videoEntity);
                }
            } catch (Exception e) {
                // 如果操作失败，记录失败的原因
                Map<String, Object> fail = new HashMap<>();
                fail.put("video_id", dto.getVideoId());
                fail.put("reason", e.getMessage());
                failedList.add(fail);
            }
        }

        Map<String, Object> result = new HashMap<>();
        result.put("failed", failedList);
        return result;
    }

    private QianchuanMaterialVideo toEntity(QianchuanMaterialVideoDTO dto) {
        QianchuanMaterialVideo entity = new QianchuanMaterialVideo();
        BeanUtils.copyProperties(dto, entity);
        return entity;
    }

    /**
     * 设置消耗区间权重 从 consume_range 字段中提取第一个数字作为权重值 例如："5w-10w" -> 5
     *
     * @param video 视频实体
     */
    private void setConsumeRangeWeight(QianchuanMaterialVideo video) {
        if (video == null || video.getConsumeRange() == null || video.getConsumeRange().isEmpty()) {
            return;
        }

        String consumeRange = video.getConsumeRange();
        try {
            // 提取第一个数字部分（例如从 "5w-10w" 中提取 "5"）
            String firstPart = consumeRange.split("-")[0];
            // 移除 "w" 字符
            String numberStr = firstPart.replace("w", "");
            // 转换为整数
            Integer weight = Integer.parseInt(numberStr);
            // 设置权重值
            video.setConsumeRangeWeight(weight);
        } catch (Exception e) {
            // 如果解析失败，不设置权重值
        }
    }

    @Override
    public List<String> findNonExistingVideoIds(List<String> videoIds, LocalDateTime startTime, LocalDateTime endTime) {
        if (videoIds == null || videoIds.isEmpty()) {
            return new ArrayList<>();
        }

        // 查询数据库中所有满足时间范围的视频ID
        LambdaQueryWrapper<QianchuanMaterialVideo> queryWrapper = new LambdaQueryWrapper<>();

        // 添加时间范围筛选
        if (startTime != null && endTime != null) {
            // 如果开始时间和结束时间都不为空，使用 between 方法
            queryWrapper.between(QianchuanMaterialVideo::getCreateTime, startTime, endTime);
        } else if (startTime != null) {
            // 如果只有开始时间
            queryWrapper.ge(QianchuanMaterialVideo::getCreateTime, startTime);
        } else if (endTime != null) {
            // 如果只有结束时间
            queryWrapper.le(QianchuanMaterialVideo::getCreateTime, endTime);
        }

        queryWrapper.select(QianchuanMaterialVideo::getVideoId); // 只选择 videoId 字段

        // 执行查询
        List<QianchuanMaterialVideo> allVideos = videoMapper.selectList(queryWrapper);

        // 提取数据库中所有满足条件的视频ID
        Set<String> allVideoIds = new HashSet<>();
        if (allVideos != null && !allVideos.isEmpty()) {
            allVideoIds = allVideos.stream().map(QianchuanMaterialVideo::getVideoId).collect(Collectors.toSet());
        }

        // 创建传入的视频ID集合
        Set<String> videoIdSet = new HashSet<>(videoIds);

        // 使用集合差集操作找出不存在的视频ID
        videoIdSet.removeAll(allVideoIds);

        // 返回不存在的视频ID列表
        return new ArrayList<>(videoIdSet);
    }

    @Override
    public boolean checkVideoExists(VideoExistCheckRequestDTO request) {
        if (request == null) {
            return false;
        }

        LambdaQueryWrapper<QianchuanMaterialVideo> queryWrapper = new LambdaQueryWrapper<>();

        // 如果提供了视频ID，优先使用视频ID查询
        if (StringUtils.hasText(request.getVideoId())) {
            queryWrapper.eq(QianchuanMaterialVideo::getVideoId, request.getVideoId());
        }
        // 如果提供了标题和时长，使用标题和时长组合查询
        else if (StringUtils.hasText(request.getTitle()) && request.getDuration() != null) {
            queryWrapper.eq(QianchuanMaterialVideo::getTitle, request.getTitle())
                    .eq(QianchuanMaterialVideo::getDuration, request.getDuration());
        } else {
            // 如果既没有提供视频ID，也没有提供标题和时长的组合，则无法查询
            return false;
        }

        // 添加未删除条件
        queryWrapper.eq(QianchuanMaterialVideo::getIsDeleted, 0);

        // 只查询一条记录，提高效率
        queryWrapper.last("LIMIT 1");

        // 执行查询并返回结果
        return videoMapper.selectCount(queryWrapper) > 0;
    }
}
