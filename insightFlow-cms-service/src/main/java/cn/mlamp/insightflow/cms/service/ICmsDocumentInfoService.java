package cn.mlamp.insightflow.cms.service;

import cn.mlamp.insightflow.cms.entity.CmsDocumentInfo;
import cn.mlamp.insightflow.cms.enums.DocumentStatusEnum;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

public interface ICmsDocumentInfoService extends IService<CmsDocumentInfo> {
    int updateDocumentInfoStatus(DocumentStatusEnum statusEnum, Integer taskId, Integer userId, Integer tenantId);

    void updateDocumentInfoStatus(DocumentStatusEnum statusEnum, Integer taskId);

    List<CmsDocumentInfo> listByUrlStatusIn(List<DocumentStatusEnum> status);

    void updateStatusByEsId(String esId, DocumentStatusEnum statusEnum);
}
