package cn.mlamp.insightflow.cms.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * Token充值明细表;
 * <AUTHOR> husper
 * @date : 2025-3-19
 */
@Data
@TableName("cms_token_recharge_detail")
public class TokenRechargeDetail extends BaseEntity {
    /** id */
    @TableId(type = IdType.AUTO)
    private Integer id ;
    /** 租户名称 */
    private String tenantName ;
    /** 租户Id */
    private Integer tenantId ;
    /** 充值用户Id */
    private Integer userId ;
    /** 充值token数 */
    private Integer rechargeTokens ;
    /** 充值后token余额 */
    private Integer balanceTokens ;
    /** 充值时间 */
    private Date rechargeTime ;


}