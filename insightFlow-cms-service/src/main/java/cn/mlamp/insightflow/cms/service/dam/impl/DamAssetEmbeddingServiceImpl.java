package cn.mlamp.insightflow.cms.service.dam.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.mlamp.insightflow.cms.entity.dam.DamAsset;
import cn.mlamp.insightflow.cms.entity.dam.DamAssetEmbedding;
import cn.mlamp.insightflow.cms.entity.dam.DamTag;
import cn.mlamp.insightflow.cms.entity.dam.DamTagValue;
import cn.mlamp.insightflow.cms.service.dam.IDamAssetEmbeddingService;
import cn.mlamp.insightflow.cms.service.dam.IDamTagService;
import cn.mlamp.insightflow.cms.service.dam.IDamTagValueService;
import cn.mlamp.insightflow.cms.vector.DamAssetEmbeddingMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class DamAssetEmbeddingServiceImpl extends ServiceImpl<DamAssetEmbeddingMapper, DamAssetEmbedding>
        implements IDamAssetEmbeddingService {

    public static final String SHOT_DESCRIPTION_TAG_NAME = "镜头描述";

    @Autowired
    private EmbeddingModel embeddingModel;

    @Autowired
    private IDamTagService tagService;

    @Autowired
    private IDamTagValueService tagValueService;

    @Override
    public void saveByAssetAndEmbeddingContent(DamAsset asset, String content, Map<String, Object> metadata) {
        var embeddingResult = embeddingModel.embed(content);
        var embedding = this.mapperByAssert(asset);
        embedding.setContent(content);
        Float[] embeddingArr = toFloatArray(embeddingResult);
        embedding.setEmbedding(embeddingArr);
        embedding.setMetadata(metadata);
        save(embedding);
    }

    private DamAssetEmbedding mapperByAssert(DamAsset asset) {
        DamAssetEmbedding embedding = new DamAssetEmbedding();
        embedding.setId(asset.getId());
        embedding.setTenantId(asset.getTenantId());
        embedding.setUserId(asset.getUserId());
        embedding.setDirectoryId(asset.getDirectoryId());
        embedding.setAspectRatio(asset.getAspectRatio());
        embedding.setCreateTime(asset.getCreateTime());
        embedding.setUpdateTime(asset.getUpdateTime());
        return embedding;
    }

    // 不允许修改分辨率aspectRatio，content和embedding
    // metadata未填就不修改
    @Override
    public void updateByAsset(DamAsset asset, Map<String, Object> metadata) {
        if (asset.getId() == null) return;
        Date updateTime = new Date();
        var entity = new DamAssetEmbedding();
        entity.setId(asset.getId());
        entity.setTenantId(asset.getTenantId());
        entity.setUserId(asset.getUserId());
        entity.setDirectoryId(asset.getDirectoryId());
        entity.setMetadata(metadata);
        entity.setUpdateTime(updateTime);
        this.baseMapper.updateByEntity(entity);
    }

    @Override
    public void moveByAsserts(List<DamAsset> assets) {
        if (CollectionUtil.isEmpty(assets)) return;
        for (DamAsset asset : assets) {
            updateByAsset(asset, null);
        }
    }

    @Override
    public void copyByAsserts(List<Integer> oldIds, List<DamAsset> assets) {
        if (CollectionUtil.isEmpty(oldIds) || CollectionUtil.isEmpty(assets)) return;
        Map<Integer, DamAssetEmbedding> assetEmbeddingMap = listByIds(oldIds).stream()
                .collect(Collectors.toMap(DamAssetEmbedding::getId, Function.identity()));
        for (int i = 0; i < oldIds.size(); i++) {
            var createTime = new Date();
            DamAssetEmbedding oldEmbedding = assetEmbeddingMap.get(oldIds.get(i));
            if (oldEmbedding == null) continue;
            var newAsset = assets.get(i);
            var newEmbedding = this.mapperByAssert(newAsset);
            newEmbedding.setContent(oldEmbedding.getContent());
            newEmbedding.setEmbedding(oldEmbedding.getEmbedding());
            newEmbedding.setMetadata(oldEmbedding.getMetadata());
            newEmbedding.setCreateTime(createTime);
            newEmbedding.setUpdateTime(createTime);
            save(newEmbedding);
        }
    }

    @Override
    public void saveOrUpdateByAssets(List<DamAsset> assets, Integer userId, Integer tenantId, boolean isUpdate) {
        if (assets.isEmpty()) {
            return;
        }
        // 获取所有涉及到的目录id
        final Set<Integer> assetIds = assets.stream().map(DamAsset::getId)
                .collect(Collectors.toSet());
        final Map<Integer, DamTag> tagMap = tagService.list(
                        new LambdaQueryWrapper<DamTag>()
                                .eq(DamTag::getTenantId, tenantId)
                                .eq(DamTag::getIsDeleted, false))
                .stream().collect(Collectors.toMap(DamTag::getId, Function.identity()));
        final Map<Integer, List<DamTagValue>> assetId2TagValuesMap = tagValueService
                .list(new LambdaQueryWrapper<DamTagValue>()
                        .in(DamTagValue::getAssetId, assetIds)
                        .eq(DamTagValue::getIsDeleted, false))
                .stream()
                .collect(Collectors.groupingBy(DamTagValue::getAssetId));
        for (DamAsset asset : assets) {
            final Integer assetId = asset.getId();
            final List<DamTagValue> tagValues = assetId2TagValuesMap.get(assetId);
            if (tagValues == null || tagValues.isEmpty()) {
                continue;
            }

            final Map<String, Object> metadata = new HashMap<>();
            for (DamTagValue tagValue : tagValues) {
                final Integer tagId = tagValue.getTagId();
                final DamTag tag = tagMap.get(tagId);
                if (tag == null) {
                    continue;
                }
                metadata.put(tag.getName(), tagValue.getValue());
            }

            if (!metadata.containsKey(SHOT_DESCRIPTION_TAG_NAME)) {
                continue;
            }
            final String shotDescription = (String) metadata.get(SHOT_DESCRIPTION_TAG_NAME);
            if (shotDescription == null || shotDescription.isEmpty()) {
                continue;
            }

            if (isUpdate) {
                asset.setUserId(userId);
                asset.setTenantId(tenantId);
                updateByAsset(asset, metadata);
            } else {
                saveByAssetAndEmbeddingContent(asset, shotDescription, metadata);
            }
        }
    }

    @Override
    public Page<DamAssetEmbedding> pageEmbedding(Integer current, Integer pageSize, String aspectRatio, String content,
                                                 Integer tenantId, Integer userId, List<Integer> directoryIds) {
        var embeddingResult = embeddingModel.embed(content);
        long total = baseMapper.totalEmbeddingCount(aspectRatio, tenantId, userId, directoryIds);
        int offset = (current - 1) * pageSize;
        Page<DamAssetEmbedding> page = new Page<>(current, pageSize, total);
        page.setRecords(baseMapper.pageEmbedding(embeddingResult, offset, pageSize, aspectRatio, tenantId, userId, directoryIds));
        return page;
    }

    @Override
    public List<DamAssetEmbedding> topKEmbedding(String content, Integer limit, Float threshold,
                                                 String aspectRatio,
                                                 Integer tenantId, Integer userId, List<Integer> directoryIds) {
        var embeddingResult = embeddingModel.embed(content);
        return baseMapper.topKEmbedding(embeddingResult, limit, threshold, aspectRatio, tenantId, userId, directoryIds);
    }

    @Override
    public List<DamAssetEmbedding> topKEmbedding(Float[] embedding, Integer limit, Float threshold,
                                                 String aspectRatio,
                                                 Integer tenantId, Integer userId, List<Integer> directoryIds) {

        var embeddingResult = toFloatArray(embedding);
        return baseMapper.topKEmbedding(embeddingResult, limit, threshold, aspectRatio, tenantId, userId, directoryIds);
    }

    @Override
    @Deprecated  // 目前版本素材不支持修改embedding内容
    public int updateEmbedding(Integer id, String content) {
        float[] embeddingResult = embeddingModel.embed(content);
        Float[] embeddingArr = toFloatArray(embeddingResult);
        Date updateTime = new Date();
        return baseMapper.updateEmbedding(id, content, embeddingArr, updateTime);
    }

    public static Float[] toFloatArray(float[] arr) {
        if (arr == null) return null;
        Float[] result = new Float[arr.length];
        for (int i = 0; i < arr.length; i++) result[i] = arr[i];
        return result;
    }

    public static float[] toFloatArray(Float[] arr) {
        if (arr == null) return null;
        float[] result = new float[arr.length];
        for (int i = 0; i < arr.length; i++) result[i] = arr[i];
        return result;
    }

    @Override
    public int deleteEmbedding(Integer id) {
        return baseMapper.deleteById(id);
    }

    @Override
    public void restoreEmbedding(List<Integer> ids) {
        Date updateTime = new Date();
        baseMapper.update(new LambdaUpdateWrapper<DamAssetEmbedding>()
                .set(DamAssetEmbedding::getIsDeleted, false)
                .set(DamAssetEmbedding::getUpdateTime, updateTime)
                .in(DamAssetEmbedding::getId, ids)
        );
    }
}
