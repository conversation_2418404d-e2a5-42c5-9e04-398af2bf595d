package cn.mlamp.insightflow.cms.model.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 视频合成脚本Excel导出VO
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
public class VideoSynthesisScriptExcelVO {

    @ExcelProperty("镜头编号")
    private Integer sceneNumber;

    @ExcelProperty("镜头描述")
    private String sceneDescription;

    @ExcelProperty("品牌植入")
    private String brandIntegration;

    @ExcelProperty("镜头类型")
    private String cameraType;

    @ExcelProperty("运镜方式")
    private String cameraView;

    @ExcelProperty("时长")
    private String duration;

    @ExcelProperty("出现演员")
    private String actors;

    @ExcelProperty("演员动作")
    private String actorActions;

    @ExcelProperty("演员表情")
    private String actorExpressions;

    @ExcelProperty("台词")
    private String dialogue;

    @ExcelProperty("台词情绪")
    private String dialogueEmotion;

    @ExcelProperty("服装造型建议")
    private String costumeSuggestions;

    @ExcelProperty("道具清单")
    private String props;

    @ExcelProperty("布景要求")
    private String setRequirements;

    @ExcelProperty("背景音乐/音效")
    private String bgm;

    @ExcelProperty("光影与色彩要求")
    private String lightColor;

    @ExcelProperty("摄影器材")
    private String equipment;

    @ExcelProperty("视频内容策略")
    private String contentStrategy;

    @ExcelProperty("服化道")
    private String costumes;
}
