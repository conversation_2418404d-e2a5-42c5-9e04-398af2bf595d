package cn.mlamp.insightflow.cms.util;

import cn.mlamp.insightflow.cms.exception.BusinessException;
import org.springframework.util.DigestUtils;

import java.util.UUID;

/**
 * @Author: husuper
 * @CreateTime: 2025-03-27
 */
public class DeepanaSignUtil {

    private static  String appKey = null;

    private static  String appId = null;


    public static String sign(String requestId, String time) {
        StringBuilder input = new StringBuilder();
        input.append(requestId)
                .append(appId)
                .append(appKey)
                .append(time);
       return DigestUtils.md5DigestAsHex(input.toString().getBytes());
    }

    public static String getAppId() {
        return appId;
    }

    public static String getRequestId() {
        return UUID.randomUUID().toString();
    }

    public static String getTime() {
        return System.currentTimeMillis() + "";
    }

    public static void setAppKey(String appKey) {
        if(DeepanaSignUtil.appKey != null){
            throw new BusinessException("appKey只能设置一次");
        }
        DeepanaSignUtil.appKey = appKey;
    }

    public static void setAppId(String appId) {
        if(DeepanaSignUtil.appId != null){
            throw new BusinessException("appId只能设置一次");
        }
        DeepanaSignUtil.appId = appId;
    }

}
