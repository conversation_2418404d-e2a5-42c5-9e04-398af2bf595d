package cn.mlamp.insightflow.cms.strategy.handle;
import cn.mlamp.insightflow.cms.exception.BusinessException;
import cn.mlamp.insightflow.cms.util.DeepanaSignUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.MediaType;

import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class VideoDownloadHandle {

    @Autowired
    private RestTemplate restTemplate;

    @Value("${video.download.baseUrl}")
    private String baseUrl="https://deepana.hsk.top";

    public static void main(String[] args) {
        DeepanaSignUtil.setAppKey("d62c83b1bd204b23826e9c7b310bc332");
        DeepanaSignUtil.setAppId("insightflow-cms");
        //写个测试方法
        VideoDownloadHandle videoDownloadHandle = new VideoDownloadHandle();
        videoDownloadHandle.restTemplate = new RestTemplate();
        List<AddJob> addJobs=new ArrayList<>();
        addJobs.add(new AddJob("31234ewwerwerr32ewr413","https://www.douyin.com/video/7480032440573037843","2025-03-23",1));
        addJobs.add(new AddJob("31234324rwer53452","https://www.douyin.com/video/7480015666175626555","2025-03-25",1));
        addJobs.add(new AddJob("31234324rwer53452","https://www.douyin.com/video/7480031796076268841","2025-03-26",1));

        AddJobRequest requests = new AddJobRequest(addJobs);
        AddJobResponse addJobResponse = videoDownloadHandle.addJob(requests);
        System.out.println(addJobResponse);

        videoDownloadHandle.getFailList("2025-03-27");
        videoDownloadHandle.getProcessingList("2025-03-27");
        videoDownloadHandle.getDoneList("2025-03-27");
    }

    public AddJobResponse addJob(AddJobRequest requests) {
        String url = baseUrl + "/add_job";
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        requests.setRequestId(DeepanaSignUtil.getRequestId());
        requests.setTime(DeepanaSignUtil.getTime());
        requests.setSign(DeepanaSignUtil.sign(requests.getRequestId(), requests.getTime()));
        requests.setAppId(DeepanaSignUtil.getAppId());
        log.info("调用视频下载服务请求{}", JSONObject.toJSONString(requests));
        HttpEntity<AddJobRequest> requestEntity = new HttpEntity<>(requests, headers);
        ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, requestEntity, String.class);
        log.info("调用视频下载服务返回{}",response.getBody());
        AddJobResponse body= JSONObject.parseObject(response.getBody(), AddJobResponse.class);
        if(body.getCode()!=1){
            log.error("调用视频下载服务失败{}",response.getBody() );
            throw new BusinessException(body.getMsg());
        }
        return body;
    }

    public ResultResponse getFailList(String date) {
        String url = baseUrl + "/get_fail_list?date=" + date;
        url=getSignUrl(url);
        log.info("查询下载结果请求{}", url);
        ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
//        log.info("查询下载结果返回{}",response.getBody());
        ResultResponse body= JSONObject.parseObject(response.getBody(), ResultResponse.class);
        check(body);
        return body;
    }

    public ResultResponse getProcessingList(String date) {
        String url = baseUrl + "/get_processing_list?date=" + date;
        url=getSignUrl(url);
        log.info("查询下载结果请求{}", url);
        ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
//        log.info("查询下载结果返回{}",response.getBody());
        ResultResponse body= JSONObject.parseObject(response.getBody(), ResultResponse.class);
        check(body);
        return body;
    }

    public ResultResponse getDoneList(String date) {
        String url = baseUrl + "/get_done_list?date=" + date;
        url=getSignUrl(url);
        log.info("查询下载结果请求{}", url);
        ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
//        log.info("查询下载结果返回{}",response.getBody());
        ResultResponse body= JSONObject.parseObject(response.getBody(), ResultResponse.class);
        check(body);
        return body;
    }

    private void check(ResultResponse response){
        if(response.getCode()!=1){
            log.error("调用视频下载服务失败{}",response );
            throw new BusinessException(response.getMsg());
        }
    }

    private String getSignUrl(String baseUrl){
        String requestId = DeepanaSignUtil.getRequestId();
        String time = DeepanaSignUtil.getTime();
        String sign = DeepanaSignUtil.sign(requestId, time);
        baseUrl = baseUrl + "&requestId=" + requestId + "&time=" + time + "&sign=" + sign+ "&appId=" + DeepanaSignUtil.getAppId();
        return baseUrl;
    }


    @Data
    public static class AddJobRequest {
        private String requestId;
        private String appId;
        private String time;
        private String sign;
        private List<AddJob> list_data;

        public AddJobRequest(List<AddJob> addJobs) {
            this.list_data = addJobs;
        }

    }

    // 请求参数和响应参数的内部类定义
    @Data
    @AllArgsConstructor
    public static class AddJob {
        private String resource_id;
        private String video_url;
        private String upload_date;

        private Integer id;

    }

    @Data
    public static class AddJobResponse {
        private int code;
        private List<JobData> data;
        private String msg;

        @Data
        public static class JobData {
            private String job_no;

        }
    }

    @Data
    public static class ResultResponse {
        private int code;
        private List<ResultData> data;
        private String msg;
    }

    @Data
    public static class ResultData {
        private String aweme_id;
        private String resource_id;

    }
}