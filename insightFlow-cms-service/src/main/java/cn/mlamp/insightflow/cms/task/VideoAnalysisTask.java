//package cn.mlamp.insightflow.cms.task;
//
//import cn.mlamp.insightflow.cms.config.TaskConfig;
//import cn.mlamp.insightflow.cms.entity.BaseEntity;
//import cn.mlamp.insightflow.cms.entity.CmsPullTaskDedupedData;
//import cn.mlamp.insightflow.cms.enums.AnalysisStatusEnum;
//import cn.mlamp.insightflow.cms.enums.AnalysisVideoTypeEnum;
//import cn.mlamp.insightflow.cms.enums.DownloadStatusEnum;
//import cn.mlamp.insightflow.cms.enums.VideoInfoStatusEnum;
//import cn.mlamp.insightflow.cms.model.query.AnalysisVideoCreateRequest;
//import cn.mlamp.insightflow.cms.model.query.AnalysisVideoQueryRequest;
//import cn.mlamp.insightflow.cms.model.vo.AnalysisVideoResultVO;
//import cn.mlamp.insightflow.cms.service.CmsPullTaskDedupedDataService;
//import cn.mlamp.insightflow.cms.strategy.video.create.AnalysisVideoStrategyMap;
//import cn.mlamp.insightflow.cms.util.DateUtil;
//import cn.mlamp.insightflow.cms.util.FileDownloadUtil;
//import cn.mlamp.insightflow.cms.util.VideoUtil;
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.data.redis.core.StringRedisTemplate;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Component;
//
//import java.time.LocalDate;
//import java.time.LocalDateTime;
//import java.time.format.DateTimeFormatter;
//import java.util.Date;
//import java.util.List;
//import java.util.UUID;
//import java.util.concurrent.TimeUnit;
//
///**
// * @Author: husuper
// * @CreateTime: 2025-03-26
// */
//@Component
//@Slf4j
//public class VideoAnalysisTask {
//
//    @Autowired
//    private StringRedisTemplate stringRedisTemplate;
//
//    public static final String VIDEO_ANALYSIS_KEY = "videoAnalysisJob.analysis.";
//
//    @Autowired
//    private TaskConfig taskConfig;
//
//    @Autowired
//    private CmsPullTaskDedupedDataService cmsPullTaskDedupedDataService;
//    @Scheduled(cron = "0 0/7 0-8 * * ?")
//    public void videoAnalysisJob()  {
//        if(taskConfig.isLocal()){
//            return;
//        }
//
//        // 分布式锁(防止同一时间多台服务器重复触发)，保证每次定时任务只有一台服务器在执行
//        boolean result = Boolean.TRUE.equals(stringRedisTemplate.opsForValue().setIfAbsent("videoAnalysisJob", "ImSyncJob-timer", 1500, TimeUnit.SECONDS));
//        if (result) {
//            videoAnalysisJob(DateUtil.getYYYYMMDD(new Date()));
//            stringRedisTemplate.delete("videoAnalysisJob");
//        } else {
//            log.warn("多台服务器，防止同一个任务重复执行");
//        }
//    }
//
//
//    public void videoAnalysisJob(String dateStr){
//        List<CmsPullTaskDedupedData> queryDatas =queryData(dateStr, AnalysisStatusEnum.PROCESSING);
//        for (CmsPullTaskDedupedData cmsPullTaskDedupedData : queryDatas){
//            boolean result = Boolean.TRUE.equals(stringRedisTemplate.opsForValue().setIfAbsent(VIDEO_ANALYSIS_KEY+cmsPullTaskDedupedData.getEsId(), "ImSyncJob-timer", 600, TimeUnit.SECONDS));
//            if(result){
//                videoResult(cmsPullTaskDedupedData);
//                stringRedisTemplate.delete(VIDEO_ANALYSIS_KEY+cmsPullTaskDedupedData.getEsId());
//            }
//        }
//
//        if(queryDatas.size()>10){
//            log.info("进行中的分析任务超过20条，暂停发送新的分析任务，进行中任务为{}条", queryDatas.size());
//            return;
//        }
//
//        for (CmsPullTaskDedupedData cmsPullTaskDedupedData : queryData(dateStr, AnalysisStatusEnum.WAITING)){
//            boolean result = Boolean.TRUE.equals(stringRedisTemplate.opsForValue().setIfAbsent(VIDEO_ANALYSIS_KEY+cmsPullTaskDedupedData.getEsId(), "ImSyncJob-timer", 600, TimeUnit.SECONDS));
//            if(result){
//                videoAnalysis(cmsPullTaskDedupedData);
//                stringRedisTemplate.delete(VIDEO_ANALYSIS_KEY+cmsPullTaskDedupedData.getEsId());
//            }
//        }
//    }
//
//
//    private List<CmsPullTaskDedupedData> queryData(String dateStr,AnalysisStatusEnum status){
//        //查询创建时间为 date的数据   date格式是yyyy-mm-dd
//        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
//        LocalDate date = LocalDate.parse(dateStr, formatter);
//
//        LocalDateTime startOfDay = date.atStartOfDay();
//        LocalDateTime endOfDay = date.plusDays(1).atStartOfDay();
//
//        LambdaQueryWrapper<CmsPullTaskDedupedData> queryWrapper = new LambdaQueryWrapper<CmsPullTaskDedupedData>()
//                .ge(CmsPullTaskDedupedData::getCreateTime, startOfDay)
//                .lt(CmsPullTaskDedupedData::getCreateTime, endOfDay);
//        queryWrapper.eq(CmsPullTaskDedupedData::getSourceType, 1);
//        queryWrapper.eq(CmsPullTaskDedupedData::getDownloadStatus, DownloadStatusEnum.SUCCESS.getCode());
//        queryWrapper.eq(CmsPullTaskDedupedData::getAnalysisStatus, status.getCode());
//        queryWrapper.groupBy(CmsPullTaskDedupedData::getEsId);
//        queryWrapper.orderByDesc(CmsPullTaskDedupedData::getLongInteractCount);
//        queryWrapper.orderByDesc(CmsPullTaskDedupedData::getLongLikeCount);
//        queryWrapper.orderByAsc(CmsPullTaskDedupedData::getCreateTime);
//        //限制在10条数据
//        queryWrapper.last("limit 5");
//
//        List<CmsPullTaskDedupedData> list = cmsPullTaskDedupedDataService.list(queryWrapper);
//        return list;
//    }
//
//
//    private void  videoAnalysis(CmsPullTaskDedupedData cmsPullTaskDedupedData){
//        try {
//            //下载视频
//
//            String videoUrl = VideoUtil.getVideoUrl(cmsPullTaskDedupedData.getDatePublishedAt(), cmsPullTaskDedupedData.getEsId());
//
//            String fileName= cmsPullTaskDedupedData.getEsId()+".mp4";
//            String localFilePath = FileDownloadUtil.getPath(fileName);
//            FileDownloadUtil.downloadFile(videoUrl, localFilePath);
//
//            //分析视频
//            AnalysisVideoCreateRequest analysisVideoCreateRequest= new AnalysisVideoCreateRequest();
//            analysisVideoCreateRequest.setEsId(cmsPullTaskDedupedData.getEsId());
////            analysisVideoCreateRequest.setLocalFilePath(localFilePath);
//            analysisVideoCreateRequest.setTypeName(AnalysisVideoTypeEnum.VIDEO_ANALYSIS.getVideoType());
//            AnalysisVideoStrategyMap.process(analysisVideoCreateRequest);
//            //黄金5秒的分析
////            analysisVideoCreateRequest.setTypeName(AnalysisVideoTypeEnum.GOLDEN_FIVE_SECONDS.getVideoType());
////            AnalysisVideoStrategyMap.process(analysisVideoCreateRequest);
//
//            updateByEsId(cmsPullTaskDedupedData.getEsId(),AnalysisStatusEnum.PROCESSING);
//        }catch (Exception e){
//            log.error("视频分析失败",e);
//            updateByEsId(cmsPullTaskDedupedData.getEsId(),AnalysisStatusEnum.ERROR);
//
//        }
//
//    }
//
//
//    private void  videoResult(CmsPullTaskDedupedData cmsPullTaskDedupedData){
//        try {
//            //查询视频结果
//            AnalysisVideoQueryRequest analysisVideoQueryRequest= new AnalysisVideoQueryRequest();
//            analysisVideoQueryRequest.setEsId(cmsPullTaskDedupedData.getEsId());
//            String fileName= cmsPullTaskDedupedData.getEsId()+".mp4";
//            String localFilePath = FileDownloadUtil.getPath(fileName);
//            analysisVideoQueryRequest.setLocalFilePath(localFilePath);
//            analysisVideoQueryRequest.setTypeName(AnalysisVideoTypeEnum.VIDEO_ANALYSIS.getVideoType());
//            AnalysisVideoResultVO analysisVideoResultVO = AnalysisVideoStrategyMap.queryResult(analysisVideoQueryRequest);
//
//            //黄金5秒的查询
////            analysisVideoQueryRequest.setTypeName(AnalysisVideoTypeEnum.GOLDEN_FIVE_SECONDS.getVideoType());
////            AnalysisVideoResultVO analysisVideoResultVO2 = AnalysisVideoStrategyMap.queryResult(analysisVideoQueryRequest);
//
//            if(analysisVideoResultVO.getStatus()== VideoInfoStatusEnum.SUCCESS.getCode()){
//                updateByEsId(cmsPullTaskDedupedData.getEsId(),AnalysisStatusEnum.ANALYZING);
//                FileDownloadUtil.deleteFile(localFilePath);
//                return;
//            }
//
//            if(analysisVideoResultVO.getStatus() == VideoInfoStatusEnum.ERROR.getCode()){
//                updateByEsId(cmsPullTaskDedupedData.getEsId(),AnalysisStatusEnum.ERROR);
//                //删除视频
//                FileDownloadUtil.deleteFile(localFilePath);
//            }
//
//        }catch (Exception e){
//            log.error("视频分析失败",e);
//            updateByEsId(cmsPullTaskDedupedData.getEsId(),AnalysisStatusEnum.ERROR);
//        }
//    }
//
//    private void  updateByEsId(String esId,AnalysisStatusEnum analysisStatusEnum){
//        cmsPullTaskDedupedDataService.update(new LambdaUpdateWrapper<CmsPullTaskDedupedData>().
//                set(CmsPullTaskDedupedData::getAnalysisStatus,analysisStatusEnum.getCode())
//                .eq(CmsPullTaskDedupedData::getEsId,esId).eq(BaseEntity::getIsDeleted,0)
//                .eq(CmsPullTaskDedupedData::getSourceType,1));
//    }
//
//
//
//}
