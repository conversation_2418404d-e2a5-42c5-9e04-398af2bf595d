package cn.mlamp.insightflow.cms.controller;

import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.config.UserContext;
import cn.mlamp.insightflow.cms.model.dto.VideoAiGenerateDTO;
import cn.mlamp.insightflow.cms.model.vo.VideoAiGenerateVO;
import cn.mlamp.insightflow.cms.service.VideoAiGenerationService;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/vidu")
@AllArgsConstructor
public class VideoAiGenerationController {

    @Autowired
    private VideoAiGenerationService videoAiGenerationService;


    // 视频生成接口
    @PostMapping("/create")
    @Operation(summary = "创建AI视频生成任务")
    public RespBody<VideoAiGenerateVO.ViduCreateResponse> createVideo(@RequestBody @Valid VideoAiGenerateDTO.ViduCreateRequest request) {
        // 获取用户信息
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();

        // 创建任务并获取任务ID
        VideoAiGenerateVO.ViduCreateResponse result = videoAiGenerationService.createVideoTask(request, userId,
                tenantId);

        // 异步执行视频生成任务
        videoAiGenerationService.generateVideoAsync(request, result.getId(), userId, tenantId);

        return RespBody.ok(result);
    }

    // 视频查询接口
    @GetMapping("/query")
    @Operation(summary = "查询AI视频生成任务状态")
    public RespBody<VideoAiGenerateVO.VideoData> queryVideo(@RequestParam String id) {
        VideoAiGenerateVO.ViduQueryResponse response = videoAiGenerationService.queryVideo(id);
        switch (response.getStatus()) {
            case "success":
                return RespBody.ok(response.getVideoData());
            case "processing":
                return RespBody.ok(response.getVideoData());
            case "failed":
                return RespBody.ok(response.getVideoData());
            default:
                return RespBody.fail("未找到该任务");
            }
    }

    // 视频列表查询接口
    @GetMapping("/query/list")
    public RespBody<VideoAiGenerateVO.ViduListResponse> queryVideoList(
            @RequestParam(value = "pageSize") Integer pageSize,
            @RequestParam(value = "currentPage") Integer currentPage) {


        // 分页参数校验（基础实现）
        int defaultLimit = 10;
        int defaultPage = 1;
        int actualLimit = (pageSize != null && pageSize > 0) ? pageSize : defaultLimit;
        int actualPage = (currentPage != null && currentPage > 0) ? currentPage : defaultPage;

        Integer userId = UserContext.getUserId();
        // 构建响应
        VideoAiGenerateVO.ViduListResponse response = videoAiGenerationService.queryListVideo(actualLimit,actualPage,userId);
        switch (response.getStatus()) {
            case "success":
                return RespBody.ok(response);
//            case "failed":
//                return RespBody.fail("任务失败");
//            case "queueing":
//                return RespBody.fail("任务排队中");
            case "processing ":
                return RespBody.fail("任务处理中");
            default:
                return RespBody.fail("未找到该任务");
        }
    }

    // 用户上传AI视频，获取目录Id（目录名称：AI生成视频，权限为个人）
    @GetMapping("/upload/directory")
    @Operation(summary = "用户上传AI视频，获取目录Id")
    public RespBody<Integer> getDirectoryId() {
        // 获取用户信息
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();
        Integer directoryId = videoAiGenerationService.getUploadDirectoryId(userId, tenantId);
        return RespBody.ok(directoryId);
    }

}
