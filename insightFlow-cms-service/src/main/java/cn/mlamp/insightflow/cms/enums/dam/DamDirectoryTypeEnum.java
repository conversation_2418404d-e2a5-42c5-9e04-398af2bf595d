package cn.mlamp.insightflow.cms.enums.dam;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import javax.annotation.Nullable;

/**
 * DAM目录类型枚举
 */
@Getter
@AllArgsConstructor
public enum DamDirectoryTypeEnum implements IEnum<Integer> {
    /**
     * 1. 个人文件夹
     * 2. 租户文件夹
     */
    PERSONAL(1, "个人文件夹"),
    TENANT(2, "租户文件夹");

    @JsonValue
    private final Integer code;
    private final String desc;

    @Override
    public Integer getValue() {
        return code;
    }

    @JsonCreator
    @Nullable
    public static DamDirectoryTypeEnum getByCode(@Nullable Integer code) {
        if (code == null) {
            return null;
        }
        for (DamDirectoryTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new IllegalArgumentException("Invalid value '" + code + "' for DamDirectoryTypeEnum");
    }
}