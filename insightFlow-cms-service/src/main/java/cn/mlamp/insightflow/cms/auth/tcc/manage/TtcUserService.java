package cn.mlamp.insightflow.cms.auth.tcc.manage;//package cn.mlamp.insightflow.cms.auth.tcc.manage;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.mlamp.insightflow.cms.auth.tcc.model.Constants;
import cn.mlamp.insightflow.cms.auth.tcc.model.Tenant;
import cn.mlamp.insightflow.cms.auth.tcc.model.User;
import cn.mlamp.insightflow.cms.events.TenantInitializeEvent;
import cn.mlamp.insightflow.cms.exception.BusinessException;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.mz.ttc.LoginService;
import com.mz.ttc.model.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;
import java.util.stream.Collectors;


@Service
@Slf4j
public class TtcUserService {

    @Autowired
    @Lazy
    private LoginService loginService;

    @Autowired
    @Lazy
    private TtcProperties ttcProperties;

    @Autowired
    @Lazy
    private ApplicationEventPublisher eventPublisher;

    /**
     * 登录
     *
     * @param name     用户登录账号
     * @param password 密码
     * @return UserBO
     */
    public User login(String name, String password) throws BusinessException {
        Stopwatch stopwatch = Stopwatch.createStarted();

        // 获取认证信息
        LoginResult loginResult = loginService.authenticate(name, password);
        if (null == loginResult) {
            log.error("TtcUserService-login login fail, loginResult is empty, name:{}", name);
            throw new BusinessException(cn.mlamp.insightflow.cms.auth.tcc.model.ErrorCodeEnum.AUTHENTICATION_FAIL.getCode(), cn.mlamp.insightflow.cms.auth.tcc.model.ErrorCodeEnum.AUTHENTICATION_FAIL.getMessage());
        }

        log.info("TtcUserService-login authenticate name:{}, costs:{}ms", name,
                stopwatch.stop().elapsed(TimeUnit.MILLISECONDS));

        // 说明有业务异常
        if (!com.mz.ttc.model.ErrorCodeEnum.SUCCESS.getCode().equals(loginResult.getCode().getCode())) {
            Map<String, Integer> ttcErrorCodeToBizErrorCodeMap = ttcProperties.getTtcErrorCodeToBizErrorCodeMap();
            Integer bizCode = ttcErrorCodeToBizErrorCodeMap.get(loginResult.getCode().getCode());
            if (null != bizCode) {
                cn.mlamp.insightflow.cms.auth.tcc.model.ErrorCodeEnum bizCodeEnum = cn.mlamp.insightflow.cms.auth.tcc.model.ErrorCodeEnum.getByCode(bizCode);
                if (null != bizCodeEnum) {
                    log.error("TtcUserService-login fail, return business exception, ttc code:{}, msg:{}",
                            loginResult.getCode().getMessage(), loginResult.getCode().getCode());
                    throw new BusinessException(bizCodeEnum.getCode(), bizCodeEnum.getMessage());
                }
            }

            // ttc未知的错误码
            log.error("TtcUserService-login fail, return unknown business exception, ttc code:{}, msg:{}",
                    loginResult.getCode().getCode(), loginResult.getCode().getMessage());
            throw new BusinessException(cn.mlamp.insightflow.cms.auth.tcc.model.ErrorCodeEnum.LOGIN_INVALID_ERROR.getCode(), cn.mlamp.insightflow.cms.auth.tcc.model.ErrorCodeEnum.LOGIN_INVALID_ERROR.getMessage());
        }

        // 获取用户的相关信息
        final User user = buildLoginUserInfo(loginResult.getTicket());

        final Integer tenantId = user.getCurrentTenantId();
        if (tenantId != null) {
            // 发布租户初始化事件
            // TODO: 待优化, 不需要每次登录检测
            eventPublisher.publishEvent(new TenantInitializeEvent(this, tenantId, user.getId()));
        }

        return user;
    }

    /**
     * 切换租户
     *
     * @param tenantId 待切换的租户ID
     * @return
     */
    public List<Tenant> switchTenant(String ticket, Integer tenantId) {
        // 先切换租户
        BaseLoginInfo baseLoginInfo = loginService.switchTenant(tenantId);
        if (null == baseLoginInfo || null == baseLoginInfo.getTenantInfo()) {

            log.error("{}", cn.mlamp.insightflow.cms.auth.tcc.model.ErrorCodeEnum.USER_NO_PRODUCT_PERMISSION_ERROR);
            throw new BusinessException(cn.mlamp.insightflow.cms.auth.tcc.model.ErrorCodeEnum.USER_NO_PRODUCT_PERMISSION_ERROR);
        }

        if (!tenantId.equals(baseLoginInfo.getTenantInfo().getId())) {
            log.error("{}", cn.mlamp.insightflow.cms.auth.tcc.model.ErrorCodeEnum.NEED_RETRY_REFRESH);
            throw new BusinessException(cn.mlamp.insightflow.cms.auth.tcc.model.ErrorCodeEnum.NEED_RETRY_REFRESH);
        }

        // 获取全部租户
        List<Tenant> tenants = getAllTenantsByTicket(ticket);
        if (CollUtil.isEmpty(tenants)) {
            log.warn("TtcUserService-switchTenant empty tenant list, ticket:{}", ticket);
            throw new BusinessException(cn.mlamp.insightflow.cms.auth.tcc.model.ErrorCodeEnum.TENANT_EMPTY);
        }
        return markCurrentTenant(tenantId, tenants);
    }

    private List<Tenant> markCurrentTenant(Integer tenantId, List<Tenant> tenants) {
        for (Tenant tenant : tenants) {
            if (Objects.equals(tenantId, tenant.getId())) {
                tenant.setCurrent(true);
                break;
            }
        }

        return tenants;
    }

    /**
     * 根据ticket获取当前租户信息
     *
     * @param ticket
     * @return
     */
    public User getCurrentUser(String ticket) {
        return buildLoginUserInfo(ticket);
    }

    /**
     * 登出
     *
     * @param
     */
    public void logout() {
        loginService.logout();
    }

    /**
     * 获取权限信息
     *
     * @return
     */
    public List<String> getPermissionList(String ticket) {
        BaseLoginInfo baseLoginInfo = loginService.getLoginInfo(ticket);
        Supplier<? extends List<String>> supplier = new Supplier<List<String>>() {
            @Override
            public List<String> get() {
                return null;
            }
        };
        // 获取权限key列表
        List<String> permissions = Optional.ofNullable(baseLoginInfo)
                .map(BaseLoginInfo::getTenantInfo)
                .map(TenantInfo::getRole)
                .map(RoleInfo::getPermissionInfoList)
                .map(it -> it.stream().map(PermissionInfo::getPermissionKey).collect(Collectors.toList()))
                .orElseGet(supplier);

        if (CollUtil.isEmpty(permissions)) {
            log.error("TtcUserService-getPermissionInfo is Empty ,ticket:{}", ticket);
            throw new BusinessException(cn.mlamp.insightflow.cms.auth.tcc.model.ErrorCodeEnum.LOGIN_ACCOUNT_ERROR);
        }

        return permissions;
    }


    /**
     * 根据ticket获取用户信息
     *
     * @param ticket ticket
     * @return 用户信息(内部包含租户列表)
     */
    private User buildLoginUserInfo(String ticket) {
        BaseLoginInfo baseLoginInfo = loginService.getLoginInfo(ticket);

        if (null == baseLoginInfo) {
            log.error("TtcUserService-fetchUserBO is empty, ticket:{}", ticket);
            throw new BusinessException(cn.mlamp.insightflow.cms.auth.tcc.model.ErrorCodeEnum.AUTHENTICATION_FAIL);
        }

        if (null == baseLoginInfo.getTenantInfo()) {
            log.error("TtcUserService-fetchUserBO baseLoginInfo.getTenantInfo() is empty, ticket:{}", ticket);
            throw new BusinessException(cn.mlamp.insightflow.cms.auth.tcc.model.ErrorCodeEnum.AUTHENTICATION_FAIL);
        }

        // 获取全部租户
        List<Tenant> tenants = getAllTenantsByTicket(ticket);
        if (CollUtil.isEmpty(tenants)) {
            log.error("TtcUserService-fetchUserBO getAllTenants is empty, ticket:{}", ticket);
            throw new BusinessException(cn.mlamp.insightflow.cms.auth.tcc.model.ErrorCodeEnum.TENANT_EMPTY);
        }

        // 标记当前租户
        markCurrentTenant(baseLoginInfo.getTenantInfo().getId(), tenants);

        // 封装用户的信息
        User user = new User();
        user.setId(baseLoginInfo.getUserId());

        // 设置name：如果fullName为空或是"用户"，则使用email
        String fullName = baseLoginInfo.getFullName();
        if (StrUtil.isBlank(fullName) || "用户".equals(fullName)) {
            user.setName(baseLoginInfo.getEmail());
        } else {
            user.setName(fullName);
        }

        // 设置email和phoneNumber
        user.setEmail(baseLoginInfo.getEmail());
        user.setPhoneNumber(baseLoginInfo.getPhoneNumber());
        user.setCurrentTenantId(baseLoginInfo.getTenantInfo().getId());

        final RoleInfo roleInfo = baseLoginInfo.getTenantInfo().getRole();
        if (roleInfo != null) {
            user.setPermissions(roleInfo.getPermissionInfoList().stream()
                    .map(PermissionInfo::getPermissionKey)
                    .toList());
        }
        user.setTenants(tenants);

        return user;
    }

    /**
     * 根据ticket获取租户列表
     *
     * @param ticket ticket
     * @return 租户列表
     */
    private List<Tenant> getAllTenantsByTicket(String ticket) {
        // 获取Ttc全部租户信息
        List<TenantBasicInfo> tenantBasicInfos = loginService.getAllTenants(ticket);
        if (CollUtil.isEmpty(tenantBasicInfos)) {
            log.warn("TtcUserService-fetchAllTenants empty tenant list, ticket:{}", ticket);
            return Lists.newArrayList();
        }

        // 如果所有的用户状态都是禁用的，需要反馈给前端账号禁用
        if (tenantBasicInfos.stream()
                .noneMatch(it -> Constants.TTC_TENANT_USER_ACTIVE_STATUS.equals(it.getUserStatus()))) {
            try {
                loginService.logout();
            } catch (Exception ex) {
                log.warn("TtcUserService-fetchAllTenants logout error, ticket:{}", ticket, ex);
            }
            throw new BusinessException(cn.mlamp.insightflow.cms.auth.tcc.model.ErrorCodeEnum.USER_FORBIDDEN_PERMISSION_ERROR);
        }

        return tenantBasicInfos.stream()
                .filter(it -> Constants.TTC_TENANT_USER_ACTIVE_STATUS.equals(it.getUserStatus()))
                .map(it -> {
                    Tenant tenant = new Tenant();
                    tenant.setId(it.getId());
                    tenant.setName(it.getName());
                    tenant.setExpireTime(it.getExpiredDate());
                    tenant.setLastLoginTimestamp(it.getLastLoginTime());
                    tenant.setCurrent(false);
                    return tenant;
                })
                .collect(Collectors.toList());
    }

    public String getUserManagerUrl() {
        return loginService.getUserManagerUrl();
    }

    public void logoutByTicket(String ticket) {
        loginService.logoutWithDomain(ticket);
    }
}
