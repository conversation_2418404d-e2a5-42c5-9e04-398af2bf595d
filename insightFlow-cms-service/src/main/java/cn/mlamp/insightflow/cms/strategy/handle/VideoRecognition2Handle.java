package cn.mlamp.insightflow.cms.strategy.handle;

import cn.mlamp.insightflow.cms.config.AnalysisVideoConfig;
import cn.mlamp.insightflow.cms.exception.BusinessException;
import cn.mlamp.insightflow.cms.util.DeepanaSignUtil;
import cn.mlamp.insightflow.cms.util.FileDownloadUtil;
import cn.mlamp.insightflow.cms.util.VideoUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

/**
 * @Author: husuper
 * @CreateTime: 2025-03-25
 */
@Service
@Slf4j
public class VideoRecognition2Handle {


    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private AnalysisVideoConfig analysisVideoConfig;

    public static void main(String[] args) {
        VideoRecognition2Handle videoRecognition2Handle = new VideoRecognition2Handle();
        videoRecognition2Handle.restTemplate = new RestTemplate();
        videoRecognition2Handle.analysisVideoConfig = new AnalysisVideoConfig();
        videoRecognition2Handle.analysisVideoConfig.setAnalyzeUrl("http://10.10.100.228:8354");

        VideoUtil.setFfmpegPath("/Users/<USER>/Documents/ffmpeg/ffmpeg");
        VideoUtil.setFfprobePath("/Users/<USER>/Documents/ffmpeg/ffprobe");

//        String url="https://ai-pc-test.oss-cn-beijing.aliyuncs.com/sample/video/-CQEDpYBd6YxY-31wA2Z.mp4";
        String url = "https://ai-pc-test.oss-cn-beijing.aliyuncs.com/test/yasuohou.mp4";
        String videoFilePath = "/Users/<USER>/Documents/OSS/yasuohou.mp4";
        // 0: 获取待处理视频数量
        CountPendingResponse countPendingResponse = videoRecognition2Handle.countPending();



//        //1:获取ASR
//        ASR response= videoRecognition2Handle.asrGpuService("/Users/<USER>/Documents/OSS/88.wav", "observationId");


        //2：视频分析
        RecognitionArg recognitionArg= new RecognitionArg();
        recognitionArg.setVideo_url(url);
        recognitionArg.setTitle("美妆");
        recognitionArg.setContent("测试视频");
        recognitionArg.setIndustry("美妆");
//        recognitionArg.setAsr_text(response.getText());
//        videoRecognition2Handle.processVideo(recognitionArg, "observationId");


        //3：查询结果 17164
//        VideoContent videoContent = videoRecognition2Handle.queryVideo("18165", "observationId");
//        Map<String, Object> industryDecoding = JSONObject.parseObject(videoContent.getData().getResponse_body(), Map.class);

//        System.out.println(industryDecoding.get("创意得分"));

        // 4: 图片打标接口（黄金3s）
        ImageDecodingRequest request = new ImageDecodingRequest();
        request.setImages(List.of(VideoUtil.cutImageOfBase64(videoFilePath, 1000),
                VideoUtil.cutImageOfBase64(videoFilePath, 2000),
                VideoUtil.cutImageOfBase64(videoFilePath, 3000),
                VideoUtil.cutImageOfBase64(videoFilePath, 4000),
                VideoUtil.cutImageOfBase64(videoFilePath, 5000)
        ));
        request.setSentences("");
        request.setTitle("美妆");
        request.setContent("测试内容");

//        ImageDecodingResponse imageDecodingResponse = videoRecognition2Handle.imageDecoding3s(request, "observationId");


        // 5: 分镜图片打标接口
        SceneDecodingRequest sceneDecodingRequest = new SceneDecodingRequest();
        sceneDecodingRequest.setImages(List.of(
                VideoUtil.cutImageOfBase64(videoFilePath, 1000),
                VideoUtil.cutImageOfBase64(videoFilePath, 2000),
                VideoUtil.cutImageOfBase64(videoFilePath, 3000)
        ));
        sceneDecodingRequest.setSentences("");
        sceneDecodingRequest.setTitle("美妆");
        sceneDecodingRequest.setContent("测试内容");

//        SceneDecodingResponse sceneDecodingResponse = videoRecognition2Handle.sceneDecoding(sceneDecodingRequest, "observationId");
//        log.info("场景打标接口返回{}", sceneDecodingResponse);

        // 6: 视频分割接口
        VideoSplitRequest videoSplitRequest = new VideoSplitRequest();
        videoSplitRequest.setVideo_url(url);

//        videoSplitRequest.setVideo_asr(response);

//        VideoSplitResponse videoSplitResponse = videoRecognition2Handle.videoSplit(videoSplitRequest, "observationId");


    }


//    /**
//     * asr抽取
//     *
//     * @param video_url
//     * @param observationId
//     * @return
//     */
//    public Response<ASR> videoASR(String video_url, String observationId) {
//        String url = analysisVideoConfig.getAnalyzeUrl() + "/asr";
//
//        HttpHeaders headers = new HttpHeaders();
//        headers.setContentType(MediaType.APPLICATION_JSON);
//        headers.set("observation-id", observationId);
//        Map<String, String> arg = new HashMap<>();
//        arg.put("video_url", video_url);
//        log.info("调用视频整体分析服务请求{}", arg);
//        HttpEntity<Map> requestEntity = new HttpEntity<>(arg, headers);
//        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, requestEntity, String.class);
//        log.info("调用视频整体分析服务返回{}", responseEntity.getBody());
//        Response<ASR> asr = JSONObject.parseObject(responseEntity.getBody(), new TypeReference<Response<ASR>>() {
//        });
//        return asr;
//    }

    /**
     * ASR-gpu服务_copy
     * 创建人：lijingsong
     * 状态：已完成
     * 更新时间：2025-04-22 17:29:53
     * 接口路径：POST /api/asr_1745314095577
     * Mock地址：https://yapi.mlamp.cn/mock/920/api/asr_1745314095577
     * 请求参数：
     * Headers：
     * 参数名称	参数值	是否必须	示例	备注
     * Content-Type	multipart/form-data	是
     * Body:
     * 参数名称	参数类型	是否必须	示例	备注
     * files	文件	是
     * files={"file":wav_bytes}
     * 二进制wav_bytes数据
     * 用file.read()读取.wav文件获取
     * 返回参数
     * Response<ASR>
     *
     * @param wavFilePath
     * @param observationId
     * @return
     */
    public ASR asrGpuService(String wavFilePath, String observationId) {
        String url = "http://asr-gpu.mlamp.cn/api/asr";

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        headers.set("observation-id", observationId);

        FileSystemResource resource = new FileSystemResource(wavFilePath);
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add("file", resource);

        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, requestEntity, String.class);
        log.info("调用 ASR-gpu 服务返回{}", responseEntity.getBody());

        ASR asrResponse = JSONObject.parseObject(responseEntity.getBody(), new TypeReference<ASR>() {
        });
        if(asrResponse.getCode()!=0){
            FileDownloadUtil.deleteFile(wavFilePath);
            throw new BusinessException("识别ASR失败");
        }

        return asrResponse;
    }


    /**
     * 异步视频整体识别请求接口
     *
     * @param recognitionArg
     * @param observationId
     * @return
     */
    public ProcessVideo processVideo(RecognitionArg recognitionArg, String observationId) {
        String url = analysisVideoConfig.getAnalyzeUrl() + "/video_flow_v2";

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("observation-id", observationId);
        log.info("调用视频整体分析服务请求{},observation-id:{}", recognitionArg, observationId);
        HttpEntity<RecognitionArg> requestEntity = new HttpEntity<>(recognitionArg, headers);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, requestEntity, String.class);
        log.info("调用视频整体分析服务返回{}", responseEntity.getBody());
        ProcessVideo processVideo = JSONObject.parseObject(responseEntity.getBody(), ProcessVideo.class);
        if(processVideo.getCode()!=200){
            throw new BusinessException(processVideo.getMessage());
        }
        return processVideo;
    }


    /**
     * 视频整体识别结果获取接口
     *
     * @param db_unique_id
     * @param observationId
     * @return
     */
    public VideoContent queryVideo(String db_unique_id, String observationId) {
        String url = analysisVideoConfig.getAnalyzeUrl() + "/unique_id/" + db_unique_id;

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("observation-id", observationId);
        log.info("调用视频分析结果查询请求{} observation-id:{}", url,  observationId);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        log.info("调用视频分析结果查询返回{}", responseEntity.getBody());
        VideoContent videoContent = JSONObject.parseObject(responseEntity.getBody(), VideoContent.class);
        if(videoContent.getCode()!=200){
            throw new BusinessException(videoContent.getMessage());
        }
        return videoContent;

    }

    /**
     * 获取待处理视频的数量
     *
     * @return
     */
    public CountPendingResponse countPending() {
        String url = analysisVideoConfig.getAnalyzeUrl() + "/count_pending";
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        log.info("调用待处理视频数量查询请求{}", url);
        HttpEntity<Void> requestEntity = new HttpEntity<>(headers);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        log.info("调用待处理视频数量查询返回{}", responseEntity.getBody());
        CountPendingResponse countPendingResponse = JSONObject.parseObject(responseEntity.getBody(), CountPendingResponse.class);
        if(!"200".equals(countPendingResponse.getCode())){
            throw new BusinessException(countPendingResponse.getMsg());
        }
        return countPendingResponse;
    }

    @Data
    public static class ImageDecodingRequest {
        private List<String> images;
        private String sentences;
        private String title;
        private String content;

        @Override
        public String toString() {
            return "ImageDecodingRequest{" +
                    "sentences='" + sentences + '\'' +
                    ", title='" + title + '\'' +
                    ", content='" + content + '\'' +
                    '}';
        }
    }

    /**
     * 图片打标接口（黄金3s）
     *
     * @param request       请求参数
     * @param observationId
     * @return
     */
    public ImageDecodingResponse imageDecoding3s(ImageDecodingRequest request, String observationId) {
        String url = analysisVideoConfig.getAnalyzeUrl() + "/image_decoding_3s";

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("observation-id", observationId);
        log.info("调用图片打标（黄金3s）接口请求{},observation-id:{}", request,observationId);
        HttpEntity<ImageDecodingRequest> requestEntity = new HttpEntity<>(request, headers);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, requestEntity, String.class);
        log.info("调用图片打标（黄金3s）接口返回{}", responseEntity.getBody());
        ImageDecodingResponse imageDecodingResponse = JSONObject.parseObject(responseEntity.getBody(), ImageDecodingResponse.class);
        return imageDecodingResponse;
    }

    /**
     * 分镜图片打标接口
     *
     * @param request       请求参数
     * @param observationId
     * @return
     */
    public SceneDecodingResponse sceneDecoding(SceneDecodingRequest request, String observationId) {
        String url = analysisVideoConfig.getAnalyzeUrl() + "/scene_decoding";

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("observation-id", observationId);
        log.info("调用分镜图片打标接口请求{},observation-id:{}", request, observationId);
        HttpEntity<SceneDecodingRequest> requestEntity = new HttpEntity<>(request, headers);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, requestEntity, String.class);
        log.info("调用分镜图片打标接口返回{}", responseEntity.getBody());
        SceneDecodingResponse sceneDecodingResponse = JSONObject.parseObject(responseEntity.getBody(), SceneDecodingResponse.class);
        return sceneDecodingResponse;
    }

    /**
     * 视频分割接口
     *
     * @param request       请求参数
     * @param observationId
     * @return
     */
    public VideoSplitResponse videoSplit(VideoSplitRequest request, String observationId) {
        String url = analysisVideoConfig.getAnalyzeUrl() + "/video_split";

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("observation-id", observationId);
        log.info("调用视频分割接口请求{},observation-id:{}", JSONObject.toJSONString(request),observationId);
        HttpEntity<VideoSplitRequest> requestEntity = new HttpEntity<>(request, headers);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, requestEntity, String.class);
        log.info("调用视频分割接口返回{}", responseEntity.getBody());
        VideoSplitResponse videoSplitResponse = JSONObject.parseObject(responseEntity.getBody(), VideoSplitResponse.class);
        if(videoSplitResponse.getStatus_code()!=200){
            throw new BusinessException(videoSplitResponse.getMessage());
        }
        return videoSplitResponse;
    }


    @Data
    public static class VideoSplitRequest {
        private String video_url;
        private ASR video_asr;
    }

    @Data
    public static class VideoSplitResponse {
        private int status_code;
        private String message;
        private VideoSplitData data;
    }

    @Data
    public static class VideoSplitData {
        //转json的key定义为ASR
        @JsonProperty("ASR")
        private Map<String, ASRFragment> ASR;
        private List<List<Integer>> 画面分镜;
        private boolean Use_ASR;
    }

    @Data
    public static class ASRFragment {
        private String text;
        private List<Integer> time_stamp;
    }


    @Data
    public static class SceneDecodingResponse {
        private Map<String,String> data;
        private int code;
        private String message;
    }

    @Data
    public static class SceneDecodingData {

//        private String scene_description; // 镜头描述（分镜描述）
//        private String scene_type; // 视频内容策略（结构）
//        private double present_image_time; // 展示画面时间戳
//        private String brand_integration; // 品牌植入
//        private String camera_type; // 镜头类型（机位）
//        private String camera_view; // 运镜方式
//        private double scene_length; // 分镜时长
//        private String bgm; // 背景音乐/音效
//        private String light_color; // 光影与色彩要求
//        private String photographic_equipment; // 摄影器材
//        private String start; // 分镜开始时间戳
//        private String end; // 分镜结束时间戳
//        private String set_requirements; // 布景要求
//        private String strategy; // 视频内容策略
//        private List<String> actor_present; // 出现演员
//        private String actor_action; // 演员动作
//        private String actor_expressions; // 演员表情
//        private String sentences; // 台词
//        private String modeling; // 服装造型
//        private String props; // 道具清单
    }

    @Data
    public static class SceneDecodingRequest {
        private List<String> images; // 头start中(start+end)/2尾end这3张图片base64
        private String sentences; // 分镜对应ASR结果，sentence start>=分镜start，<分镜end
        private String title; // 标题
        private String content; // 内容
        private List<Tag> tags;

        @Data
        public static class Tag {
            @JsonProperty("标签名")
            private String name;
            @JsonProperty("标签描述")
            private String description;
            @JsonProperty("标签样例")
            private String example;
        }

        @Override
        public String toString() {
            return "SceneDecodingRequest{" +
                    "sentences='" + sentences + '\'' +
                    ", title='" + title + '\'' +
                    ", content='" + content + '\'' +
                    '}';
        }
    }


    @Data
    public static class ImageDecodingResponse {
        private ImageDecodingData data;
        private int code;
        private String message;
    }

    @Data
    public static class ImageDecodingData {
        private int is_highlight; // 是否为黄金3秒  0不是，1是的
        private String highlight_type; // 黄金3秒的类型
        private String highlight_description; // 画面描述
        private String highlight_reason; // 高光原因
    }


    @Data
    public static class CountPendingResponse {
        private String msg;
        private String code;
        private CountPendingData data;
    }

    @Data
    public static class CountPendingData {
        private int pending_count;
    }


    @Data
    public static class RecognitionArg {

        //视频URL
        private String video_url;

        //标题
        private String title;

        //内容
        private String content;

        //行业
        private String asr_text;

        //默认美妆
        private String industry;

    }

    @Data
    public static class ProcessVideo {
        // 消息内容
        private String message;

        // 响应代码
        private int code;

        private ProcessVideoData data;

    }

    @lombok.Data
    public static class ProcessVideoData {
        private String db_unique_id;
    }


    @Data
    public static class VideoContent {
        // 消息内容
        private String message;

        // 响应代码
        private int code;

        // 数据对象
        private VideoContentData data;

    }

    @Data
    public static class VideoContentData {
        //Pending, Processing, Finished, Error
        private String status;

        private String error_msg;

        private String response_body;

    }


    @lombok.Data
    public static class Response<T> {

        private Integer code;

        private String message;

        private T data;
    }


    @lombok.Data
    public static class ASR {

        private Integer code;

        // 视频台词完整拼接
        private String text;

        // 带时间戳的句子
        private List<Sentences> sentences;

    }

    @lombok.Data
    public static class Sentences {
        // 台词
        private String text;

        // 开始时间(毫秒)
        private Integer start;

        // 结束时间(毫秒)
        private Integer end;

    }


}
