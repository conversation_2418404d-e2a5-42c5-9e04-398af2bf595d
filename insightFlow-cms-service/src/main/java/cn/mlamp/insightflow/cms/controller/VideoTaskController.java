package cn.mlamp.insightflow.cms.controller;

import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.config.UserContext;
import cn.mlamp.insightflow.cms.enums.VideoTaskStatusEnum;
import cn.mlamp.insightflow.cms.model.query.TaskDetailImageGenRequest;
import cn.mlamp.insightflow.cms.model.query.TaskDetailMagicRequest;
import cn.mlamp.insightflow.cms.model.query.VideoScriptGenRequest;
import cn.mlamp.insightflow.cms.model.vo.VideoStoryBoardVO;
import cn.mlamp.insightflow.cms.model.vo.VideoTaskListInfoVO;
import cn.mlamp.insightflow.cms.model.vo.VideoTaskScriptDetailVO;
import cn.mlamp.insightflow.cms.service.ICmsTaskInfoService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

@Slf4j
@RequestMapping("/task")
@RestController
@RequiredArgsConstructor
@Tag(name = "生成任务相关接口")
public class VideoTaskController {

    private final ICmsTaskInfoService taskInfoService;

    @PostMapping("/video/imitate")
    @Operation(summary = "AI仿写")
    public RespBody<String> videoTaskAiImitate(@RequestBody VideoScriptGenRequest request) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();
        request.setUserId(userId);
        request.setTenantId(tenantId);
        // 调用dify生成脚本和分镜描述
        var taskId = taskInfoService.videoTaskAiImitate(request);
        taskInfoService.videoTaskAiImitateAsync(request, taskId);
        return RespBody.ok(taskId.toString());
    }

    @GetMapping("/video/list")
    @Operation(summary = "视频任务列表")
    public RespBody<Page<VideoTaskListInfoVO>> videoTaskPage(@RequestParam(value = "current", defaultValue = "1") Integer current,
                                                             @RequestParam(value = "pageSize", defaultValue = "5") Integer pageSize,
                                                             @RequestParam(value = "type", defaultValue = "0") Integer type) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();
        return RespBody.ok(taskInfoService.videoTaskPage(current, pageSize, type, userId, tenantId));
    }

    @GetMapping("/video/detail")
    @Operation(summary = "脚本任务详情")
    public RespBody<VideoTaskScriptDetailVO> videoTaskDetail(@RequestParam(value = "taskId") Integer taskId) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();
        return RespBody.ok(taskInfoService.videoTaskDetail(taskId, userId, tenantId));
    }

    @GetMapping("/video/recommend")
    @Operation(summary = "AI仿写脚本视频素材推荐")
    public RespBody<VideoTaskScriptDetailVO> videoTaskRecommendVideo(@RequestParam(value = "taskId") Integer taskId,
                                                                     @RequestParam(value = "aspectRatio") String aspectRatio) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();
        return RespBody.ok(taskInfoService.recommendVideo(taskId, aspectRatio, userId, tenantId));
    }

    @PostMapping("/video/storyboard/recommend")
    @Operation(summary = "AI仿写分镜推荐")
    public RespBody<List<Integer>> storyboardRecommend(@RequestBody VideoScriptGenRequest request) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();
        request.setUserId(userId);
        request.setTenantId(tenantId);
        return RespBody.ok(taskInfoService.storyBoardRecommend(request));
    }

    @PutMapping("/video/detail")
    @Operation(summary = "修改脚本信息")
    public RespBody<VideoTaskScriptDetailVO> updateVideoTaskDetail(@RequestBody VideoTaskScriptDetailVO videoTaskScriptDetailVO) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();
        VideoTaskScriptDetailVO result = taskInfoService.updateVideoTaskDetail(videoTaskScriptDetailVO, userId, tenantId);
        return RespBody.ok(result);
    }

    @PostMapping("/video/detail/magic")
    @Operation(summary = "魔术棒自动生成内容")
    public RespBody<String> videoTaskDetailMagic(@RequestBody TaskDetailMagicRequest request) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();
        request.setUserId(userId);
        request.setTenantId(tenantId);
        String result = taskInfoService.videoTaskDetailMagic(request);
        return RespBody.ok(result);
    }

    @PostMapping("/video/detail/imageGen")
    @Operation(summary = "分镜任务图片生成")
    public RespBody<VideoStoryBoardVO> videoTaskDetailImageGen(@RequestBody TaskDetailImageGenRequest request) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();
        request.setUserId(userId);
        request.setTenantId(tenantId);
        VideoStoryBoardVO result = taskInfoService.videoTaskDetailImageGen(request);
        return RespBody.ok(result);
    }

    @PostMapping("/video/detail/reverse")
    @Operation(summary = "AI仿写内容还原")
    public RespBody<VideoTaskScriptDetailVO> videoTaskDetailReverse(@RequestParam(value = "taskId") Integer taskId) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();
        VideoTaskScriptDetailVO result = taskInfoService.videoTaskDetailReverse(taskId, userId, tenantId);
        return RespBody.ok(result);
    }

    @GetMapping("/result/excel")
    @Operation(summary = "视频任务结果导出excel")
    public void exportVideoTask(@RequestParam(value = "taskId", required = true) Integer taskId,
                                @RequestParam(value = "storyboardId", required = false) Integer storyboardId,
                                HttpServletResponse response) throws IOException {
        taskInfoService.exportVideoTask(taskId, storyboardId, response);
    }

    /**
     * 取消视频脚本生成任务
     */
    @PostMapping("/video/cancel/{taskId}")
    @Operation(summary = "取消视频脚本生成任务")
    public RespBody<Void> videoTaskCancel(@PathVariable Integer taskId) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();
        taskInfoService.videoTaskCancel(taskId, userId, tenantId);
        return RespBody.ok();
    }

    /**
     * 重试视频脚本生成
     */
    @PostMapping("/video/retry/{taskId}")
    @Operation(summary = "重试视频脚本生成")
    public RespBody<Void> videoTaskRetry(@PathVariable Integer taskId) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();
        VideoTaskStatusEnum lastStatus = taskInfoService.videoTaskRetry(taskId, userId, tenantId);
        taskInfoService.retryVideoTaskAsync(taskId, lastStatus);
        return RespBody.ok();
    }

    /**
     * 重试视频脚本生成
     */
    @DeleteMapping("/video/delete/{taskId}")
    @Operation(summary = "删除视频生成任务")
    public RespBody<Void> videoTaskDelete(@PathVariable Integer taskId) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();
        taskInfoService.deleteVideoTask(taskId, userId, tenantId);
        return RespBody.ok();
    }
}
