package cn.mlamp.insightflow.cms.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户查询历史记录实体类
 * 
 * <AUTHOR>
 * @date 2025-05-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("cms_search_history")
public class CmsSearchHistory extends BaseEntity {

    @TableField("user_id")
    private Integer userId;

    @TableField("tenant_id")
    private Integer tenantId;

    private String keyword;

    @TableField("search_type")
    private String searchType;

    @TableField("search_module")
    private String searchModule;

}
