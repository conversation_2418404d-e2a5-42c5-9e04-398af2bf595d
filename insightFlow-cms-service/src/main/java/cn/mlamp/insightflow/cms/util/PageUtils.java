package cn.mlamp.insightflow.cms.util;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.function.Function;
import java.util.stream.Collectors;

public class PageUtils {
    public static <T, VO> Page<VO> convertVOPage(Page<T> page, Function<T, VO> mapper) {
        Page<VO> voPage = new Page<>();
        voPage.setTotal(page.getTotal());
        voPage.setPages(page.getPages());
        voPage.setCurrent(page.getCurrent());
        voPage.setSize(page.getSize());
        voPage.setRecords(page.getRecords().stream().map(mapper).collect(Collectors.toList()));
        return voPage;
    }
}
