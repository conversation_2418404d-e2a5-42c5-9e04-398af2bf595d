package cn.mlamp.insightflow.cms.entity.dam;

import cn.mlamp.insightflow.cms.enums.dam.DamDirectoryTypeEnum;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * DAM素材目录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
@Getter
@Setter
@TableName("cms_directory")
@Schema(name = "CmsDirectory", description = "DAM素材目录表")
public class DamDirectory implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @Schema(description = "租户ID")
    @TableField("tenant_id")
    private Integer tenantId;

    @Schema(description = "创建用户ID")
    @TableField("user_id")
    private Integer userId;

    @Schema(description = "目录名称")
    @TableField("name")
    private String name;

    @Schema(description = "目录类型：1-个人文件夹，2-租户文件夹")
    @TableField("type")
    private DamDirectoryTypeEnum type;

    @Schema(description = "创建时间")
    @TableField("create_time")
    private Date createTime;

    @Schema(description = "更新时间")
    @TableField("update_time")
    private Date updateTime;

    @Schema(description = "逻辑删除：0-未删除，1-已删除")
    @TableField("is_deleted")
    private Boolean isDeleted;
}
