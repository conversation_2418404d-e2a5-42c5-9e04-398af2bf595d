package cn.mlamp.insightflow.cms.strategy.video.create;

import cn.mlamp.insightflow.cms.common.file.IS3FlowService;
import cn.mlamp.insightflow.cms.config.AnalysisVideoConfig;
import cn.mlamp.insightflow.cms.entity.*;
import cn.mlamp.insightflow.cms.enums.*;
import cn.mlamp.insightflow.cms.exception.BusinessException;
import cn.mlamp.insightflow.cms.mapper.CmsVideoAnalysisMapper;
import cn.mlamp.insightflow.cms.model.query.AnalysisVideoCreateRequest;
import cn.mlamp.insightflow.cms.model.query.AnalysisVideoQueryRequest;
import cn.mlamp.insightflow.cms.model.query.AsyncResultRequest;
import cn.mlamp.insightflow.cms.model.vo.AnalysisVideoCreateVO;
import cn.mlamp.insightflow.cms.model.vo.AnalysisVideoResultVO;
import cn.mlamp.insightflow.cms.service.*;
import cn.mlamp.insightflow.cms.strategy.handle.VideoRecognition3Handle;
import cn.mlamp.insightflow.cms.util.FileDownloadUtil;
import cn.mlamp.insightflow.cms.util.FilePathBuilder;
import cn.mlamp.insightflow.cms.util.VideoUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;

/**
 * 千川视频策略实现类
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class QianchuanVideoStrategy implements ProcessAnalysisVideoStrategyInterface {

    private final QianchuanMaterialVideoService qianchuanMaterialVideoService;

    private final VideoRecognition3Handle videoRecognition3Handle;

    private final IVideoInfoService videoInfoService;

    private final IVideoResultService videoResultService;

    private final IVideoAsrService videoAsrService;

    private final FileService fileService;

    @Resource(name = "cmsS3FlowService")
    private IS3FlowService cmsS3FlowService;

    private final AnalysisVideoConfig analysisVideoConfig;

    @Autowired
    private final TokenUseDetailService tokenUseDetailService;

    @Autowired
    private CmsAsyncTaskService cmsAsyncTaskService;

    @Autowired
    private CmsVideoAnalysisMapper  cmsVideoAnalysisMapper;

    @Resource(name = "analysisThreadExecutor")
    private final ExecutorService analysisThreadExecutor;

    @Autowired
    private CmsVideoAnalysisService  cmsVideoAnalysisService;


    private static final String TASK_QUEUE="daily";

    @PostConstruct
    public void init() {
        AnalysisVideoStrategyMap.register(AnalysisVideoTypeEnum.QIANCHUAN_VIDEO.getVideoType(), this);
    }

    @Override
    @Transactional
    public AnalysisVideoCreateVO process(AnalysisVideoCreateRequest request) {
        Integer id = null;
        try {
            //查询千川的数据
            if (StringUtils.isBlank(request.getEsId())) {
                throw new BusinessException("esId不能为空");
            }
            QianchuanMaterialVideo qianchuanMaterialVideo = qianchuanMaterialVideoService.getOne(new LambdaQueryWrapper<QianchuanMaterialVideo>().eq(QianchuanMaterialVideo::getVideoId, request.getEsId()));
            if (qianchuanMaterialVideo == null) {
                throw new BusinessException("esId不能存在");
            } else {
                if (qianchuanMaterialVideo.getAnalysisStatus() != 0) {
                    throw new BusinessException("不是待分析状态");
                }
            }
            //下载视频
            CmsVideoAnalysisLog cmsVideoAnalysisLog = new CmsVideoAnalysisLog();
            /*
             * video_info_type:整体理解 task_type：文件下载
             */
            cmsVideoAnalysisLog.setVideoId(qianchuanMaterialVideo.getVideoId());
            cmsVideoAnalysisLog.setVideoInfoType(VideoAnalysisLogTypeEnum.OVERALL_ANALYSIS.getDesc());
            cmsVideoAnalysisLog.setTaskType(VideoAnalysisLogTaskTypeEnum.DOWNLOAD.getDesc());
            String videoUrl = analysisVideoConfig.getDomain() + "/" + qianchuanMaterialVideo.getOssid();
            String fileName = qianchuanMaterialVideo.getVideoId() + ".mp4";
            String localFilePath = FileDownloadUtil.getPath(fileName);
            long downloadStart = System.currentTimeMillis();
            FileDownloadUtil.downloadFile(videoUrl, localFilePath);
            long downloadEnd = System.currentTimeMillis();
            cmsVideoAnalysisLog.setTime((int) (downloadEnd - downloadStart));
            cmsVideoAnalysisService.save(cmsVideoAnalysisLog);


            //ASR抽离
            String audiofileName = qianchuanMaterialVideo.getVideoId() + ".wav";
            String audiolocalFilePath = FileDownloadUtil.getPath(audiofileName);

            handleWavDownload(qianchuanMaterialVideo.getVideoId(), localFilePath, audiolocalFilePath,  audiofileName);

            /*
             * video_info_type:ASR识别 task_type：算法接口调用
             */
            CmsVideoAnalysisLog cmsVideoAnalysisLog2 = new CmsVideoAnalysisLog();
            cmsVideoAnalysisLog2.setVideoId(qianchuanMaterialVideo.getVideoId());
            cmsVideoAnalysisLog2.setVideoInfoType(VideoAnalysisLogTypeEnum.ASR_RECOGNITION.getDesc());
            cmsVideoAnalysisLog2.setTaskType(VideoAnalysisLogTaskTypeEnum.ALGORITHM.getDesc());
            long algorithmStart = System.currentTimeMillis();
            VideoRecognition3Handle.ASR asr = videoRecognition3Handle.asrGpuService(audiolocalFilePath, request.getEsId());
            long algorithmEnd = System.currentTimeMillis();
            cmsVideoAnalysisLog2.setTime((int) (algorithmEnd - algorithmStart));
            cmsVideoAnalysisService.save(cmsVideoAnalysisLog2);

            FileDownloadUtil.deleteFile(audiolocalFilePath);


            //压缩视频
            String compressMp4Path = FileDownloadUtil.getPath("compress_" + qianchuanMaterialVideo.getVideoId() + ".mp4");
            String compressMp4OSSId = handleCompressDownload(qianchuanMaterialVideo, localFilePath, compressMp4Path,  request);

            //分析视频
            VideoRecognition3Handle.RecognitionArg recognitionArg = new VideoRecognition3Handle.RecognitionArg();
            recognitionArg.setVideo_url(analysisVideoConfig.getDomain() + "/" + compressMp4OSSId);
            recognitionArg.setTitle(qianchuanMaterialVideo.getTitle());
            recognitionArg.setContent(null);
            recognitionArg.setAsr_text(asr.getText());
            recognitionArg.setIndustry(qianchuanMaterialVideo.getIndustry());
            recognitionArg.setVideo_asr(asr);
            recognitionArg.setTask_queue(TASK_QUEUE);
            /*
             * video_info_type:整体理解 task_type：算法接口调用
             */
            CmsVideoAnalysisLog cmsVideoAnalysisLog5 = new CmsVideoAnalysisLog();
            cmsVideoAnalysisLog5.setVideoId(qianchuanMaterialVideo.getVideoId());
            cmsVideoAnalysisLog5.setVideoInfoType(VideoAnalysisLogTypeEnum.OVERALL_ANALYSIS.getDesc());
            cmsVideoAnalysisLog5.setTaskType(VideoAnalysisLogTaskTypeEnum.ALGORITHM.getDesc());
            long algorithmStart1 = System.currentTimeMillis();
            VideoRecognition3Handle.ProcessVideo processVideo = videoRecognition3Handle.processVideo(recognitionArg, request.getEsId());
            long algorithmEnd1 = System.currentTimeMillis();
            cmsVideoAnalysisLog5.setTime((int) (algorithmEnd1 - algorithmStart1));
            cmsVideoAnalysisLog5.setDbUniqueId(Long.valueOf(processVideo.getData().getDb_unique_id()));
            cmsVideoAnalysisService.save(cmsVideoAnalysisLog5);
            String db_unique_id = processVideo.getData().getDb_unique_id();

            //保存异步任务表数据
            cmsAsyncTaskService.save(db_unique_id, AnalysisVideoTypeEnum.QIANCHUAN_VIDEO.getVideoCode(), 1, qianchuanMaterialVideo.getVideoId(), "async_video_flow_v2");

            //保存数据
            Arg arg = new Arg();
            arg.setDbUniqueId(db_unique_id);
            arg.setObservationId(request.getEsId());
            arg.setCompressMp4OSSId(compressMp4OSSId);

            CmsVideoInfo videoInfo = new CmsVideoInfo();
            videoInfo.setEsId(qianchuanMaterialVideo.getVideoId());
            videoInfo.setType(AnalysisVideoTypeEnum.QIANCHUAN_VIDEO.getVideoCode());
            videoInfo.setStatus(VideoInfoStatusEnum.PROCESSING.getCode());
            videoInfo.setUserId(request.getUserId());
            videoInfo.setTenantId(request.getTenantId());
            videoInfo.setArg(JSONObject.toJSONString(arg));
            videoInfo.setIndustry(qianchuanMaterialVideo.getIndustry());
            videoInfoService.save(videoInfo);
            id = videoInfo.getId();

            //更新状态
            qianchuanMaterialVideo.setAnalysisStatus(1);
            qianchuanMaterialVideo.setUpdateTime(new Date());
            qianchuanMaterialVideo.setKwVideoContent(asr.getText());
            qianchuanMaterialVideoService.updateById(qianchuanMaterialVideo);

            AnalysisVideoCreateVO analysisVideoCreateVO = new AnalysisVideoCreateVO();
            analysisVideoCreateVO.setId(videoInfo.getId());
            return analysisVideoCreateVO;
        } catch (Exception e) {
            log.error("视频分析失败", e);
            videoInfoService.updateVideoInfoStatusFailed(id, e.getMessage());
            throw new BusinessException(e.getMessage());
        }
    }


    private void handleWavDownload(String videoId, String localFilePath, String audiolocalFilePath,String audiofileName){
        //从OSS上下载
        try {
            String ossId= FilePathBuilder.getOpenVideoDecodeOssPath(videoId,audiofileName);
            FileDownloadUtil.downloadFile3(analysisVideoConfig.getDomain() + "/"+ossId,audiolocalFilePath);
            if(FileDownloadUtil.fileExistenceChecker(audiolocalFilePath)){
                return;
            }
        }catch (Exception e){
            log.info("wav文件下载失败{}",e.getMessage());
        }
        log.info("本地处理wav文件");
        /*
         * video_info_type:ASR识别 task_type：WAV转码
         */
        CmsVideoAnalysisLog cmsVideoAnalysisLog1 = new CmsVideoAnalysisLog();
        cmsVideoAnalysisLog1.setVideoId(videoId);
        cmsVideoAnalysisLog1.setVideoInfoType(VideoAnalysisLogTypeEnum.ASR_RECOGNITION.getDesc());
        cmsVideoAnalysisLog1.setTaskType(VideoAnalysisLogTaskTypeEnum.AUDIO_CONVERT.getDesc());
        long convertStart = System.currentTimeMillis();
        VideoUtil.convertVideoToAudio(localFilePath, audiolocalFilePath);
        long convertEnd = System.currentTimeMillis();
        cmsVideoAnalysisLog1.setTime((int) (convertEnd - convertStart));
        cmsVideoAnalysisService.save(cmsVideoAnalysisLog1);
    }


    private String  handleCompressDownload(QianchuanMaterialVideo qianchuanMaterialVideo, String localFilePath, String compressMp4Path,AnalysisVideoCreateRequest request){
        //上传压缩视频
        String compressMp4OSSId = FilePathBuilder.getOpenVideoDecodeOssPath(request.getEsId(), "compress_" + qianchuanMaterialVideo.getVideoId() + ".mp4");

        try {
            FileDownloadUtil.downloadFile3(analysisVideoConfig.getDomain() + "/"+compressMp4OSSId,compressMp4Path);
            if(FileDownloadUtil.fileExistenceChecker(compressMp4Path)){
                return compressMp4OSSId;
            }
        }catch (Exception e){
            log.info("压缩文件下载失败{}",e.getMessage());
        }

        log.info("本地处理压缩视频文件");

        /*
         * video_info_type:整体理解 task_type：文件压缩
         */
        CmsVideoAnalysisLog cmsVideoAnalysisLog3 = new CmsVideoAnalysisLog();
        cmsVideoAnalysisLog3.setVideoId(qianchuanMaterialVideo.getVideoId());
        cmsVideoAnalysisLog3.setTaskType(VideoAnalysisLogTaskTypeEnum.COMPRESS.getDesc());
        cmsVideoAnalysisLog3.setVideoInfoType(VideoAnalysisLogTypeEnum.OVERALL_ANALYSIS.getDesc());
        long compressStart = System.currentTimeMillis();
        VideoUtil.compressVideo(localFilePath, compressMp4Path);
        long compressEnd = System.currentTimeMillis();
        cmsVideoAnalysisLog3.setTime((int) (compressEnd - compressStart));
        cmsVideoAnalysisService.save(cmsVideoAnalysisLog3);

        /*
         * video_info_type:整体理解 task_type：文件上传
         */
        CmsVideoAnalysisLog cmsVideoAnalysisLog4 = new CmsVideoAnalysisLog();
        cmsVideoAnalysisLog4.setVideoId(qianchuanMaterialVideo.getVideoId());
        cmsVideoAnalysisLog4.setVideoInfoType(VideoAnalysisLogTypeEnum.OVERALL_ANALYSIS.getDesc());
        cmsVideoAnalysisLog4.setTaskType(VideoAnalysisLogTaskTypeEnum.UPLOAD.getDesc());
        long uploadStart = System.currentTimeMillis();
        uploadVideo(compressMp4OSSId, compressMp4Path);
        long uploadEnd = System.currentTimeMillis();
        cmsVideoAnalysisLog4.setTime((int) (uploadEnd - uploadStart));
        cmsVideoAnalysisService.save(cmsVideoAnalysisLog4);
        return compressMp4OSSId;
    }

    private void saveASR(CmsVideoInfo videoInfo, VideoRecognition3Handle.ASR asr,QianchuanMaterialVideo qianchuanMaterialVideo){
        //保存结果数据
        CmsVideoResult videoResult = new CmsVideoResult();
        videoResult.setVideoId(videoInfo.getId());
        videoResult.setType(VideoResultTypeEnum.ALL_ASR.getCode());
        videoResult.setData(JSONObject.toJSONString(asr));
        videoResultService.save(videoResult);

        //保存结果数据
        CmsVideoResult videoResult2 = new CmsVideoResult();
        videoResult2.setVideoId(videoInfo.getId());
        videoResult2.setType(VideoResultTypeEnum.ASR5.getCode());
        videoResult2.setData(JSONObject.toJSONString(getFirst5SecondsASR(asr)));
        videoResultService.save(videoResult2);

        qianchuanMaterialVideo.setUpdateTime(new Date());
        qianchuanMaterialVideo.setKwVideoContent(asr.getText());
        qianchuanMaterialVideoService.updateById(qianchuanMaterialVideo);
    }


    /**
     * 获取前5秒的ASR结果
     *
     * @param asr VideoRecognition2Handle.ASR对象
     * @return 拼接后的前5秒ASR文本
     */
    public ASR5 getFirst5SecondsASR(VideoRecognition3Handle.ASR asr) {
        if (asr == null || asr.getSentences() == null || asr.getSentences().isEmpty()) {
            return new ASR5("");
        }

        StringBuilder result = new StringBuilder();
        for (VideoRecognition3Handle.Sentences sentence : asr.getSentences()) {
            if (sentence.getStart() < 3000) { // 保留start小于3000毫秒的句子
                result.append(sentence.getText()).append(" ");
            }
        }

        return new ASR5(result.toString().trim()); // 去除末尾多余的空格
    }


    public String getASR(VideoRecognition3Handle.ASR asr, Integer start, Integer end) {
        if (asr == null || asr.getSentences() == null || asr.getSentences().isEmpty()) {
            return "";
        }
        StringBuilder result = new StringBuilder();
        for (VideoRecognition3Handle.Sentences sentence : asr.getSentences()) {
            if (sentence.getStart() >= start && sentence.getStart() < end) {
                result.append(sentence.getText());
            }

        }
        return result.toString();

    }


    @Data
    @AllArgsConstructor
    public static class ASR5 {
        private String asr5;
    }


    @Data
    public static class Arg {
        //视频分析任务Id
        private String dbUniqueId;

        private String observationId;

        private String compressMp4OSSId;

    }

    @Override
    @Transactional
    public AnalysisVideoResultVO queryResult(AnalysisVideoQueryRequest request) {
        CmsVideoInfo cmsVideoInfo = null;
        QianchuanMaterialVideo qianchuanMaterialVideo = null;
        try {
            //查询千川的数据
            if (StringUtils.isBlank(request.getEsId())) {
                throw new BusinessException("esId不能为空");
            }
            qianchuanMaterialVideo = qianchuanMaterialVideoService.getOne(new LambdaQueryWrapper<QianchuanMaterialVideo>().eq(QianchuanMaterialVideo::getVideoId, request.getEsId()));
            if (qianchuanMaterialVideo == null) {
                throw new BusinessException("esId不能存在");
            }
            cmsVideoInfo = videoInfoService.getOne(new LambdaQueryWrapper<CmsVideoInfo>().eq(CmsVideoInfo::getEsId, request.getEsId()).eq(CmsVideoInfo::getType, AnalysisVideoTypeEnum.QIANCHUAN_VIDEO.getVideoCode()));
            if (cmsVideoInfo == null) {
                throw new BusinessException("没有查询到视频分析结果信息");
            }
            AnalysisVideoResultVO analysisVideoResultVO = new AnalysisVideoResultVO();
            analysisVideoResultVO.setId(cmsVideoInfo.getId());
            analysisVideoResultVO.setStatus(cmsVideoInfo.getStatus());
            return analysisVideoResultVO;
        } catch (Exception e) {
            log.error("视频分析失败", e);
            throw new BusinessException(e.getMessage());
        }

    }


    public AnalysisVideoResultVO queryResult2(String response_body, CmsVideoInfo cmsVideoInfo, QianchuanMaterialVideo qianchuanMaterialVideo) {
        AnalysisVideoResultVO analysisVideoResultVO = new AnalysisVideoResultVO();
        analysisVideoResultVO.setId(cmsVideoInfo.getId());

//        //查询视频整体分析流程
        Arg arg = JSONObject.parseObject(cmsVideoInfo.getArg(), Arg.class);

        //保存整体分析结果数据
        CmsVideoResult videoResult = new CmsVideoResult();
        videoResult.setVideoId(cmsVideoInfo.getId());
        videoResult.setType(VideoResultTypeEnum.INDUSTRY_DECODING2.getCode());
        videoResult.setData(response_body);
        videoResultService.save(videoResult);

        //整体分析结果
        Map<String, Object> industryDecoding = JSONObject.parseObject(response_body, Map.class);

        //取出清洗后的ASR
        VideoRecognition3Handle.ASR asr2=JSONObject.parseObject(JSONObject.toJSONString(industryDecoding.get("clean_asr")),  VideoRecognition3Handle.ASR.class);
        saveASR(cmsVideoInfo, asr2,qianchuanMaterialVideo);


        //黄金3秒打标
        String compressMp4Path = FileDownloadUtil.getPath("compress_" + qianchuanMaterialVideo.getVideoId() + ".mp4");
        String fileName = qianchuanMaterialVideo.getVideoId() + ".mp4";
        String localMp4FilePath = FileDownloadUtil.getPath(fileName);

        //判断localMp4FilePath和compressMp4Path本地是否存在视频，如果不存在就下载
        FileDownloadUtil.downloadFile3(analysisVideoConfig.getDomain() + "/" + qianchuanMaterialVideo.getOssid(), localMp4FilePath);
        FileDownloadUtil.downloadFile3(analysisVideoConfig.getDomain() + "/" + arg.getCompressMp4OSSId(), compressMp4Path);

        VideoRecognition3Handle.ImageDecodingRequest imageDecodingRequest = new VideoRecognition3Handle.ImageDecodingRequest();
        imageDecodingRequest.setImages(List.of(VideoUtil.cutImageOfBase64(compressMp4Path, 1000),
                VideoUtil.cutImageOfBase64(compressMp4Path, 2000),
                VideoUtil.cutImageOfBase64(compressMp4Path, 3000),
                VideoUtil.cutImageOfBase64(compressMp4Path, 4000),
                VideoUtil.cutImageOfBase64(compressMp4Path, 5000)
        ));
        imageDecodingRequest.setTask_queue(TASK_QUEUE);

        CmsVideoResult videoResult2 = videoResultService.getOne(new LambdaQueryWrapper<CmsVideoResult>().eq(CmsVideoResult::getVideoId, cmsVideoInfo.getId()).eq(CmsVideoResult::getType, VideoResultTypeEnum.ASR5.getCode()));
        ASR5 asr5 = JSONObject.toJavaObject(JSONObject.parseObject(videoResult2.getData()), ASR5.class);
        imageDecodingRequest.setSentences(asr5.getAsr5());
        imageDecodingRequest.setTitle(qianchuanMaterialVideo.getTitle());
        imageDecodingRequest.setContent(null);

        /*
         * video_info_type:黄金三秒 task_type：算法接口调用
         */
        CmsVideoAnalysisLog cmsVideoAnalysisLog = new CmsVideoAnalysisLog();
        cmsVideoAnalysisLog.setVideoId(qianchuanMaterialVideo.getVideoId());
        cmsVideoAnalysisLog.setVideoInfoType(VideoAnalysisLogTypeEnum.GOLDEN_3_SECOND.getDesc());
        cmsVideoAnalysisLog.setTaskType(VideoAnalysisLogTaskTypeEnum.ALGORITHM.getDesc());
        long algorithmStartTime = System.currentTimeMillis();
        VideoRecognition3Handle.ImageDecodingResponse imageDecodingResponse = videoRecognition3Handle.imageDecoding3s(imageDecodingRequest, "observationId");
        long algorithmEndTime = System.currentTimeMillis();
        cmsVideoAnalysisLog.setDbUniqueId(Long.valueOf(imageDecodingResponse.getData().getDb_unique_id()));
        cmsVideoAnalysisLog.setTime((int) (algorithmEndTime - algorithmStartTime));
        cmsVideoAnalysisService.save(cmsVideoAnalysisLog);
        //保存黄金3秒的打标
        cmsAsyncTaskService.save(imageDecodingResponse.getData().getDb_unique_id(), AnalysisVideoTypeEnum.QIANCHUAN_VIDEO.getVideoCode(), 1, qianchuanMaterialVideo.getVideoId(), "async_image_decoding_3s");


        qianchuanMaterialVideo.setProductName(joinWithSemicolon(industryDecoding.get("产品名称") + ""));
        qianchuanMaterialVideo.setCellingPoint(joinWithSemicolon(industryDecoding.get("卖点") + ""));
        qianchuanMaterialVideo.setAimingTribe(joinWithSemicolon(industryDecoding.get("受众人群") + ""));
//        qianchuanMaterialVideo.setHighlight(imageDecodingResponse.getData().getIs_highlight());


        // 6: 视频分割接口
        CmsVideoResult videoResultAsr = videoResultService.getOne(new LambdaQueryWrapper<CmsVideoResult>().eq(CmsVideoResult::getVideoId, cmsVideoInfo.getId()).eq(CmsVideoResult::getType, VideoResultTypeEnum.ALL_ASR.getCode()));
        VideoRecognition3Handle.ASR asr = JSONObject.parseObject(videoResultAsr.getData(), VideoRecognition3Handle.ASR.class);
        VideoRecognition3Handle.VideoSplitRequest videoSplitRequest = new VideoRecognition3Handle.VideoSplitRequest();

        videoSplitRequest.setVideo_url(analysisVideoConfig.getDomain() + "/" + qianchuanMaterialVideo.getOssid());
        videoSplitRequest.setVideo_asr(asr);
        videoSplitRequest.setTask_queue(TASK_QUEUE);
        /*
        * video_info_type:视频分割 task_type:算法接口调用
         */
        CmsVideoAnalysisLog cmsVideoAnalysisLog1 = new CmsVideoAnalysisLog();
        cmsVideoAnalysisLog1.setVideoId(qianchuanMaterialVideo.getVideoId());
        cmsVideoAnalysisLog1.setVideoInfoType(VideoAnalysisLogTypeEnum.VIDEO_SEGMENTATION.getDesc());
        cmsVideoAnalysisLog1.setTaskType(VideoAnalysisLogTaskTypeEnum.ALGORITHM.getDesc());
        long algorithmStartTime1 = System.currentTimeMillis();
        VideoRecognition3Handle.VideoSplitResponse videoSplitResponse = videoRecognition3Handle.videoSplit(videoSplitRequest, "observationId");
        long algorithmEndTime1 = System.currentTimeMillis();
        cmsVideoAnalysisLog1.setDbUniqueId(Long.valueOf(videoSplitResponse.getData().getDb_unique_id()));
        cmsVideoAnalysisLog1.setTime((int) (algorithmEndTime1 - algorithmStartTime1));
        cmsVideoAnalysisService.save(cmsVideoAnalysisLog1);

        cmsAsyncTaskService.save(videoSplitResponse.getData().getDb_unique_id(), AnalysisVideoTypeEnum.QIANCHUAN_VIDEO.getVideoCode(), 1, qianchuanMaterialVideo.getVideoId(), "async_video_split");


//        //更新黄金3秒类型
        cmsVideoInfo.setRating(industryDecoding.get("创意得分") + "");
        cmsVideoInfo.setUpdateTime(new Date());
        videoInfoService.updateById(cmsVideoInfo);

        qianchuanMaterialVideo.setRating(Float.parseFloat(cmsVideoInfo.getRating()));
        qianchuanMaterialVideo.setUpdateTime(new Date());
        qianchuanMaterialVideoService.updateById(qianchuanMaterialVideo);

        return analysisVideoResultVO;
    }


    private void handleVideoSplitData(VideoRecognition3Handle.VideoSplitData splitData, String compressMp4Path, VideoRecognition3Handle.ASR asr, QianchuanMaterialVideo qianchuanMaterialVideo, String localMp4FilePath, CmsVideoInfo cmsVideoInfo) {
        Map<String, VideoRecognition3Handle.ASRFragment> map = splitData.getASR();
        List<ASRFragment> asrFragmentList = handleUserASRPre(map, qianchuanMaterialVideo.getVideoId(), true);

        //存入Result表
        CmsVideoResult videoResult = new CmsVideoResult();
        videoResult.setVideoId(cmsVideoInfo.getId());
        videoResult.setType(VideoResultTypeEnum.VIDEO_SPLIT_ASR.getCode());
        videoResult.setData(JSONObject.toJSONString(asrFragmentList));
        videoResultService.save(videoResult);

        List<List<Integer>> list = splitData.get画面分镜();
        Long videoLength = VideoUtil.getVideoLength(compressMp4Path) * 1000;
        //更新千川视频时长
        qianchuanMaterialVideo.setDuration(videoLength.intValue()/1000);
        qianchuanMaterialVideo.setUpdateTime(new Date());
        qianchuanMaterialVideoService.updateById(qianchuanMaterialVideo);

        int index = 0;
        for (List<Integer> time : list) {
            Integer start = time.get(0);
            Integer end = time.get(1);
            if (end > videoLength) {
                end = videoLength.intValue();
            }
            //5: 分镜图片打标接口
            VideoRecognition3Handle.AsyncData asyncData = sceneDecoding(compressMp4Path, asr, start, end, qianchuanMaterialVideo);
            index++;
            Map<String,String> data=new HashMap<>();
            data.put("index",index+"");
            data.put("start",start+"");
            data.put("end",end+"");
            cmsAsyncTaskService.save(asyncData.getDb_unique_id(), AnalysisVideoTypeEnum.QIANCHUAN_VIDEO.getVideoCode(), 1, qianchuanMaterialVideo.getVideoId(), "async_scene_decoding",  JSONObject.toJSONString(data));
        }
    }


    public List<ASRFragment> handleUserASRPre(Map<String, VideoRecognition3Handle.ASRFragment> map, String esId, Boolean isOpen) {
        if (map == null || map.isEmpty()) {
            return new ArrayList<>();
        }
        // 假设 map 的 key 是时间戳，value 是 ASR 片段
        List<ASRFragment> asrFragments = new ArrayList<>();
        for (Map.Entry<String, VideoRecognition3Handle.ASRFragment> entry : map.entrySet()) {
            VideoRecognition3Handle.ASRFragment fragment = entry.getValue();

            // 获取开始和结束时间
            List<Integer> timeStamps = fragment.getTime_stamp();
            if (timeStamps == null || timeStamps.size() < 2) {
                log.warn("Invalid ASR fragment time stamps for timestamp: {}", timeStamps);
                continue;
            }
            Integer start = timeStamps.get(0);
            Integer end = timeStamps.get(1);

            // 获取压缩视频路径
            String localMp4FilePath = FileDownloadUtil.getPath(esId + ".mp4");

            // 切视频片段
            String fileName = "segment_asr_" + esId + "_" + start + "_" + end + ".mp4";
            String segmentFilePath = FileDownloadUtil.getPath(fileName);
            log.info("compressMp4Path:{}, segmentFilePath:{}, start:{}, end:{}", localMp4FilePath, segmentFilePath, start, end);
            /*
            * video_info_type:视频分割 task_type：视频分割
             */
            CmsVideoAnalysisLog cmsVideoAnalysisLog = new CmsVideoAnalysisLog();
            cmsVideoAnalysisLog.setVideoId(esId);
            cmsVideoAnalysisLog.setVideoInfoType(VideoAnalysisLogTypeEnum.VIDEO_SEGMENTATION.getDesc());
            cmsVideoAnalysisLog.setTaskType(VideoAnalysisLogTaskTypeEnum.SEGMENTATION.getDesc());
            long cutVideoSegmentStart = System.currentTimeMillis();
            VideoUtil.cutVideoSegment(localMp4FilePath, segmentFilePath, start, end);
            long cutVideoSegmentEnd = System.currentTimeMillis();
            cmsVideoAnalysisLog.setTime((int) (cutVideoSegmentEnd - cutVideoSegmentStart));
            cmsVideoAnalysisService.save(cmsVideoAnalysisLog);

            // 上传视频片段到OSS
            String ossId = FilePathBuilder.getVideoDecodeOssPath(esId, fileName, isOpen);

            /*
             * video_info_type:视频分割 task_type：文件上传
             */
            CmsVideoAnalysisLog cmsVideoAnalysisLog3 = new CmsVideoAnalysisLog();
            cmsVideoAnalysisLog3.setVideoId(esId);
            cmsVideoAnalysisLog3.setVideoInfoType(VideoAnalysisLogTypeEnum.VIDEO_SEGMENTATION.getDesc());
            cmsVideoAnalysisLog3.setTaskType(VideoAnalysisLogTaskTypeEnum.UPLOAD.getDesc());
            long uploadVideoStart1 = System.currentTimeMillis();
            uploadVideo(ossId, segmentFilePath);
            long uploadVideoEnd1 = System.currentTimeMillis();
            cmsVideoAnalysisLog3.setTime((int)(uploadVideoEnd1 - uploadVideoStart1));
            cmsVideoAnalysisService.save(cmsVideoAnalysisLog3);
            //切除视频封面
            String picFileName = "pic_asr_" + esId + "_" + start + "_" + end + ".jpg";
            String picFilePath = FileDownloadUtil.getPath(picFileName);
            /*
             * video_info_type:视频分割 task_type：切帧处理
             */
            CmsVideoAnalysisLog cmsVideoAnalysisLog1 = new CmsVideoAnalysisLog();
            cmsVideoAnalysisLog1.setVideoId(esId);
            cmsVideoAnalysisLog1.setVideoInfoType(VideoAnalysisLogTypeEnum.VIDEO_SEGMENTATION.getDesc());
            cmsVideoAnalysisLog1.setTaskType(VideoAnalysisLogTaskTypeEnum.FRAME_CUTTING.getDesc());
            long cutImageStart = System.currentTimeMillis();
            VideoUtil.cutImage(segmentFilePath, picFilePath, 0);
            long cutImageEnd = System.currentTimeMillis();
            cmsVideoAnalysisLog1.setTime((int) (cutImageEnd - cutImageStart));
            cmsVideoAnalysisService.save(cmsVideoAnalysisLog1);
            String picOssId = FilePathBuilder.getVideoDecodeOssPath(esId, picFileName, isOpen);
            /*
             * video_info_type:分镜切割 task_type：文件上传
             */
            CmsVideoAnalysisLog cmsVideoAnalysisLog2 = new CmsVideoAnalysisLog();
            cmsVideoAnalysisLog2.setVideoId(esId);
            cmsVideoAnalysisLog2.setVideoInfoType(VideoAnalysisLogTypeEnum.VIDEO_SEGMENTATION.getDesc());
            cmsVideoAnalysisLog2.setTaskType(VideoAnalysisLogTaskTypeEnum.UPLOAD.getDesc());
            long uploadStart = System.currentTimeMillis();
            uploadVideo(picOssId, picFilePath);
            long uploadEnd = System.currentTimeMillis();
            cmsVideoAnalysisLog2.setTime((int) (uploadEnd - uploadStart));
            cmsVideoAnalysisService.save(cmsVideoAnalysisLog2);

            ASRFragment asrFragment = new ASRFragment();
            asrFragment.setText(fragment.getText());
            asrFragment.setStart(start);
            asrFragment.setEnd(end);
            asrFragment.setOssId(ossId);
            asrFragment.setPicOssId(picOssId);
            asrFragments.add(asrFragment);

            // 删除本地的素材
            FileDownloadUtil.deleteFile(segmentFilePath);
            FileDownloadUtil.deleteFile(picFilePath);

        }
        return asrFragments;
    }


    @Data
    public static class ASRFragment {
        private String text;
        private Integer start;
        private Integer end;
        private String ossId;
        private String picOssId;
    }


    public void handleUserASR(Map<String, String> sceneDecoding, List<ASRFragment> asrFragments) {
        // 遍历每个 ASR 片段
        for (ASRFragment fragment : asrFragments) {
            Integer fragmentStart = fragment.getStart();
            Integer fragmentEnd = fragment.getEnd();

            // 获取 sceneDecoding 中的 start 和 end 时间
            Integer start = Integer.parseInt(sceneDecoding.get("start"));
            Integer end = Integer.parseInt(sceneDecoding.get("end"));

            // 计算重叠部分
            Integer overlapStart = Math.max(start, fragmentStart);
            Integer overlapEnd = Math.min(end, fragmentEnd);
            Integer overlapDuration = overlapEnd - overlapStart;

            // 如果有重叠
            if (overlapDuration > 0) {
                sceneDecoding.put("台词", fragment.getText());
                sceneDecoding.put("sceneDecodingOssId", fragment.getOssId());
                sceneDecoding.put("sceneDecodingPicOssId", fragment.getPicOssId());
                return;
            }
        }
    }


    private VideoRecognition3Handle.AsyncData sceneDecoding(String compressMp4Path, VideoRecognition3Handle.ASR asr, Integer start, Integer end, QianchuanMaterialVideo qianchuanMaterialVideo) {
        // 5: 分镜图片打标接口
        VideoRecognition3Handle.SceneDecodingRequest sceneDecodingRequest = new VideoRecognition3Handle.SceneDecodingRequest();
        sceneDecodingRequest.setImages(List.of(
                VideoUtil.cutImageOfBase64(compressMp4Path, start),
                VideoUtil.cutImageOfBase64(compressMp4Path, (start + end) / 2),
                VideoUtil.cutImageOfBase64(compressMp4Path, end)
        ));
        sceneDecodingRequest.setSentences(getASR(asr, start, end));
        sceneDecodingRequest.setTitle(qianchuanMaterialVideo.getTitle());
        sceneDecodingRequest.setContent(null);
        sceneDecodingRequest.setTask_queue(TASK_QUEUE);
        /*
         * video_info_type:分镜打标 task_type:算法接口调用
         */
        CmsVideoAnalysisLog cmsVideoAnalysisLog = new CmsVideoAnalysisLog();
        cmsVideoAnalysisLog.setVideoId(qianchuanMaterialVideo.getVideoId());
        cmsVideoAnalysisLog.setVideoInfoType(VideoAnalysisLogTypeEnum.SHOT_MARKING.getDesc());
        cmsVideoAnalysisLog.setTaskType(VideoAnalysisLogTaskTypeEnum.ALGORITHM.getDesc());
        long shotMarkingStartTime = System.currentTimeMillis();
        VideoRecognition3Handle.SceneDecodingResponse sceneDecodingResponse = videoRecognition3Handle.sceneDecoding(sceneDecodingRequest, "observationId");
        long shotMarkingEndTime = System.currentTimeMillis();
        cmsVideoAnalysisLog.setDbUniqueId(Long.valueOf(sceneDecodingResponse.getData().getDb_unique_id()));
        cmsVideoAnalysisLog.setTime((int) (shotMarkingEndTime - shotMarkingStartTime));
        cmsVideoAnalysisService.save(cmsVideoAnalysisLog);
        return sceneDecodingResponse.getData();
    }


    private void uploadVideo(String ossId, String localFilePath) {
        try {
            cmsS3FlowService.upload(ossId, new File(localFilePath));
        } catch (Exception e) {
            log.error("上传视频图片失败", e);
            throw new BusinessException("上传视频图片失败");
        }
    }

    public String joinWithSemicolon(String data) {
        if (data == null || StringUtils.isBlank(data)) {
            return "";
        }
        List<String> items = JSONObject.parseArray(data, String.class);
        if (items == null || items.isEmpty()) {
            return "";
        }
        return String.join("；", items);
    }



    @Override
    @Transactional
    public void processAsyncTask(CmsAsyncTask cmsAsyncTask, AsyncResultRequest asyncResultRequest) {
        Long dbUniqueId = cmsAsyncTask.getDbUniqueId();
        LambdaQueryWrapper<CmsVideoAnalysisLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CmsVideoAnalysisLog::getDbUniqueId, dbUniqueId);
        CmsVideoAnalysisLog cmsVideoAnalysisLog = cmsVideoAnalysisMapper.selectOne(queryWrapper);
        switch (cmsAsyncTask.getTaskType()) {
            case "async_video_flow_v2":
                if(cmsVideoAnalysisLog != null){
                    long time = System.currentTimeMillis()-cmsVideoAnalysisLog.getCreateTime().getTime();
                    cmsVideoAnalysisLog.setTime((int) time);
                    cmsVideoAnalysisLog.setUpdateTime(new Date());
                    cmsVideoAnalysisMapper.updateById(cmsVideoAnalysisLog);
                }
                processVideoFlowV2(cmsAsyncTask, asyncResultRequest);
                break;
            case "async_video_split":
                if (cmsVideoAnalysisLog != null){
                    long time1 = System.currentTimeMillis()-cmsVideoAnalysisLog.getCreateTime().getTime();
                    cmsVideoAnalysisLog.setTime((int) time1);
                    cmsVideoAnalysisLog.setUpdateTime(new Date());
                    cmsVideoAnalysisMapper.updateById(cmsVideoAnalysisLog);
                }
                processVideoSplit(cmsAsyncTask, asyncResultRequest);
                break;
            case "async_scene_decoding":
                if(cmsVideoAnalysisLog != null){
                    long time2 = System.currentTimeMillis()-cmsVideoAnalysisLog.getCreateTime().getTime();
                    cmsVideoAnalysisLog.setTime((int) time2);
                    cmsVideoAnalysisLog.setUpdateTime(new Date());
                    cmsVideoAnalysisMapper.updateById(cmsVideoAnalysisLog);
                }
                processSceneDecoding(cmsAsyncTask, asyncResultRequest);
                break;
            case "async_image_decoding_3s":
                if(cmsVideoAnalysisLog != null){
                    long time3 = System.currentTimeMillis()-cmsVideoAnalysisLog.getCreateTime().getTime();
                    cmsVideoAnalysisLog.setTime((int) time3);
                    cmsVideoAnalysisLog.setUpdateTime(new Date());
                    cmsVideoAnalysisMapper.updateById(cmsVideoAnalysisLog);
                }
                processImageDecoding3s(cmsAsyncTask, asyncResultRequest);
                break;
            default:
                break;
        }
    }

    //异步整体视频理解接口
    public void processVideoFlowV2(CmsAsyncTask cmsAsyncTask, AsyncResultRequest asyncResultRequest) {
        log.info("开始处理视频整体理解任务: {}", cmsAsyncTask.getEsId());
        try {
            // 1. 查询视频信息
            CmsVideoInfo videoInfo = videoInfoService.getOne(
                    new LambdaQueryWrapper<CmsVideoInfo>()
                            .eq(CmsVideoInfo::getEsId, cmsAsyncTask.getEsId())
                            .eq(CmsVideoInfo::getType, cmsAsyncTask.getVideoInfoType()));
            if (videoInfo == null) {
                throw new BusinessException("未找到对应的视频分析信息");
            }

            // 2. 解析异步结果
            String responseBody = JSONObject.toJSONString(asyncResultRequest.getData());

            // 3. 后续可继续触发其他异步任务（如黄金三秒打标）
            QianchuanMaterialVideo qianchuanMaterialVideo = qianchuanMaterialVideoService.getOne(new LambdaQueryWrapper<QianchuanMaterialVideo>().eq(QianchuanMaterialVideo::getVideoId, cmsAsyncTask.getEsId()));
            queryResult2(responseBody, videoInfo,qianchuanMaterialVideo);

            cmsAsyncTask.setTaskStatus(2);
            cmsAsyncTask.setUpdateTime(LocalDateTime.now());
            cmsAsyncTaskService.updateById(cmsAsyncTask);
        } catch (Exception e) {
            log.error("视频整体理解任务失败", e);
            videoInfoService.updateVideoInfoStatusFailed(videoInfoService.getOne(new LambdaQueryWrapper<CmsVideoInfo>().eq(CmsVideoInfo::getEsId, cmsAsyncTask.getEsId())).getId(), e.getMessage());
            handleTaskError(cmsAsyncTask);
        }
    }


    //分镜的消息
    public void processVideoSplit(CmsAsyncTask cmsAsyncTask, AsyncResultRequest asyncResultRequest) {
        log.info("开始处理视频分镜任务: {}", cmsAsyncTask.getEsId());

        try {
            // 1. 查询视频信息
            CmsVideoInfo videoInfo = videoInfoService.getOne(
                    new LambdaQueryWrapper<CmsVideoInfo>()
                            .eq(CmsVideoInfo::getEsId, cmsAsyncTask.getEsId())
                            .eq(CmsVideoInfo::getType, cmsAsyncTask.getVideoInfoType()));
            if (videoInfo == null) {
                throw new BusinessException("未找到对应的视频分析信息");
            }

            //视频分割数据存储
            CmsVideoResult videoResult4 = new CmsVideoResult();
            videoResult4.setVideoId(videoInfo.getId());
            videoResult4.setType(VideoResultTypeEnum.VIDEO_SPLIT.getCode());
            videoResult4.setData(JSONObject.toJSONString(asyncResultRequest.getData()));
            videoResultService.save(videoResult4);

            // 3. 处理分镜数据并保存
            CmsVideoResult videoResultAsr = videoResultService.getOne(new LambdaQueryWrapper<CmsVideoResult>().eq(CmsVideoResult::getVideoId, videoInfo.getId()).eq(CmsVideoResult::getType, VideoResultTypeEnum.ALL_ASR.getCode()));
            VideoRecognition3Handle.ASR asr = JSONObject.parseObject(videoResultAsr.getData(), VideoRecognition3Handle.ASR.class);
            QianchuanMaterialVideo qianchuanMaterialVideo = qianchuanMaterialVideoService.getOne(new LambdaQueryWrapper<QianchuanMaterialVideo>().eq(QianchuanMaterialVideo::getVideoId, cmsAsyncTask.getEsId()));

            String compressMp4Path = FileDownloadUtil.getPath("compress_" + qianchuanMaterialVideo.getVideoId() + ".mp4");
            String fileName = qianchuanMaterialVideo.getVideoId() + ".mp4";
            String localMp4FilePath = FileDownloadUtil.getPath(fileName);

            Arg arg = JSONObject.parseObject(videoInfo.getArg(), Arg.class);

            //判断localMp4FilePath和compressMp4Path本地是否存在视频，如果不存在就下载
            FileDownloadUtil.downloadFile3(analysisVideoConfig.getDomain() + "/" + qianchuanMaterialVideo.getOssid(), localMp4FilePath);
            FileDownloadUtil.downloadFile3(analysisVideoConfig.getDomain() + "/" + arg.getCompressMp4OSSId(), compressMp4Path);

            VideoRecognition3Handle.VideoSplitData splitData = JSONObject.parseObject(JSONObject.toJSONString(asyncResultRequest.getData()), VideoRecognition3Handle.VideoSplitData.class);

            handleVideoSplitData(splitData, compressMp4Path, asr, qianchuanMaterialVideo, localMp4FilePath, videoInfo);

            cmsAsyncTask.setTaskStatus(2);
            cmsAsyncTask.setUpdateTime(LocalDateTime.now());
            cmsAsyncTaskService.updateById(cmsAsyncTask);
        } catch (Exception e) {
            log.error("视频分镜任务失败", e);
            videoInfoService.updateVideoInfoStatusFailed(videoInfoService.getOne(new LambdaQueryWrapper<CmsVideoInfo>().eq(CmsVideoInfo::getEsId, cmsAsyncTask.getEsId())).getId(), e.getMessage());
            handleTaskError(cmsAsyncTask);
        }
    }


    //异步分镜图片打标接口
    public void processSceneDecoding(CmsAsyncTask cmsAsyncTask, AsyncResultRequest asyncResultRequest) {
        log.info("开始处理分镜图片打标任务: {}", cmsAsyncTask.getEsId());
        String picFilePath=null;
        String segmentFilePath=null;
        try {
            // 1. 查询视频信息
            CmsVideoInfo videoInfo = videoInfoService.getOne(
                    new LambdaQueryWrapper<CmsVideoInfo>()
                            .eq(CmsVideoInfo::getEsId, cmsAsyncTask.getEsId())
                            .eq(CmsVideoInfo::getType, cmsAsyncTask.getVideoInfoType()));
            if (videoInfo == null) {
                throw new BusinessException("未找到对应的视频分析信息");
            }
            Map<String,String> map= JSONObject.parseObject(cmsAsyncTask.getData(),Map.class);
            int start = Integer.parseInt(map.get("start"));
            int end = Integer.parseInt(map.get("end"));
            int index = Integer.parseInt(map.get("index"));

            // 2. 解析异步结果
            Map<String, String> sceneDecoding =(Map<String, String>)asyncResultRequest.getData();
            QianchuanMaterialVideo qianchuanMaterialVideo = qianchuanMaterialVideoService.getOne(new LambdaQueryWrapper<QianchuanMaterialVideo>().eq(QianchuanMaterialVideo::getVideoId, cmsAsyncTask.getEsId()));

            sceneDecoding.put("start", start + "");
            sceneDecoding.put("end", end + "");

            String compressMp4Path = FileDownloadUtil.getPath("compress_" + qianchuanMaterialVideo.getVideoId() + ".mp4");
            String fileName = qianchuanMaterialVideo.getVideoId() + ".mp4";
            String localMp4FilePath = FileDownloadUtil.getPath(fileName);

            Arg arg = JSONObject.parseObject(videoInfo.getArg(), Arg.class);

            //判断localMp4FilePath和compressMp4Path本地是否存在视频，如果不存在就下载
            FileDownloadUtil.downloadFile3(analysisVideoConfig.getDomain() + "/" + qianchuanMaterialVideo.getOssid(), localMp4FilePath);
            FileDownloadUtil.downloadFile3(analysisVideoConfig.getDomain() + "/" + arg.getCompressMp4OSSId(), compressMp4Path);


            //切视频片段
            String fileName2 = "segment_" + qianchuanMaterialVideo.getVideoId() + "_" + start + "_" + end + ".mp4";
            segmentFilePath = FileDownloadUtil.getPath(fileName2);
            log.info("localMp4FilePath:{},segmentFilePath:{},start:{},end:{}", localMp4FilePath, segmentFilePath, start, end);
            /*
            * video_info_type:视频分割 task_type:视频分割
             */
            CmsVideoAnalysisLog cmsVideoAnalysisLog = new CmsVideoAnalysisLog();
            cmsVideoAnalysisLog.setVideoId(qianchuanMaterialVideo.getVideoId());
            cmsVideoAnalysisLog.setVideoInfoType(VideoAnalysisLogTypeEnum.VIDEO_SEGMENTATION.getDesc());
            cmsVideoAnalysisLog.setTaskType(VideoAnalysisLogTypeEnum.VIDEO_SEGMENTATION.getDesc());
            long cutVideoStart = System.currentTimeMillis();
            VideoUtil.cutVideoSegment(localMp4FilePath, segmentFilePath, start, end);
            long cutVideoEnd = System.currentTimeMillis();
            cmsVideoAnalysisLog.setTime((int)(cutVideoEnd - cutVideoStart));
            cmsVideoAnalysisService.save(cmsVideoAnalysisLog);
            //上传视频片段到OSS
            String ossId = FilePathBuilder.getOpenVideoDecodeOssPath(qianchuanMaterialVideo.getVideoId(), fileName2);

            /*
             * video_info_type:分镜打标 task_type:文件上传
             */
            CmsVideoAnalysisLog cmsVideoAnalysisLog1 = new CmsVideoAnalysisLog();
            cmsVideoAnalysisLog1.setVideoId(qianchuanMaterialVideo.getVideoId());
            cmsVideoAnalysisLog1.setVideoInfoType(VideoAnalysisLogTypeEnum.SHOT_MARKING.getDesc());
            cmsVideoAnalysisLog1.setTaskType(VideoAnalysisLogTaskTypeEnum.UPLOAD.getDesc());
            long uploadVideoStart = System.currentTimeMillis();
            uploadVideo(ossId, segmentFilePath);
            long uploadVideoEnd = System.currentTimeMillis();
            cmsVideoAnalysisLog1.setTime((int)(uploadVideoEnd - uploadVideoStart));
            cmsVideoAnalysisService.save(cmsVideoAnalysisLog1);

            sceneDecoding.put("sceneDecodingSegmentOssId", ossId);
            //切出视频头图
            String picFileName = "pic_" + qianchuanMaterialVideo.getVideoId() + "_" + start + "_" + end + ".jpg";
            picFilePath = FileDownloadUtil.getPath(picFileName);
            /*
             * video_info_type:视频分割 task_type:切帧处理
             */
            CmsVideoAnalysisLog cmsVideoAnalysisLog2 = new CmsVideoAnalysisLog();
            cmsVideoAnalysisLog2.setVideoId(qianchuanMaterialVideo.getVideoId());
            cmsVideoAnalysisLog2.setVideoInfoType(VideoAnalysisLogTypeEnum.VIDEO_SEGMENTATION.getDesc());
            cmsVideoAnalysisLog2.setTaskType(VideoAnalysisLogTaskTypeEnum.FRAME_CUTTING.getDesc());
            long cutImageStart = System.currentTimeMillis();
            VideoUtil.cutImage(segmentFilePath, picFilePath, 0);
            long cutImageEnd = System.currentTimeMillis();
            cmsVideoAnalysisLog2.setTime((int)(cutImageEnd - cutImageStart));
            cmsVideoAnalysisService.save(cmsVideoAnalysisLog2);

            String picOssId = FilePathBuilder.getOpenVideoDecodeOssPath(qianchuanMaterialVideo.getVideoId(), picFileName);
            sceneDecoding.put("sceneDecodingSegmentPicOssId", picOssId);

            /*
             * video_info_type:分镜打标 task_type:文件上传
             */
            CmsVideoAnalysisLog cmsVideoAnalysisLog3 = new CmsVideoAnalysisLog();
            cmsVideoAnalysisLog3.setVideoId(qianchuanMaterialVideo.getVideoId());
            cmsVideoAnalysisLog3.setVideoInfoType(VideoAnalysisLogTypeEnum.SHOT_MARKING.getDesc());
            cmsVideoAnalysisLog3.setTaskType(VideoAnalysisLogTaskTypeEnum.UPLOAD.getDesc());
            long uploadVideoStart1 = System.currentTimeMillis();
            uploadVideo(picOssId, picFilePath);
            long uploadVideoEnd1 = System.currentTimeMillis();
            cmsVideoAnalysisLog3.setTime((int)(uploadVideoEnd1 - uploadVideoStart1));
            cmsVideoAnalysisService.save(cmsVideoAnalysisLog3);


            if (start < 5000) {
                sceneDecoding.put("highlight", qianchuanMaterialVideo.getHighlight() + "");
            }

            //从数据库查出ASR片段
            CmsVideoResult videoResultAsr = videoResultService.getOne(new LambdaQueryWrapper<CmsVideoResult>().eq(CmsVideoResult::getVideoId, videoInfo.getId()).eq(CmsVideoResult::getType,VideoResultTypeEnum.VIDEO_SPLIT.getCode()));
            VideoRecognition3Handle.VideoSplitData splitData = JSONObject.parseObject(videoResultAsr.getData(), VideoRecognition3Handle.VideoSplitData.class);
            CmsVideoResult videoResultASR2 = videoResultService.getOne(new LambdaQueryWrapper<CmsVideoResult>().eq(CmsVideoResult::getVideoId, videoInfo.getId()).eq(CmsVideoResult::getType,VideoResultTypeEnum.VIDEO_SPLIT_ASR.getCode()));
            List<ASRFragment> asrFragmentList= JSONObject.parseArray(videoResultASR2.getData(), ASRFragment.class);

            //把ASR片段加入到这个里面
            if (splitData.isUse_ASR()) {
                //加入台词，视频ASR分片信息
                handleUserASR(sceneDecoding, asrFragmentList);
            } else {
                sceneDecoding.put("sceneDecodingOssId", ossId);
                sceneDecoding.put("sceneDecodingPicOssId", picOssId);
            }

            //保存数据
            CmsVideoResult videoResult = new CmsVideoResult();
            videoResult.setVideoId(videoInfo.getId());
            videoResult.setIndex(index);
            videoResult.setType(VideoResultTypeEnum.SCENE_SPLIT2.getCode());
            videoResult.setData(JSONObject.toJSONString(sceneDecoding));
            videoResultService.save(videoResult);

            //更新异步任务
            cmsAsyncTask.setTaskStatus(2);
            cmsAsyncTask.setUpdateTime(LocalDateTime.now());
            cmsAsyncTaskService.updateById(cmsAsyncTask);

            handleStatus(videoInfo,cmsAsyncTask);
        } catch (Exception e) {
            log.error("分镜图片打标任务失败", e);
            videoInfoService.updateVideoInfoStatusFailed(videoInfoService.getOne(new LambdaQueryWrapper<CmsVideoInfo>().eq(CmsVideoInfo::getEsId, cmsAsyncTask.getEsId())).getId(), e.getMessage());
            handleTaskError(cmsAsyncTask);
        }finally {
            //删除本地的素材
            FileDownloadUtil.deleteFile(picFilePath);
            FileDownloadUtil.deleteFile(segmentFilePath);
        }
    }




    //异步黄金三秒
    public void processImageDecoding3s(CmsAsyncTask cmsAsyncTask, AsyncResultRequest asyncResultRequest) {
        log.info("开始处理黄金三秒打标任务: {}", cmsAsyncTask.getEsId());

        try {
            // 1. 查询视频信息
            CmsVideoInfo videoInfo = videoInfoService.getOne(
                    new LambdaQueryWrapper<CmsVideoInfo>()
                            .eq(CmsVideoInfo::getEsId, cmsAsyncTask.getEsId())
                            .eq(CmsVideoInfo::getType, cmsAsyncTask.getVideoInfoType()));
            if (videoInfo == null) {
                throw new BusinessException("未找到对应的视频分析信息");
            }

            // 2. 解析异步结果
            Map<String, Object> data = (Map<String, Object>) asyncResultRequest.getData();

            // 3. 保存黄金三秒结果
            CmsVideoResult videoResult = new CmsVideoResult();
            videoResult.setVideoId(videoInfo.getId());
            videoResult.setType(VideoResultTypeEnum.GOLD_FIVE2.getCode());
            videoResult.setData(JSONObject.toJSONString(asyncResultRequest.getData()));
            videoResultService.save(videoResult);

            // 4. 更新视频信息中的黄金三秒类型字段
            videoInfo.setThreeGoldType((String) data.get("highlight_type"));
            videoInfoService.updateById(videoInfo);

            //更新千川的数据
            QianchuanMaterialVideo qianchuanMaterialVideo = qianchuanMaterialVideoService.getOne(new LambdaQueryWrapper<QianchuanMaterialVideo>().eq(QianchuanMaterialVideo::getVideoId, cmsAsyncTask.getEsId()));
            qianchuanMaterialVideo.setHighlight((Integer) data.get("is_highlight"));
            qianchuanMaterialVideo.setUpdateTime(new Date());
            qianchuanMaterialVideoService.updateById(qianchuanMaterialVideo);

            //更新异步任务表的数据
            cmsAsyncTask.setTaskStatus(2);
            cmsAsyncTask.setUpdateTime(LocalDateTime.now());
            cmsAsyncTaskService.updateById(cmsAsyncTask);


            log.info("黄金三秒打标任务完成: {}", cmsAsyncTask.getEsId());

            handleStatus(videoInfo,cmsAsyncTask);


        } catch (Exception e) {
            log.error("黄金三秒打标任务失败", e);
            videoInfoService.updateVideoInfoStatusFailed(videoInfoService.getOne(new LambdaQueryWrapper<CmsVideoInfo>().eq(CmsVideoInfo::getEsId, cmsAsyncTask.getEsId())).getId(), e.getMessage());
            handleTaskError(cmsAsyncTask);
        }
    }


    private void  handleStatus(CmsVideoInfo videoInfo,CmsAsyncTask cmsAsyncTask){
        QianchuanMaterialVideo qianchuanMaterialVideo = qianchuanMaterialVideoService.getOne(new LambdaQueryWrapper<QianchuanMaterialVideo>().eq(QianchuanMaterialVideo::getVideoId, cmsAsyncTask.getEsId()));
        Boolean  status = cmsAsyncTaskService.AllStatusSuccess(cmsAsyncTask.getEsId(),cmsAsyncTask.getVideoInfoType());
        if(status){
            videoInfo.setStatus(3);
            videoInfo.setUpdateTime(new Date());
            videoInfoService.updateById(videoInfo);

            qianchuanMaterialVideo.setAnalysisStatus(2);
            qianchuanMaterialVideo.setUpdateTime(new Date());
            qianchuanMaterialVideoService.updateById(qianchuanMaterialVideo);

            Arg arg = JSONObject.parseObject(videoInfo.getArg(), Arg.class);

            String esId= cmsAsyncTask.getEsId();
            //删除本地视频
            String compressMp4Path = FileDownloadUtil.getPath("compress_" + esId + ".mp4");
            String fileName = esId + ".mp4";
            String localMp4FilePath = FileDownloadUtil.getPath(fileName);
            FileDownloadUtil.deleteFile(localMp4FilePath);
            FileDownloadUtil.deleteFile(compressMp4Path);

            //删除oss视频
            try {
                fileService.deleteFile(arg.getCompressMp4OSSId());
            } catch (Exception e) {
                log.error("删除oss视频失败:{}", arg.getCompressMp4OSSId());
            }

        }
    }

    private void handleTaskError(CmsAsyncTask cmsAsyncTask) {
        cmsAsyncTask.setTaskStatus(3);
        cmsAsyncTask.setUpdateTime(LocalDateTime.now());
        cmsAsyncTaskService.updateById(cmsAsyncTask);
    }


}