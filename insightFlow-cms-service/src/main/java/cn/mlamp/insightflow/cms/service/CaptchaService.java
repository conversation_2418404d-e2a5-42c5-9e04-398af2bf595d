package cn.mlamp.insightflow.cms.service;


import com.tencentcloudapi.captcha.v20190722.CaptchaClient;
import com.tencentcloudapi.captcha.v20190722.models.DescribeCaptchaResultRequest;
import com.tencentcloudapi.captcha.v20190722.models.DescribeCaptchaResultResponse;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;

import cn.mlamp.insightflow.cms.config.CaptchaProperties;
import cn.mlamp.insightflow.cms.exception.BusinessException;
import cn.mlamp.insightflow.cms.util.JsonUtil;
import cn.mlamp.insightflow.cms.util.StopWatchUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * 腾讯验证码service
 * 
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/11
 **/
@Service
@Slf4j
public class CaptchaService {

    @Resource
    private CaptchaClient client;
    @Resource
    private CaptchaProperties captchaProperties;

    private final static Long OK = 1L;

    public void checkCaptcha(String randStr, String ticket, String ip) {
        // 实例化一个请求对象,每个接口都会对应一个request对象
        DescribeCaptchaResultRequest req = new DescribeCaptchaResultRequest();
        req.setCaptchaAppId(captchaProperties.getCaptchaAppId());
        req.setAppSecretKey(captchaProperties.getAppSecretKey());
        req.setCaptchaType(captchaProperties.getCaptchaType());
        req.setRandstr(randStr);
        req.setUserIp(ip);
        req.setTicket(ticket);
        StopWatchUtil.start("checkCaptcha");
        try {
            // 返回的resp是一个DescribeCaptchaResultResponse的实例，与请求对象对应
            DescribeCaptchaResultResponse resp = client.DescribeCaptchaResult(req);
            log.info("CaptchaService-checkCaptcha resp:{}", JsonUtil.encode(req));
            if (!OK.equals(resp.getCaptchaCode())) {
                log.warn("CaptchaService-checkCaptcha failed:{}", JsonUtil.encode(resp));
                throw new BusinessException(500, "验证失败");
            }
        } catch (TencentCloudSDKException e) {
            log.error("CaptchaService-checkCaptcha error:", e);
            throw new BusinessException(500, "验证失败");
        } finally {
            log.debug(StopWatchUtil.prettyPrint(true));
        }
    }
}
