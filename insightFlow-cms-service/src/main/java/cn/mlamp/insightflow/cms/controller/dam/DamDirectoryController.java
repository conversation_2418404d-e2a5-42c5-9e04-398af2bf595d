package cn.mlamp.insightflow.cms.controller.dam;

import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.config.UserContext;
import cn.mlamp.insightflow.cms.enums.dam.DamDirectoryTypeEnum;
import cn.mlamp.insightflow.cms.model.dto.dam.DamDirectoryDTO;
import cn.mlamp.insightflow.cms.model.vo.dam.DamDirectoryVO;
import cn.mlamp.insightflow.cms.service.dam.IDamDirectoryService;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * DAM 文件夹 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/dam/directories")
public class DamDirectoryController {

    @Autowired
    private IDamDirectoryService directoryService;

    /**
     * 创建目录
     */
    @PostMapping
    public RespBody<DamDirectoryVO> createDirectory(@RequestBody DamDirectoryDTO directoryDTO,
                                                    HttpServletRequest request) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();

        DamDirectoryVO directoryVO = directoryService.createDirectory(directoryDTO, userId, tenantId);
        return RespBody.ok(directoryVO);
    }

    /**
     * 获取目录列表
     */
    @GetMapping
    public RespBody<List<DamDirectoryVO>> getDirectoryList(@RequestParam(required = false) Integer type,
                                                           HttpServletRequest request) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();

        List<DamDirectoryVO> directoryList = directoryService.getDirectoryList(DamDirectoryTypeEnum.getByCode(type), userId, tenantId);
        return RespBody.ok(directoryList);
    }

    /**
     * 获取目录详情
     */
    @GetMapping("/{directoryId}")
    public RespBody<DamDirectoryVO> getDirectoryDetail(@PathVariable Integer directoryId,
                                                       HttpServletRequest request) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();

        DamDirectoryVO directoryVO = directoryService.getDirectoryDetail(directoryId, userId, tenantId);
        return RespBody.ok(directoryVO);
    }

    /**
     * 更新目录
     */
    @PutMapping("/{directoryId}")
    public RespBody<Void> updateDirectory(@PathVariable Integer directoryId,
                                          @RequestBody DamDirectoryDTO directoryDTO,
                                          HttpServletRequest request) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();

        boolean result = directoryService.updateDirectory(directoryId, directoryDTO, userId, tenantId);
        return result ? RespBody.ok() : RespBody.fail("更新目录失败");
    }

    /**
     * 删除目录
     */
    @DeleteMapping("/{directoryId}")
    public RespBody<Void> deleteDirectory(@PathVariable Integer directoryId,
                                          HttpServletRequest request) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();

        boolean result = directoryService.deleteDirectory(directoryId, userId, tenantId);
        return result ? RespBody.ok() : RespBody.fail("删除目录失败");
    }
}