package cn.mlamp.insightflow.cms.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Repeatable;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * 验证对象指定字段不能为null，且必须一致 https://www.jianshu.com/p/a3ed25e96daf
 *
 * <AUTHOR>
 * @since 2022-09-08 16:35:19
 */
@Target({ ElementType.TYPE })
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = { ObjectPropertiesMustBeConsistentValidator.class })
@Documented
@Repeatable(ObjectPropertiesMustBeConsistentConstraint.List.class)
public @interface ObjectPropertiesMustBeConsistentConstraint {

    /**
     * 默认错误消息
     *
     * @return String
     */
    String message() default "指定属性不完全一致";

    /**
     * 校验的属性集合
     *
     * @return String[]
     */
    String[] properties() default {};

    /**
     * 分组
     *
     * @return Class<?>[]
     */
    Class<?>[] groups() default {};

    /**
     * 负载
     *
     * @return Class
     */
    Class<? extends Payload>[] payload() default {};

    @Target({ TYPE })
    @Retention(RUNTIME)
    @Documented
    @interface List {
        ObjectPropertiesMustBeConsistentConstraint[] value();
    }
}
