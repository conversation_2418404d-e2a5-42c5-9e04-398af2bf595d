package cn.mlamp.insightflow.cms.util.dify.model;//package cn.mlamp.insightflow.cms.util.dify.model;


import lombok.Builder;
import lombok.Data;

/**
 * dify请求的AppParams参数类
 */
@Data
@Builder
public class AppParam {
    private String variable;
    private String label;
    private String type;
    private Boolean required;
    private Integer maxLength;
    private String[] options;

    private String[] allowedFileTypes;
    private String[] allowedFileUploadMethods;
    private String[] allowedFileExtensions;
}
