package cn.mlamp.insightflow.cms.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Author: husuper
 * @CreateTime: 2025-03-19
 */
@Data
public class TenantTokenSummaryVO {

    @Schema(description = "余额", required = true)
    private Integer balance;

    @Schema(description = "累计充值", required = true)
    private Integer accumulatedRecharge;

    @Schema(description = "本月消耗", required = true)
    private Integer monthExpenses;

    @Schema(description = "累计消费", required = true)
    private Integer accumulatedExpenses;

    @Schema(description = "租户ID", required = true)
    private Integer tenantId;

}
