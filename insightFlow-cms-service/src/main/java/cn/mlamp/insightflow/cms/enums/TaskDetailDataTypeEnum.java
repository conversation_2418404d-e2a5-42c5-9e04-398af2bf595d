package cn.mlamp.insightflow.cms.enums;


import lombok.Getter;

/**
 * 文件的状态信息
 */


@Getter
public enum TaskDetailDataTypeEnum {

    /**
     * dify
     */
    DIFY("0", "dify"),

    /**
     * 算法
     */
    ALGORITHM("1", "算法"),

    /**
     * 用户保存
     */
    USER_SAVE("2", "用户保存"),
    ;

    private final String code;
    private final String msg;

    TaskDetailDataTypeEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static TaskDetailDataTypeEnum getByCode(String code) {
        for (TaskDetailDataTypeEnum type : TaskDetailDataTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
