package cn.mlamp.insightflow.cms.generator;

import cn.mlamp.insightflow.cms.entity.BaseEntity;
import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.OutputFile;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;
import com.google.common.collect.Lists;

import java.util.Collections;

public class MybatisPlusCodeGenerator {

    public static void main(String[] args) {
        FastAutoGenerator.create(
                        "****************************************************************************************************************",
                        "mysql",
                        "mysql")
                .globalConfig(builder -> {
                    builder.enableSwagger()
                            .enableSpringdoc()
                            .outputDir("src/main/java")
                            .commentDate("yyyy-MM-dd");
                })
                .packageConfig(builder -> {
                    builder.parent("cn.mlamp.insightflow.cms")
                            .entity("entity.dam")
                            .mapper("mapper.dam")
                            .service("service.dam")
                            .serviceImpl("service.dam.impl")
                            .mapper("mapper.dam")
                            .controller("controller.dam")
                            .pathInfo(Collections.singletonMap(OutputFile.xml, "src/main/resources/mapper/dam"));
                })
                .strategyConfig(builder -> builder.addInclude(Lists.newArrayList(
                                "cms_asset", "cms_asset_upload_task_detail", "cms_directory", "cms_public_tag", "cms_recycle_bin", "cms_tag", "cms_tag_value"))
                        .entityBuilder()
                        .enableLombok()
                        .enableFileOverride()
                        .superClass(BaseEntity.class)
                        .enableTableFieldAnnotation())
                .templateEngine(new FreemarkerTemplateEngine())
                .execute();
    }
}