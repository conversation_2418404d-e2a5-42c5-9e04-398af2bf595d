package cn.mlamp.insightflow.cms.mapper.dam;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.github.yulichang.base.MPJBaseMapper;

import cn.mlamp.insightflow.cms.entity.dam.DamAssetUploadTaskDetail;

/**
 * <p>
 * DAM素材上传任务表-记录细节 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
@Mapper
public interface DamAssetUploadTaskDetailMapper extends MPJBaseMapper<DamAssetUploadTaskDetail> {

    /**
     * 根据任务ID获取任务详情
     *
     * @param taskId 任务ID
     * @return 任务详情列表
     */
    List<DamAssetUploadTaskDetail> selectByTaskId(@Param("taskId") Integer taskId);

    /**
     * 批量插入任务详情
     *
     * @param details 任务详情列表
     * @return 影响行数
     */
    int batchInsert(@Param("list") List<DamAssetUploadTaskDetail> details);

    /**
     * 更新任务状态
     *
     * @param id     任务详情ID
     * @param status 状态
     * @param result 结果
     * @param error  错误信息
     * @return 影响行数
     */
    int updateStatus(@Param("id") Integer id,
                     @Param("status") Integer status,
                     @Param("result") String result,
                     @Param("error") String error);

}
