package cn.mlamp.insightflow.cms.model.vo;

import cn.mlamp.insightflow.cms.entity.CmsDocumentInfo;
import cn.mlamp.insightflow.cms.entity.CmsTaskInfo;
import cn.mlamp.insightflow.cms.enums.DocumentStatusEnum;
import cn.mlamp.insightflow.cms.enums.VideoTaskStatusEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.function.Function;

@Data
public class DocTaskUploadVO {
    @Schema(description = "任务id", required = true)
    private Integer taskId;
    @Schema(description = "任务名称(脚本任务)", required = false)
    private String taskName;
    @Schema(description = "任务状态", required = true)
    private Integer status;
    @Schema(description = "任务状态名称", required = true)
    private String statusName;
    @Schema(description = "文件信息", required = false)
    private TaskDocInfo docInfo;
    @Schema(description = "创建时间", required = true)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "GMT+8")
    private Date createTime;

    @Schema(description = "esId", required = false)
    private String esId;

    @Data
    public static class TaskDocInfo {
        @Schema(description = "文件id", required = true)
        private String docId;
        @Schema(description = "文件名", required = true)
        private String name;
        @Schema(description = "文件大小", required = false)
        private Long size;
        @Schema(description = "文件类型", required = false)
        private String docType;
        @Schema(description = "文件图标", required = false)
        private String iconKey;
    }

    public static DocTaskUploadVO mapperByDocumentInfo(CmsDocumentInfo documentInfo, Function<String, String> getIconKey) {
        var result = new DocTaskUploadVO();
        result.setTaskId(documentInfo.getId());
        result.setTaskName(documentInfo.getDocName());
        var statusEnum = DocumentStatusEnum.valueOf(documentInfo.getStatus());
        result.setStatus(statusEnum.getCode());
        result.setStatusName(statusEnum.getMsg());
        result.setCreateTime(documentInfo.getCreateTime());
        result.setEsId(documentInfo.getEsId());
        var docInfo = new TaskDocInfo();
        docInfo.setDocId(documentInfo.getId().toString());
        docInfo.setName(documentInfo.getDocName());
        docInfo.setSize(documentInfo.getSize());
        if (documentInfo.getDocType() != null)
            docInfo.setDocType(documentInfo.getDocType());
        if (documentInfo.getLink() != null) {
            docInfo.setIconKey(getIconKey.apply(documentInfo.getLink()));
        }
        result.setDocInfo(docInfo);
        return result;
    }


    public static DocTaskUploadVO mapperByTaskInfo(CmsTaskInfo cmsTaskInfo) {
        var result = new DocTaskUploadVO();
        result.setTaskId(cmsTaskInfo.getId());
        result.setTaskName(cmsTaskInfo.getName());
        var statusEnum = VideoTaskStatusEnum.getByCode(cmsTaskInfo.getTaskStatus());
        if (statusEnum != null) {
            result.setStatus(statusEnum.getCode());
            result.setStatusName(statusEnum.getMsg());
        }
        result.setCreateTime(cmsTaskInfo.getCreateTime());
        return result;
    }

}
