package cn.mlamp.insightflow.cms.model.dto.dam;

import java.util.Date;

import cn.mlamp.insightflow.cms.entity.dam.DamTag;
import cn.mlamp.insightflow.cms.entity.dam.DamTagValue;
import cn.mlamp.insightflow.cms.enums.dam.DamTagTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * DAM标签值DTO
 */
@Data
@Schema(description = "DAM标签值DTO")
public class DamTagValueDTO {

    @Schema(description = "标签值ID")
    private Integer id;

    @Schema(description = "素材ID")
    private Integer assetId;

    @Schema(description = "标签ID")
    private Integer tagId;

    @Schema(description = "标签类型")
    private DamTagTypeEnum tagType;

    @NotBlank(message = "标签名称不能为空")
    @Schema(description = "标签名称",
            requiredMode = RequiredMode.REQUIRED)
    private String name;

    @NotBlank(message = "标签值不能为空")
    @Schema(description = "标签值",
            requiredMode = RequiredMode.REQUIRED)
    private String value;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;

    public static DamTagValueDTO from(DamTagValue tagValue, DamTag tag) {
        final DamTagValueDTO dto = new DamTagValueDTO();
        dto.setId(tagValue.getId());
        dto.setTagId(tagValue.getTagId());
        dto.setTagType(tag.getType());
        dto.setName(tag.getName());
        dto.setValue(tagValue.getValue());
        dto.setCreateTime(tagValue.getCreateTime());
        dto.setUpdateTime(tagValue.getUpdateTime());
        return dto;
    }
} 