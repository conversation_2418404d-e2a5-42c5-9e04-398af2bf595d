package cn.mlamp.insightflow.cms.mapper.dam;

import cn.mlamp.insightflow.cms.entity.dam.DamAsset;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.base.MPJBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * DAM素材表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
@Mapper
public interface DamAssetMapper extends MPJBaseMapper<DamAsset> {

    /**
     * 获取素材列表，包含标签信息
     *
     * @param page        分页参数
     * @param directoryId 目录ID
     * @param keyword     关键词
     * @param tenantId    租户ID
     * @param userId      用户ID
     * @return 素材列表
     */
    IPage<DamAsset> selectAssetList(
            Page<DamAsset> page,
            @Param("directoryId") Integer directoryId,
            @Param("keyword") String keyword,
            @Param("tenantId") Integer tenantId,
            @Param("userId") Integer userId);
}
