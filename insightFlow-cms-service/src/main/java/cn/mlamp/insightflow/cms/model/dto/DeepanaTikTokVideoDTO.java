package cn.mlamp.insightflow.cms.model.dto;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.mlamp.insightflow.cms.entity.CmsDocumentInfo;
import cn.mlamp.insightflow.cms.entity.CmsPullTaskDedupedData;
import cn.mlamp.insightflow.cms.entity.CmsVideoInfo;
import cn.mlamp.insightflow.cms.enums.DownloadStatusEnum;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;

@Data
public class DeepanaTikTokVideoDTO {
    @JsonProperty("CollectCount")
    private Long collectCount;

    @JsonProperty("CommentCount")
    private Long commentCount;

    @JsonProperty("Content")
    private String content;

    @JsonProperty("FollowerCount")
    private Long followerCount;

    @JsonProperty("IsDeleted")
    private Boolean isDeleted;

    @JsonProperty("LikeCount")
    private Long likeCount;

    @JsonProperty("NickName")
    private String nickName;

    @JsonProperty("PublishedAt")
    private Long publishedAt;  // Unix时间戳

    @JsonProperty("RepostCount")
    private Long repostCount;

    @JsonProperty("VideoDuration")
    private Long videoDuration;  // 毫秒

    @JsonProperty("kwHeadImage")
    private String headImageUrl;

    @JsonProperty("kwProfileImageUrl")
    private String profileImageUrl;

    @JsonProperty("kwUserUrl")
    private String userUrl;

    @JsonProperty("kwVideoUrl")
    private String videoUrl;

    @JsonProperty("video_url")
    private String alternativeVideoUrl;

    /**
     * 将Unix时间戳转换为LocalDateTime
     */
    public Date getPublishedDate() {
        return new Date(publishedAt * 1000);
    }

    /**
     * 获取视频时长(秒)
     */
    public Long getVideoDurationInSeconds() {
        return videoDuration / 1000;
    }

    public static CmsPullTaskDedupedData buildBy(CmsVideoInfo videoInfo,
                                                 CmsDocumentInfo documentInfo,
                                                 DeepanaTikTokVideoDTO dto) {
        var cmsPullTaskDedupedData = new CmsPullTaskDedupedData();
        cmsPullTaskDedupedData.setTaskId(videoInfo.getId());
        cmsPullTaskDedupedData.setEsId(videoInfo.getEsId());
        cmsPullTaskDedupedData.setType(3); //视频解码
        cmsPullTaskDedupedData.setSourceType(2); //本地上传
        cmsPullTaskDedupedData.setUserId(videoInfo.getUserId());
        cmsPullTaskDedupedData.setTenantId(videoInfo.getTenantId());
        // 下载时间固定成文档创建时间，方便后续按日期查询下载状态
        cmsPullTaskDedupedData.setDownloadDate(DateUtil.format(
                documentInfo.getCreateTime(), DatePattern.NORM_DATE_PATTERN));
        cmsPullTaskDedupedData.setDownloadStatus(DownloadStatusEnum.SUCCESS.getCode());
        cmsPullTaskDedupedData.setTextContent(documentInfo.getDocName()); //本地上传文件名放入列表名称
        cmsPullTaskDedupedData.setKwVideoUrl(documentInfo.getObjId()); //本地上传文件连接取objId,方便签名取原视频
        if (dto != null) { // url上传
            cmsPullTaskDedupedData.setDownloadStatus(DownloadStatusEnum.DOWNLOADING.getCode()); // 下载中
            cmsPullTaskDedupedData.setSourceType(3); //链接上传
            cmsPullTaskDedupedData.setKwSource("douyin.com");
            cmsPullTaskDedupedData.setKwUrl(documentInfo.getLink());
            cmsPullTaskDedupedData.setCreateTime(documentInfo.getCreateTime());
            if (dto.getIsDeleted()) { // 帖子已删除
                cmsPullTaskDedupedData.setBoolIsDeleted(true);
            } else {
                cmsPullTaskDedupedData.setBoolIsDeleted(false);
                cmsPullTaskDedupedData.setTextContent(dto.getContent());
                cmsPullTaskDedupedData.setDatePublishedAt(dto.getPublishedDate());
                cmsPullTaskDedupedData.setLongRepostCount(dto.getRepostCount());
                cmsPullTaskDedupedData.setLongCommentCount(dto.getCommentCount());
                cmsPullTaskDedupedData.setLongLikeCount(dto.getLikeCount());
                // 互动数 = 点赞 + 转发 + 评论
                cmsPullTaskDedupedData.setLongInteractCount(dto.getLikeCount()
                        + dto.getRepostCount() + dto.getCommentCount());
                cmsPullTaskDedupedData.setLongCollectCount(dto.getCollectCount());
                cmsPullTaskDedupedData.setLongFollowerCount(dto.getFollowerCount());
                cmsPullTaskDedupedData.setTextNickName(dto.getNickName());
                cmsPullTaskDedupedData.setKwProfileImageUrl(dto.getProfileImageUrl());
                cmsPullTaskDedupedData.setKwUserUrl(dto.getUserUrl());
                cmsPullTaskDedupedData.setLongVideoDuration(dto.getVideoDurationInSeconds());
                cmsPullTaskDedupedData.setKwHeadImage(dto.getHeadImageUrl());
                cmsPullTaskDedupedData.setKwVideoUrl(dto.getVideoUrl());
            }
        }
        return cmsPullTaskDedupedData;
    }
}
