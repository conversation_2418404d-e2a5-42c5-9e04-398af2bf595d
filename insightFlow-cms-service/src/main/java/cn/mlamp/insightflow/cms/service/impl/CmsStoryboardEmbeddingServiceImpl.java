package cn.mlamp.insightflow.cms.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.mlamp.insightflow.cms.entity.CmsStoryboardEmbedding;
import cn.mlamp.insightflow.cms.model.dto.DifyScriptGenResponseDTO;
import cn.mlamp.insightflow.cms.model.vo.VideoStoryBoardVO;
import cn.mlamp.insightflow.cms.service.ICmsStoryboardEmbeddingService;
import cn.mlamp.insightflow.cms.vector.CmsStoryboardEmbeddingMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 分镜嵌入向量服务实现类
 */
@Service
@Slf4j
public class CmsStoryboardEmbeddingServiceImpl extends ServiceImpl<CmsStoryboardEmbeddingMapper, CmsStoryboardEmbedding>
        implements ICmsStoryboardEmbeddingService {

    @Autowired
    private EmbeddingModel embeddingModel;

    @Override
    public CmsStoryboardEmbedding saveStoryboardEmbedding(CmsStoryboardEmbedding storyboardEmbedding) {
        try {
            var embeddingResult = embeddingModel.embed(storyboardEmbedding.getContent());
            storyboardEmbedding.setEmbedding(toFloatArray(embeddingResult));
            save(storyboardEmbedding);
        } catch (Exception e) {
            log.error("保存分镜信息Embedding出错", e);
        }
        return storyboardEmbedding;
    }

    @Override
    public int updateEmbedding(Integer id, String content) {
        float[] embeddingResult = embeddingModel.embed(content);
        Float[] embeddingArr = toFloatArray(embeddingResult);
        return baseMapper.updateEmbedding(id, content, embeddingArr);
    }

    @Override
    public int deleteEmbedding(Integer id) {
        return baseMapper.deleteById(id);
    }

    @Override
    public Page<CmsStoryboardEmbedding> pageEmbedding(Integer current, Integer pageSize, Integer taskId, String content,
                                                      Integer tenantId, Integer userId) {
        var embeddingResult = embeddingModel.embed(content);
        long total = baseMapper.totalEmbeddingCount(taskId, tenantId, userId);
        int offset = (current - 1) * pageSize;
        Page<CmsStoryboardEmbedding> page = new Page<>(current, pageSize, total);
        page.setRecords(baseMapper.pageEmbedding(embeddingResult, offset, pageSize, taskId, tenantId, userId));
        return page;
    }

    @Override
    public List<CmsStoryboardEmbedding> topKEmbedding(String content, Integer limit, Float threshold,
                                                      Integer taskId, Integer tenantId, Integer userId) {
        var embeddingResult = embeddingModel.embed(content);
        return baseMapper.topKEmbedding(embeddingResult, limit, threshold, taskId, tenantId, userId);
    }

    @Override
    @Async("commonTaskExecutor")
    public void asyncEmbedding(Integer taskId, List<DifyScriptGenResponseDTO.Scene> scenes,
                               Integer[] status, Integer tenantId, Integer userId) {
        // 存储镜头描述
        for (int i = 0; i < status.length; i++) {
            var sbId = status[i]; // 镜头id, 不等于-1时存储embedding
            if (sbId == -1) continue;
            var content = scenes.get(i).getSceneDescription(); //获取镜头描述
            if (StrUtil.isNotBlank(content)) {
                var storyboardEmbedding = new CmsStoryboardEmbedding();
                storyboardEmbedding.setId(sbId);
                storyboardEmbedding.setTaskId(taskId);
                storyboardEmbedding.setContent(content);
                storyboardEmbedding.setTenantId(tenantId);
                storyboardEmbedding.setUserId(userId);
                saveStoryboardEmbedding(storyboardEmbedding);
            }
        }
    }

    @Override
    @Async("commonTaskExecutor")
    public void asyncEmbedding(Integer taskId, List<VideoStoryBoardVO> needSaveEmbedding, Integer tenantId, Integer userId) {
        for (var storyboard : needSaveEmbedding) {
            var content = storyboard.getSceneDescription(); //获取镜头描述
            if (StrUtil.isNotBlank(content)) {
                var storyboardEmbedding = new CmsStoryboardEmbedding();
                storyboardEmbedding.setId(storyboard.getId());
                storyboardEmbedding.setTaskId(taskId);
                storyboardEmbedding.setContent(content);
                storyboardEmbedding.setTenantId(tenantId);
                storyboardEmbedding.setUserId(userId);
                saveStoryboardEmbedding(storyboardEmbedding);
            }
        }
    }

    @Override
    public void asyncUpdate(List<VideoStoryBoardVO> oldStoryboards, List<VideoStoryBoardVO> newStoryboards) {
        // 获取镜头描述
        Map<Integer, String> oldContentMap = oldStoryboards.stream()
                .filter(storyboard -> storyboard.getId() != null)
                .collect(Collectors.toMap(VideoStoryBoardVO::getId, VideoStoryBoardVO::getSceneDescription));
        for (var newStoryboard : newStoryboards) {
            // 判断是否需要更新或者新增镜头描述
            Integer id = newStoryboard.getId();
            String newContent = newStoryboard.getSceneDescription(); //获取新的镜头描述
            if (id != null && StrUtil.isNotBlank(newContent)) { // 镜头id不为空,且镜头描述不为空
                if (oldContentMap.containsKey(id)) {
                    String oldContent = oldContentMap.get(id);
                    if (!oldContent.equals(newContent)) {
                        updateEmbedding(id, newContent);
                    }
                }
            }
        }
    }

    @Override
    public List<CmsStoryboardEmbedding> getByTaskId(Integer taskId) {
        return baseMapper.selectList(new LambdaQueryWrapper<CmsStoryboardEmbedding>()
                .eq(CmsStoryboardEmbedding::getTaskId, taskId));
    }

    /**
     * 将float数组转换为Float数组
     *
     * @param arr float数组
     * @return Float数组
     */
    private Float[] toFloatArray(float[] arr) {
        if (arr == null) return null;
        Float[] result = new Float[arr.length];
        for (int i = 0; i < arr.length; i++) result[i] = arr[i];
        return result;
    }
}
