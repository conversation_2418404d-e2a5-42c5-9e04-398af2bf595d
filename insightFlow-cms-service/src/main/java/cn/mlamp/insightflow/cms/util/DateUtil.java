package cn.mlamp.insightflow.cms.util;

import java.awt.*;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * @Author: husuper
 * @CreateTime: 2025-03-28
 */
public class DateUtil {


    public static String getYYYYMMDD(Date date) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        String formattedDate = formatter.format(date);
        return formattedDate;
    }

    public static String getYYYYMMDD2(Date date) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
        String formattedDate = formatter.format(date);
        return formattedDate;
    }


    public static void main(String[] args) {
        System.out.println(getYYYYMMDD2(new Date()));
        GraphicsEnvironment ge = GraphicsEnvironment.getLocalGraphicsEnvironment();
        String[] fontFamilies = ge.getAvailableFontFamilyNames();

        System.out.println("当前环境中可用的字体族名称：");
        for (String font : fontFamilies) {
            System.out.println(font);
        }
    }
}
