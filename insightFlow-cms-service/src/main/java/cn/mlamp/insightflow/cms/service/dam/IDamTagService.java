package cn.mlamp.insightflow.cms.service.dam;

import java.util.List;

import javax.annotation.Nullable;

import com.baomidou.mybatisplus.extension.service.IService;

import cn.mlamp.insightflow.cms.entity.dam.DamTag;
import cn.mlamp.insightflow.cms.enums.dam.DamTagTypeEnum;
import cn.mlamp.insightflow.cms.model.dto.dam.DamTagDTO;
import cn.mlamp.insightflow.cms.model.vo.dam.DamTagVO;

/**
 * <p>
 * DAM标签表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
public interface IDamTagService extends IService<DamTag> {
    /**
     * 创建自定义标签
     *
     * @param tagDTO   标签DTO
     * @param userId   用户ID
     * @param tenantId 租户ID
     * @return 标签VO
     */
    DamTagVO createCustomTag(DamTagDTO tagDTO, Integer userId, Integer tenantId);

    /**
     * 获取标签列表
     *
     * @param type          标签类型：1-公共标签, 2-自定义标签
     * @param suggestValues 是否包含推荐搜索标签值
     * @param userId        用户ID
     * @param tenantId      租户ID
     * @return 标签列表
     */
    List<DamTagVO> getTagList(@Nullable DamTagTypeEnum type,
                              boolean suggestValues,
                              Integer userId,
                              Integer tenantId);

    /**
     * 更新标签
     *
     * @param tagId    标签ID
     * @param tagDTO   标签DTO
     * @param userId   用户ID
     * @param tenantId 租户ID
     * @return 更新结果
     */
    DamTagVO updateTag(Integer tagId, DamTagDTO tagDTO, Integer userId, Integer tenantId);

    /**
     * 删除标签
     *
     * @param tagId    标签ID
     * @param userId   用户ID
     * @param tenantId 租户ID
     * @return 是否成功
     */
    boolean deleteTag(Integer tagId, Integer userId, Integer tenantId);

    List<DamTagVO> searchTags(String keyword, Integer userId, Integer tenantId);

}
