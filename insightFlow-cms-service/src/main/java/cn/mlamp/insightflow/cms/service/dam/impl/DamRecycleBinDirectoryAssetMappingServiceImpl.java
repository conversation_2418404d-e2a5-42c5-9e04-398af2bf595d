package cn.mlamp.insightflow.cms.service.dam.impl;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import cn.mlamp.insightflow.cms.entity.dam.DamRecycleBinDirectoryAssetMapping;
import cn.mlamp.insightflow.cms.mapper.dam.DamRecycleBinDirectoryAssetMappingMapper;
import cn.mlamp.insightflow.cms.service.dam.IDamRecycleBinDirectoryAssetMappingService;

/**
 * <p>
 * DAM回收站表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
@Service
public class DamRecycleBinDirectoryAssetMappingServiceImpl extends ServiceImpl<DamRecycleBinDirectoryAssetMappingMapper, DamRecycleBinDirectoryAssetMapping>
        implements IDamRecycleBinDirectoryAssetMappingService {

}
