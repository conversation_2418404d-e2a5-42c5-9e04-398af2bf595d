package cn.mlamp.insightflow.cms.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 验证码发送类型的枚举
 *
 * <AUTHOR>
 * @since 2022-9-21 11:25:50
 */
@AllArgsConstructor
@Getter
public enum VerificationTypeEnum {

    /**
     * 验证码发送类型-邮箱
     */
    TYPE_EMAIL(1, "邮箱"),

    /**
     * 验证码发送类型-手机号
     */
    TYPE_MOBILE(2, "手机号");

    /**
     * Code
     */
    private Integer code;

    /**
     * 描述
     */
    private String message;
}
