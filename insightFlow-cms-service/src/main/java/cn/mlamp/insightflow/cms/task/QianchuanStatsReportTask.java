package cn.mlamp.insightflow.cms.task;

import cn.mlamp.insightflow.cms.config.AdminConfig;
import cn.mlamp.insightflow.cms.config.TaskConfig;
import cn.mlamp.insightflow.cms.service.DataStatisticsService;
import cn.mlamp.insightflow.cms.service.WechatWebhookService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 千川数据统计报告定时任务
 *
 * <AUTHOR>
 * @since 2024-10-15
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class QianchuanStatsReportTask {

    private final DataStatisticsService dataStatisticsService;
    private final WechatWebhookService wechatWebhookService;
    private final StringRedisTemplate stringRedisTemplate;
    private final TaskConfig taskConfig;

    /**
     * 每天早上9点55分执行推送千川数据统计报告
     */
    @Scheduled(cron = "0 55 9 * * ?")
    public void pushQianchuanStatsReport() {
        if (taskConfig.isLocal()) {
            log.info("本地环境不执行千川数据统计报告推送任务");
            return;
        }

        // 分布式锁(防止同一时间多台服务器重复触发)，保证每次定时任务只有一台服务器在执行
        boolean result = Boolean.TRUE.equals(stringRedisTemplate.opsForValue()
                .setIfAbsent("qianchuan_stats_report_task", "running", 300, TimeUnit.SECONDS));

        if (!result) {
            log.warn("已有其他实例在执行千川数据统计报告推送任务，跳过执行");
            return;
        }

        try {
            log.info("开始执行千川数据统计报告推送任务");

            // 获取千川数据统计
            Map<String, Object> statistics = dataStatisticsService.getQianchuanPushStatistics();

            // 格式化为Markdown消息
            String markdownContent = dataStatisticsService.formatStatisticsToMarkdown(statistics);

            // 发送到微信webhook
            boolean success = wechatWebhookService.sendMarkdownMessage(markdownContent);

            if (success) {
                log.info("千川数据统计报告推送成功");
            } else {
                log.error("千川数据统计报告推送失败");
            }
        } catch (Exception e) {
            log.error("执行千川数据统计报告推送任务异常", e);
        } finally {
            // 释放分布式锁
            stringRedisTemplate.delete("qianchuan_stats_report_task");
        }
    }
}
