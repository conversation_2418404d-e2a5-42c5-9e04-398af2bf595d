package cn.mlamp.insightflow.cms.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 任务分享报告表
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("cms_task_share_report")
public class CmsTaskShareReport extends BaseEntity {

    /**
     * 任务id
     */
    private Integer taskId;

    /**
     * 类型：1：视频合成任务
     */
    private Integer type;

    /**
     * 授权code
     */
    private String authorizeCode;

    /**
     * 授权时长（单位分钟）
     */
    private Integer authorizeTime;

    /**
     * 内容（json存储，前端定义）
     */
    private String content;

    /**
     * 数据（json存储，后端定义）
     */
    private String data;

    /**
     * 创建者id
     */
    private Integer userId;

    /**
     * 租户id
     */
    private Integer tenantId;

    /**
     * 过期时间
     */
    private Date expireTime;
}
