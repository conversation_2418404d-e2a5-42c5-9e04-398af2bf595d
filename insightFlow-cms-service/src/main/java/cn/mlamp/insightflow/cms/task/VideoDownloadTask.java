//package cn.mlamp.insightflow.cms.task;
//
//import cn.mlamp.insightflow.cms.config.TaskConfig;
//import cn.mlamp.insightflow.cms.entity.CmsPullTaskDedupedData;
//import cn.mlamp.insightflow.cms.enums.DownloadStatusEnum;
//import cn.mlamp.insightflow.cms.service.CmsPullTaskDedupedDataService;
//import cn.mlamp.insightflow.cms.strategy.handle.VideoDownloadHandle;
//import cn.mlamp.insightflow.cms.util.DateUtil;
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.data.redis.core.StringRedisTemplate;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Component;
//
//import java.time.LocalDate;
//import java.time.LocalDateTime;
//import java.time.format.DateTimeFormatter;
//import java.util.*;
//import java.util.concurrent.TimeUnit;
//
///**
// * @Author: husuper
// * @CreateTime: 2025-03-26
// */
//@Component
//@Slf4j
//public class VideoDownloadTask {
//
//    @Autowired
//    private StringRedisTemplate stringRedisTemplate;
//
//    @Autowired
//    private CmsPullTaskDedupedDataService cmsPullTaskDedupedDataService;
//
//    @Autowired
//    private VideoDownloadHandle videoDownloadHandle;
//
//    @Autowired
//    private TaskConfig taskConfig;
//
//
//    @Scheduled(cron = "0 0/13 0-8 * * ?")
//    public void videoDownloadJob() {
//        if(taskConfig.isLocal()){
//            return;
//        }
//        // 分布式锁(防止同一时间多台服务器重复触发)，保证每次定时任务只有一台服务器在执行
//        boolean result = Boolean.TRUE.equals(stringRedisTemplate.opsForValue().setIfAbsent("videoDownloadJob", "ImSyncJob-timer", 350, TimeUnit.SECONDS));
//        if (result) {
//            videoDownloadJob(DateUtil.getYYYYMMDD(new Date()));
//            stringRedisTemplate.delete("videoDownloadJob");
//        } else {
//            log.warn("多台服务器，防止同一个任务重复执行");
//        }
//    }
//
//    public void videoDownloadJob(String dateStr) {
//        videoDownloadStatusRefresh(dateStr);
//        videoDownload(dateStr);
//    }
//
//
//    public void videoDownload(String dateStr) {
//        //查询创建时间为 date的数据   date格式是yyyy-mm-dd
//        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
//        LocalDate date = LocalDate.parse(dateStr, formatter);
//
//        LocalDateTime startOfDay = date.atStartOfDay();
//        LocalDateTime endOfDay = date.plusDays(1).atStartOfDay();
//
//        LambdaQueryWrapper<CmsPullTaskDedupedData> queryWrapper = new LambdaQueryWrapper<CmsPullTaskDedupedData>()
//                .ge(CmsPullTaskDedupedData::getCreateTime, startOfDay)
//                .lt(CmsPullTaskDedupedData::getCreateTime, endOfDay);
//        queryWrapper.eq(CmsPullTaskDedupedData::getSourceType, 1);
//        queryWrapper.eq(CmsPullTaskDedupedData::getDownloadStatus, DownloadStatusEnum.PENDING.getCode());
//        queryWrapper.groupBy(CmsPullTaskDedupedData::getEsId);
//        queryWrapper.orderByDesc(CmsPullTaskDedupedData::getLongInteractCount);
//        queryWrapper.orderByDesc(CmsPullTaskDedupedData::getLongLikeCount);
//        //限制在10条数据
//        queryWrapper.last("limit 300");
//
//        List<CmsPullTaskDedupedData> list = cmsPullTaskDedupedDataService.list(queryWrapper);
//
//        if (list.isEmpty()) {
//            return;
//        }
//
//        List<VideoDownloadHandle.AddJob> list_data = new ArrayList<>();
//        for (CmsPullTaskDedupedData cmsPullTaskDedupedData : list) {
//            VideoDownloadHandle.AddJob addJob = new VideoDownloadHandle.AddJob(cmsPullTaskDedupedData.getEsId(), cmsPullTaskDedupedData.getKwUrl(), DateUtil.getYYYYMMDD(cmsPullTaskDedupedData.getDatePublishedAt()), cmsPullTaskDedupedData.getId());
//            list_data.add(addJob);
//        }
//        VideoDownloadHandle.AddJobResponse addJobResponse = videoDownloadHandle.addJob(new VideoDownloadHandle.AddJobRequest(list_data));
//        log.info("视频下载任务添加成功");
//        List<CmsPullTaskDedupedData> updateList = new ArrayList<>();
//        for (VideoDownloadHandle.AddJob addJob : list_data) {
//            //更新下载状态
//            CmsPullTaskDedupedData cmsPullTaskDedupedData = new CmsPullTaskDedupedData();
//            cmsPullTaskDedupedData.setDownloadStatus(DownloadStatusEnum.DOWNLOADING.getCode());
//            cmsPullTaskDedupedData.setDownloadDate(dateStr);
//            cmsPullTaskDedupedData.setEsId(addJob.getResource_id());
//            cmsPullTaskDedupedData.setId(addJob.getId());
//            updateList.add(cmsPullTaskDedupedData);
//        }
//        cmsPullTaskDedupedDataService.updateBatch(updateList);
//    }
//
//    private List<CmsPullTaskDedupedData> getList(String date,DownloadStatusEnum downloadStatusEnum){
//        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
//        LocalDate dateTime = LocalDate.parse(date, formatter);
//
//        LocalDateTime startOfDay = dateTime.atStartOfDay();
//        LocalDateTime endOfDay = dateTime.plusDays(1).atStartOfDay();
//
//        LambdaQueryWrapper<CmsPullTaskDedupedData> queryWrapper = new LambdaQueryWrapper<CmsPullTaskDedupedData>()
//                .ge(CmsPullTaskDedupedData::getCreateTime, startOfDay)
//                .lt(CmsPullTaskDedupedData::getCreateTime, endOfDay);
//        queryWrapper.eq(CmsPullTaskDedupedData::getSourceType, 1);
//        queryWrapper.eq(CmsPullTaskDedupedData::getDownloadStatus, downloadStatusEnum.getCode());
//        queryWrapper.groupBy(CmsPullTaskDedupedData::getEsId);
//        queryWrapper.orderByDesc(CmsPullTaskDedupedData::getLongInteractCount);
//        queryWrapper.orderByDesc(CmsPullTaskDedupedData::getLongLikeCount);
//
//        List<CmsPullTaskDedupedData> list = cmsPullTaskDedupedDataService.list(queryWrapper);
//        return list;
//    }
//
//    public void videoDownloadStatusRefresh(String date) {
//        List<CmsPullTaskDedupedData> list = getList(date,DownloadStatusEnum.DOWNLOADING);
//        if (list.isEmpty()) {
//            return;
//        }
//
//        List<CmsPullTaskDedupedData> okList = getList(date,DownloadStatusEnum.SUCCESS);
//        Set<String> esSet=new HashSet<>();
//        for (CmsPullTaskDedupedData cmsPullTaskDedupedData : okList){
//            esSet.add(cmsPullTaskDedupedData.getEsId());
//        }
//
//        VideoDownloadHandle.ResultResponse resultResponse = videoDownloadHandle.getDoneList(date);
//        for (VideoDownloadHandle.ResultData resultData : resultResponse.getData()) {
//            LambdaUpdateWrapper<CmsPullTaskDedupedData> updateWrapper = new LambdaUpdateWrapper<>();
//            updateWrapper.set(CmsPullTaskDedupedData::getDownloadStatus, DownloadStatusEnum.SUCCESS.getCode())
//                    .eq(CmsPullTaskDedupedData::getEsId, resultData.getResource_id())
//                    .eq(CmsPullTaskDedupedData::getSourceType, 1)
//                    .ne(CmsPullTaskDedupedData::getDownloadStatus, DownloadStatusEnum.SUCCESS.getCode());
//            if(!esSet.contains(resultData.getResource_id())){
//                cmsPullTaskDedupedDataService.update(updateWrapper);
//            }
//        }
//
//        //失败的
//        VideoDownloadHandle.ResultResponse resultResponseErr222 = videoDownloadHandle.getProcessingList(date);
//
//        Set<String> esSetError222=new HashSet<>();
//        for (CmsPullTaskDedupedData cmsPullTaskDedupedData : list){
//            esSetError222.add(cmsPullTaskDedupedData.getEsId());
//        }
//
//        for (VideoDownloadHandle.ResultData resultData : resultResponseErr222.getData()) {
//            if(esSetError222.contains(resultData.getResource_id())){
//                continue;
//            }
//            LambdaUpdateWrapper<CmsPullTaskDedupedData> updateWrapper = new LambdaUpdateWrapper<>();
//            updateWrapper.set(CmsPullTaskDedupedData::getDownloadStatus, DownloadStatusEnum.DOWNLOADING.getCode())
//                    .eq(CmsPullTaskDedupedData::getEsId, resultData.getResource_id())
//                    .eq(CmsPullTaskDedupedData::getSourceType, 1);
//            cmsPullTaskDedupedDataService.update(updateWrapper);
//        }
//
//
//        List<CmsPullTaskDedupedData> errorList = getList(date,DownloadStatusEnum.FAILURE);
//        Set<String> esSetError=new HashSet<>();
//        for (CmsPullTaskDedupedData cmsPullTaskDedupedData : errorList){
//            esSetError.add(cmsPullTaskDedupedData.getEsId());
//        }
//
//        VideoDownloadHandle.ResultResponse resultResponseErr = videoDownloadHandle.getFailList(date);
//        for (VideoDownloadHandle.ResultData resultData : resultResponseErr.getData()) {
//            if(esSetError.contains(resultData.getResource_id())){
//                continue;
//            }
//            LambdaUpdateWrapper<CmsPullTaskDedupedData> updateWrapper = new LambdaUpdateWrapper<>();
//            updateWrapper.set(CmsPullTaskDedupedData::getDownloadStatus, DownloadStatusEnum.FAILURE.getCode())
//                    .eq(CmsPullTaskDedupedData::getEsId, resultData.getResource_id())
//                    .eq(CmsPullTaskDedupedData::getSourceType, 1)
//                    .ne(CmsPullTaskDedupedData::getDownloadStatus, DownloadStatusEnum.FAILURE.getCode());
//            cmsPullTaskDedupedDataService.update(updateWrapper);
//        }
//    }
//
//
//}
