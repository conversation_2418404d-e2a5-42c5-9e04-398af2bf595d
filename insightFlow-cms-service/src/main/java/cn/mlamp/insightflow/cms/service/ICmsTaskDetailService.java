package cn.mlamp.insightflow.cms.service;

import cn.mlamp.insightflow.cms.entity.CmsTaskDetail;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

public interface ICmsTaskDetailService extends IService<CmsTaskDetail> {
    CmsTaskDetail saveTaskDetail(Integer taskId, Integer type, String dataType, String data);

    List<CmsTaskDetail> getTaskResult(Integer taskId);

    CmsTaskDetail getUserTaskDetail(Integer taskId);

    CmsTaskDetail getTaskDetail(Integer taskId, Integer type, String dataType);

    void deleteByTaskId(Integer taskId);
}
