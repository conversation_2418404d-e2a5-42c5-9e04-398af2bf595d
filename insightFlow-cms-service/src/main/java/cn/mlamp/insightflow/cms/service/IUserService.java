package cn.mlamp.insightflow.cms.service;

import cn.mlamp.insightflow.cms.entity.CmsUser;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;


/**
 * <AUTHOR>
 * @description 针对表【user(用户表)】的数据库操作Service
 * @createDate 2024-09-27 14:46:32
 */
public interface IUserService extends IService<CmsUser> {

    void saveUser();

    List<CmsUser> listByUserIds(List<Integer> userIds);

    boolean isTenantAdmin();

    boolean checkUserTenant(Integer tenantId);

}
