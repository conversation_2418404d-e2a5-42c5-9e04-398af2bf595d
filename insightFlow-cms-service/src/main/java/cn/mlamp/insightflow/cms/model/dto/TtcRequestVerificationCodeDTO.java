package cn.mlamp.insightflow.cms.model.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * Ttc验证码DTO
 *
 * <AUTHOR>
 * @since 2022-09-08 12:00:39
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class TtcRequestVerificationCodeDTO extends TtcRequestBaseDTO {
    /**
     * 行为：1-账号注册 2-重置密码
     */
    @NotNull(message = "override_message.verification_code_source_not_null")
    private Integer source;
    /**
     * 类型： 1-邮箱验证 2-手机号验证
     */
    @NotNull(message = "override_message.verification_type_not_null")
    private Integer verificationType;
    /**
     * 根据verification_type，填写 邮箱或手机号
     */
    @NotBlank(message = "override_message.verification_target_not_blank")
    private String verificationTarget;
}
