package cn.mlamp.insightflow.cms.model.dto.dam;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import jodd.util.StringUtil;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * DAM 素材更新 DTO
 */
@Data
@Schema(description = "DAM标签值DTO")
public class DamAssertUpdateDTO {

    @NotBlank(message = "素材名称不能为空")
    @Size(max = 20, message = "素材名称不能超过20个字符")
    @Pattern(regexp = "^[^\\\\/:*?\"<>|]*$",
            message = "目录名称不能包含以下字符：\\、/、:、*、?、\"、<、>、|")
    @Schema(description = "素材名")
    private String name;

    @Schema(description = "标签列表")
    private List<DamTagValueDTO> tags;

    public boolean validate() {
        return StringUtil.isNotBlank(name) || CollectionUtils.isNotEmpty(tags);
    }
}