package cn.mlamp.insightflow.cms.strategy.handle;

import cn.mlamp.insightflow.cms.config.AnalysisVideoConfig;
import cn.mlamp.insightflow.cms.exception.BusinessException;
import cn.mlamp.insightflow.cms.util.VideoUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class VideoAiGenerationHandle {


    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private AnalysisVideoConfig analysisVideoConfig;

    public static void main(String[] args) {
        VideoAiGenerationHandle VideoAiGenerationHandle = new VideoAiGenerationHandle();
        VideoAiGenerationHandle.restTemplate = new RestTemplate();
        VideoAiGenerationHandle.analysisVideoConfig = new AnalysisVideoConfig();
        VideoAiGenerationHandle.analysisVideoConfig.setAnalyzeUrl("http://10.10.100.228:8354");

        VideoUtil.setFfmpegPath("\"D:\\software\\ffmpeg\\ffmpeg-7.1.1-essentials_build\\bin\\ffmpeg.exe\"");
        VideoUtil.setFfprobePath("\"D:\\software\\ffmpeg\\ffmpeg-7.1.1-essentials_build\\bin\\ffprobe.exe\"");

//        String url="https://ai-pc-test.oss-cn-beijing.aliyuncs.com/sample/video/-CQEDpYBd6YxY-31wA2Z.mp4";
        String url = "https://ai-pc-test.oss-cn-beijing.aliyuncs.com/test/yasuohou.mp4";
        String videoFilePath = "\"D:\\zancun\\test.mp4\"";

        //  生成视频接口
        // 初始化配置
        VideoAiGenerationHandle.analysisVideoConfig.setViduApiKey("vda_817610077610455040_MjJoDi63A027QugWtsze6i3ZzCqsEqos");
        // 构建生成请求
        ViduGenerateRequest viduRequest = new ViduGenerateRequest();
        viduRequest.setModel("vidu2.0");
        viduRequest.setImages(List.of(
                "data:image/png;base64," + VideoUtil.cutImageOfBase64(videoFilePath, 2000), // 获取视频帧
                "data:image/png;base64," + VideoUtil.cutImageOfBase64(videoFilePath, 3000)
        ));
        log.info(viduRequest.toString());
        viduRequest.setPrompt("化妆品展示：模特在自然光下展示唇彩的滋润效果");
        viduRequest.setResolution("720p");

        try {
            ViduGenerateResponse response = VideoAiGenerationHandle.generateVideo(viduRequest);
            System.out.println("生成任务ID：" + response.getTaskId());
            System.out.println("初始状态：" + response.getState());
        } catch (BusinessException e) {
            System.err.println("生成失败：" + e.getMessage());
        }


        //   查询视频生成状态接口
        try {
            // 查询之前生成的任务
            VideoAiGenerationHandle.TaskStatusResponse status = VideoAiGenerationHandle.queryTaskStatus("819343756103819264");

            System.out.println("任务状态：" + status.getState());
            if ("success".equals(status.getState())) {
                status.getCreations().forEach(creation -> {
                    System.out.println("生成视频URL：" + creation.getUrl());
                    System.out.println("封面URL：" + creation.getCover_url());
                });
            }
        } catch (BusinessException e) {
            System.err.println("查询失败：" + e.getMessage());
        }

    }

    /**
     * Vidu视频生成服务处理
     */
    @Data
    public static class ViduGenerateRequest {
        private String model;       // 模型版本
        private List<String> images; // 图片列表(1-3张)
        private String prompt;      // 提示词(<=1500字)
        private Integer duration = 4; // 视频时长
        private Integer seed = 0;    // 随机种子
        private String aspectRatio = "16:9"; // 画面比例
        private String resolution = "360p";  // 分辨率
        private String movementAmplitude = "auto"; // 运动幅度
        private String callbackUrl; // 回调地址
    }

    @Data
    public static class ViduGenerateResponse {
        private String taskId;
        private String state;
        private String model;
        private List<String> images;
        private String prompt;
        private Integer duration;
        private Integer seed;
        private String aspectRatio;
        private String resolution;
        private String movementAmplitude;
        private String createdAt;
    }

    /**
     * 调用Vidu视频生成API
     */
    public ViduGenerateResponse generateVideo(ViduGenerateRequest request) {
        // 参数校验
        validateViduRequest(request);

        // 构造请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", "Token " + analysisVideoConfig.getViduApiKey());

        // 构造请求体
        Map<String, Object> body = new HashMap<>();
        body.put("model", request.getModel());
        body.put("images", request.getImages());
        body.put("prompt", request.getPrompt());
        body.put("duration", request.getDuration());
        body.put("seed", request.getSeed());
        body.put("aspect_ratio", request.getAspectRatio());
        body.put("resolution", request.getResolution());
        body.put("movement_amplitude", request.getMovementAmplitude());
        if (StringUtils.isNotBlank(request.getCallbackUrl())) {
            body.put("callback_url", request.getCallbackUrl());
        }

        HttpEntity<Map<String, Object>> entity = new HttpEntity<>(body, headers);

        try {
            ResponseEntity<String> response = restTemplate.postForEntity(
                    "https://api.vidu.cn/ent/v2/reference2video",
                    entity,
                    String.class
            );

            return parseViduResponse(response.getBody());
        } catch (RestClientException e) {
            log.error("Vidu API调用失败", e);
            throw new BusinessException("视频生成服务暂时不可用");
        }
    }

    private void validateViduRequest(ViduGenerateRequest request) {
        // 模型校验
        if (!Arrays.asList("vidu2.0", "vidu1.5").contains(request.getModel())) {
            throw new BusinessException("不支持的模型版本");
        }

        // 图片数量校验
        if (request.getImages() == null || request.getImages().size() < 1 || request.getImages().size() > 3) {
            throw new BusinessException("请提供1-3张参考图片");
        }

        // 提示词长度校验
        if (request.getPrompt().length() > 1500) {
            throw new BusinessException("提示词长度不能超过1500个字符");
        }

        // 模型与分辨率兼容性校验
        if ("vidu2.0".equals(request.getModel())) {
            if (!Arrays.asList("360p", "720p").contains(request.getResolution())) {
                throw new BusinessException("Vidu2.0仅支持360p/720p分辨率");
            }
            if (request.getDuration() != 4) {
                throw new BusinessException("Vidu2.0仅支持4秒视频");
            }
        } else {
            if (request.getDuration() == 8 && !"720p".equals(request.getResolution())) {
                throw new BusinessException("8秒视频仅支持720p分辨率");
            }
        }
    }

    private ViduGenerateResponse parseViduResponse(String responseBody) {
        JSONObject json = JSONObject.parseObject(responseBody);

        ViduGenerateResponse response = new ViduGenerateResponse();
        response.setTaskId(json.getString("task_id"));
        response.setState(json.getString("state"));
        response.setModel(json.getString("model"));
        response.setImages(json.getJSONArray("images").toJavaList(String.class));
        response.setPrompt(json.getString("prompt"));
        response.setDuration(json.getInteger("duration"));
        response.setSeed(json.getInteger("seed"));
        response.setAspectRatio(json.getString("aspect_ratio"));
        response.setResolution(json.getString("resolution"));
        response.setMovementAmplitude(json.getString("movement_amplitude"));
        response.setCreatedAt(json.getString("created_at"));

        return response;
    }

    // 在VideoAiGenerationHandle类中添加以下内容

    @Data
    public static class TaskStatusResponse {
        private String id;
        private String state;
        private String err_code;
        private List<CreationItem> creations;
    }

    @Data
    public static class CreationItem {
        private String id;
        private String url;
        private String cover_url;
    }

    /**
     * 查询视频生成任务状态
     *
     * @param taskId 任务ID
     * @return 任务状态响应
     */
    public TaskStatusResponse queryTaskStatus(String taskId) {
        // 参数校验
        if (StringUtils.isBlank(taskId)) {
            throw new BusinessException("任务ID不能为空");
        }

        String url = "https://api.vidu.cn/ent/v2/tasks/" + taskId + "/creations";

        // 构造请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", "Token " + analysisVideoConfig.getViduApiKey());

        try {
            log.info("调用视频任务状态查询接口，任务ID：{}", taskId);
            ResponseEntity<String> response = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    new HttpEntity<>(headers),
                    String.class
            );

            TaskStatusResponse result = JSONObject.parseObject(
                    response.getBody(),
                    new TypeReference<TaskStatusResponse>() {
                    }
            );

            log.info("任务状态查询结果：{}", JSON.toJSONString(result));
            return result;
        } catch (RestClientException e) {
            log.error("视频任务状态查询失败，任务ID：{}", taskId, e);
            throw new BusinessException("任务状态查询失败：" + e.getMessage());
        }
    }
}    
