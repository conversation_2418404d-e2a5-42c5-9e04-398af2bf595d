package cn.mlamp.insightflow.cms.model.dto.dam;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import lombok.Data;

import jakarta.validation.constraints.NotNull;

/**
 * DAM回收站恢复DTO
 */
@Data
@Schema(description = "DAM回收站恢复DTO")
public class DamRecycleBinRecoverDTO {

    @NotNull(message = "回收站对象ID不能为空")
    @Schema(description = "回收站对象ID",
            requiredMode = RequiredMode.REQUIRED)
    private Integer recycleBinId;

    @Schema(description = "目标目录ID，仅恢复素材时需要")
    private Integer targetDirectoryId;

} 