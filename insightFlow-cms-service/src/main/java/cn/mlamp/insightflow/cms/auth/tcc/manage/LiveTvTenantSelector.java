package cn.mlamp.insightflow.cms.auth.tcc.manage;//package cn.mlamp.insightflow.cms.auth.tcc.manage;

import cn.hutool.core.collection.CollUtil;
import cn.mlamp.insightflow.cms.auth.tcc.model.Constants;
import com.mz.ttc.model.TenantInfo;
import com.mz.ttc.model.TicketInfo;
import com.mz.ttc.selector.AbstractTenantSelector;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;


@Slf4j
@Service
@Lazy
public class LiveTvTenantSelector extends AbstractTenantSelector {

    @Override
    public TenantInfo doSelect(TicketInfo ticketInfo, Integer tenantId) {
        List<TenantInfo> tenantInfos;
        if (ticketInfo == null || CollUtil.isEmpty(tenantInfos = ticketInfo.getTenants())) {
            return null;
        }

        TenantInfo tenantInfo = null;
        // 先找出所有有效的租户
        List<TenantInfo> availableList = tenantInfos.stream()
                .filter(it -> Constants.TTC_TENANT_USER_ACTIVE_STATUS.equals(it.getUserStatus()))
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(availableList)) {
            return null;
        }

        // 先找出一个候选的
        if (availableList.size() == Constants.INT_ONE) {
            tenantInfo = availableList.get(Constants.INT_ZERO);
        } else {
            // 有多个的话就找出登录时间有效且最近的租户，否则就选第一个
            tenantInfo = availableList.stream()
                    .filter(tenant -> null != tenant.getLastLoginTime())
                    .max(Comparator.comparing(TenantInfo::getLastLoginTime, Comparator.nullsFirst(Comparator.naturalOrder())))
                    .orElse(availableList.get(Constants.INT_ZERO));
        }

        // 切换租户的情况下指定tenantId
        if (tenantId != null && tenantId > 0) {
            tenantInfo = tenantInfos.stream()
                    .filter(w -> tenantId.equals(w.getId()) && Constants.TTC_TENANT_USER_ACTIVE_STATUS.equals(w.getUserStatus()))
                    .findFirst()
                    .orElse(tenantInfo);
        }
        return tenantInfo;

    }
}

