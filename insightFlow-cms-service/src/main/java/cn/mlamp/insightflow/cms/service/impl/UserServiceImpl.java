package cn.mlamp.insightflow.cms.service.impl;

import java.util.List;

import cn.mlamp.insightflow.cms.auth.tcc.manage.TtcUserService;
import cn.mlamp.insightflow.cms.auth.tcc.model.User;
import cn.mlamp.insightflow.cms.constant.PermissionConstant;
import com.mz.ttc.util.TtcUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import cn.mlamp.insightflow.cms.config.UserContext;
import cn.mlamp.insightflow.cms.entity.CmsUser;
import cn.mlamp.insightflow.cms.events.TenantInitializeEvent;
import cn.mlamp.insightflow.cms.mapper.UserMapper;
import cn.mlamp.insightflow.cms.service.IUserService;
import cn.mlamp.insightflow.cms.service.TenantTokenInitService;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @description 针对表【user(用户表)】的数据库操作Service实现
 * @createDate 2024-09-27 14:46:32
 */
@Slf4j
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, CmsUser>
        implements IUserService {

    @Autowired
    private TenantTokenInitService tenantTokenInitService;

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    @Autowired
    @Lazy
    private TtcUserService ttcUserService;

    @Override
    public void saveUser() {
        Integer userId = UserContext.getUserId();
        String userName = UserContext.getUserName();
        Integer tenantId = UserContext.getTenantId();

        // 1. 保存用户信息
        List<CmsUser> list = this.baseMapper
                .selectList(new LambdaQueryWrapper<CmsUser>().eq(CmsUser::getUserId, userId));
        if (list.isEmpty()) {
            CmsUser cmsUser = new CmsUser();
            cmsUser.setUserId(userId);
            cmsUser.setUserName(userName);
            this.baseMapper.insert(cmsUser);
           log.info("新用户信息已保存，userId: {}, userName: {}", userId, userName);
       }

       // 2. 初始化租户Token记录
       if (tenantId != null) {
           tenantTokenInitService.initTenantToken(tenantId);

           // 发布租户初始化事件
           eventPublisher.publishEvent(new TenantInitializeEvent(this, tenantId, userId));
       }
    }

    @Override
    public List<CmsUser> listByUserIds(List<Integer> userIds) {
        return this.baseMapper.selectList(new LambdaQueryWrapper<CmsUser>().in(CmsUser::getUserId, userIds));
    }

    /**
     * 用于调用 ttc 检查是否租户管理员
     */
    @RequiresPermissions({
            PermissionConstant.DAM_ADMIN
    })
    public boolean isTenantAdmin() {
        return true;
    }



    public boolean checkUserTenant(Integer tenantId) {
        String ticket = TtcUtil.getCurrentTicket();
        User user = ttcUserService.getCurrentUser(ticket);
        if(user == null){
            return false;
        }
        if(user.getTenants() == null){
            return false;
        }
        return user.getTenants().stream().anyMatch(tenant -> tenant.getId().equals(tenantId));
    }
}




