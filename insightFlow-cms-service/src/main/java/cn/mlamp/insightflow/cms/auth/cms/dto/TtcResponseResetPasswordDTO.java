package cn.mlamp.insightflow.cms.auth.cms.dto;

import lombok.Data;

import java.util.List;

/**
 * Ttc重置密码DTO
 *
 * <AUTHOR>
 * @since 2022年09月13日16:43:36
 **/
@Data
public class TtcResponseResetPasswordDTO {
    /**
     * 是否成功
     */
    private boolean success;
    /**
     * 用户唯一标识,success=true时有值
     */
    private String userNumber;
    /**
     * 错误信息，success=false时有值
     */
    private List<TtcResponseErrorDTO> errors;

}
