package cn.mlamp.insightflow.cms.mapper;

import cn.mlamp.insightflow.cms.entity.CmsSubjectInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;


public interface SubjectInfoMapper extends BaseMapper<CmsSubjectInfo> {

    /**
     * 查询主体总数
     */
    @Select("SELECT COUNT(id) FROM cms_subject_info")
    Integer countAllSubjects();



}
