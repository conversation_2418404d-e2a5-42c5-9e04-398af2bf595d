package cn.mlamp.insightflow.cms.service.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONException;
import org.json.JSONObject;
import org.redisson.api.RLock;

import cn.mlamp.insightflow.cms.adapter.EsAdapter;
import cn.mlamp.insightflow.cms.common.redis.LockService;
import cn.mlamp.insightflow.cms.entity.CmsPullTask;
import cn.mlamp.insightflow.cms.entity.CmsPullTaskDedupedData;
import cn.mlamp.insightflow.cms.entity.CmsPullTaskRawData;
import cn.mlamp.insightflow.cms.mapper.CmsPullTaskMapper;
import cn.mlamp.insightflow.cms.service.CmsPullTaskDedupedDataService;
import cn.mlamp.insightflow.cms.service.CmsPullTaskRawDataService;
import cn.mlamp.insightflow.cms.service.CmsPullTaskService;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.ai.document.Document;
// import org.springframework.ai.vectorstore.pgvector.PgVectorStore;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CmsPullTaskServiceImpl extends ServiceImpl<CmsPullTaskMapper, CmsPullTask> implements CmsPullTaskService {

    @Resource
    private CmsPullTaskRawDataService cmsPullTaskRawDataService; // 注入 Service

    @Resource
    private CmsPullTaskDedupedDataService cmsPullTaskDedupedDataService; // 注入 Service

    @Autowired
    public EsAdapter esAdapter;

    // @Autowired
    // @Qualifier("userNicknameVectorStore")
    // private PgVectorStore userNicknameVectorStore;

    // @Autowired
    // @Qualifier("textContentVectorStore")
    // private PgVectorStore textContentVectorStore;

    // @Autowired
    // @Qualifier("combinedDataVectorStore")
    // private PgVectorStore combinedDataVectorStore;

    @Autowired
    private LockService lockService;

    public static final Map<String, String> TRIBE_MAP;

    static {
        TRIBE_MAP = new HashMap<>();
        TRIBE_MAP.put("Tribe-1.1", "美妆护肤圈");
        TRIBE_MAP.put("Tribe-1.2", "美发圈");
        TRIBE_MAP.put("Tribe-1.3", "时尚穿搭圈");
        TRIBE_MAP.put("Tribe-1.4", "个性风格圈");
        TRIBE_MAP.put("Tribe-1.5", "生活态度圈");
        TRIBE_MAP.put("Tribe-1.6", "日常分享圈");
        TRIBE_MAP.put("Tribe-1.7", "手工手作圈");
        TRIBE_MAP.put("Tribe-1.8", "萌宠圈");
        TRIBE_MAP.put("Tribe-1.9", "园艺植物圈");
        TRIBE_MAP.put("Tribe-1.10", "户外探索圈");
        TRIBE_MAP.put("Tribe-1.11", "家居圈");
        TRIBE_MAP.put("Tribe-1.12", "美食圈");
        TRIBE_MAP.put("Tribe-2.1", "动漫圈");
        TRIBE_MAP.put("Tribe-2.2", "衍生内容圈");
        TRIBE_MAP.put("Tribe-2.3", "网文小说圈");
        TRIBE_MAP.put("Tribe-2.4", "模型玩具圈");
        TRIBE_MAP.put("Tribe-3.1", "游戏设备圈");
        TRIBE_MAP.put("Tribe-3.2", "游戏类型圈");
        TRIBE_MAP.put("Tribe-4.1", "公益圈");
        TRIBE_MAP.put("Tribe-5.1", "数码科技圈");
        TRIBE_MAP.put("Tribe-6.1", "极限运动圈");
        TRIBE_MAP.put("Tribe-6.2", "街头运动圈");
        TRIBE_MAP.put("Tribe-6.3", "户外运动圈");
        TRIBE_MAP.put("Tribe-6.4", "健身圈");
        TRIBE_MAP.put("Tribe-6.5", "传统运动圈");
        TRIBE_MAP.put("Tribe-7.1", "小众音乐圈");
        TRIBE_MAP.put("Tribe-7.2", "嘻哈文化圈");
        TRIBE_MAP.put("Tribe-7.3", "民族音乐圈");
        TRIBE_MAP.put("Tribe-7.4", "乐器圈");
        TRIBE_MAP.put("Tribe-8.1", "戏剧艺术圈");
        TRIBE_MAP.put("Tribe-8.2", "当代艺术圈");
        TRIBE_MAP.put("Tribe-8.3", "书画舞蹈圈");
    }

    /**
     * Spring 容器启动完成后，手动调用一次拉取任务
     */
    @PostConstruct
    public void init() {
        // executeIndustryDataPullTask();
        // executeTribeDataPullTask();
        // syncDataFromEsToDbTask();
    }

    // @Scheduled(cron = "0 0 0 * * ?")
    public void executeIndustryDataPullTask() {
        LocalDate endDate = LocalDate.now();

        // 循环 7 次，执行方法，并每天往前推一天
        for (int i = 0; i < 7; i++) {
            executeIndustryDataPull(endDate.minusDays(i));
        }
    }

    /**
     * 每天 0 点执行拉取任务
     */
    public void executeIndustryDataPull(LocalDate endDate) {
        // 如果 endDate 为空，则使用当前日期
        if (endDate == null) {
            endDate = LocalDate.now();
        }
        LocalDate startDate = endDate.minusDays(1);
        // 生成锁 key（按日期区分）
        String lockName = "industry_data_pull_" + endDate;
        RLock lock = lockService.getLock(lockName);
        try {
            boolean isLocked = lock.tryLock(0, 24, TimeUnit.HOURS);
            if (!isLocked) {
                System.out.println("已有其他实例在执行 " + endDate + " 的任务，跳过执行");
                return;
            }
            log.info("执行行业数据拉取任务，结束日期：" + endDate);

            log.info("=== 开始行业数据拉取任务 ===");

            // 创建任务
            CmsPullTask task = new CmsPullTask();
            task.setStartTime(new Date());
            task.setStatus("running");
            task.setType("Industry");
            // 将索引列表和查询条件存储到 task.setPullParams
            String[] indexs = generateIndexList(startDate, endDate);
            BoolQueryBuilder boolQuery = createIndustryBoolQuery("beauty", startDate, endDate);
            saveQueryInfoToTask(task, indexs, boolQuery);
            this.save(task);
            log.info("创建行业数据拉取任务 taskId: {}", task.getId());

            // 这里可以传入拉取参数
            String[] queryParameters = { "beauty", "health", "3c", "food", "automobile", "" };
            String failMessage = "";
            // 遍历所有查询参数，顺序执行每个查询
            for (String queryParam : queryParameters) {
                try {
                    Date startDateAsDate = java.sql.Date.valueOf(startDate);
                    long count = cmsPullTaskDedupedDataService.countRecentDedupedData(startDateAsDate, queryParam, 1);
                    if (count >= 250) {
                        log.info("已有250数据，跳过查询: {}", queryParam);
                        continue;
                    }
                    log.info("正在查询: {}", queryParam);
                    List<CmsPullTaskRawData> fetchedData = fetchIndustryDataForQuery(queryParam, task.getId(), indexs,
                            1, startDate, endDate);

                    // 如果有数据，插入数据库
                    if (!fetchedData.isEmpty()) {
                        // 3. 插入拉取数据
                        // 2. 批量插入数据
                        // cmsPullTaskRawDataService.batchInsertRawData(fetchedData);

                        // 4. 更新任务状态
                        LambdaUpdateWrapper<CmsPullTask> updateWrapper = new LambdaUpdateWrapper<>();
                        updateWrapper.set(CmsPullTask::getStatus, "pulled").eq(CmsPullTask::getId, task.getId());
                        this.update(updateWrapper);
                        log.info("=== 行业数据拉取任务完成 ===");

                        // 内容去重，ID去重等后续处理
                        log.info("=== 行业数据内容去重复 ===");
                        List<CmsPullTaskRawData> uniqueData = contentDeduplication(fetchedData);
                        log.info("=== 行业数据id去重复 ===");
                        Set<String> dedupedDataIds = queryCmsPullTaskDedupedDataIds(1, startDate, endDate);
                        List<CmsPullTaskRawData> idDeduplicationData = idDeduplication(uniqueData, dedupedDataIds);
                        log.info("行业数据发起聚类请求");
                        List<CmsPullTaskRawData> finalData = new ArrayList<>();
                        try {
                            finalData = filterByMaxInteraction(idDeduplicationData);
                        } catch (JSONException e) {
                            log.error("JSON 解析失败: {}", e.getMessage(), e);
                        }
                        log.info("发起行业聚类去重完成，保存去重数据到数据库");
                        // 计算还需要补充多少条数据
                        int remaining = 250 - (int) count;
                        if (finalData.size() > remaining) {
                            finalData = finalData.subList(0, remaining);
                        }
                        cmsPullTaskDedupedDataService.saveDedupedDatas(finalData);
                        // saveToVectorStore(finalData);
                        log.info("行业{}--> {}条数据，已完成处理", queryParam, finalData.size());
                    } else {
                        log.info("没有查询到数据: {}", queryParam);
                    }
                } catch (Exception e) {
                    log.error("查询失败: {}", queryParam, e);
                    failMessage += "拉取{}失败: " + queryParam + "Message: " + e.getMessage() + ", ";
                }
            }
            log.info("=== 所有行业数据拉取任务完成 ===");
            // 更新任务状态
            LambdaUpdateWrapper<CmsPullTask> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(CmsPullTask::getStatus, "completed").set(CmsPullTask::getEndTime, new Date())
                    .set(CmsPullTask::getFailMessage, failMessage).eq(CmsPullTask::getId, task.getId());
            this.update(updateWrapper);
        } catch (InterruptedException e) {
            e.printStackTrace();
        } finally {
            lock.unlock();
        }
    }

    // @Scheduled(cron = "0 30 0 * * ? ")
    public void executeTribeDataPullTask() {
        LocalDate endDate = LocalDate.now().minusDays(7);
        // 循环 7 次，执行方法，并每天往前推一天
        for (int i = 0; i < 7; i++) {
            executeTribeDataPull(endDate.minusDays(i));
        }
    }

    public void executeTribeDataPull(LocalDate endDate) {
        // 如果 endDate 为空，则使用当前日期
        if (endDate == null) {
            endDate = LocalDate.now();
        }
        LocalDate startDate = endDate.minusDays(1);
        String lockName = "tribe_data_pull_" + endDate;
        RLock lock = lockService.getLock(lockName);
        try {
            boolean isLocked = lock.tryLock(0, 24, TimeUnit.HOURS);
            if (!isLocked) {
                System.out.println("已有其他实例在执行 " + endDate + " 的任务，跳过执行");
                return;
            }
            log.info("执行圈层数据拉取任务，结束日期：" + endDate);

            log.info("=== 开始圈层数据拉取任务 ===");

            // 创建任务
            CmsPullTask task = new CmsPullTask();
            task.setStartTime(new Date());
            task.setStatus("running");
            task.setType("Tribe");
            // 将索引列表和查询条件存储到 task.setPullParams
            String[] indexs = generateIndexList(startDate, endDate);
            BoolQueryBuilder boolQuery = createTribeBoolQuery("Tribe-1.1", startDate, endDate);
            saveQueryInfoToTask(task, indexs, boolQuery);
            this.save(task);
            log.info("创建圈层数据拉取任务 taskId: {}", task.getId());

            // 这里可以传入拉取参数
            String[] queryParameters = TRIBE_MAP.keySet().toArray(new String[0]);
            String failMessage = "";
            // 遍历所有查询参数，顺序执行每个查询
            for (String queryParam : queryParameters) {
                try {
                    Date startDateAsDate = java.sql.Date.valueOf(startDate);
                    long count = cmsPullTaskDedupedDataService.countRecentDedupedData(startDateAsDate, queryParam, 2);
                    if (count >= 30) {
                        log.info("已有30数据，跳过查询: {}", queryParam);
                        continue;
                    }

                    log.info("正在查询: {}", queryParam);
                    List<CmsPullTaskRawData> fetchedData = fetchTribeDataForQuery(queryParam, task.getId(), indexs, 2,
                            startDate, endDate);

                    // 如果有数据，插入数据库
                    if (!fetchedData.isEmpty()) {
                        // 3. 插入拉取数据
                        // cmsPullTaskRawDataService.batchInsertRawData(fetchedData);

                        // 4. 更新任务状态
                        LambdaUpdateWrapper<CmsPullTask> updateWrapper = new LambdaUpdateWrapper<>();
                        updateWrapper.set(CmsPullTask::getStatus, "pulled").eq(CmsPullTask::getId, task.getId());
                        this.update(updateWrapper);

                        log.info("=== 圈层数据拉取任务完成 ===");

                        // 内容去重，ID去重等后续处理
                        log.info("=== 圈层数据内容去重复 ===");
                        List<CmsPullTaskRawData> uniqueData = contentDeduplication(fetchedData);
                        log.info("=== 圈层数据id去重复 ===");
                        Set<String> dedupedDataIds = queryCmsPullTaskDedupedDataIds(2, startDate, endDate);
                        List<CmsPullTaskRawData> idDeduplicationData = idDeduplication(uniqueData, dedupedDataIds);
                        log.info("发起圈层数据聚类请求");
                        List<CmsPullTaskRawData> finalData = new ArrayList<>();
                        try {
                            finalData = filterByMaxInteraction(idDeduplicationData);
                        } catch (JSONException e) {
                            log.error("JSON 解析失败: {}", e.getMessage(), e);
                        }
                        log.info("发起圈层聚类去重完成，保存去重数据到数据库");
                        // 计算还需要补充多少条数据
                        int remaining = 30 - (int) count;
                        if (finalData.size() > remaining) {
                            finalData = finalData.subList(0, remaining);
                        }
                        cmsPullTaskDedupedDataService.saveDedupedDatas(finalData);
                        log.info("{}--> {}条数据，已完成处理", queryParam, finalData.size());
                    } else {
                        log.info("没有查询到数据: {}", queryParam);
                    }
                } catch (Exception e) {
                    log.error("查询失败: {}", queryParam, e);
                    failMessage += "拉取{}失败: " + queryParam + "Message: " + e.getMessage() + ", ";
                }
            }
            log.info("=== 所有圈层数据拉取任务完成 ===");
            // 更新任务状态
            LambdaUpdateWrapper<CmsPullTask> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(CmsPullTask::getStatus, "completed").set(CmsPullTask::getEndTime, new Date())
                    .set(CmsPullTask::getFailMessage, failMessage).eq(CmsPullTask::getId, task.getId());
            this.update(updateWrapper);
        } catch (InterruptedException e) {
            e.printStackTrace();
        } finally {
            lock.unlock();
        }

    }

    // @Scheduled(cron = "0 0 1 * * ? ")
    public void syncDataFromEsToDbTask() {
        LocalDate endDate = LocalDate.now();
        syncDataFromEsToDb(endDate);
    }

    public void syncDataFromEsToDb(LocalDate endDate) {
        try {
            // 如果 endDate 为空，则使用当前日期
            if (endDate == null) {
                endDate = LocalDate.now();
            }
            LocalDate startDate = endDate.minusDays(8);
            String lockName = "sync_Data_from_es" + endDate;
            RLock lock = lockService.getLock(lockName);
            try {
                boolean isLocked = lock.tryLock(0, 24, TimeUnit.HOURS);
                if (!isLocked) {
                    System.out.println("已有其他实例在执行 " + endDate + " 的任务，跳过执行");
                    return;
                }
                log.info("执行数据同步任务，结束日期：" + endDate);

                // 创建任务
                CmsPullTask task = new CmsPullTask();
                task.setStartTime(new Date());
                task.setStatus("running");
                task.setType("sync");
                // 将索引列表和查询条件存储到 task.setPullParams
                String[] indexList = generateIndexList(startDate, endDate);
                BoolQueryBuilder boolQuery = new BoolQueryBuilder();
                saveQueryInfoToTask(task, indexList, boolQuery);
                this.save(task);

                try {

                    // 2. 查询数据库，获取 `esId`
                    List<CmsPullTaskDedupedData> dbRecords = cmsPullTaskDedupedDataService
                            .getTribeDataForAll(Arrays.asList(1), startDate, endDate);

                    if (dbRecords.isEmpty()) {
                        log.info("数据库中无需要同步的行业数据");
                        return;
                    }

                    Map<String, CmsPullTaskDedupedData> esIdToDbIdMap = dbRecords.stream()
                            .collect(Collectors.toMap(CmsPullTaskDedupedData::getEsId, data -> data));

                    // 3. 从 ES 拉取数据
                    List<CmsPullTaskDedupedData> updateList = fetchDataFromEsReturnDedupedData(
                            new ArrayList<>(esIdToDbIdMap.keySet()), indexList, esIdToDbIdMap);

                    if (!updateList.isEmpty()) {
                        cmsPullTaskDedupedDataService.updateBatch(updateList);
                        log.info("成功更新 {} 条行业数据", updateList.size());
                    } else {
                        log.info("没有需要更新的行业数据");
                    }
                    // 2. 查询数据库，获取 `esId`
                    List<CmsPullTaskDedupedData> newDbRecords = cmsPullTaskDedupedDataService
                            .getTribeDataForAll(Arrays.asList(2), startDate, endDate);

                    if (newDbRecords.isEmpty()) {
                        log.info("数据库中无需要同步的圈层数据");
                        return;
                    }

                    Map<String, CmsPullTaskDedupedData> newEsIdToDbIdMap = newDbRecords.stream()
                            .collect(Collectors.toMap(CmsPullTaskDedupedData::getEsId, data -> data));

                    // 3. 从 ES 拉取数据
                    List<CmsPullTaskDedupedData> newUpdateList = fetchDataFromEsReturnDedupedData(
                            new ArrayList<>(newEsIdToDbIdMap.keySet()), indexList, newEsIdToDbIdMap);

                    if (!newUpdateList.isEmpty()) {
                        cmsPullTaskDedupedDataService.updateBatch(newUpdateList);
                        log.info("成功更新 {} 条圈层数据", newUpdateList.size());
                    } else {
                        log.info("没有需要更新的圈层数据");
                    }
                    // 更新任务状态
                    LambdaUpdateWrapper<CmsPullTask> updateWrapper = new LambdaUpdateWrapper<>();
                    updateWrapper.set(CmsPullTask::getStatus, "completed").set(CmsPullTask::getEndTime, new Date())
                            .set(CmsPullTask::getFailMessage, "").eq(CmsPullTask::getId, task.getId());
                    this.update(updateWrapper);
                } catch (Exception e) {
                    log.error("同步数据时发生错误", e);
                    LambdaUpdateWrapper<CmsPullTask> updateWrapper = new LambdaUpdateWrapper<>();
                    updateWrapper.set(CmsPullTask::getStatus, "failed").set(CmsPullTask::getEndTime, new Date())
                            .set(CmsPullTask::getFailMessage, e.getMessage()).eq(CmsPullTask::getId, task.getId());
                    this.update(updateWrapper);
                }

            } catch (InterruptedException e) {
                e.printStackTrace();
            } finally {
                lock.unlock();
            }
        } catch (Exception e) {
            log.error("同步数据时发生错误", e);

        }

    }

    private String[] generateIndexList(LocalDate startDate, LocalDate endDate) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");

        List<String> indexList = new ArrayList<>();
        for (LocalDate date = startDate; date.isBefore(endDate); date = date.plusDays(1)) {
            indexList.add("ik_sl_v2_" + date.format(formatter) + "_video");
        }

        return indexList.toArray(new String[0]);
    }

    private List<CmsPullTaskRawData> fetchIndustryDataForQuery(String queryParam, Integer taskId, String[] indexs,
            Integer type, LocalDate startDate, LocalDate endDate) {
        // 创建一个基本的查询条件
        BoolQueryBuilder boolQuery = null;
        boolQuery = createIndustryBoolQuery(queryParam, startDate, endDate);

        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        sourceBuilder.query(boolQuery);
        sourceBuilder.size(500);
        sourceBuilder.sort("long_interactCount", SortOrder.DESC);

        SearchRequest searchRequest = new SearchRequest(indexs);
        searchRequest.source(sourceBuilder);

        try {
            SearchResponse searchResponse = esAdapter.es7Client.search(searchRequest, RequestOptions.DEFAULT);
            return fetchDataFromEsResponse(searchResponse, taskId, type);
        } catch (Exception e) {
            log.error("查询失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    private List<CmsPullTaskRawData> fetchTribeDataForQuery(String queryParam, Integer taskId, String[] indexs,
            Integer type, LocalDate startDate, LocalDate endDate) {
        // 创建一个基本的查询条件
        BoolQueryBuilder boolQuery = null;
        boolQuery = createTribeBoolQuery(queryParam, startDate, endDate);

        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        sourceBuilder.query(boolQuery);
        sourceBuilder.size(60);
        sourceBuilder.sort("long_interactCount", SortOrder.DESC);

        SearchRequest searchRequest = new SearchRequest(indexs);
        searchRequest.source(sourceBuilder);

        try {
            SearchResponse searchResponse = esAdapter.es7Client.search(searchRequest, RequestOptions.DEFAULT);
            return fetchDataFromEsResponse(searchResponse, taskId, type);
        } catch (Exception e) {
            log.error("查询失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 模拟从外部拉取数据
     */
    private List<CmsPullTaskRawData> fetchDataFromEsResponse(SearchResponse searchResponse, Integer taskId,
                                                             Integer type) {
        List<CmsPullTaskRawData> dataList = new ArrayList<>();

        // 获取从 Elasticsearch 返回的 hits
        SearchHit[] hits = searchResponse.getHits().getHits();

        // 将每一条数据转化为 CmsPullTaskRawData 对象并加入列表
        for (SearchHit hit : hits) {
            CmsPullTaskRawData data = new CmsPullTaskRawData();
            data.setTaskId(taskId);
            data.setEsId(hit.getId());
            data.setType(type);
            data.setKwSource((String) hit.getSourceAsMap().get("kw_source"));
            data.setTextContent((String) hit.getSourceAsMap().get("text_content"));
            data.setTextTitle((String) hit.getSourceAsMap().get("text_title"));
            data.setKwUrl((String) hit.getSourceAsMap().get("kw_url"));
            String dateStr = (String) hit.getSourceAsMap().get("date_publishedAt");
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
            try {
                Date date = sdf.parse(dateStr);
                data.setDatePublishedAt(date);
            } catch (Exception e) {
                e.printStackTrace();
                // 处理解析异常，可以设置为 null 或其他默认值
                data.setDatePublishedAt(null);
            }
            data.setBoolIsDeleted((Boolean) hit.getSourceAsMap().get("bool_isDeleted"));
            data.setLongRepostCount(parseLong(hit.getSourceAsMap().get("long_repostCount")));
            data.setLongCommentCount(parseLong(hit.getSourceAsMap().get("long_commentCount")));
            data.setLongInteractCount(parseLong(hit.getSourceAsMap().get("long_interactCount")));
            data.setLongLikeCount(parseLong(hit.getSourceAsMap().get("long_likeCount")));
            data.setTextNickName((String) hit.getSourceAsMap().get("text_nickName"));
            Object dataKwKbIndustryObj = hit.getSourceAsMap().get("kw_kbIndustry");
            if (dataKwKbIndustryObj != null && dataKwKbIndustryObj instanceof List) {
                List<String> dataKwKbIndustryList = (List<String>) dataKwKbIndustryObj;
                data.setKwKbIndustry(String.join(",", dataKwKbIndustryList));
            } else {
                data.setKwKbIndustry("");
            }
            Object dataTwoLevelTribeTagObj = hit.getSourceAsMap().get("kw_twoLevelTribeTag");
            if (dataTwoLevelTribeTagObj != null && dataTwoLevelTribeTagObj instanceof List) {
                List<String> dataTwoLevelTribeTagList = (List<String>) dataTwoLevelTribeTagObj;
                data.setKwTwoLevelTribeTag(String.join(",", dataTwoLevelTribeTagList));
            } else {
                data.setKwTwoLevelTribeTag("");
            }
            data.setKwProfileImageUrl((String) hit.getSourceAsMap().get("kw_profileImageUrl"));
            data.setKwUserUrl((String) hit.getSourceAsMap().get("kw_userUrl"));
            data.setLongVideoDuration(parseLong(hit.getSourceAsMap().get("long_videoDuration")));
            data.setKwHeadImage((String) hit.getSourceAsMap().get("kw_headImage"));
            data.setKwVideoUrl((String) hit.getSourceAsMap().get("kw_videoUrl"));
            data.setKwVideoContent((String) hit.getSourceAsMap().get("kw_videoContent"));
            data.setKwCommonSentimentPlus((String) hit.getSourceAsMap().get("kw_commonSentimentPlus"));
            Object dataTagPlusObj = hit.getSourceAsMap().get("kw_dataTagPlus");
            if (dataTagPlusObj != null && dataTagPlusObj instanceof List) {
                List<String> dataTagPlusList = (List<String>) dataTagPlusObj;
                data.setKwDataTagPlus(String.join(",", dataTagPlusList)); // 以逗号分隔元素并将它们连接成一个字符串
            } else {
                data.setKwDataTagPlus(""); // 或者设置为空字符串，如果值为空或不是一个 List
            }
            data.setLongViewCount(parseLong(hit.getSourceAsMap().get("long_viewCount")));
            data.setLongFollowerCount(parseLong(hit.getSourceAsMap().get("long_followerCount")));
            data.setLongCollectCount(parseLong(hit.getSourceAsMap().get("long_collectCount")));
            data.setRawData(hit.getSourceAsString());

            // 加入数据列表
            dataList.add(data);
        }

        return dataList;
    }

    private void saveQueryInfoToTask(CmsPullTask task, String[] indexs, BoolQueryBuilder boolQuery) {
        // 将索引列表和过滤条件转换为 JSON 字符串
        String indexListJson = Arrays.toString(indexs);
        String filterJson = boolQuery.toString(); // 转换查询条件为 JSON 字符串

        // 创建一个 JSON 字符串，存储到 task 的 pullParams 中
        String pullParamsJson = String.format("{\"indexList\": \"%s\", \"filter\": \"%s\"}", indexListJson, filterJson);

        // 存储到 task
        task.setPullParams(pullParamsJson);

    }

    private BoolQueryBuilder createIndustryBoolQuery(String kw_kbIndustry, LocalDate startDate, LocalDate endDate) {
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        String startTime = startDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String endTime = endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        // must 逻辑
        BoolQueryBuilder mustQuery = QueryBuilders.boolQuery()
                .must(QueryBuilders.rangeQuery("long_interactCount").gte(500))
                .must(QueryBuilders.termQuery("kw_docType", "1"))
                .must(QueryBuilders.termQuery("kw_source", "douyin.com"))
                .must(QueryBuilders.rangeQuery("long_videoDuration").gt(15))
                .must(QueryBuilders.rangeQuery("long_videoDuration").lt(120))
                .must(QueryBuilders.termsQuery("kw_commonSentimentPlus", "正面", "中性"))
                .must(QueryBuilders.rangeQuery("long_contentLength").gte(5))
                .must(QueryBuilders.rangeQuery("date_publishedAt").gte(startTime).lte(endTime))
                .must(QueryBuilders.existsQuery("kw_kbIndustry")); // 添加字段存在检查

        if (kw_kbIndustry != null && !kw_kbIndustry.isEmpty()) {
            mustQuery.must(QueryBuilders.termQuery("kw_kbIndustry", kw_kbIndustry));
        }
        // 作为 filter
        boolQuery.filter(mustQuery);

        return boolQuery;
    }

    private BoolQueryBuilder createTribeBoolQuery(String kw_twoLevelTribeTag, LocalDate startDate, LocalDate endDate) {
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        String startTime = startDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String endTime = endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        // must 逻辑
        BoolQueryBuilder mustQuery = QueryBuilders.boolQuery()
                .must(QueryBuilders.rangeQuery("long_interactCount").gte(500))
                .must(QueryBuilders.termQuery("kw_docType", "1"))
                .must(QueryBuilders.termQuery("kw_source", "douyin.com"))
                .must(QueryBuilders.rangeQuery("long_videoDuration").gt(15))
                .must(QueryBuilders.rangeQuery("long_videoDuration").lt(120))
                .must(QueryBuilders.termsQuery("kw_commonSentimentPlus", "正面", "中性"))
                .must(QueryBuilders.rangeQuery("long_contentLength").gte(5))
                .must(QueryBuilders.rangeQuery("date_publishedAt").gte(startTime).lte(endTime))
                .must(QueryBuilders.termQuery("kw_twoLevelTribeTag", kw_twoLevelTribeTag));

        // 作为 filter
        boolQuery.filter(mustQuery);

        return boolQuery;
    }

    private Long parseLong(Object value) {
        if (value == null) {
            return null; // 或者设置为默认值，比如 0L
        }

        try {
            // 如果是字符串类型，进行转换
            if (value instanceof String) {
                return Long.parseLong((String) value);
            } else if (value instanceof Number) {
                // 如果已经是数字类型，直接返回
                return ((Number) value).longValue();
            }
        } catch (NumberFormatException e) {
            e.printStackTrace();
        }

        return null; // 或者返回一个默认值，如 0L
    }

    // 内容去重 - 保留互动数较高的记录
    private List<CmsPullTaskRawData> contentDeduplication(List<CmsPullTaskRawData> fetchedData) {
        return fetchedData.stream()
                .collect(Collectors.toMap(
                        CmsPullTaskRawData::getTextContent, // 根据 text_content 去重
                        data -> data,
                        (existing, replacement) -> existing.getLongInteractCount() > replacement.getLongInteractCount()
                                ? existing
                                : replacement // 保留互动数较高的记录
                ))
                .values()
                .stream()
                .collect(Collectors.toList()); // 转换成 List
    }

    private Set<String> queryCmsPullTaskDedupedDataIds(Integer type, LocalDate startDate, LocalDate endDate) {
        // 将 LocalDate 转换为 Date（开始时间）
        Date startDateTime = Date.from(startDate.atStartOfDay(ZoneId.systemDefault()).toInstant());

        // 将 LocalDate 转换为 Date（结束时间）
        Date endDateTime = Date.from(endDate.atTime(LocalTime.MAX).atZone(ZoneId.systemDefault()).toInstant());

        // 查询指定时间范围的数据
        List<CmsPullTaskDedupedData> dedupedDataList = cmsPullTaskDedupedDataService.getRecentDedupedData(startDateTime,
                endDateTime, type);

        // 提取所有的 id 并返回去重后的 Set
        return dedupedDataList.stream().map(data -> (String) data.getEsId()).collect(Collectors.toSet());

    }

    // ID 去重 - 从数据中去除已存在于 CmsPullTaskDedupedData 中的 id
    private List<CmsPullTaskRawData> idDeduplication(List<CmsPullTaskRawData> uniqueData, Set<String> dedupedDataIds) {
        return uniqueData.stream()
                .filter(data -> !dedupedDataIds.contains(data.getEsId())) // 过滤掉重复的 id
                .collect(Collectors.toList()); // 只保留不重复的记录
    }

    private List<CmsPullTaskRawData> filterByMaxInteraction(List<CmsPullTaskRawData> idDeduplicationData)
            throws JSONException {
        JSONObject dataJson = new JSONObject();
        for (CmsPullTaskRawData data : idDeduplicationData) {
            dataJson.put(data.getEsId(), data.getTextContent());
        }

        JSONObject requestJson = new JSONObject();
        requestJson.put("min_sim", 0.5);
        requestJson.put("k", 100);
        requestJson.put("mask_len", 512);
        requestJson.put("data", dataJson == null ? new JSONObject() : dataJson);
        HttpResponse response = null;
        String responseBody = null;
        try {
            response = HttpRequest.post("http://10.10.100.228:8103/cluster").header("Content-Type", "application/json")
                    .body(requestJson.toString()).timeout(180 * 1000).execute();
            responseBody = response.body(); // 获取响应内容
        } catch (Exception e) {
            log.error("聚类请求失败: {}", e.getMessage(), e);
            throw new RuntimeException("聚类请求失败" + "原因：" + e.getMessage());
        } finally {
            if (response != null) {
                response.close();
            }
        }
        // 解析 responseBody
        JSONObject responseJson = new JSONObject(responseBody);
        JSONObject resultJson = responseJson.optJSONObject("result");
        if (resultJson == null) {
            log.warn("聚类结果为空，直接返回原始数据");
            return idDeduplicationData;
        }

        // 1. 构建分类映射: Map<类别, List<es_id>>
        Map<String, List<String>> clusterGroups = new HashMap<>();
        for (Iterator<String> it = resultJson.keys(); it.hasNext();) {
            String esId = it.next();
            String clusterId = String.valueOf(resultJson.get(esId)); // 获取分类 ID
            clusterGroups.computeIfAbsent(clusterId, k -> new ArrayList<>()).add(esId);
        }

        // 2. 构建互动数映射: Map<es_id, 互动数>
        Map<String, Long> interactionCountMap = idDeduplicationData.stream()
                .collect(Collectors.toMap(CmsPullTaskRawData::getEsId, CmsPullTaskRawData::getLongInteractCount));

        // 3. 选择每个类别中互动数最高的 es_id
        Set<String> selectedEsIds = new HashSet<>();
        for (List<String> esIdList : clusterGroups.values()) {
            String bestEsId = esIdList.stream()
                    .max(Comparator.comparingLong(esId -> interactionCountMap.getOrDefault(esId, 0L))).orElse(null);
            if (bestEsId != null) {
                selectedEsIds.add(bestEsId);
            }
        }

        // 4. 根据筛选出的 es_id 过滤 finalData
        return idDeduplicationData.stream().filter(data -> selectedEsIds.contains(data.getEsId()))
                .collect(Collectors.toList());
    }

    public List<CmsPullTaskDedupedData> fetchDataFromEsReturnDedupedData(List<String> esIdList, String[] indexList,
            Map<String, CmsPullTaskDedupedData> esIdToDbIdMap) {
        List<CmsPullTaskDedupedData> updateList = new ArrayList<>();
        int batchSize = 500; // 每批次的大小，可以根据实际情况调整
        int totalSize = esIdList.size();

        // 将 esIdList 按照 batchSize 切分为多个批次进行查询
        for (int i = 0; i < totalSize; i += batchSize) {
            List<String> batch = esIdList.subList(i, Math.min(i + batchSize, totalSize));

            // 1. 创建基本的查询条件
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();

            // 2. 使用 terms 查询来匹配这些 esId
            boolQuery.filter(QueryBuilders.termsQuery("_id", batch));

            // 3. 设置查询条件和排序
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
            sourceBuilder.query(boolQuery);
            sourceBuilder.size(batchSize); // 每次查询的大小

            // 4. 创建 SearchRequest 对象并执行查询
            SearchRequest searchRequest = new SearchRequest(indexList);
            searchRequest.source(sourceBuilder);

            // 5. 执行查询并处理响应
            try {
                SearchResponse searchResponse = esAdapter.es7Client.search(searchRequest, RequestOptions.DEFAULT);
                // 将查询结果放入 result 中，获取 CmsPullTaskDedupedData 对象
                updateList.addAll(fetchDataFromEsResponse(searchResponse, esIdToDbIdMap)); // 处理查询响应并合并结果
            } catch (IOException e) {
                log.error("从ES查询数据失败", e);
            }
        }

        return updateList; // 返回包含 CmsPullTaskDedupedData 对象的结果
    }

    public List<CmsPullTaskDedupedData> fetchDataFromEsResponse(SearchResponse searchResponse,
            Map<String, CmsPullTaskDedupedData> esIdToDbIdMap) {
        List<CmsPullTaskDedupedData> updateList = new ArrayList<>();

        // 处理搜索响应，将结果转换为 CmsPullTaskDedupedData 对象并放入 result
        for (SearchHit hit : searchResponse.getHits()) {
            String esId = hit.getId(); // 假设 esId 就是 Elasticsearch 文档的 ID
            CmsPullTaskDedupedData data = new CmsPullTaskDedupedData();

            // 填充 CmsPullTaskDedupedData 对象的属性
            data.setKwSource((String) hit.getSourceAsMap().get("kw_source"));
            data.setTextContent((String) hit.getSourceAsMap().get("text_content"));
            data.setTextTitle((String) hit.getSourceAsMap().get("text_title"));
            data.setKwUrl((String) hit.getSourceAsMap().get("kw_url"));

            String dateStr = (String) hit.getSourceAsMap().get("date_publishedAt");
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
            try {
                Date date = sdf.parse(dateStr);
                data.setDatePublishedAt(date);
            } catch (Exception e) {
                e.printStackTrace();
                // 处理解析异常，可以设置为 null 或其他默认值
                data.setDatePublishedAt(null);
            }

            data.setBoolIsDeleted((Boolean) hit.getSourceAsMap().get("bool_isDeleted"));
            data.setLongRepostCount(parseLong(hit.getSourceAsMap().get("long_repostCount")));
            data.setLongCommentCount(parseLong(hit.getSourceAsMap().get("long_commentCount")));
            data.setLongInteractCount(parseLong(hit.getSourceAsMap().get("long_interactCount")));
            data.setLongLikeCount(parseLong(hit.getSourceAsMap().get("long_likeCount")));
            data.setTextNickName((String) hit.getSourceAsMap().get("text_nickName"));

            Object dataKwKbIndustryObj = hit.getSourceAsMap().get("kw_kbIndustry");
            if (dataKwKbIndustryObj != null && dataKwKbIndustryObj instanceof List) {
                List<String> dataKwKbIndustryList = (List<String>) dataKwKbIndustryObj;
                data.setKwKbIndustry(String.join(",", dataKwKbIndustryList));
            } else {
                data.setKwKbIndustry("");
            }

            Object dataTwoLevelTribeTagObj = hit.getSourceAsMap().get("kw_twoLevelTribeTag");
            if (dataTwoLevelTribeTagObj != null && dataTwoLevelTribeTagObj instanceof List) {
                List<String> dataTwoLevelTribeTagList = (List<String>) dataTwoLevelTribeTagObj;
                data.setKwTwoLevelTribeTag(String.join(",", dataTwoLevelTribeTagList));
            } else {
                data.setKwTwoLevelTribeTag("");
            }

            data.setKwProfileImageUrl((String) hit.getSourceAsMap().get("kw_profileImageUrl"));
            data.setKwUserUrl((String) hit.getSourceAsMap().get("kw_userUrl"));
            data.setLongVideoDuration(parseLong(hit.getSourceAsMap().get("long_videoDuration")));
            data.setKwHeadImage((String) hit.getSourceAsMap().get("kw_headImage"));
            data.setKwVideoUrl((String) hit.getSourceAsMap().get("kw_videoUrl"));

            data.setKwCommonSentimentPlus((String) hit.getSourceAsMap().get("kw_commonSentimentPlus"));

            Object dataTagPlusObj = hit.getSourceAsMap().get("kw_dataTagPlus");
            if (dataTagPlusObj != null && dataTagPlusObj instanceof List) {
                List<String> dataTagPlusList = (List<String>) dataTagPlusObj;
                data.setKwDataTagPlus(String.join(",", dataTagPlusList)); // 以逗号分隔元素并将它们连接成一个字符串
            } else {
                data.setKwDataTagPlus(""); // 或者设置为空字符串，如果值为空或不是一个 List
            }

            data.setLongViewCount(parseLong(hit.getSourceAsMap().get("long_viewCount")));
            data.setLongFollowerCount(parseLong(hit.getSourceAsMap().get("long_followerCount")));
            data.setLongCollectCount(parseLong(hit.getSourceAsMap().get("long_collectCount")));
            CmsPullTaskDedupedData cms = esIdToDbIdMap.get(esId);

            if (cms != null) {
                data.setId(cms.getId()); // 设置数据库 ID
                data.setEsId(esId);
                data.setTaskId(cms.getTaskId());
                data.setType(cms.getType());
                data.setRating(cms.getRating());
                data.setSourceType(cms.getSourceType());
                data.setUserId(cms.getUserId());
                data.setTenantId(cms.getTenantId());
                data.setAnalysisStatus(cms.getAnalysisStatus());
                data.setDownloadStatus(cms.getDownloadStatus());
                data.setDbUniqueId(cms.getDbUniqueId());
                data.setDownloadDate(cms.getDownloadDate());
                data.setCreateTime(cms.getCreateTime());
                data.setIsDeleted(cms.getIsDeleted());
                data.setKwVideoContent(cms.getKwVideoContent());
            }

            updateList.add(data);
        }

        return updateList;
    }

    // public void saveToVectorStore(List<CmsPullTaskRawData> rawDataList) {

    // // 创建两个列表，分别存储 TextNickName 和 TextContent 的 Document
    // List<Document> textNickNamesDocuments = new ArrayList<>();
    // List<Document> textContentsDocuments = new ArrayList<>();

    // // 遍历 rawDataList，分别构造 Document 对象并存储
    // for (CmsPullTaskRawData rawData : rawDataList) {
    // // 创建存储 TextNickName 的 Document
    // textNickNamesDocuments.add(new Document(rawData.getTextNickName(), // 存储
    // TextNickName
    // Map.of("id", rawData.getEsId(), "datePublishedAt",
    // rawData.getDatePublishedAt()) // 存储 id
    // ));

    // // 创建存储 TextContent 的 Document
    // textContentsDocuments.add(new Document(rawData.getTextContent(), // 存储
    // TextContent
    // Map.of("id", rawData.getEsId(), "datePublishedAt",
    // rawData.getDatePublishedAt()) // 存储 id
    // ));
    // }

    // // 将 TextNickNames 和 TextContents 的 Document 列表保存到向量库
    // userNicknameVectorStore.add(textNickNamesDocuments);
    // textContentVectorStore.add(textContentsDocuments);
    // List<Document> combinedDocuments = new ArrayList<>();
    // combinedDocuments.addAll(textContentsDocuments);
    // combinedDocuments.addAll(textNickNamesDocuments);
    // combinedDataVectorStore.add(combinedDocuments);
    // log.info("数据已成功保存到向量库");

    // }

}
