package cn.mlamp.insightflow.cms.model.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * @Author: husuper
 * @CreateTime: 2025-03-19
 */
@Data
public class VideoInfoVO implements Serializable {

    @Schema(description = "视频分析Id", required = true)
    private Integer id;

    @Schema(description = "1:视频分析;2：黄金5秒;3:圈层热点；4：上传视频", required = true)
    private Integer type;

    @Schema(description = "帖子ID", required = true)
    private String esId;

    @Schema(description = "状态名称 1：待处理；2：处理中，3：完成，4：失败", required = true)
    private String statusName;

    @Schema(description = "状态 1：待处理；2：处理中，3：完成，4：失败", required = true)
    private Integer status;

    @Schema(description = "用户userID", required = false)
    private Integer userId;

    @Schema(description = "四有三好分数", required = false)
    private Integer rating;

    @Schema(description = "视频结果", required = false)
    private List<VideoInfoReuslt> videoInfoReuslts;


    @Data
    public static class VideoInfoReuslt implements Serializable {

        @Schema(description = "视频分析结果Id", required = true)
        private Integer videoInfoReusltId;

        @Schema(description = "视频分析Id", required = true)
        private Integer videoId;

        @Schema(description = "1：AI解码；2：分镜；3：ASR", required = true)
        private Integer type;

        @Schema(description = "data数据序号", required = true)
        private Integer index;

        @Schema(description = "视频分析结果数据,json格式", required = true)
        private String data;

    }

    @Schema(description = "拉取的原始数据", required = false)
    private PullVideoInfoVO pullVideoInfo;


    @Data
    public static class PullVideoInfoVO implements Serializable {

        @Schema(description = "内容", required = false)
        private String textContent; // 内容

        @Schema(description = "标题", required = false)
        private String textTitle; // 标题

        @Schema(description = "转发数", required = false)
        private Long longRepostCount; // 转发数

        @Schema(description = "评论数", required = false)
        private Long longCommentCount; // 评论数

        @Schema(description = "互动数", required = false)
        private Long longInteractCount; // 互动数

        @Schema(description = "点赞数", required = false)
        private Long longLikeCount; // 点赞数

        @Schema(description = "视频头图", required = false)
        private String kwHeadImage; // 视频头图

        @Schema(description = "视频时长", required = false)
        private Integer taskId; // 任务 ID，关联 cms_pull_tasks

        @Schema(description = "视频链接", required = false)
        private String esId; // ES 唯一 ID（外部系统唯一标识）

        @Schema(description = "行业分类", required = false)
        private String kwKbIndustry; // 圈成关键词行业

        @Schema(description = "帖子链接", required = false)
        private String kwUrl; // 帖子链接

        @Schema(description = "发布时间", required = false)
        private Date datePublishedAt; // 发布时间

        @Schema(description = "是否删除", required = false)
        private Boolean boolIsDeleted; // 是否删除

        @Schema(description = "用户昵称", required = false)
        private String textNickName; // 用户昵称

        @Schema(description = "用户头像链接", required = false)
        private String kwProfileImageUrl; // 用户头像链接

        @Schema(description = "用户主页链接", required = false)
        private String kwUserUrl; // 用户主页链接

        @Schema(description = "视频时长", required = false)
        private Long longVideoDuration; // 视频时长

        @Schema(description = "视频链接", required = false)
        private String kwVideoUrl; // 视频链接

        @Schema(description = "视频语音识别", required = false)
        private String kwVideoContent; // 视频语音识别

        @Schema(description = "通用情感 Plus 版", required = false)
        private String kwCommonSentimentPlus; // 通用情感 Plus 版

        @Schema(description = "数据标签 Plus 版", required = false)
        private String kwDataTagPlus; // 数据标签 Plus 版

        @Schema(description = "阅读数", required = false)
        private Long longViewCount; // 阅读数

        @Schema(description = "发帖用户粉丝数", required = false)
        private Long longFollowerCount; // 发帖用户粉丝数

        @Schema(description = "收藏数", required = false)
        private Long longCollectCount; // 收藏数

        @Schema(description = "来源", required = false)
        private String kwSource; // 来源

        @Schema(description = "类型", required = false)
        private Integer type; // 类型

        @Schema(description = "二级标签", required = false)
        private String kwTwoLevelTribeTag; // 圈成二级标签

        @Schema(description = "来源类型", required = false)
        private Integer sourceType; // 来源类型

        @Schema(description = "用户ID", required = false)
        private Integer userId; // 用户 ID

        @Schema(description = "租户ID", required = false)
        private Integer tenantId; // 租户 ID

        @Schema(description = "创意分", required = false)
        private Float rating;

        //创意原因
        @Schema(description = "创意分理由", required = false)
        private String creativeReasons;




    }


}
