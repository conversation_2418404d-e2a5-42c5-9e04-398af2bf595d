package cn.mlamp.insightflow.cms.mapper;

import java.util.Date;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import cn.mlamp.insightflow.cms.entity.TokenUseDetail;

public interface TokenUseDetailMapper extends BaseMapper<TokenUseDetail> {

    @Select("SELECT COALESCE(SUM(tokens), 0) FROM cms_token_use_detail WHERE tenant_id = #{tenantId}")
    Integer selectTotalTokens(@Param("tenantId") Integer tenantId);

    @Select("SELECT COALESCE(SUM(tokens), 0) FROM cms_token_use_detail WHERE tenant_id = #{tenantId} AND usage_time >= #{startTime}")
    Integer selectMonthlyTokens(@Param("tenantId") Integer tenantId, @Param("startTime") Date startTime);

}
