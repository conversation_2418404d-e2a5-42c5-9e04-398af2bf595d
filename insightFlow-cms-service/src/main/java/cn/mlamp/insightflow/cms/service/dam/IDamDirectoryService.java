package cn.mlamp.insightflow.cms.service.dam;

import cn.mlamp.insightflow.cms.entity.dam.DamDirectory;
import cn.mlamp.insightflow.cms.enums.dam.DamDirectoryTypeEnum;
import cn.mlamp.insightflow.cms.model.dto.dam.DamDirectoryDTO;
import cn.mlamp.insightflow.cms.model.vo.dam.DamDirectoryVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * DAM素材目录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
public interface IDamDirectoryService extends IService<DamDirectory> {

    /**
     * 创建目录
     *
     * @param directoryDTO 目录DTO
     * @param userId       用户ID
     * @param tenantId     租户ID
     * @return 目录VO
     */
    DamDirectoryVO createDirectory(DamDirectoryDTO directoryDTO, Integer userId, Integer tenantId);

    /**
     * 获取目录列表
     *
     * @param type     目录类型：1-个人文件夹，2-租户文件夹
     * @param userId   用户ID
     * @param tenantId 租户ID
     * @return 目录列表
     */
    List<DamDirectoryVO> getDirectoryList(DamDirectoryTypeEnum type, Integer userId, Integer tenantId);

    // 获取个人可查看的文件夹
    List<DamDirectory> getViewableDirectory(Integer userId, Integer tenantId);

    // 获取个人AI视频上传文件id，如果没有就创建
    Integer getAiVideoUploadDictionaryId(Integer userId, Integer tenantId);

    /**
     * 获取目录详情
     *
     * @param directoryId 目录ID
     * @param userId      用户ID
     * @param tenantId    租户ID
     * @return 目录详情
     */
    DamDirectoryVO getDirectoryDetail(Integer directoryId, Integer userId, Integer tenantId);

    /**
     * 更新目录
     *
     * @param directoryId  目录ID
     * @param directoryDTO 目录DTO
     * @param userId       用户ID
     * @param tenantId     租户ID
     * @return 是否成功
     */
    boolean updateDirectory(Integer directoryId, DamDirectoryDTO directoryDTO, Integer userId, Integer tenantId);

    /**
     * 删除目录
     *
     * @param directoryId 目录ID
     * @param userId      用户ID
     * @param tenantId    租户ID
     * @return 是否成功
     */
    boolean deleteDirectory(Integer directoryId, Integer userId, Integer tenantId);
}
