package cn.mlamp.insightflow.cms.model.vo.dam;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * DAM素材上传任务VO
 */
@Data
@Schema(description = "DAM素材上传任务VO")
public class DamAssetUploadTaskVO {
    
    @Schema(description = "任务ID")
    private Integer id;
    
    @Schema(description = "用户ID")
    private Integer userId;
    
    @Schema(description = "失败数量")
    private Integer failCount;
    
    @Schema(description = "成功数量")
    private Integer successCount;
    
    @Schema(description = "素材总数")
    private Integer totalCount;
    
    @Schema(description = "错误信息")
    private String error;
    
    @Schema(description = "任务状态：1-待处理，2-处理中，3-完成，4-失败")
    private Integer status;
    
    @Schema(description = "创建时间")
    private Date createTime;
    
    @Schema(description = "更新时间")
    private Date updateTime;

}