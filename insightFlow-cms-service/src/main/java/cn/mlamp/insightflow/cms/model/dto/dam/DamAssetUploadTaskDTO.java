package cn.mlamp.insightflow.cms.model.dto.dam;

import cn.mlamp.insightflow.cms.enums.dam.DamTaskTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

import java.util.List;

/**
 * DAM素材上传任务DTO
 */
@Data
@Schema(description = "DAM素材上传任务DTO")
public class DamAssetUploadTaskDTO {

    @Schema(description = "任务ID")
    private Integer id;

    @Schema(description = "AI分镜视频任务ID（type=2时必填）")
    private Integer videoTaskId;

    @NotNull(message = "目录ID不能为空")
    @Schema(description = "目录ID",
            requiredMode = RequiredMode.REQUIRED)
    private Integer directoryId;

    @NotNull(message = "任务类型不能为空")
    @Schema(description = "任务类型: 1-素材上传任务, 2-AI分镜镜头入库",
            requiredMode = RequiredMode.REQUIRED)
    private DamTaskTypeEnum type;

    @NotEmpty(message = "素材列表不能为空")
    @Schema(description = "素材列表",
            requiredMode = RequiredMode.REQUIRED)
    private List<DamAssetUploadItemDTO> assets;

    /**
     * 素材上传项DTO
     */
    @Data
    @Schema(description = "素材上传项DTO")
    public static class DamAssetUploadItemDTO {

        @NotNull(message = "素材名称不能为空")
        @Schema(description = "素材名称",
                requiredMode = RequiredMode.REQUIRED)
        private String name;

        @NotNull(message = "素材OSS URL不能为空")
        @Schema(description = "素材OSS URL",
                requiredMode = RequiredMode.REQUIRED)
        private String ossId;

        @Schema(description = "剪辑开始位置")
        private Integer start;

        @Schema(description = "剪辑结束位置")
        private Integer end;
    }

    public void validate() {
        // 检验素材名称:
    }
}