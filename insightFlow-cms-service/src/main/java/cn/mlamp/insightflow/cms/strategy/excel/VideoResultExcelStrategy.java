package cn.mlamp.insightflow.cms.strategy.excel;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;

import java.util.List;

public class VideoResultExcelStrategy  implements CellWriteHandler {

    private final int[] mergeColumnIndices; // 需要合并的列索引数组，例如：{0, 1} 表示合并第0列和第1列
    private final int startRowIndex;     // 数据开始的行索引（通常为1，跳过表头）
    private final int dialogueColIndex = 1; // 台词列索引

    public VideoResultExcelStrategy(int startRowIndex, int... mergeColumnIndices) {
        this.mergeColumnIndices = mergeColumnIndices;
        this.startRowIndex = startRowIndex;
    }

    @Override
    public void afterCellDispose(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder,
                                 List<WriteCellData<?>> cellDataList, Cell cell, Head head, Integer relativeRowIndex,
                                 Boolean isHead) {
        Sheet sheet = writeSheetHolder.getSheet();
        int rowIndex = cell.getRowIndex();
        int colIndex = cell.getColumnIndex();

        // 仅处理指定需要合并的列
        boolean isMergeColumn = false;
        for (int mergeColIndex : mergeColumnIndices) {
            if (colIndex == mergeColIndex) {
                isMergeColumn = true;
                break;
            }
        }
        if (!isMergeColumn || rowIndex <= startRowIndex) {
            return;
        }


    // 只在处理“台词列”时检查是否为空
        if (colIndex == dialogueColIndex) {
        String dialogueValue = getCellStringValue(cell);
        if (dialogueValue.isEmpty()) {
            return; // 台词为空，不合并台词列
        }
    }


    String currentValue = getCellStringValue(cell);
        Row previousRow = sheet.getRow(rowIndex - 1);
        if (previousRow == null) {
            return;
        }
        Cell previousCell = previousRow.getCell(colIndex);
        if (previousCell == null) {
            return;
        }
        String previousValue = getCellStringValue(previousCell);

        if (currentValue.equals(previousValue)) {
            // 查找之前是否已经合并过
            boolean merged = false;
            for (int i = 0; i < sheet.getNumMergedRegions(); i++) {
                CellRangeAddress region = sheet.getMergedRegion(i);
                if (region.isInRange(rowIndex - 1, colIndex)) {
                    sheet.removeMergedRegion(i);
                    region.setLastRow(rowIndex);
                    sheet.addMergedRegion(region);
                    merged = true;
                    break;
                }
            }
            if (!merged) {
                CellRangeAddress newRegion = new CellRangeAddress(rowIndex - 1, rowIndex, colIndex, colIndex);
                sheet.addMergedRegion(newRegion);
            }
        }
    }

    private String getCellStringValue(Cell cell) {
        if (cell == null) {
            return "";
        }
        cell.setCellType(CellType.STRING);
        return cell.getStringCellValue().trim();
    }
}