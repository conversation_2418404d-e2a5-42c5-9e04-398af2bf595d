package cn.mlamp.insightflow.cms.message;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * 国际化 MessageFactory
 *
 * <AUTHOR>
 * @since 2023-08-31 11:17:32
 */
@Slf4j
@Component
public class MessageFactory {

    /**
     * 通过自动注入排序过后的MessageHandler
     */
    private static List<AbstractMessageHandler> messageHandlers;

    /**
     * 根据code和参数获取消息，委托给spring messageSource
     *
     * @param messageCode messageCode
     * @param args        可填充的参数
     * @return 获取国际化翻译值，没有获取值或抛异常时返回messageCode本身
     */
    public static String getMessage(String messageCode, Object... args) {
        String message = getMessageNullAble(messageCode, args);
        return Objects.isNull(message) ? messageCode : message;
    }

    /**
     * （同上）但当没有找到对应值或发生异常时，返回null
     *
     * @param messageCode messageCode
     * @param args        可填充的参数
     * @return 获取国际化翻译值，没有获取值或抛异常时返回null
     */
    public static String getMessageNullAble(String messageCode, Object... args) {
        try {
            for (AbstractMessageHandler abstractMessageHandler : messageHandlers) {
                String message = abstractMessageHandler.getMessage(messageCode, args);
                if (StrUtil.isNotBlank(message)) {
                    return message;
                }
            }
        } catch (Exception e) {
            log.error("messageSource getMessage error. code:[{}], args:[{}], defaultMessage:[{}], Locale:[{}]",
                    messageCode, args, null, LocaleContextHolder.getLocale(), e);
        }
        return null;
    }

}