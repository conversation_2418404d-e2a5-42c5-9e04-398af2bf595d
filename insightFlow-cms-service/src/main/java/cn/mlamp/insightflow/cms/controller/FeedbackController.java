package cn.mlamp.insightflow.cms.controller;

import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.config.UserContext;
import cn.mlamp.insightflow.cms.model.query.FeedbackRequest;
import cn.mlamp.insightflow.cms.service.CmsFeedbackService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RequestMapping("/feedback")
@RestController
@RequiredArgsConstructor
@Tag(name = "用户反馈相关接口")
public class FeedbackController {

    private final CmsFeedbackService feedbackService;

    @PostMapping
    @Operation(summary = "提交反馈")
    public RespBody<Void> videoTaskProductUrl(@RequestBody FeedbackRequest uploadVO) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();
        feedbackService.userFeedback(uploadVO, userId, tenantId);
        return RespBody.ok();
    }
}
