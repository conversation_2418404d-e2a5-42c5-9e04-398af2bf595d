package cn.mlamp.insightflow.cms.validation;

import lombok.extern.slf4j.Slf4j;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.Map;
import java.util.Objects;

import cn.mlamp.insightflow.cms.util.ObjReflectUtil;

/**
 * 验证对象指定字段不能为null，且必须一致 对应的校验逻辑
 *
 * <AUTHOR>
 * @since 2022-09-08 16:39:47
 */
@Slf4j
public class ObjectPropertiesMustBeConsistentValidator
        implements ConstraintValidator<ObjectPropertiesMustBeConsistentConstraint, Object> {

    String[] properties;

    /**
     * 初始化数据
     */
    @Override
    public void initialize(ObjectPropertiesMustBeConsistentConstraint constraintAnnotation) {
        properties = constraintAnnotation.properties();
        ConstraintValidator.super.initialize(constraintAnnotation);
    }

    /**
     * 校验逻辑
     */
    @Override
    public boolean isValid(Object obj, ConstraintValidatorContext context) {
        // 未指定属性，不验证
        if (properties.length == 0) {
            return true;
        }
        try {
            // 获取对象类型的Field数据
            Map<String, Object> fieldMap = ObjReflectUtil.getKeyValueMap(obj);
            // 定义临时对比对象
            Object tmpObj = null;
            // 获取指定属性的数据
            for (String property : properties) {
                Object o = fieldMap.get(property);

                // 值为null则认为不一致
                if (Objects.isNull(o)) {
                    return false;
                }
                if (Objects.isNull(tmpObj)) {
                    tmpObj = o;
                } else {
                    if (!tmpObj.equals(o)) {
                        return false;
                    }
                }
            }
            return true;
        } catch (IllegalAccessException e) {
            log.error("反射获取值异常, e:", e);
            return false;
        }
    }
}
