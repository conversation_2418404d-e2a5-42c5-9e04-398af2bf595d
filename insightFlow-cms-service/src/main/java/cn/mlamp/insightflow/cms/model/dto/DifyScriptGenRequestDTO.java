package cn.mlamp.insightflow.cms.model.dto;

import cn.mlamp.insightflow.cms.model.query.VideoScriptGenRequest;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;

import java.util.Map;

@Data
public class DifyScriptGenRequestDTO {

    private String brand;
    private String product;
    private String duration;
    @JsonProperty("NumberOfLenses")
    private Integer numberOfLenses;
    @JsonProperty("SellingPoint")
    private String sellingPoint;
    private String scene;
    private String festival;
    private Integer people;


    private String scheme; //画面套路
    private String wording; //台词套路

    @SuppressWarnings("unchecked")
    public static Map<String, Object> buildDifyParams(VideoScriptGenRequest videoScriptGenRequest) {
        var dto = new DifyScriptGenRequestDTO();
        var content = videoScriptGenRequest.getContent();
        // 必填
        dto.setBrand(content.getBrand());
        dto.setProduct(content.getProduct());
        dto.setDuration(content.getDuration() + "秒");
        dto.setNumberOfLenses(content.getLensNum());
        // 选填
        dto.setSellingPoint(content.getSellingPoint());
        dto.setScene(content.getScene());
        dto.setFestival(content.getFestival());
        dto.setPeople(content.getPeopleNum());
        // 画面套路和台词套路（必填）
        var decodeResult = videoScriptGenRequest.getDecodeResult();
        if (decodeResult.get("画面套路") == null || decodeResult.get("台词套路") == null) {
            throw new RuntimeException("画面套路和台词套路不能为空");
        }
        dto.setScheme(String.join("，", decodeResult.get("画面套路")));
        dto.setWording(String.join("，", decodeResult.get("台词套路")));

        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        return objectMapper.convertValue(dto, Map.class);
    }
}
