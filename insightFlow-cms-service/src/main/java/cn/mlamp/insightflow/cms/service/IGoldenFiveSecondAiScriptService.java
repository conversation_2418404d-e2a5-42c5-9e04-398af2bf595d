package cn.mlamp.insightflow.cms.service;

import cn.mlamp.insightflow.cms.model.query.ActivityScriptGenerationRequest;
import cn.mlamp.insightflow.cms.model.query.PersonaGenerationRequest;
import cn.mlamp.insightflow.cms.model.vo.ActivityScriptGenerationVO;
import cn.mlamp.insightflow.cms.model.vo.PersonaGenerationVO;

/**
 * 黄金5秒AI脚本生成服务接口
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
public interface IGoldenFiveSecondAiScriptService {

    /**
     * 生成人物画像
     *
     * @param request 人物画像生成请求
     * @return 人物画像生成响应
     */
    PersonaGenerationVO generatePersona(PersonaGenerationRequest request);

    /**
     * 生成活动逼单话术
     * 
     * @param request 活动逼单话术生成请求
     * @return 活动逼单话术生成响应
     */
    ActivityScriptGenerationVO generateActivityScript(ActivityScriptGenerationRequest request);
}
