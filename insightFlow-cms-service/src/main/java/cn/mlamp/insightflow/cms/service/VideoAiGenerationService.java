package cn.mlamp.insightflow.cms.service;

import cn.mlamp.insightflow.cms.enums.VideoTaskStatusEnum;
import cn.mlamp.insightflow.cms.model.dto.VideoAiGenerateDTO;
import cn.mlamp.insightflow.cms.model.vo.VideoAiGenerateVO;
import cn.mlamp.insightflow.cms.entity.CmsTaskInfo;
import org.springframework.scheduling.annotation.Async;

import java.util.List;

public interface VideoAiGenerationService {
    /**
     * 创建视频生成任务
     *
     * @param request  请求参数
     * @param userId   用户ID
     * @param tenantId 租户ID
     * @return 任务创建响应
     */
    VideoAiGenerateVO.ViduCreateResponse createVideoTask(VideoAiGenerateDTO.ViduCreateRequest request, Integer userId,
            Integer tenantId);

    /**
     * 异步执行视频生成任务
     *
     * @param request  请求参数
     * @param taskId   任务ID
     * @param userId   用户ID
     * @param tenantId 租户ID
     */
    @Async("commonTaskExecutor")
    void generateVideoAsync(VideoAiGenerateDTO.ViduCreateRequest request, String taskId, Integer userId,
            Integer tenantId);

    /**
     * 查询视频生成任务状态
     *
     * @param id 任务ID
     * @return 查询响应
     */
    VideoAiGenerateVO.ViduQueryResponse queryVideo(String id);

    /**
     * 查询视频生成任务列表
     *
     * @param actualLimit 每页数量
     * @param actualPage  页码
     * @param userId      用户ID
     * @return 列表响应
     */
    VideoAiGenerateVO.ViduListResponse queryListVideo(int actualLimit, int actualPage, Integer userId);

    /**
     * 获取用户上传AI视频的目录
     *
     * @param userId   用户ID
     * @param tenantId 租户ID
     * @return 目录ID
     */
    Integer getUploadDirectoryId(Integer userId, Integer tenantId);

    /**
     * 更新任务状态
     *
     * @param taskId   任务ID
     * @param status   状态
     * @param userId   用户ID
     * @param tenantId 租户ID
     * @return 更新结果
     */
    int updateTaskStatus(Integer taskId, VideoTaskStatusEnum status, Integer userId, Integer tenantId);
}
