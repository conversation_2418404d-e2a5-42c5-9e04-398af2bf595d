package cn.mlamp.insightflow.cms.message;

import lombok.extern.slf4j.Slf4j;

/**
 * 国际化 AbstractHandler
 *
 * <AUTHOR>
 * @since 2023-08-31 11:17:32
 */
@Slf4j
public abstract class AbstractMessageHandler {

    /**
     * 根据code和参数获取消息，委托给spring messageSource
     *
     * @param messageCode messageCode
     * @param args        可填充的参数
     * @return 获取国际化翻译值，没有获取值或抛异常时返回messageCode本身
     */
    protected abstract String getMessage(String messageCode, Object... args);

    /**
     * 排序位，值越小，越先执行
     */
    protected abstract int sort();

}