package cn.mlamp.insightflow.cms.mapper.dam;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.github.yulichang.base.MPJBaseMapper;

import cn.mlamp.insightflow.cms.entity.dam.DamTag;

/**
 * <p>
 * DAM标签表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
@Mapper
public interface DamTagMapper extends MPJBaseMapper<DamTag> {

    /**
     * 根据标签ID获取已使用次数
     *
     * @param tagId 标签ID
     * @return 使用次数
     */
    int countUsageByTagId(@Param("tagId") Integer tagId);

    /**
     * 查询标签列表，包含使用次数
     *
     * @param type     标签类型
     * @param tenantId 租户ID
     * @param userId   用户ID
     * @return 标签列表
     */
    List<DamTag> selectTagListWithUsedNum(@Param("type") Integer type,
                                          @Param("tenantId") Integer tenantId,
                                          @Param("userId") Integer userId);

}
