package cn.mlamp.insightflow.cms.util;

import cn.hutool.core.collection.CollUtil;
import cn.mlamp.insightflow.cms.message.MessageFactory;
import lombok.extern.slf4j.Slf4j;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import java.util.Set;

/**
 * Validator校验类
 *
 * <AUTHOR>
 * @since 2022/10/27 11:41
 */
@Slf4j
public class ValidatorUtil {

    /**
     * 手动触发参数校验
     *
     * @param t   具有继承关系的包装类
     * @param <T> 泛型
     * @return 是否校验通过
     */
    public static <T> boolean checkArgsIsOk(T t) {
        // 参数校验
        Validator validator = Validation.buildDefaultValidatorFactory().getValidator();
        Set<ConstraintViolation<T>> set = validator.validate(t);
        if (CollUtil.isEmpty(set)) {
            return true;
        }

        for (ConstraintViolation<T> constraintViolation : set) {
            log.warn("ValidatorUtil checkArgs error: [{}]",
                    MessageFactory.getMessage(constraintViolation.getMessage()));
        }
        return false;
    }

}
