package cn.mlamp.insightflow.cms.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.mlamp.insightflow.cms.auth.cms.dto.TtcRequestActivateUserTemplateBO;
import cn.mlamp.insightflow.cms.auth.cms.dto.TtcRequestCheckTenantNameExistDTO;
import cn.mlamp.insightflow.cms.auth.cms.dto.TtcRequestRegisterUserTemplateDTO;
import cn.mlamp.insightflow.cms.auth.cms.dto.TtcRequestResetPasswordDTO;
import cn.mlamp.insightflow.cms.auth.cms.service.QuotaService;
import cn.mlamp.insightflow.cms.auth.cms.service.TtcAdaptor;
import cn.mlamp.insightflow.cms.enums.TtcBizCode;
import cn.mlamp.insightflow.cms.enums.VerificationTypeEnum;
import cn.mlamp.insightflow.cms.exception.BusinessException;
import cn.mlamp.insightflow.cms.model.query.ActivateTenantParam;
import cn.mlamp.insightflow.cms.model.query.RegisterTenantParam;
import cn.mlamp.insightflow.cms.model.query.ResetPasswordParam;
import cn.mlamp.insightflow.cms.service.RegisterService;

/**
 * 自助注册Service
 *
 * <AUTHOR>
 * @since 2022-09-16 14:55:50
 **/
@Slf4j
@Service
public class RegisterServiceImpl implements RegisterService {

    @Autowired
    private TtcAdaptor ttcAdaptor;
    @Autowired
    private QuotaService quotaService;

    @Override
    public void checkTenantNameExist(String nickName) {
        TtcRequestCheckTenantNameExistDTO requestDTO = new TtcRequestCheckTenantNameExistDTO();
        requestDTO.setTenantName(nickName);
        ttcAdaptor.checkTenantNameExist(requestDTO);
    }

    @Override
    public void registerTenant(RegisterTenantParam registerTenantParam) {
        TtcRequestRegisterUserTemplateDTO requestDTO = new TtcRequestRegisterUserTemplateDTO();
        requestDTO.setTenantName(registerTenantParam.getNickName());
        requestDTO.setEmail(registerTenantParam.getEmail());
        requestDTO.setEmailCode(registerTenantParam.getEmailCode());
        requestDTO.setMobile(registerTenantParam.getMobile());
        requestDTO.setMobileCode(registerTenantParam.getMobileCode());
        requestDTO.setPassword(registerTenantParam.getPassword());
        requestDTO.setPasswordCheck(registerTenantParam.getPasswordCheck());
        ttcAdaptor.registerUser(requestDTO);
        log.info("TTC自助注册成功，tenantName=[{}]", registerTenantParam.getNickName());
    }

    @Override
    public void activateTenant(ActivateTenantParam activateTenantParam) {
        TtcRequestActivateUserTemplateBO requestDTO = new TtcRequestActivateUserTemplateBO();
        requestDTO.setTenantName(activateTenantParam.getNickName());
        requestDTO.setEmail(activateTenantParam.getEmail());
        ttcAdaptor.activateUser(requestDTO);
        log.info("TTC自助注册成功，tenantName=[{}]", activateTenantParam.getNickName());

    }

    @Override
    public void resetPassword(ResetPasswordParam resetPasswordParam) {
        // 内部用户校验
        if (VerificationTypeEnum.TYPE_EMAIL.getCode().equals(resetPasswordParam.getVerificationType())) {
            String email = resetPasswordParam.getVerificationTarget();
            if (quotaService.checkIsInternalUsersByUserName(email)) {
                throw new BusinessException(TtcBizCode.INTERNAL_USER_RESET_PASSWORD_ERROR);
            }
        }

        TtcRequestResetPasswordDTO requestDTO = new TtcRequestResetPasswordDTO();
        requestDTO.setVerificationType(resetPasswordParam.getVerificationType());
        requestDTO.setVerificationCode(resetPasswordParam.getVerificationCode());
        requestDTO.setVerificationTarget(resetPasswordParam.getVerificationTarget());
        requestDTO.setPassword(resetPasswordParam.getPassword());
        requestDTO.setPasswordCheck(resetPasswordParam.getPasswordCheck());
        ttcAdaptor.resetPassword(requestDTO);
        log.info("重置密码成功");
    }

}
