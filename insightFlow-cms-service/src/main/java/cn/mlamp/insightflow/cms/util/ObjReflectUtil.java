package cn.mlamp.insightflow.cms.util;

import com.google.common.collect.Maps;

import java.lang.reflect.Field;
import java.util.Map;

/**
 * Object反射工具类
 *
 * <AUTHOR>
 * @since 2022/9/27 11:57
 */
public class ObjReflectUtil {

    /**
     * 获取对象类型的 字段名-字段值 map
     *
     * @param obj 当前对象
     * @return 反射读取的 字段名-字段值 map
     */
    public static Map<String, Object> getKeyValueMap(Object obj) throws IllegalAccessException {
        Class<?> clazz = obj.getClass();
        Map<String, Object> fieldMap = Maps.newHashMap();
        // 获取当前class属性
        Field[] fieldArray = clazz.getDeclaredFields();
        if (fieldArray.length > 0) {
            for (Field field : fieldArray) {
                field.setAccessible(true);
                fieldMap.put(field.getName(), field.get(obj));
            }
        }
        // 获取父级class属性
        Class<?> parentClazz = clazz.getSuperclass();
        if (parentClazz != null) {
            fieldArray = parentClazz.getDeclaredFields();
            for (Field field : fieldArray) {
                field.setAccessible(true);
                fieldMap.put(field.getName(), field.get(obj));
            }
        }
        return fieldMap;
    }

    /**
     * 获取对象类型的所有Field数据
     *
     * @param value 当前对象
     * @return 反射读取的 字段名-字段 map
     */
    public static Map<String, Field> getFieldMap(Object value) {
        Class<?> clazz = value.getClass();
        Map<String, Field> fieldMap = Maps.newHashMap();
        Field[] fieldArray = clazz.getDeclaredFields();
        if (fieldArray.length > 0) {
            for (Field field : fieldArray) {
                fieldMap.put(field.getName(), field);
            }
        }
        Class<?> parentClazz = clazz.getSuperclass();
        if (parentClazz != null) {
            fieldArray = parentClazz.getDeclaredFields();
            for (Field field : fieldArray) {
                fieldMap.put(field.getName(), field);
            }
        }
        return fieldMap;
    }

}
