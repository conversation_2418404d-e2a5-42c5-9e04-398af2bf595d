package cn.mlamp.insightflow.cms.events;

import org.springframework.context.ApplicationEvent;

import lombok.Getter;

@Getter
public class TenantInitializeEvent extends ApplicationEvent {

    private final Integer tenantId;
    private final Integer userId;

    public TenantInitializeEvent(
            Object source,
            Integer tenantId,
            Integer userId
    ) {
        super(source);
        this.tenantId = tenantId;
        this.userId = userId;
    }
}
