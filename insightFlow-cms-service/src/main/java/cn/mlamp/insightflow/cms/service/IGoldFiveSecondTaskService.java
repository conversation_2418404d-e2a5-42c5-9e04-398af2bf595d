package cn.mlamp.insightflow.cms.service;

import cn.mlamp.insightflow.cms.entity.CmsVideoFiveGold;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 黄金五秒任务服务接口
 * <AUTHOR> yangzhibo
 * @date : 2025-5-15
 */
public interface IGoldFiveSecondTaskService extends IService<CmsVideoFiveGold> {

    /**
     * 处理黄金五秒任务
     * @param dateStr 日期字符串，格式为yyyy-MM-dd
     */
    void processGoldFiveSecondTask(String dateStr);

    /**
     * 手动触发任务
     * 
     * @param dateStr  日期字符串，格式为yyyy-MM-dd，如果为空则使用当前日期
     * @param industry 行业
     * @param tag      标签
     */
    void manualTriggerTask(String dateStr, String industry, String tag);
}
