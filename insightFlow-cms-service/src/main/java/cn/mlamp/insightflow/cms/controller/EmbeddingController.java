// package cn.mlamp.insightflow.cms.controller;

// import java.time.LocalDateTime;
// import java.time.ZoneOffset;
// import java.util.List;
// import java.util.Map;

// import org.springframework.ai.document.Document;
// import org.springframework.ai.embedding.EmbeddingModel;
// import org.springframework.ai.embedding.EmbeddingResponse;
// import org.springframework.ai.vectorstore.SearchRequest;
// import org.springframework.ai.vectorstore.filter.FilterExpressionBuilder;
// import org.springframework.ai.vectorstore.pgvector.PgVectorStore;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.beans.factory.annotation.Qualifier;
// import org.springframework.web.bind.annotation.GetMapping;
// import org.springframework.web.bind.annotation.PostMapping;
// import org.springframework.web.bind.annotation.RequestParam;
// import org.springframework.web.bind.annotation.RestController;

// import cn.mlamp.insightflow.cms.entity.CmsPullTaskDedupedData;

// @RestController
// public class EmbeddingController {

// @Autowired
// private EmbeddingModel embeddingModel;

// @Autowired
// @Qualifier("userNicknameVectorStore")
// private PgVectorStore userNicknameVectorStore;

// @Autowired
// @Qualifier("combinedDataVectorStore")
// private PgVectorStore combinedDataVectorStore;

// @GetMapping("/ai/embedding")
// public Map<String, Object> embed(@RequestParam(value = "message",
// defaultValue = "Tell me a joke") String message) {
// // 生成嵌入向量
// EmbeddingResponse embeddingResponse =
// this.embeddingModel.embedForResponse(List.of(message));
// return Map.of("embedding", embeddingResponse);
// }

// @PostMapping("/ai/embedding")
// public Map<String, Object> embedAndStore(@RequestParam(defaultValue = "Tell
// me a joke") String message) {

// // // 将嵌入向量存储到 userNicknameVectorStore
// // Document document = new Document(message, Map.of("1", '1'));
// // userNicknameVectorStore.add(List.of(document));
// LocalDateTime sevenDaysAgo = LocalDateTime.now().minusDays(8);
// long startTime = sevenDaysAgo.toInstant(ZoneOffset.UTC).toEpochMilli();
// long endTime = System.currentTimeMillis(); // 结束时间戳（当前时间）
// FilterExpressionBuilder b = new FilterExpressionBuilder();
// SearchRequest searchRequest = SearchRequest.builder().query("又渴了大王") //
// 使用传入的关键词
// .topK(10).similarityThreshold(0.8) // 设定相似度阈值
// .filterExpression(b.and(b.gte("datePublishedAt", startTime),
// b.lte("datePublishedAt", endTime)).build())
// .build();
// List<Document> documents =
// userNicknameVectorStore.similaritySearch(searchRequest);

// return Map.of("status", "stored");
// }
// }
