package cn.mlamp.insightflow.cms.util;

import cn.mlamp.insightflow.cms.enums.AnalysisVideoTypeEnum;
import cn.mlamp.insightflow.cms.enums.TokenTaskTypeEnum;

import java.util.UUID;

/**
 * @Author: husuper
 * @CreateTime: 2025-04-05
 */
public class ObservationIdUtil {

    public static String getObservationId(TokenTaskTypeEnum taskType) {
        StringBuilder sb = new StringBuilder();
        sb.append(taskType.getTaskCode()).append(UUID.randomUUID().toString().replace("-", ""));
        return sb.toString();
    }


    public static String getObservationId(AnalysisVideoTypeEnum videoTypeEnum) {
        StringBuilder sb = new StringBuilder();
        if(videoTypeEnum.getVideoCode().equals(AnalysisVideoTypeEnum.UPLOAD_VIDEO.getVideoCode())){
            sb.append(TokenTaskTypeEnum.UPLOAD_TASK.getTaskCode()).append(UUID.randomUUID().toString().replace("-", ""));
        }else{
            sb.append(videoTypeEnum.getVideoType()).append(UUID.randomUUID().toString().replace("-", ""));
        }
        return sb.toString();
    }


    public static void main(String[] args) {
        System.out.println(getObservationId(AnalysisVideoTypeEnum.UPLOAD_VIDEO));
        System.out.println(getObservationId(AnalysisVideoTypeEnum.VIDEO_ANALYSIS));
        System.out.println(getObservationId(TokenTaskTypeEnum.UPLOAD_TASK));
    }


}
