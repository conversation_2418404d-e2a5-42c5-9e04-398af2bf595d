package cn.mlamp.insightflow.cms.config.properties;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * 链接上传配置
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Configuration
@ConfigurationProperties(prefix = "upload-link")
public class UploadLinkProperties {


    private List<Domain> domains;

    private List<String> productUrls;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Domain {
        private String id;
        private String name;
        private String iconKey;
        private List<String> urls;
    }

}
