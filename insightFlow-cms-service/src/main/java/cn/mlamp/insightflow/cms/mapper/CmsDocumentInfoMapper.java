package cn.mlamp.insightflow.cms.mapper;

import cn.mlamp.insightflow.cms.entity.CmsDocumentInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Select;

public interface CmsDocumentInfoMapper extends BaseMapper<CmsDocumentInfo> {

    /**
     * 根据 obj_id 查询记录
     */
    @Select("SELECT * FROM cms_document_info WHERE obj_id = #{objId}")
    CmsDocumentInfo selectByObjId(String objId);
}

