package cn.mlamp.insightflow.cms.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class TaskUploadRequest implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "上传文件名", required = true)
    private String filename;

    @Schema(description = "上传文件类型 0:excel 1:图片 2:视频", required = false)
    private Integer type;

    @Schema(description = "上传文件地址", required = false)
    private String fileUrl;

    @Schema(description = "bucket地址", required = false)
    private String bucket;

}
