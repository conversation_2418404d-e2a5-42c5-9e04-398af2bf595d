package cn.mlamp.insightflow.cms.service.dam.impl;

import cn.mlamp.insightflow.cms.entity.dam.DamAssetUploadTaskDetail;
import cn.mlamp.insightflow.cms.mapper.dam.DamAssetUploadTaskDetailMapper;
import cn.mlamp.insightflow.cms.service.dam.IDamAssetUploadTaskDetailService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * DAM素材上传任务表-记录细节 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
@Slf4j
@Service
public class DamAssetUploadTaskDetailServiceImpl
        extends ServiceImpl<DamAssetUploadTaskDetailMapper, DamAssetUploadTaskDetail>
        implements IDamAssetUploadTaskDetailService {

    @Override
    public List<DamAssetUploadTaskDetail> listByTaskId(Integer taskId) {
        return list(new LambdaQueryWrapper<DamAssetUploadTaskDetail>()
                .eq(DamAssetUploadTaskDetail::getTaskId, taskId)
                .eq(DamAssetUploadTaskDetail::getIsDeleted, false));
    }

    @Override
    public List<DamAssetUploadTaskDetail> listByTaskIds(List<Integer> taskIds) {
        return list(new LambdaQueryWrapper<DamAssetUploadTaskDetail>()
                .in(DamAssetUploadTaskDetail::getTaskId, taskIds)
                .eq(DamAssetUploadTaskDetail::getIsDeleted, false));
    }

    @Override
    public List<DamAssetUploadTaskDetail> listByTaskIdAndAssetIds(Integer taskId, List<Integer> assetIds) {
        return list(new LambdaQueryWrapper<DamAssetUploadTaskDetail>()
                .eq(DamAssetUploadTaskDetail::getTaskId, taskId)
                .in(DamAssetUploadTaskDetail::getAssetId, assetIds)
                .eq(DamAssetUploadTaskDetail::getIsDeleted, false));
    }

}
