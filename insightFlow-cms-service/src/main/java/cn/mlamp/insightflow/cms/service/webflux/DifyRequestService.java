package cn.mlamp.insightflow.cms.service.webflux;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.mlamp.insightflow.cms.config.properties.DifyRequestProperties;
import cn.mlamp.insightflow.cms.model.dto.DifyAiImitateRequestDTO;
import cn.mlamp.insightflow.cms.model.dto.DifyStoryboardModifyRequestDTO;
import cn.mlamp.insightflow.cms.model.dto.DifyStoryboardRecRequestDTO;
import cn.mlamp.insightflow.cms.model.query.VideoScriptGenRequest;
import cn.mlamp.insightflow.cms.util.dify.DifyUtil;
import cn.mlamp.insightflow.cms.util.dify.model.DifyRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Service
public class DifyRequestService {
    private final DifyRequestProperties difyRequestProperties;

    private final DifyUtil difyUtil;

    public DifyRequestService(DifyRequestProperties difyRequestProperties) {
        this.difyRequestProperties = difyRequestProperties;
        this.difyUtil = new DifyUtil();
    }

    public String productInfoSummary(String productInfoStr) throws Exception {
        Map<String, Object> input = Map.of("json", productInfoStr);
        var difyRequest = DifyRequest.builder()
                .appKey(difyRequestProperties.getProductSummaryKey())
                .baseUrl(difyRequestProperties.getBaseUrl())
                .inputsParams(input)
                .observationId(UUID.randomUUID().toString().replace("-", ""))
                .build();
        log.info("产品信息摘要中");
        var difyResult = difyUtil.executeAndMergeStreaming(difyRequest);
        if (!difyResult.containsKey("text")) {
            throw new Exception("调用dify的产品信息摘要失败");
        }
        return difyResult.get("text").toString();
    }

    public String storyboardRecommend(VideoScriptGenRequest request, String storyBoardJson) throws Exception {
        var difyRequest = DifyRequest.builder()
                .appKey(difyRequestProperties.getStoryboardRecommendKey())
                .baseUrl(difyRequestProperties.getBaseUrl())
                .inputsParams(DifyStoryboardRecRequestDTO.buildDifyParams(request, storyBoardJson))
                .observationId(UUID.randomUUID().toString().replace("-", ""))
                .build();
        log.info("分镜推荐中");
        var difyResult = difyUtil.executeAndMergeStreaming(difyRequest);
        if (!difyResult.containsKey("text")) {
            throw new Exception("调用dify的分镜推荐失败");
        }
        String resultStr = difyResult.get("text").toString();
        // dify输出不稳定，只获取[]中的内容（包含[]）
        Pattern pattern = Pattern.compile("\\[(.*?)\\]");
        Matcher matcher = pattern.matcher(resultStr);
        return matcher.find() ? matcher.group() : null;
    }

    public String storyboardModify(VideoScriptGenRequest request, Map<String, String> storyboard,
                                   String key, String prompt) throws Exception {
        var difyRequest = DifyRequest.builder()
                .appKey(difyRequestProperties.getStoryboardModifyKey())
                .baseUrl(difyRequestProperties.getBaseUrl())
                .inputsParams(DifyStoryboardModifyRequestDTO.buildDifyParams(request, storyboard, key, prompt))
                .observationId(UUID.randomUUID().toString().replace("-", ""))
                .build();
        log.info("AI魔法棒调用中");
        var difyResult = difyUtil.executeAndMergeStreaming(difyRequest);
        if (!difyResult.containsKey("text")) {
            throw new Exception("调用dify的分镜修改失败");
        }
        return difyResult.get("text").toString();
    }

    public String aiImitate(VideoScriptGenRequest request, String storyBoardJson, String observationId) throws Exception {
        var difyRequest = DifyRequest.builder()
                .appKey(difyRequestProperties.getAiWriteKey())
                .baseUrl(difyRequestProperties.getBaseUrl())
                .inputsParams(DifyAiImitateRequestDTO.buildDifyParams(request, storyBoardJson))
                .observationId(observationId)
                .build();
        log.info("AI仿写生成中");
        var difyResult = difyUtil.executeAndMergeStreaming(difyRequest);
        if (!difyResult.containsKey("text")) {
            throw new Exception("调用dify的AI仿写失败");
        }
        return difyResult.get("text").toString();
    }

    public String imagePromptGen(String query) throws RuntimeException {
        var difyRequest = DifyRequest.builder()
                .appKey(difyRequestProperties.getImagePromptTranslateKey())
                .baseUrl(difyRequestProperties.getBaseUrl())
                .query(query)
                .build();
        var difyResult = difyUtil.blockingChatRequest(difyRequest, UUID.randomUUID().toString());
        if (difyResult == null) {
            throw new RuntimeException("图片提示词翻译失败");
        }
        return difyResult;
    }

}
