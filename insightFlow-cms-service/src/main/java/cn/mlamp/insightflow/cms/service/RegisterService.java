package cn.mlamp.insightflow.cms.service;

import cn.mlamp.insightflow.cms.model.query.ActivateTenantParam;
import cn.mlamp.insightflow.cms.model.query.RegisterTenantParam;
import cn.mlamp.insightflow.cms.model.query.ResetPasswordParam;

/**
 * 验证码Service
 *
 * <AUTHOR>
 * @since 2022-09-08 15:20:16
 **/
public interface RegisterService {

    /**
     * 校验租户名是否存在
     *
     * @param nickName 租户名
     */
    void checkTenantNameExist(String nickName);

    /**
     * 自助注册租户
     *
     * @param registerTenantParam 自助注册租户Param
     */
    void registerTenant(RegisterTenantParam registerTenantParam);

    /**
     * 自助激活租户
     *
     * @param activateTenantParam 自助激活租户Param
     */
    void activateTenant(ActivateTenantParam activateTenantParam);

    /**
     * 重置密码
     *
     * @param resetPasswordParam 重置密码Param
     */
    void resetPassword(ResetPasswordParam resetPasswordParam);

}
