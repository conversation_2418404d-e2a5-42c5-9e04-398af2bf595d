package cn.mlamp.insightflow.cms.model.query;

import lombok.Data;

import java.util.List;

@Data
public class PageRequest {

    /**
     * 当前页号
     */
//    @ApiModelProperty(value = "当前页号", required = true)
    private Integer current=1;

    /**
     * 页面大小
     */
//    @ApiModelProperty(value = "页面大小", required = true)
    private Integer pageSize=10;

    /**
     * 排序字段列表
     */
//    @ApiModelProperty(value = "排序字段列表", required = false)
    private List<String> sortFieldList;

    /**
     * 排序顺序列表
     */
//    @ApiModelProperty(value = "排序顺序列表 一个字段一个顺序 升序: a 降序: d", required = false)
    private List<String> sortOrderList;


    /**
     * 升序
     */
    public static final String SORT_ORDER_ASC = "a";

    /**
     * 降序
     */
    public static final String SORT_ORDER_DESC = " d";
}
