package cn.mlamp.insightflow.cms.model.query.dam;

import cn.mlamp.insightflow.cms.model.query.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * DAM素材语义检索参数
 */
@Data
@Schema(description = "DAM素材语义检索参数")
public class DamAssetSemanticSearchParam extends PageParam {
    
    @NotBlank(message = "检索内容不能为空")
    @Schema(description = "检索内容", required = true)
    private String query;
    
    @NotNull(message = "画面比例不能为空")
    @Schema(description = "画面比例", required = true)
    private String aspectRatio;
} 