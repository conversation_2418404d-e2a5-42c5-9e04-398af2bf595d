package cn.mlamp.insightflow.cms.model.vo;

import cn.hutool.json.JSONUtil;
import cn.mlamp.insightflow.cms.entity.CmsTaskInfo;
import cn.mlamp.insightflow.cms.entity.CmsVideoInfo;
import cn.mlamp.insightflow.cms.enums.TaskTypeEnum;
import cn.mlamp.insightflow.cms.enums.VideoTaskStatusEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 视频任务列表信息
 */
@Data
public class VideoTaskListInfoVO {
    private Integer id;
    private String esId;
    private List<Integer> sceneIds;
    private Integer sourceId;

    @Schema(description = "任务id", required = false)
    private String taskId;

    @Schema(description = "任务状态", required = false)
    private Integer status;

    @Schema(description = "状态名称", required = false)
    private String statusName;

    @Schema(description = "帖子标题", required = false)
    private String sourceTitle;

    @Schema(description = "标题", required = false)
    private String title;

    @Schema(description = "头图链接")
    private String headImageUrl;

    @Schema(description = "文案", required = false)
    private String text;

    @Schema(description = "消耗点数", required = false)
    private Integer points = 0;

    @Schema(description = "提交时间(开始时间)", required = false)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy/MM/dd/HH:mm", timezone = "GMT+8")
    private Date startTime;

    @Schema(description = "完成时间(结束时间)", required = false)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy/MM/dd/HH:mm", timezone = "GMT+8")
    private Date endTime;

    @Schema(description = "分镜信息（脚本任务时返回）", required = false)
    private List<VideoStoryBoardVO> storyBoard;

    @Schema(description = "任务类型", required = true)
    private Integer taskType;

    @Schema(description = "任务类型名称", required = true)
    private String taskTypeName;


    public static VideoTaskListInfoVO mapperByCmsTaskInfo(CmsTaskInfo taskInfo) {
        var result = new VideoTaskListInfoVO();
        result.setId(taskInfo.getId());
        result.setEsId(taskInfo.getVideoId());
        result.setSceneIds(JSONUtil.toList(taskInfo.getResultFileIds(), Integer.class));
        result.setTaskId(taskInfo.getId().toString());
        result.setTitle(taskInfo.getName());
        TaskTypeEnum taskType = TaskTypeEnum.getByCode(taskInfo.getTaskType());
        if (taskType != null) {
            result.setTaskTypeName(taskType.getMsg());
            result.setTaskType(taskType.getCode());
        }
        var status = VideoTaskStatusEnum.getByCode(taskInfo.getTaskStatus());
        if (status != null) {
            result.setStatus(status.getCode());
            result.setStatusName(status.getMsg());
        }
        result.setStartTime(taskInfo.getCreateTime());
        if (status == VideoTaskStatusEnum.COMPLETED) {
            result.setEndTime(taskInfo.getUpdateTime());
        }
        return result;
    }

    public static VideoTaskListInfoVO mapperByCmsVideoInfo(CmsVideoInfo videoInfo) {
        var result = new VideoTaskListInfoVO();
        result.setId(videoInfo.getId());
        result.setEsId(videoInfo.getEsId());
        result.setStatus(videoInfo.getStatus());
        result.setStartTime(videoInfo.getCreateTime());
        result.setSourceId(videoInfo.getSourceFileId());
        if (videoInfo.getStatus() == 3) { // 完成展示完成时间
            result.setEndTime(videoInfo.getUpdateTime());
        }
        return result;
    }
}
