package cn.mlamp.insightflow.cms.task;

import cn.mlamp.insightflow.cms.config.TaskConfig;
import cn.mlamp.insightflow.cms.entity.BaseEntity;
import cn.mlamp.insightflow.cms.entity.CmsPullTaskDedupedData;
import cn.mlamp.insightflow.cms.entity.CmsVideoInfo;
import cn.mlamp.insightflow.cms.entity.QianchuanMaterialVideo;
import cn.mlamp.insightflow.cms.enums.AnalysisStatusEnum;
import cn.mlamp.insightflow.cms.enums.AnalysisVideoTypeEnum;
import cn.mlamp.insightflow.cms.enums.DownloadStatusEnum;
import cn.mlamp.insightflow.cms.enums.VideoInfoStatusEnum;
import cn.mlamp.insightflow.cms.model.query.AnalysisVideoCreateRequest;
import cn.mlamp.insightflow.cms.model.query.AnalysisVideoQueryRequest;
import cn.mlamp.insightflow.cms.model.vo.AnalysisVideoResultVO;
import cn.mlamp.insightflow.cms.service.CmsAsyncTaskService;
import cn.mlamp.insightflow.cms.service.CmsPullTaskDedupedDataService;
import cn.mlamp.insightflow.cms.service.IVideoInfoService;
import cn.mlamp.insightflow.cms.service.QianchuanMaterialVideoService;
import cn.mlamp.insightflow.cms.service.impl.VideoInfoServiceImpl;
import cn.mlamp.insightflow.cms.strategy.handle.VideoRecognition2Handle;
import cn.mlamp.insightflow.cms.strategy.video.create.AnalysisVideoStrategyMap;
import cn.mlamp.insightflow.cms.util.DateUtil;
import cn.mlamp.insightflow.cms.util.FileDownloadUtil;
import cn.mlamp.insightflow.cms.util.VideoUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * @Author: husuper
 * @CreateTime: 2025-03-26
 */
@Component
@Slf4j
public class QianchuanVideoAnalysisTask {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    public static final String VIDEO_ANALYSIS_KEY = "qianchuanVideoAnalysisJob.analysis.";

    @Autowired
    private TaskConfig taskConfig;

    @Autowired
    private QianchuanMaterialVideoService qianchuanMaterialVideoService;

    @Autowired
    private IVideoInfoService videoInfoService;

    @Autowired
    private VideoRecognition2Handle videoRecognition2Handle;

    @Resource(name = "analysisThreadExecutor")
    private ExecutorService analysisThreadExecutor;

    public static void main(String[] args) {
        Date twoDaysAgo = java.sql.Date.valueOf(LocalDate.now().minusDays(2));
        System.out.println(DateUtil.getYYYYMMDD(twoDaysAgo));
        System.out.println(DateUtil.getYYYYMMDD(new Date()));

    }



    @Scheduled(cron = "0 0/3 * * * ?")
    public void queryDataVideoAnalysisJob() {
        if (taskConfig.isLocal()) {
            return;
        }

        if (isVideoAnalysisJobRunning()) {
            return;
        }

        // 分布式锁(防止同一时间多台服务器重复触发)，保证每次定时任务只有一台服务器在执行
        boolean result = Boolean.TRUE.equals(stringRedisTemplate.opsForValue().setIfAbsent("queryDataVideoAnalysisJob", "ImSyncJob-timer", 300, TimeUnit.SECONDS));
        if (result) {
            try {
                queryDataVideoAnalysisJob(DateUtil.getYYYYMMDD(new Date()));
            }catch (Exception e){
                log.error("定时任务执行失败",e);
            }finally {
                stringRedisTemplate.delete("queryDataVideoAnalysisJob");
            }
        } else {
            log.warn("多台服务器，防止同一个任务重复执行");
        }
    }

    /**
     * 检查 Redis 中是否存在视频分析任务锁
     *
     * @return 如果存在返回 true，否则返回 false
     */
    public boolean isVideoAnalysisJobRunning() {
        String jobKey = "qianchuanVideoAnalysisJob";
        return Boolean.TRUE.equals(stringRedisTemplate.hasKey(jobKey));
    }


    public void queryDataVideoAnalysisJob(String dateStr) {
        List<QianchuanMaterialVideo> queryDatas = queryData(dateStr, AnalysisStatusEnum.WAITING);
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        for (QianchuanMaterialVideo qianchuanMaterialVideo : queryDatas) {
            String lockKey = VIDEO_ANALYSIS_KEY + qianchuanMaterialVideo.getVideoId();
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    boolean result = Boolean.TRUE.equals(stringRedisTemplate.opsForValue().setIfAbsent(lockKey, "ImSyncJob-timer", 900, TimeUnit.SECONDS));
                    if (result) {
                        try {
                            videoAnalysis(qianchuanMaterialVideo);
                        } finally {
                            stringRedisTemplate.delete(lockKey);
                        }
                    }
                } catch (Exception e) {
                    log.error("并发处理视频分析失败，videoId: {}", qianchuanMaterialVideo.getVideoId(), e);
                }
            }, analysisThreadExecutor);
            futures.add(future);
        }

        // 可选：等待所有任务完成
//        try {
//            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).get(300, TimeUnit.SECONDS);
//        } catch (Exception e) {
//            log.error("并发任务执行异常", e);
//        }
    }


    private List<QianchuanMaterialVideo> queryData(String dateStr, AnalysisStatusEnum status) {
        //查询创建时间为 date的数据   date格式是yyyy-mm-dd
//        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
//        LocalDate date = LocalDate.parse(dateStr, formatter);

//        LocalDateTime startOfDay = date.atStartOfDay();
//        LocalDateTime endOfDay = date.plusDays(1).atStartOfDay();

        LambdaQueryWrapper<QianchuanMaterialVideo> queryWrapper = new LambdaQueryWrapper<QianchuanMaterialVideo>();
        queryWrapper.eq(QianchuanMaterialVideo::getAnalysisStatus, status.getCode());
        queryWrapper.orderByDesc(QianchuanMaterialVideo::getPublishTime);
        queryWrapper.last("limit 15");
        List<QianchuanMaterialVideo> list = qianchuanMaterialVideoService.list(queryWrapper);
        return list;
    }


    private void videoAnalysis(QianchuanMaterialVideo qianchuanMaterialVideo) {
        try {
            //分析视频
            AnalysisVideoCreateRequest analysisVideoCreateRequest = new AnalysisVideoCreateRequest();
            analysisVideoCreateRequest.setEsId(qianchuanMaterialVideo.getVideoId());
            analysisVideoCreateRequest.setTypeName(AnalysisVideoTypeEnum.QIANCHUAN_VIDEO.getVideoType());
            AnalysisVideoStrategyMap.process(analysisVideoCreateRequest);
        } catch (Exception e) {
            log.error("视频分析失败", e);

        }
    }

}
