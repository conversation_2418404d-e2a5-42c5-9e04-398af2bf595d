package cn.mlamp.insightflow.cms.controller.dam;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;

import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.config.UserContext;
import cn.mlamp.insightflow.cms.enums.dam.DamTaskTypeEnum;
import cn.mlamp.insightflow.cms.model.dto.dam.DamAssetUploadTaskDTO;
import cn.mlamp.insightflow.cms.model.dto.dam.DamAssetUploadTaskStorageDTO;
import cn.mlamp.insightflow.cms.model.query.PageParam;
import cn.mlamp.insightflow.cms.model.vo.dam.DamAssetUploadTaskDetailVO;
import cn.mlamp.insightflow.cms.model.vo.dam.DamPageResult;
import cn.mlamp.insightflow.cms.service.dam.IDamAssetUploadTaskService;
import jakarta.validation.Valid;

/**
 * DAM素材上传任务Controller
 */
@RestController
@RequestMapping("/dam/asset-upload-tasks")
public class DamAssetUploadTaskController {

    @Autowired
    private IDamAssetUploadTaskService taskService;

    /**
     * 创建素材上传任务
     */
    @PostMapping
    public RespBody<Integer> createAssetUploadTask(@RequestBody @Valid DamAssetUploadTaskDTO taskDTO) {
        // 从请求中获取用户ID和租户ID，实际情况应该从认证信息中获取
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();

        Integer taskId = taskService.createAssetUploadTask(taskDTO, userId, tenantId);
        return RespBody.ok(taskId);
    }

    /**
     * 获取素材上传任务列表
     */
    @GetMapping
    public RespBody<DamPageResult<DamAssetUploadTaskDetailVO>> getAssetUploadTaskList(
            @RequestParam(required = false) Integer type,
            @RequestParam(required = false, defaultValue = "1") Integer pageNum,
            @RequestParam(required = false, defaultValue = "10") Integer pageSize,
            @RequestParam(required = false, defaultValue = "id") String sortField,
            @RequestParam(required = false, defaultValue = "desc") String sortOrder) throws JsonProcessingException {
        // 从请求中获取用户ID和租户ID，实际情况应该从认证信息中获取
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();

        // 构造分页参数
        final PageParam pageParam = new PageParam();
        pageParam.setPageNum(pageNum);
        pageParam.setPageSize(pageSize);
        pageParam.setSortField(sortField);
        pageParam.setSortOrder(sortOrder);

        final DamPageResult<DamAssetUploadTaskDetailVO> result = taskService.getAssetUploadTaskListV2(DamTaskTypeEnum.getByCode(type),
                pageParam, userId, tenantId);
        return RespBody.ok(result);
    }

    /**
     * 获取素材上传任务详情
     */
    @GetMapping("/{taskId}/detail")
    public RespBody<DamAssetUploadTaskDetailVO> getAssetUploadTaskDetail(@PathVariable Integer taskId) {
        // 从请求中获取用户ID和租户ID，实际情况应该从认证信息中获取
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();

        final DamAssetUploadTaskDetailVO detail = taskService.getAssetUploadTaskDetailV2(taskId, userId, tenantId);
        return RespBody.ok(detail);
    }

    /**
     * 素材入库
     */
    @PostMapping("/{taskId}/storage")
    public RespBody<Void> storageAssets(@PathVariable Integer taskId,
                                        @RequestBody DamAssetUploadTaskStorageDTO request) throws JsonProcessingException {
        // 从请求中获取用户ID和租户ID，实际情况应该从认证信息中获取
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();

        boolean result = taskService.storageAssets(taskId, request, userId, tenantId);
        return result ? RespBody.ok() : RespBody.fail("素材入库失败");
    }

}