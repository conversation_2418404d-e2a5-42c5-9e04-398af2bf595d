package cn.mlamp.insightflow.cms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商品记录表
 * @TableName cms_product_record
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value ="cms_product_record")
@Data
public class CmsProductRecord extends BaseEntity implements Serializable {
    /**
     * 导入的URL
     */
    @TableField(value = "url")
    private String url;

    /**
     * 标题
     */
    @TableField(value = "title")
    private String title;

    /**
     * 品牌
     */
    @TableField(value = "brand")
    private String brand;

    /**
     * 产品名称
     */
    @TableField(value = "product_name")
    private String productName;

    /**
     * 卖点
     */
    @TableField(value = "selling_point")
    private String sellingPoint;

    /**
     * 时长
     */
    @TableField(value = "duration")
    private Integer duration;

    /**
     * 镜头数量
     */
    @TableField(value = "lens_num")
    private Integer lensNum;

    /**
     * 场景
     */
    @TableField(value = "scene")
    private String scene;

    /**
     * 人数
     */
    @TableField(value = "people_num")
    private Integer peopleNum;

    /**
     * 节日
     */
    @TableField(value = "festival")
    private String festival;

    /**
     * 用户Id
     */
    @TableField(value = "user_id")
    private Integer userId;

    /**
     * 租户Id
     */
    @TableField(value = "tenant_id")
    private Integer tenantId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}