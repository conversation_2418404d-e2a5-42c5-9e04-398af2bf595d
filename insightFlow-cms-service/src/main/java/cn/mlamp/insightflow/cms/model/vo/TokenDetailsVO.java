package cn.mlamp.insightflow.cms.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
public class TokenDetailsVO implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "使用任务类型", required = false)
    private String taskType;

    @Schema(description = "名称", required = false)
    private String taskName;

    @Schema(description = "使用人", required = false)
    private String userName;

    @Schema(description = "使用时间", required = false)
    private Date usageTime;

    @Schema(description = "使用消耗", required = false)
    private Integer tokens;
}
