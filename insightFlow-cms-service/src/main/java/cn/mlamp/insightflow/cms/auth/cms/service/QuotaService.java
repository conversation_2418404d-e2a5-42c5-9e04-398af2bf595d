package cn.mlamp.insightflow.cms.auth.cms.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 租户配额Service
 *
 * <AUTHOR>
 * @since 2022/2/22 19:48
 */
@Slf4j
@Service
public class QuotaService {

    /**
     * 内部邮箱后缀
     */
    @Value("#{'${internal.user.email.suffix:miaozhen.com,mininglamp.com}'.split(',')}")
    private List<String> internalUsersEmailSuffix;

    public boolean checkIsInternalUsersByUserName(String userName) {
        List<String> splits = StrUtil.split(userName, StrUtil.AT);
        if (CollUtil.isNotEmpty(splits) && splits.size() >= 2 && internalUsersEmailSuffix.contains(splits.get(1))) {
            return true;
        }

        return false;
    }

}