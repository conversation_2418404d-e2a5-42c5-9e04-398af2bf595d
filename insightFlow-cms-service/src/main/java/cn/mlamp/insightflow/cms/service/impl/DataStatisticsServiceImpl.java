package cn.mlamp.insightflow.cms.service.impl;

import cn.mlamp.insightflow.cms.service.DataStatisticsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据统计服务实现类
 *
 * <AUTHOR>
 * @since 2024-10-15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DataStatisticsServiceImpl implements DataStatisticsService {

    private final JdbcTemplate jdbcTemplate;

    /**
     * 获取千川推送数据统计
     *
     * @return 统计结果
     */
    @Override
    public Map<String, Object> getQianchuanPushStatistics() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 查询1：千川推送新增数据和重复率
            Map<String, Object> pushStats = jdbcTemplate.queryForMap(
                    "SELECT COUNT(*) AS new_data_count, ROUND((5000 - COUNT(*)) / 5000, 4) AS duplicate_rate, "
                            + "COUNT(CASE WHEN duration >= 120 THEN 1 END) AS long_videos_count, "
                            + "MAX(duration) AS max_duration "
                            + "FROM cms_qianchuan_material_video "
                            + "WHERE DATE(create_time) = CURDATE() - INTERVAL 1 DAY");

            result.put("pushStats", pushStats);

            // 查询2：最近1000条分析状态统计
            List<Map<String, Object>> recentAnalysisStats = jdbcTemplate
                    .queryForList("SELECT analysis_status AS status, COUNT(*) AS count, "
                            + "ROUND(COUNT(*) / total.total_count * 100, 2) AS percentage "
                            + "FROM (SELECT analysis_status FROM cms_qianchuan_material_video "
                            + "WHERE analysis_status NOT IN (0, 1) ORDER BY update_time DESC LIMIT 1000) AS sub "
                            + "JOIN (SELECT COUNT(*) AS total_count FROM (SELECT analysis_status "
                            + "FROM cms_qianchuan_material_video WHERE analysis_status NOT IN (0, 1) "
                            + "ORDER BY update_time DESC LIMIT 1000) AS total_sub) AS total ON 1=1 "
                            + "GROUP BY analysis_status");

            result.put("recentAnalysisStats", recentAnalysisStats);

            // 查询3：最近10小时视频处理速度
            List<Map<String, Object>> processingSpeedStats = jdbcTemplate
                    .queryForList("SELECT analysis_status AS status, COUNT(*) AS count, "
                            + "ROUND(COUNT(*) / total.total_count * 100, 2) AS percentage, "
                            + "CASE WHEN analysis_status = 2 THEN ROUND(COUNT(*) / 10, 2) ELSE NULL END AS hourly_rate "
                            + "FROM cms_qianchuan_material_video "
                            + "JOIN (SELECT COUNT(*) AS total_count FROM cms_qianchuan_material_video "
                            + "WHERE update_time >= NOW() - INTERVAL 10 HOUR AND analysis_status IN (2, 3)) AS total ON 1=1 "
                            + "WHERE update_time >= NOW() - INTERVAL 10 HOUR AND analysis_status IN (2, 3) "
                            + "GROUP BY analysis_status");

            result.put("processingSpeedStats", processingSpeedStats);

            // 查询4：AI 生成脚本成功失败率
            Map<String, Object> scriptGenStats = jdbcTemplate.queryForMap("SELECT " + "COUNT(*) AS total_count, "
                    + "SUM(CASE WHEN task_status = 3 THEN 1 ELSE 0 END) AS success_count, "
                    + "SUM(CASE WHEN task_status = 5 THEN 1 ELSE 0 END) AS fail_count, "
                    + "ROUND(SUM(CASE WHEN task_status = 3 THEN 1 ELSE 0 END) / COUNT(*) * 100, 2) AS success_rate, "
                    + "ROUND(SUM(CASE WHEN task_status = 5 THEN 1 ELSE 0 END) / COUNT(*) * 100, 2) AS fail_rate "
                    + "FROM cms_task_info "
                    + "WHERE task_type = 1 AND task_status IN (3, 5) AND update_time >= NOW() - INTERVAL 24 HOUR");

            result.put("scriptGenStats", scriptGenStats);

            // 查询5：用户视频解析成功失败率 - 链接上传
            Map<String, Object> urlVideoStats = jdbcTemplate.queryForMap("SELECT " + "COUNT(*) AS total_count, "
                    + "SUM(CASE WHEN v.status = 3 THEN 1 ELSE 0 END) AS success_count, "
                    + "SUM(CASE WHEN v.status = 4 THEN 1 ELSE 0 END) AS fail_count, "
                    + "ROUND(SUM(CASE WHEN v.status = 3 THEN 1 ELSE 0 END) / COUNT(*) * 100, 2) AS success_rate, "
                    + "ROUND(SUM(CASE WHEN v.status = 4 THEN 1 ELSE 0 END) / COUNT(*) * 100, 2) AS fail_rate "
                    + "FROM cms_document_info d " + "LEFT JOIN cms_video_info v ON v.source_file_id = d.id "
                    + "WHERE d.source_type = 'from_url' AND v.status IN (3, 4) AND d.update_time >= NOW() - INTERVAL 24 HOUR");

            result.put("urlVideoStats", urlVideoStats);

            // 查询6：用户视频解析成功失败率 - 个人上传
            Map<String, Object> localVideoStats = jdbcTemplate.queryForMap("SELECT " + "COUNT(*) AS total_count, "
                    + "SUM(CASE WHEN v.status = 3 THEN 1 ELSE 0 END) AS success_count, "
                    + "SUM(CASE WHEN v.status = 4 THEN 1 ELSE 0 END) AS fail_count, "
                    + "ROUND(SUM(CASE WHEN v.status = 3 THEN 1 ELSE 0 END) / COUNT(*) * 100, 2) AS success_rate, "
                    + "ROUND(SUM(CASE WHEN v.status = 4 THEN 1 ELSE 0 END) / COUNT(*) * 100, 2) AS fail_rate "
                    + "FROM cms_document_info d " + "LEFT JOIN cms_video_info v ON v.source_file_id = d.id "
                    + "WHERE d.source_type = 'from_local' AND v.status IN (3, 4) AND d.update_time >= NOW() - INTERVAL 24 HOUR");

            result.put("localVideoStats", localVideoStats);

            // 查询7：异步任务平均处理时长统计
            Map<String, Object> taskDurationStats = getAsyncTaskDurationStats();
            result.put("taskDurationStats", taskDurationStats);

        } catch (Exception e) {
            log.error("获取千川推送数据统计失败", e);
            result.put("error", e.getMessage());
        }

        return result;
    }

    /**
     * 获取异步任务平均处理时长统计
     *
     * @return 统计结果
     */
    private Map<String, Object> getAsyncTaskDurationStats() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 查询整体视频理解任务平均处理时长
            Map<String, Object> videoFlowStats = jdbcTemplate
                    .queryForMap("SELECT AVG(TIMESTAMPDIFF(SECOND, create_time, update_time)) AS avg_duration_seconds "
                            + "FROM cms_async_task " + "WHERE task_type = 'async_video_flow_v2' "
                            + "AND is_deleted = 0 AND task_status = 2 " + "ORDER BY id DESC " + "LIMIT 1000");
            result.put("videoFlowStats", videoFlowStats);

            // 查询视频分镜分割任务平均处理时长
            Map<String, Object> videoSplitStats = jdbcTemplate
                    .queryForMap("SELECT AVG(TIMESTAMPDIFF(SECOND, create_time, update_time)) AS avg_duration_seconds "
                            + "FROM cms_async_task " + "WHERE task_type = 'async_video_split' "
                            + "AND is_deleted = 0 AND task_status = 2 " + "ORDER BY id DESC " + "LIMIT 1000");
            result.put("videoSplitStats", videoSplitStats);

            // 查询黄金三秒任务平均处理时长
            Map<String, Object> imageDecoding3sStats = jdbcTemplate
                    .queryForMap("SELECT AVG(TIMESTAMPDIFF(SECOND, create_time, update_time)) AS avg_duration_seconds "
                            + "FROM cms_async_task " + "WHERE task_type = 'async_image_decoding_3s' "
                            + "AND is_deleted = 0 AND task_status = 2 " + "ORDER BY id DESC " + "LIMIT 1000");
            result.put("imageDecoding3sStats", imageDecoding3sStats);

            // 查询分镜图片打标任务平均处理时长（按视频分组，从最早的创建时间到最晚的更新时间）
            Map<String, Object> sceneDecodingStats = jdbcTemplate.queryForMap(
                    "SELECT AVG(total_duration) AS avg_duration_seconds " + "FROM (" + "  SELECT " + "    es_id, "
                            + "    TIMESTAMPDIFF(SECOND, MIN(create_time), MAX(update_time)) AS total_duration, "
                            + "    MAX(id) AS max_id " + "  FROM " + "    cms_async_task " + "  WHERE "
                            + "    task_type = 'async_scene_decoding' " + "    AND is_deleted = 0 AND task_status = 2 "
                            + "  GROUP BY " + "    es_id " + "  ORDER BY " + "    max_id DESC " + "  LIMIT 1000 "
                            + ") AS video_stats");
            result.put("sceneDecodingStats", sceneDecodingStats);

        } catch (Exception e) {
            log.error("获取异步任务平均处理时长统计失败", e);
            result.put("error", e.getMessage());
        }

        return result;
    }

    /**
     * 格式化统计数据为Markdown消息
     *
     * @param statistics 统计数据
     * @return Markdown格式的消息
     */
    @Override
    public String formatStatisticsToMarkdown(Map<String, Object> statistics) {
        StringBuilder markdown = new StringBuilder();
        markdown.append("# 千川数据统计报告\n\n");

        // 格式化千川推送新增数据和重复率
        if (statistics.containsKey("pushStats")) {
            Map<String, Object> pushStats = (Map<String, Object>) statistics.get("pushStats");
            markdown.append("## 千川推送数据统计\n");
            markdown.append("- **新增数据量**: ").append(pushStats.get("new_data_count")).append("\n");
            markdown.append("- **重复率**: ").append(formatPercentage(pushStats.get("duplicate_rate"))).append("\n");
            markdown.append("- **2分钟以上视频数量**: ").append(pushStats.get("long_videos_count")).append("\n");
            markdown.append("- **最长视频时长**: ").append(formatDurations(pushStats.get("max_duration"))).append("\n\n");
        }

        // 格式化最近1000条分析状态统计
        if (statistics.containsKey("recentAnalysisStats")) {
            List<Map<String, Object>> recentAnalysisStats = (List<Map<String, Object>>) statistics
                    .get("recentAnalysisStats");
            markdown.append("## 最近1000条分析状态统计\n");
            markdown.append("| 分析状态 | 个数 | 百分比 |\n");

            for (Map<String, Object> stat : recentAnalysisStats) {
                String status = formatAnalysisStatus(stat.get("status"));
                markdown.append("| ").append(status).append(" | ").append(stat.get("count")).append(" | ")
                        .append(formatPercentage(stat.get("percentage"))).append(" |\n");
            }
            markdown.append("\n");
        }

        // 格式化最近10小时视频处理速度
        if (statistics.containsKey("processingSpeedStats")) {
            List<Map<String, Object>> processingSpeedStats = (List<Map<String, Object>>) statistics
                    .get("processingSpeedStats");
            markdown.append("## 最近10小时视频处理速度\n");
            markdown.append("| 分析状态 | 个数 | 百分比 | 每小时处理量 |\n");

            for (Map<String, Object> stat : processingSpeedStats) {
                String status = formatAnalysisStatus(stat.get("status"));
                String hourlyRate = stat.get("hourly_rate") != null ? stat.get("hourly_rate").toString() : "-";

                markdown.append("| ").append(status).append(" | ").append(stat.get("count")).append(" | ")
                        .append(formatPercentage(stat.get("percentage"))).append(" | ").append(hourlyRate)
                        .append(" |\n");
            }
            markdown.append("\n");
        }

        // 格式化AI生成脚本成功失败率
        if (statistics.containsKey("scriptGenStats")) {
            Map<String, Object> scriptGenStats = (Map<String, Object>) statistics.get("scriptGenStats");
            markdown.append("## AI生成脚本成功失败率（24小时）\n");
            markdown.append("| 状态 | 个数 | 百分比 |\n");
            markdown.append("| 成功 | ").append(scriptGenStats.get("success_count")).append(" | ")
                    .append(formatPercentage(scriptGenStats.get("success_rate"))).append(" |\n");
            markdown.append("| 失败 | ").append(scriptGenStats.get("fail_count")).append(" | ")
                    .append(formatPercentage(scriptGenStats.get("fail_rate"))).append(" |\n\n");
            markdown.append("**总任务数**: ").append(scriptGenStats.get("total_count")).append("\n\n");
        }

        // 格式化用户视频解析成功失败率 - 链接上传
        if (statistics.containsKey("urlVideoStats")) {
            Map<String, Object> urlVideoStats = (Map<String, Object>) statistics.get("urlVideoStats");
            markdown.append("## 用户视频解析成功失败率 - 链接上传（24小时）\n");
            markdown.append("| 状态 | 个数 | 百分比 |\n");
            markdown.append("| 成功 | ").append(urlVideoStats.get("success_count")).append(" | ")
                    .append(formatPercentage(urlVideoStats.get("success_rate"))).append(" |\n");
            markdown.append("| 失败 | ").append(urlVideoStats.get("fail_count")).append(" | ")
                    .append(formatPercentage(urlVideoStats.get("fail_rate"))).append(" |\n\n");
            markdown.append("**总任务数**: ").append(urlVideoStats.get("total_count")).append("\n\n");
        }

        // 格式化用户视频解析成功失败率 - 个人上传
        if (statistics.containsKey("localVideoStats")) {
            Map<String, Object> localVideoStats = (Map<String, Object>) statistics.get("localVideoStats");
            markdown.append("## 用户视频解析成功失败率 - 个人上传（24小时）\n");
            markdown.append("| 状态 | 个数 | 百分比 |\n");
            markdown.append("| 成功 | ").append(localVideoStats.get("success_count")).append(" | ")
                    .append(formatPercentage(localVideoStats.get("success_rate"))).append(" |\n");
            markdown.append("| 失败 | ").append(localVideoStats.get("fail_count")).append(" | ")
                    .append(formatPercentage(localVideoStats.get("fail_rate"))).append(" |\n\n");
            markdown.append("**总任务数**: ").append(localVideoStats.get("total_count")).append("\n");
        }

        // 格式化异步任务平均处理时长统计
        if (statistics.containsKey("taskDurationStats")) {
            Map<String, Object> taskDurationStats = (Map<String, Object>) statistics.get("taskDurationStats");
            markdown.append("## 异步任务平均处理时长统计\n");
            markdown.append("| 任务类型 | 平均处理时长 |\n");

            // 整体视频理解
            if (taskDurationStats.containsKey("videoFlowStats")) {
                Map<String, Object> videoFlowStats = (Map<String, Object>) taskDurationStats.get("videoFlowStats");
                markdown.append("| 整体视频理解 | ").append(formatDuration(videoFlowStats.get("avg_duration_seconds")))
                        .append(" |\n");
            }

            // 视频分镜分割
            if (taskDurationStats.containsKey("videoSplitStats")) {
                Map<String, Object> videoSplitStats = (Map<String, Object>) taskDurationStats.get("videoSplitStats");
                markdown.append("| 视频分镜分割 | ").append(formatDuration(videoSplitStats.get("avg_duration_seconds")))
                        .append(" |\n");
            }

            // 黄金三秒
            if (taskDurationStats.containsKey("imageDecoding3sStats")) {
                Map<String, Object> imageDecoding3sStats = (Map<String, Object>) taskDurationStats
                        .get("imageDecoding3sStats");
                markdown.append("| 黄金三秒 | ").append(formatDuration(imageDecoding3sStats.get("avg_duration_seconds")))
                        .append(" |\n");
            }

            // 分镜图片打标
            if (taskDurationStats.containsKey("sceneDecodingStats")) {
                Map<String, Object> sceneDecodingStats = (Map<String, Object>) taskDurationStats
                        .get("sceneDecodingStats");
                markdown.append("| 分镜图片打标 | ").append(formatDuration(sceneDecodingStats.get("avg_duration_seconds")))
                        .append(" |\n");
            }

            markdown.append("\n");
        }

        return markdown.toString();
    }

    /**
     * 格式化时间时长（秒）
     *
     * @param value 时间时长值（秒）
     * @return 格式化后的时间时长字符串
     */
    private String formatDuration(Object value) {
        if (value == null) {
            return "-";
        }

        try {
            double seconds = Double.parseDouble(value.toString());
            if (seconds < 60) {
                return String.format("%.2f秒", seconds);
            } else if (seconds < 3600) {
                int minutes = (int) (seconds / 60);
                double remainingSeconds = seconds % 60;
                return String.format("%d分%.2f秒", minutes, remainingSeconds);
            } else {
                int hours = (int) (seconds / 3600);
                int minutes = (int) ((seconds % 3600) / 60);
                double remainingSeconds = seconds % 60;
                return String.format("%d小时%d分%.2f秒", hours, minutes, remainingSeconds);
            }
        } catch (NumberFormatException e) {
            return value.toString();
        }
    }

    /**
     * 格式化分析状态
     *
     * @param status 状态码
     * @return 格式化后的状态描述
     */
    private String formatAnalysisStatus(Object status) {
        if (status == null) {
            return "未知";
        }

        int statusCode = Integer.parseInt(status.toString());
        switch (statusCode) {
        case 0:
            return "未分析";
        case 1:
            return "分析中";
        case 2:
            return "分析成功";
        case 3:
            return "分析失败";
        default:
            return "未知状态(" + statusCode + ")";
        }
    }

    /**
     * 格式化百分比
     *
     * @param value 百分比值
     * @return 格式化后的百分比字符串
     */
    private String formatPercentage(Object value) {
        if (value == null) {
            return "-";
        }

        try {
            double percentage = Double.parseDouble(value.toString());
            // 如果值已经是百分比形式（大于1），则直接格式化
            if (percentage > 1.0) {
                return String.format("%.2f%%", percentage);
            } else {
                return String.format("%.2f%%", percentage * 100);
            }
        } catch (NumberFormatException e) {
            return value.toString();
        }
    }

    /**
     * 格式化时长（秒）为可读形式
     *
     * @param value 时长值（秒）
     * @return 格式化后的时长字符串
     */
    private String formatDurations(Object value) {
        if (value == null) {
            return "-";
        }

        try {
            int seconds = Integer.parseInt(value.toString());
            int minutes = seconds / 60;
            int remainingSeconds = seconds % 60;

            if (minutes > 0) {
                return String.format("%d分%d秒 (%d秒)", minutes, remainingSeconds, seconds);
            } else {
                return String.format("%d秒", seconds);
            }
        } catch (NumberFormatException e) {
            return value.toString();
        }
    }
}
