package cn.mlamp.insightflow.cms.util;

/**
 * <AUTHOR>
 * @since 2024-10-08
 */
public class PathUtil {

    /**
     * 从给定的路径中提取文件名
     * <p>
     * 此方法旨在处理存储在图片服务器上的文件路径，通过找到路径中最后一个斜杠('/')的位置，
     * 然后提取从该斜杠后面的所有字符，这些字符代表了服务器上的文件名
     *
     * @param path 图片服务器上的文件路径，格式如：/folder1/folder2/filename.ext
     * @return 返回提取的文件名，如：filename.ext
     */
    public static String pathToName(String path) {
        // 通过substring方法提取从最后一个斜杠('/')之后的所有字符作为图片服务器上的文件路径
        return path.substring(path.lastIndexOf('/') + 1);
    }

    /**
     * @param path 图片服务器上的文件路径，格式如：/folder1/folder2/filename.ext
     * @return 返回提取的文件后缀，如：ext
     */
    public static String pathToSuffix(String path) {
        // 通过substring方法提取从最后一个点（'.'）之后的所有字符作为文件后缀
        return path.substring(path.lastIndexOf('.'));
    }

    public static String pathToType(String path) {
        // 通过substring方法提取从最后一个点（'.'）之后的所有字符作为文件后缀
        return path.substring(path.lastIndexOf('.'));
    }

}
