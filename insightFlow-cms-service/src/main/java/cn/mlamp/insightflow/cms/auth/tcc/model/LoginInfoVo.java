package cn.mlamp.insightflow.cms.auth.tcc.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class LoginInfoVo {

    @Schema(description = "用户id", required = true)
    private Integer id;

    @Schema(description = "用户名称", required = true)
    private String name;

    /**
     * 用户邮箱
     */
    @Schema(description = "用户邮箱", required = false)
    private String email;

    /**
     * 用户手机号
     */
    @Schema(description = "用户手机号", required = false)
    private String phoneNumber;

    /**
     * 当前租户Id
     */
    @Schema(description = "当前租户Id", required = true)
    private Integer currentTenantId;

    @Schema(description = "租户列表", required = true)
    private List<TenantVo> tenants;
}
