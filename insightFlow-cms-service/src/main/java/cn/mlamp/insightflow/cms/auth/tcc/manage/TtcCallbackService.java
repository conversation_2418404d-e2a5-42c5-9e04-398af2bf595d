package cn.mlamp.insightflow.cms.auth.tcc.manage;//package cn.mlamp.insightflow.cms.auth.tcc.manage;

import cn.hutool.core.collection.CollUtil;
import cn.mlamp.insightflow.cms.auth.tcc.model.User;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mz.ttc.callback.CallbackService;
import com.mz.ttc.callback.Modification;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

/**
 * 统一多租户回调的Service
 */

@Service
@Slf4j
@Lazy
public class TtcCallbackService implements CallbackService {

    @Autowired
    @Lazy
    private TtcUserService userService;

    @Override
    public void handleModified(Modification message) {
        log.info("TtcCallbackService-handleModified Start Executor ttcCallback Logic handler........");
        if (message == null || CollUtil.isEmpty(message.getMessage())) {
            return;
        }
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            log.info("TtcCallbackService-handleModified ttcCallback info:{}", objectMapper.writeValueAsString(message));
        } catch (Exception e) {
            log.error("Failed to convert message to JSON", e);
        }
        message.getMessage().forEach(it -> {
            try {
                // 如果当前租户的话，需要进行强制退出
                User user = userService.getCurrentUser(it.getTicket());
                log.info("TtcCallbackService-handleModified user:{}", objectMapper.writeValueAsString(user));
                if (null != user && user.getCurrentTenantId().equals(it.getTenantId())) {
                    userService.logoutByTicket(it.getTicket());
                    log.info("TtcCallbackService-handleModified logout, success:{},{}", it.getDomain(), it.getTicket());
                }
            } catch (Exception e) {
                try {
                    log.warn("TtcCallbackService-handleModified ttc CallBack handler, logout error，msg:{}, e:", objectMapper.writeValueAsString(it), e);
                } catch (JsonProcessingException ex) {
                    throw new RuntimeException(ex);
                }
            }
        });

        log.info("TtcCallbackService-handleModified End Executor ttcCallback Logic handler........");
    }
}
