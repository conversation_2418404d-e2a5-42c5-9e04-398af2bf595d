
package cn.mlamp.insightflow.cms.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.baomidou.mybatisplus.core.metadata.IPage;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.config.UserContext;
import cn.mlamp.insightflow.cms.entity.CmsVideoAsr;
import cn.mlamp.insightflow.cms.entity.QianchuanMaterialVideo;
import cn.mlamp.insightflow.cms.model.dto.QianchuanVideoHotspotQueryDTO;
import cn.mlamp.insightflow.cms.model.query.RecommenListRequest;
import cn.mlamp.insightflow.cms.model.query.RecommendationRequest;
import cn.mlamp.insightflow.cms.model.vo.BrandProductRecommendVO;
import cn.mlamp.insightflow.cms.service.CmsSearchHistoryService;
import cn.mlamp.insightflow.cms.service.IVideoAsrService;
import cn.mlamp.insightflow.cms.service.QianchuanVideoHotspotService;

@Tag(name = "千川视频热点接口")
@RestController
@RequestMapping("/qianchuan-hotspot")
public class QianchuanVideoHotspotController {

    @Autowired
    private QianchuanVideoHotspotService videoHotspotService;

    @Autowired
    private IVideoAsrService iVideoAsrService;

    @Autowired
    private CmsSearchHistoryService searchHistoryService;

    @Operation(summary = "获取千川视频热点列表")
    @PostMapping("/list")
    public RespBody<IPage<QianchuanMaterialVideo>> list(@RequestBody QianchuanVideoHotspotQueryDTO queryDTO) {
        // 保存查询历史
        if (queryDTO != null && queryDTO.getKeyword() != null) {
            // 从 queryDTO 中获取 userId 和 tenantId
            Integer userId = queryDTO.getUserId();
            Integer tenantId = queryDTO.getTenantId();
            String searchType = queryDTO.getSelectType();

            // 异步保存查询历史，不影响主流程
            try {
                searchHistoryService.saveSearchHistory(userId, tenantId, queryDTO.getKeyword(), searchType,
                        "qianchuan_hotspot");
            } catch (Exception e) {
                // 异常不影响主流程，只记录日志
                e.printStackTrace();
            }
        }

        return RespBody.ok(videoHotspotService.listVideos(queryDTO));
    }

    @Operation(summary = "获取曝光最高的视频数据")
    @PostMapping("/top-exposure")
    public RespBody<String> getTopExposureVideo(@RequestBody RecommendationRequest request) {
        String recommendation = "";

        // 获取今天的曝光最高的视频数据
        QianchuanMaterialVideo topData = videoHotspotService.getTopExposureVideo();
        if (topData == null) {
            return RespBody.ok(recommendation);
        }
        switch (request.getType()) {
        case "1":
            recommendation = topData.getTitle(); // 返回“标题”
            break;
        case "2":
            CmsVideoAsr asrResult = iVideoAsrService.getByEsIdWithMinStart(topData.getVideoId());
            recommendation = (asrResult != null) ? asrResult.getText() : "";
            break;
        default:
            return RespBody.ok(recommendation);
        }
        return RespBody.ok(recommendation);
    }

    /**
     * 获取品牌和产品名称推荐列表
     *
     * @param request 请求参数（关键词、开始时间、结束时间）
     * @return 返回品牌和产品名称推荐列表
     */
    @Operation(summary = "获取品牌和产品名称推荐列表")
    @PostMapping("/recommend/list")
    public RespBody<List<BrandProductRecommendVO>> getRecommendationList(@RequestBody RecommenListRequest request) {
        // 调用 service 层处理业务逻辑
        List<BrandProductRecommendVO> recommendations = videoHotspotService.getBrandProductRecommendList(request);
        return RespBody.ok(recommendations);
    }
}
