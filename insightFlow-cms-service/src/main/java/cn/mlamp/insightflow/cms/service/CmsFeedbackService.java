package cn.mlamp.insightflow.cms.service;

import cn.mlamp.insightflow.cms.entity.CmsFeedback;
import cn.mlamp.insightflow.cms.model.query.FeedbackRequest;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @description 针对表【cms_feedback(反馈意见表)】的数据库操作Service
 */
public interface CmsFeedbackService extends IService<CmsFeedback> {

    void userFeedback(FeedbackRequest uploadVO, Integer userId, Integer tenantId);

}
