// package cn.mlamp.insightflow.cms.config;

// import org.springframework.ai.embedding.EmbeddingModel;
// import org.springframework.ai.vectorstore.pgvector.PgVectorStore;
// import org.springframework.beans.factory.annotation.Qualifier;
// import org.springframework.context.annotation.Bean;
// import org.springframework.context.annotation.Configuration;
// import org.springframework.jdbc.core.JdbcTemplate;

// @Configuration
// public class PgVectorStoreConfig {

// @Bean
// public PgVectorStore
// userNicknameVectorStore(@Qualifier("postgresJdbcTemplate") JdbcTemplate
// jdbcTemplate,
// EmbeddingModel embeddingModel) {
// return PgVectorStore.builder(jdbcTemplate,
// embeddingModel).dimensions(1536).schemaName("public")
// .vectorTableName("user_nike_name_vector_store").maxDocumentBatchSize(10000).build();
// }

// @Bean
// public PgVectorStore
// textContentVectorStore(@Qualifier("postgresJdbcTemplate") JdbcTemplate
// jdbcTemplate,
// EmbeddingModel embeddingModel) {
// return PgVectorStore.builder(jdbcTemplate,
// embeddingModel).dimensions(1536).schemaName("public")
// .vectorTableName("text_content_vector_store").maxDocumentBatchSize(10000).build();
// }

// @Bean
// public PgVectorStore
// videoContentVectorStore(@Qualifier("postgresJdbcTemplate") JdbcTemplate
// jdbcTemplate,
// EmbeddingModel embeddingModel) {
// return PgVectorStore.builder(jdbcTemplate,
// embeddingModel).dimensions(1536).schemaName("public")
// .vectorTableName("video_content_vector_store").maxDocumentBatchSize(10000).build();
// }

// @Bean
// public PgVectorStore
// combinedDataVectorStore(@Qualifier("postgresJdbcTemplate") JdbcTemplate
// jdbcTemplate,
// EmbeddingModel embeddingModel) {
// return PgVectorStore.builder(jdbcTemplate,
// embeddingModel).dimensions(1536).schemaName("public")
// .vectorTableName("combined_data_vector_store").maxDocumentBatchSize(10000).build();
// }

// }
