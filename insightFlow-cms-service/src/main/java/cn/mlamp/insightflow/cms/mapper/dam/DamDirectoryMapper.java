package cn.mlamp.insightflow.cms.mapper.dam;

import cn.mlamp.insightflow.cms.entity.dam.DamDirectory;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * DAM素材目录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
@Mapper
public interface DamDirectoryMapper extends BaseMapper<DamDirectory> {

    List<DamDirectory> getViewableDirectory(@Param("userId") Integer userId,
                                            @Param("tenantId") Integer tenantId);

}
