package cn.mlamp.insightflow.cms.controller;

import java.util.List;
import java.util.Map;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.mlamp.insightflow.cms.annotation.SignCheck;
import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.model.dto.QianchuanMaterialVideoDTO;
import cn.mlamp.insightflow.cms.model.dto.VideoExistCheckRequestDTO;
import cn.mlamp.insightflow.cms.model.dto.VideoIdsRequestDTO;
import cn.mlamp.insightflow.cms.service.QianchuanMaterialVideoService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping("/open-api/material-videos")
@RequiredArgsConstructor
public class QianchuanMaterialVideoController {

    private final QianchuanMaterialVideoService materialVideoService;

    @SignCheck
    @PostMapping("/batch")
    public RespBody<Map<String, Object>> batchInsertOrUpdate(@RequestBody List<QianchuanMaterialVideoDTO> videoList) {
        Map<String, Object> result = materialVideoService.batchInsertOrUpdate(videoList);
        return RespBody.ok(result);
    }

    /**
     * 查询不存在于数据库中的视频ID
     *
     * @param request 包含要检查的视频ID列表的请求
     * @return 不存在于数据库中的视频ID列表
     */
    @SignCheck
    @PostMapping("/check-non-existing")
    public RespBody<List<String>> checkNonExistingVideoIds(@RequestBody VideoIdsRequestDTO request) {
        List<String> nonExistingIds = materialVideoService.findNonExistingVideoIds(request.getVideoIds(),
                request.getStartTime(), request.getEndTime());
        return RespBody.ok(nonExistingIds);
    }

    /**
     * 检查视频是否已存在
     *
     * @param request 包含视频ID或标题和时长的请求
     * @return 如果视频已存在返回true，否则返回false
     */
    @SignCheck
    @PostMapping("/check-exists")
    @Operation(summary = "检查视频是否已存在")
    public RespBody<Boolean> checkVideoExists(@RequestBody VideoExistCheckRequestDTO request) {
        boolean exists = materialVideoService.checkVideoExists(request);
        return RespBody.ok(exists);
    }
}
