package cn.mlamp.insightflow.cms.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @Author: husuper
 * @CreateTime: 2025-04-21
 */
@Data
public class VideoResultRequest implements Serializable {


    @Schema(description = "视频分析结果Id", required = false)
    private Integer videoInfoReusltId;

    @Schema(description = "视频分析结果Ids", required = false)
    private List<Integer> videoInfoReusltIds;


    @Schema(description = "分镜数据", required = true)
    private Map<String,Object> sceneDecoding;


    @Data
    public static class SceneDecoding{

        @Schema(description = "分镜视频url", required = false)
        private String sceneDecodingUrl;

        @Schema(description = "分镜视频Girl url", required = false)
        private String sceneDecodingGifUrl;

        @Schema(description = "是否黄金3秒", required = false)
        private Boolean threeGold;

        @Schema(description = "镜头描述", required = false)
        private String 镜头描述;

        @Schema(description = "视频内容策略", required = false)
        private String 视频内容策略;

        @Schema(description = "镜头参考", required = false)
        private String 镜头参考;

        @Schema(description = "品牌植入", required = false)
        private String 品牌植入;

        @Schema(description = "镜头类型", required = false)
        private String 镜头类型;

        @Schema(description = "运镜方式", required = false)
        private String 运镜方式;

        @Schema(description = "分镜开始时间戳", required = false)
        private String 分镜开始时间戳;

        @Schema(description = "分镜结束时间戳", required = false)
        private String 分镜结束时间戳;

        @Schema(description = "出现演员", required = false)
        private String 出现演员;

        @Schema(description = "演员动作", required = false)
        private String 演员动作;

        @Schema(description = "演员表情", required = false)
        private String 演员表情;

        @Schema(description = "台词", required = false)
        private String 台词;

        @Schema(description = "台词情绪", required = false)
        private String 台词情绪;

        @Schema(description = "服装造型", required = false)
        private String 服装造型;

        @Schema(description = "道具清单", required = false)
        private String 道具清单;

        @Schema(description = "布景要求", required = false)
        private String 布景要求;

        @Schema(description = "背景音乐/音效", required = false)
        private String 背景音乐音效;

        @Schema(description = "光影与色彩要求", required = false)
        private String 光影与色彩要求;

        @Schema(description = "摄影器材", required = false)
        private String 摄影器材;

    }



}
