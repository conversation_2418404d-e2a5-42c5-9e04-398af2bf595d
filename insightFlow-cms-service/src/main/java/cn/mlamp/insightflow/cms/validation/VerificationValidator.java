package cn.mlamp.insightflow.cms.validation;

import cn.mlamp.insightflow.cms.constant.CommonConstant;
import cn.mlamp.insightflow.cms.enums.VerificationTypeEnum;
import cn.mlamp.insightflow.cms.util.ObjReflectUtil;
import lombok.extern.slf4j.Slf4j;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Pattern;

/**
 * 验证类型校验器
 *
 * <AUTHOR>
 * @since 2022-09-20 16:52:26
 **/
@Slf4j
public class VerificationValidator implements ConstraintValidator<VerificationConstraint, Object> {

    @Override
    public void initialize(VerificationConstraint constraintAnnotation) {
        ConstraintValidator.super.initialize(constraintAnnotation);
    }

    @Override
    public boolean isValid(Object obj, ConstraintValidatorContext constraintValidatorContext) {
        try {
            // 获取对象类型的Field数据
            Map<String, Object> fieldMap = ObjReflectUtil.getKeyValueMap(obj);

            Object verificationTypeObj = fieldMap.get("verificationType");
            Object verificationTargetObj = fieldMap.get("verificationTarget");
            if (Objects.isNull(verificationTypeObj) || Objects.isNull(verificationTargetObj)) {
                return false;
            }

            String targetStr = String.valueOf(verificationTargetObj);
            if (VerificationTypeEnum.TYPE_EMAIL.getCode().equals(verificationTypeObj)) {
                Pattern compile = Pattern.compile(CommonConstant.EMAIL_REGX);
                return compile.matcher(targetStr).matches();
            } else if (VerificationTypeEnum.TYPE_MOBILE.getCode().equals(verificationTypeObj)) {
                Pattern compile = Pattern.compile(CommonConstant.MOBILE_REGX);
                return compile.matcher(targetStr).matches();
            }
        } catch (IllegalAccessException e) {
            log.error("反射获取值异常, e:", e);
        }

        return false;
    }

}
