package cn.mlamp.insightflow.cms.controller;

import cn.hutool.core.collection.CollUtil;
import cn.mlamp.insightflow.cms.auth.tcc.manage.TtcUserService;
import cn.mlamp.insightflow.cms.auth.tcc.model.*;

import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.exception.BusinessException;
import cn.mlamp.insightflow.cms.model.query.ActivateTenantParam;
import cn.mlamp.insightflow.cms.model.query.RegisterTenantParam;
import cn.mlamp.insightflow.cms.model.query.ResetPasswordParam;
import cn.mlamp.insightflow.cms.service.IUserService;
import cn.mlamp.insightflow.cms.service.RegisterService;

import com.google.common.collect.Lists;
import com.mz.ttc.annotation.Login;
import com.mz.ttc.annotation.Logout;
import com.mz.ttc.annotation.SwitchTenant;
import com.mz.ttc.util.TtcUtil;
import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.apache.shiro.web.util.WebUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@RequestMapping("/ttc")
@RestController
@Validated
@Tag(name = "用户租户管理关接口")
public class User2Controller {

    @Autowired
    @Lazy
    private TtcUserService ttcUserService;

    @Resource
    private IUserService userService;


    /**
     * 用户登录
     *
     * @param loginRequest
     * @return
     */
    @PostMapping("/login")
    @Login
    @Operation(summary = "用户登录接口")
    @Timed(value = "ttc_login_service", extraTags = {"login_step", "ttc"}, percentiles = {0.99D,
            0.999D}, histogram = true)
    public RespBody<LoginInfoVo> login(@RequestBody LoginDto loginRequest) throws BusinessException {
        User user = ttcUserService.login(loginRequest.getUsername(), loginRequest.getPassword());
        LoginInfoVo loginInfoVo = covertUser2LoginInfoVo(user);
        return RespBody.ok(loginInfoVo);
    }

    /**
     * 用户登出
     *
     * @return
     */
    @GetMapping("/logout")
    @Logout
    @Operation(summary = "退出登录接口")
    public RespBody<Boolean> logout() {
        ttcUserService.logout();
        return RespBody.ok(true);
    }

    /**
     * 获取用户权限列表
     *
     * @return
     */
    @GetMapping("/permissions")
    @Operation(summary = "获取用户权限列表")
    public RespBody<List<String>> getPermissions() {
        String ticket = TtcUtil.getCurrentTicket();
        List<String> permissionList = ttcUserService.getPermissionList(ticket);
        userService.saveUser();
        return RespBody.ok(permissionList);
    }

    /**
     * 切换租户
     *
     * @param id
     * @return
     */
    @GetMapping("/switchTenant")
    @SwitchTenant
    @Operation(summary = "切换租户接口")
    public RespBody<List<TenantVo>> switchTenant(@RequestParam("id") Integer id){
        String ticket = TtcUtil.getCurrentTicket();
        List<Tenant> tenants = ttcUserService.switchTenant(ticket, id);
        List<TenantVo> tenantVoList = covertTenantVo(tenants);
        return RespBody.ok(tenantVoList);
    }

    /**
     * 获取用户详情
     *
     * @return
     */
    @GetMapping("/userinfo")
    @Operation(summary = "获取用户详情接口")
    public RespBody<LoginInfoVo> getUserInfo(){
        String ticket = TtcUtil.getCurrentTicket();
        User user = ttcUserService.getCurrentUser(ticket);

        LoginInfoVo loginInfoVo = covertUser2LoginInfoVo(user);
        return RespBody.ok(loginInfoVo);
    }

    private LoginInfoVo covertUser2LoginInfoVo(User user) {
        List<TenantVo> tenantVos = covertTenantVo(user.getTenants());

        LoginInfoVo loginInfoVo = new LoginInfoVo();

        loginInfoVo.setId(user.getId());
        loginInfoVo.setName(user.getName());
        loginInfoVo.setEmail(user.getEmail());
        loginInfoVo.setPhoneNumber(user.getPhoneNumber());
        loginInfoVo.setCurrentTenantId(user.getCurrentTenantId());
        loginInfoVo.setTenants(tenantVos);
        return loginInfoVo;
    }

    @GetMapping("/redirect/user-manager")
    @RequiresPermissions(value = {"userManage"})
    @Operation(summary = "跳转到用户角色管理接口")
    public void getUserManageUrl(HttpServletRequest request, HttpServletResponse response) {
        String userManagerUrl = ttcUserService.getUserManagerUrl();
        try {
            WebUtils.redirectToSavedRequest(request, response, userManagerUrl);
        } catch (Exception ex) {
            log.error("UserController-getUserManageUrl redirect error:{}, ex: ", userManagerUrl, ex);
        }
    }

    private List<TenantVo> covertTenantVo(List<Tenant> tenants) {
        // 判断相关信息
        if (CollUtil.isEmpty(tenants)) {
            return Lists.newArrayList();
        }

        return tenants.stream().map(t -> {
            TenantVo tenantVo = new TenantVo();
            tenantVo.setId(t.getId());
            tenantVo.setName(t.getName());
            tenantVo.setExpiredTime(t.getExpireTime().getTime());
            tenantVo.setIsCurrent(t.isCurrent());
            return tenantVo;
        }).collect(Collectors.toList());
    }

    @Autowired
    private RegisterService registerService;

    /**
     * 校验租户名是否存在
     */
    @GetMapping("/user/register/check_tenant_name_exist")
    public RespBody<?> checkTenantNameExist(
            @NotBlank(message = "override_message.tenant_name_not_blank") @Size(max = 20, message = "override_message.tenant_name_out_of_length") String nickName) {
        registerService.checkTenantNameExist(nickName);
        return RespBody.ok();
    }

    /**
     * 自助注册
     */
    @PostMapping("/user/register")
    public RespBody<?> registerTenant(@Valid @RequestBody RegisterTenantParam registerTenantParam) {
        registerService.registerTenant(registerTenantParam);
        return RespBody.ok();
    }

    /**
     * 自助激活
     */
    @PostMapping("/user/activate")
    public RespBody<?> activateTenant(@Valid @RequestBody ActivateTenantParam activateTenantParam) {
        registerService.activateTenant(activateTenantParam);
        return RespBody.ok();
    }

    /**
     * 重置密码
     */
    @PostMapping("/user/reset_password")
    public RespBody<?> resetPassword(@Valid @RequestBody ResetPasswordParam resetPasswordParam) {
        registerService.resetPassword(resetPasswordParam);
        return RespBody.ok();
    }

}
