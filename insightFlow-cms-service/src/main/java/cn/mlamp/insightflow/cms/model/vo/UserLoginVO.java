package cn.mlamp.insightflow.cms.model.vo;

import lombok.Data;

import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * @Author: husuper
 * @CreateTime: 2024-10-07
 */
@Data
public class UserLoginVO implements Serializable {

    @Schema(description = "用户userID", required = true)
    private Integer userId;

    @Schema(description = "刷新token", required = true)
    private String refreshToken;

    @Schema(description = "鉴权token", required = true)
    private String token;

}

