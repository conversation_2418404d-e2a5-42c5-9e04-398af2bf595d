package cn.mlamp.insightflow.cms.service.impl;


import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.mlamp.insightflow.cms.common.file.IS3FlowService;
import cn.mlamp.insightflow.cms.config.S3FlowConfig;
import cn.mlamp.insightflow.cms.config.properties.ObjectStorageFlowProperties;
import cn.mlamp.insightflow.cms.constant.FileConstant;
import cn.mlamp.insightflow.cms.entity.CmsTaskInfo;
import cn.mlamp.insightflow.cms.entity.CmsTaskDetail;
import cn.mlamp.insightflow.cms.enums.TaskDetailDataTypeEnum;
import cn.mlamp.insightflow.cms.enums.TaskDetailTypeEnum;
import cn.mlamp.insightflow.cms.enums.TaskTypeEnum;
import cn.mlamp.insightflow.cms.enums.VideoSynthesisStatusEnum;
import cn.mlamp.insightflow.cms.util.VideoUtil;
import cn.mlamp.insightflow.cms.exception.BusinessException;
import cn.mlamp.insightflow.cms.model.query.VideoSynthesisDetailUpdateRequest;
import cn.mlamp.insightflow.cms.model.query.VideoSynthesisListRequest;
import cn.mlamp.insightflow.cms.model.query.VideoSynthesisRequest;
import cn.mlamp.insightflow.cms.model.vo.VideoSynthesisListItemVO;
import cn.mlamp.insightflow.cms.model.vo.VideoSynthesisScriptExcelVO;
import cn.mlamp.insightflow.cms.model.vo.VideoSynthesisVO;
import cn.mlamp.insightflow.cms.service.FileService;
import cn.mlamp.insightflow.cms.service.ICmsTaskDetailService;
import cn.mlamp.insightflow.cms.service.ICmsTaskInfoService;
import cn.mlamp.insightflow.cms.service.IVideoSynthesisService;
import cn.mlamp.insightflow.cms.util.FileDownloadUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import net.bramp.ffmpeg.FFmpeg;
import net.bramp.ffmpeg.FFmpegExecutor;
import net.bramp.ffmpeg.builder.FFmpegBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.net.URL;

import com.alibaba.excel.EasyExcel;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;

import java.io.File;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.StringJoiner;
import java.util.stream.Collectors;

/**
 * 视频合成服务实现
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
@Slf4j
@Service
public class VideoSynthesisServiceImpl implements IVideoSynthesisService {

    @Autowired
    private ICmsTaskInfoService taskInfoService;

    @Autowired
    private ICmsTaskDetailService taskDetailService;

    @Autowired
    private FileService fileService;

    @Autowired
    private ObjectStorageFlowProperties objectStorageFlowProperties;

    @Autowired
    private IS3FlowService ossService;

    @Autowired
    private cn.mlamp.insightflow.cms.service.dam.IDamAssetService damAssetService;

    @Value("${spring.profiles.active:prod}")
    private String activeProfile;

    @Value("${analysis.video.ffmpeg}")
    private String ffmpegPath;

    @Override
    public VideoSynthesisVO createSynthesisTask(VideoSynthesisRequest request) {
        // 创建任务记录
        CmsTaskInfo taskInfo = new CmsTaskInfo();
        taskInfo.setTaskType(TaskTypeEnum.VIDEO_SYNTHESIS.getCode());

        // 设置任务名称
        String taskName = request.getTaskName();
        if (StrUtil.isBlank(taskName)) {
            taskName = "视频合成_" + DateUtil.format(new Date(), "yyyyMMddHHmmss");
        }
        taskInfo.setName(taskName);

        // 设置任务状态为排队中
        taskInfo.setTaskStatus(VideoSynthesisStatusEnum.QUEUING.getCode());

        // 将视频OSS ID列表转为JSON存入taskArg字段
        taskInfo.setTaskArg(JSONUtil.toJsonStr(request.getVideoOssIds()));

        // 设置用户ID和租户ID
        taskInfo.setUserId(request.getUserId());
        taskInfo.setTenantId(request.getTenantId());

        // 保存任务记录
        taskInfoService.save(taskInfo);

        // 创建任务详情记录，将任务信息存入data字段
        taskDetailService.saveTaskDetail(taskInfo.getId(), TaskDetailTypeEnum.INPUT.getCode(),
                TaskDetailDataTypeEnum.USER_SAVE.getCode(), request.getTaskInfo());

        // 更新素材使用次数
        if (request.getAssetIds() != null && !request.getAssetIds().isEmpty()) {
            damAssetService.updateUsedNum(request.getAssetIds(), request.getUserId(), request.getTenantId());
        }

        // 返回任务ID
        VideoSynthesisVO vo = new VideoSynthesisVO();
        vo.setTaskId(taskInfo.getId());
        vo.setTaskStatus(taskInfo.getTaskStatus());
        vo.setTaskStatusDesc(VideoSynthesisStatusEnum.getByCode(taskInfo.getTaskStatus()).getMsg());
        return vo;
    }

    @Async("commonTaskExecutor")
    @Override
    public void synthesizeVideoAsync(VideoSynthesisRequest request, Integer taskId) {
        List<String> videoOssIds = request.getVideoOssIds();
        List<String> videoFilePaths = new ArrayList<>();
        String concatFilePath = null;
        String resultOssId = null;

        try {
            // 更新任务状态为下载中
            updateSynthesisTaskStatus(taskId, VideoSynthesisStatusEnum.DOWNLOADING, null);

            // 下载视频文件
            List<String> downloadUrls = fileService.getDownloadSignatureUrl(videoOssIds);
            for (int i = 0; i < downloadUrls.size(); i++) {
                String downloadUrl = downloadUrls.get(i);
                String videoFilePath = FileDownloadUtil.getPath("synthesis_" + taskId + "_" + i + ".mp4");
                // 使用downloadFile2方法替代downloadFile方法，更好地处理带有复杂查询参数的URL
                FileDownloadUtil.downloadFile2(downloadUrl, videoFilePath);
                videoFilePaths.add(videoFilePath);
            }

            // 更新任务状态为合成中
            updateSynthesisTaskStatus(taskId, VideoSynthesisStatusEnum.SYNTHESIZING, null);

            // 合成视频
            concatFilePath = FileDownloadUtil.getPath("synthesis_result_" + taskId + ".mp4");
            concatVideos(videoFilePaths, concatFilePath);

            // 更新任务状态为上传中
            updateSynthesisTaskStatus(taskId, VideoSynthesisStatusEnum.UPLOADING, null);

            // 上传合成后的视频到OSS
            resultOssId = uploadSynthesisVideo(taskId, concatFilePath);

            // 获取视频首帧图片
            String firstFrameImagePath = FileDownloadUtil.getPath("first_frame_" + taskId + ".jpg");
            VideoUtil.cutImage(concatFilePath, firstFrameImagePath, 0);
            String firstFrameOssId = uploadFirstFrameImage(taskId, firstFrameImagePath);

            // 获取视频时长
            Long videoDuration = VideoUtil.getVideoLength(concatFilePath);

            // 更新任务详情信息
            updateTaskDetailWithVideoInfo(taskId, firstFrameOssId, videoDuration);

            // 更新任务状态为已完成，并设置resultFileIds字段
            CmsTaskInfo taskInfo = taskInfoService.getById(taskId);
            taskInfo.setResultFileIds(resultOssId);
            taskInfo.setTaskStatus(VideoSynthesisStatusEnum.COMPLETED.getCode());
            taskInfoService.updateById(taskInfo);

            // 清理首帧图片临时文件
            FileDownloadUtil.deleteFile(firstFrameImagePath);

        } catch (Exception e) {
            log.error("视频合成失败，taskId={}", taskId, e);
            updateSynthesisTaskStatus(taskId, VideoSynthesisStatusEnum.FAILED, e.getMessage());
        } finally {
            // 清理临时文件
            cleanupTempFiles(videoFilePaths, concatFilePath);
        }
    }

    /**
     * 合成视频
     *
     * @param inputVideoPaths 输入视频文件路径列表
     * @param outputFilePath  输出文件路径
     * @throws IOException IO异常
     */
    private void concatVideos(List<String> inputVideoPaths, String outputFilePath) throws IOException {
        List<String> tsFiles = new ArrayList<>();
        List<String> tempFiles = new ArrayList<>(); // 用于存储需要清理的临时文件
        try {
            FFmpeg ffmpeg = new FFmpeg(ffmpegPath);
            FFmpegExecutor executor = new FFmpegExecutor(ffmpeg);

            // 1. 检查音轨并处理视频，然后转成 .ts
            for (int i = 0; i < inputVideoPaths.size(); i++) {
                String inputPath = inputVideoPaths.get(i);
                String processedPath = inputPath;

                // 检查视频是否有音轨
                boolean hasAudioTrack = checkVideoHasAudioTrack(inputPath);
                log.info("视频 {} 音轨检查结果: {}", inputPath, hasAudioTrack ? "有音轨" : "无音轨");

                // 如果没有音轨，添加静音音轨
                if (!hasAudioTrack) {
                    String audioAddedPath = inputPath.replace(".mp4", "_with_audio.mp4");
                    addSilentAudioTrack(inputPath, audioAddedPath, executor);
                    processedPath = audioAddedPath;
                    tempFiles.add(audioAddedPath); // 添加到临时文件清理列表
                    log.info("为视频 {} 添加静音音轨，输出: {}", inputPath, audioAddedPath);
                }

                // 转换为 .ts 格式
                String tsPath = inputPath.replace(".mp4", ".ts");
                FFmpegBuilder tsBuilder = new FFmpegBuilder().setInput(processedPath).overrideOutputFiles(true)
                        .addOutput(tsPath).addExtraArgs("-c", "copy").addExtraArgs("-bsf:v", "h264_mp4toannexb")
                        .addExtraArgs("-f", "mpegts").done();

                executor.createJob(tsBuilder).run();
                tsFiles.add(tsPath);
            }

            // 2. 拼接 .ts 文件为 concat:ts1|ts2|ts3 格式
            String concatInput = tsFiles.stream().collect(Collectors.joining("|"));

            // 3. 合并为 mp4
            FFmpegBuilder concatBuilder = new FFmpegBuilder().setInput("concat:" + concatInput)
                    .overrideOutputFiles(true).addOutput(outputFilePath).addExtraArgs("-c", "copy")
                    .addExtraArgs("-bsf:a", "aac_adtstoasc").done();

            executor.createJob(concatBuilder).run();

        } catch (Exception e) {
            log.error("视频合成失败", e);
            throw new BusinessException("视频合成失败: " + e.getMessage());
        } finally {
            // 4. 清理临时的 .ts 文件
            for (String tsFile : tsFiles) {
                try {
                    FileDownloadUtil.deleteFile(tsFile);
                    log.debug("删除临时ts文件: {}", tsFile);
                } catch (Exception e) {
                    log.warn("删除临时ts文件失败: {}", tsFile, e);
                }
            }

            // 5. 清理临时的带音轨文件
            for (String tempFile : tempFiles) {
                try {
                    FileDownloadUtil.deleteFile(tempFile);
                    log.debug("删除临时带音轨文件: {}", tempFile);
                } catch (Exception e) {
                    log.warn("删除临时带音轨文件失败: {}", tempFile, e);
                }
            }
        }
    }

    /**
     * 检查视频是否有音轨
     *
     * @param videoPath 视频文件路径
     * @return true表示有音轨，false表示无音轨
     */
    private boolean checkVideoHasAudioTrack(String videoPath) {
        try {
            FFmpeg ffmpeg = new FFmpeg(ffmpegPath);
            FFmpegExecutor executor = new FFmpegExecutor(ffmpeg);

            // 使用ffprobe检查音轨信息
            // 这里使用ffmpeg的-f null输出来检查是否有音频流
            FFmpegBuilder probeBuilder = new FFmpegBuilder().setInput(videoPath).addExtraArgs("-v", "error")
                    .addExtraArgs("-select_streams", "a:0").addExtraArgs("-show_entries", "stream=codec_type")
                    .addExtraArgs("-of", "csv=p=0").addOutput("/dev/null").addExtraArgs("-f", "null").done();

            try {
                executor.createJob(probeBuilder).run();
                return true; // 如果没有异常，说明有音轨
            } catch (Exception e) {
                // 如果执行失败，可能是没有音轨
                log.debug("检查音轨时出现异常，可能无音轨: {}", e.getMessage());
                return false;
            }
        } catch (Exception e) {
            log.warn("检查视频音轨失败: {}", videoPath, e);
            // 如果检查失败，默认认为有音轨，避免不必要的处理
            return true;
        }
    }

    /**
     * 为视频添加静音音轨
     *
     * @param inputPath  输入视频路径
     * @param outputPath 输出视频路径
     * @param executor   FFmpeg执行器
     */
    private void addSilentAudioTrack(String inputPath, String outputPath, FFmpegExecutor executor) {
        try {
            // 获取视频时长
            Long videoDuration = VideoUtil.getVideoLength(inputPath);
            if (videoDuration == null || videoDuration <= 0) {
                log.warn("无法获取视频时长，使用默认时长10秒: {}", inputPath);
                videoDuration = 10L; // 默认10秒
            }

            // 添加静音音轨，指定时长
            FFmpegBuilder audioBuilder = new FFmpegBuilder().setInput(inputPath).addExtraArgs("-f", "lavfi")
                    .addExtraArgs("-i", "anullsrc=channel_layout=stereo:sample_rate=44100:duration=" + videoDuration)
                    .addExtraArgs("-c:v", "copy").addExtraArgs("-c:a", "aac").addExtraArgs("-shortest")
                    .overrideOutputFiles(true).addOutput(outputPath).done();

            executor.createJob(audioBuilder).run();
            log.info("成功为视频添加静音音轨: {} -> {}", inputPath, outputPath);
        } catch (Exception e) {
            log.error("为视频添加静音音轨失败: {} -> {}", inputPath, outputPath, e);
            throw new BusinessException("为视频添加静音音轨失败: " + e.getMessage());
        }
    }

    /**
     * 上传合成视频到OSS
     *
     * @param taskId        任务ID
     * @param videoFilePath 视频文件路径
     * @return OSS ID
     */
    private String uploadSynthesisVideo(Integer taskId, String videoFilePath) {
        try {
            // 生成OSS路径，根据环境添加前缀
            String envPrefix = "prod".equals(activeProfile) ? "prod/" : "test/";
            String ossPath = envPrefix + "videos/synthesis/" + taskId + "_"
                    + DateUtil.format(new Date(), "yyyyMMddHHmmss")
                    + ".mp4";

            // 使用IS3FlowService上传文件
            File file = new File(videoFilePath);
            if (!file.exists()) {
                throw new BusinessException("合成视频文件不存在");
            }

            // 使用cmsS3FlowService上传文件
            IS3FlowService ossService = S3FlowConfig.getByBucket(objectStorageFlowProperties.getCms().getBucketName());
            ossService.upload(ossPath, file);

            return ossPath;
        } catch (Exception e) {
            log.error("上传合成视频失败，taskId={}", taskId, e);
            throw new BusinessException("上传合成视频失败: " + e.getMessage());
        }
    }

    /**
     * 清理临时文件
     *
     * @param videoFilePaths 视频文件路径列表
     * @param concatFilePath 合成文件路径
     */
    private void cleanupTempFiles(List<String> videoFilePaths, String concatFilePath) {
        // 删除下载的视频文件
        if (videoFilePaths != null) {
            for (String videoFilePath : videoFilePaths) {
                FileDownloadUtil.deleteFile(videoFilePath);
            }
        }

        // 删除合成的视频文件
        if (concatFilePath != null) {
            FileDownloadUtil.deleteFile(concatFilePath);
        }
    }

    /**
     * 上传视频首帧图片
     *
     * @param taskId    任务ID
     * @param imagePath 图片路径
     * @return OSS ID
     * @throws IOException IO异常
     */
    private String uploadFirstFrameImage(Integer taskId, String imagePath) throws IOException {
        // 根据环境添加前缀
        String envPrefix = "prod".equals(activeProfile) ? "prod/" : "test/";
        String ossId = envPrefix + "video_synthesis/first_frame/" + taskId + "/" + System.currentTimeMillis() + ".jpg";
        IS3FlowService s3Service = S3FlowConfig.getByBucket(objectStorageFlowProperties.getCms().getBucketName());
        s3Service.upload(ossId, new File(imagePath));
        return ossId;
    }

    /**
     * 更新任务详情信息，添加视频首帧图片和时长
     *
     * @param taskId        任务ID
     * @param firstFrameOss d 首帧图片OSS ID
     * @param videoDuration 视频时长（秒）
     */
    private void updateTaskDetailWithVideoInfo(Integer taskId, String firstFrameOssId, Long videoDuration) {
        // 获取原始任务详情
        CmsTaskDetail taskDetail = taskDetailService.getTaskDetail(taskId, TaskDetailTypeEnum.INPUT.getCode(),
                TaskDetailDataTypeEnum.USER_SAVE.getCode());
        if (taskDetail != null && StrUtil.isNotBlank(taskDetail.getData())) {
            try {
                // 解析原始任务信息
                JSONObject taskInfoJson = JSONUtil.parseObj(taskDetail.getData());

                // 添加视频首帧图片和时长信息
                taskInfoJson.set("firstFrameOssId", firstFrameOssId);
                taskInfoJson.set("videoDuration", videoDuration);

                // 更新任务详情
                taskDetail.setData(taskInfoJson.toString());
                taskDetailService.updateById(taskDetail);

                log.info("更新任务详情信息成功，taskId={}, firstFrameOssId={}, videoDuration={}", taskId, firstFrameOssId,
                        videoDuration);
            } catch (Exception e) {
                log.error("更新任务详情信息失败，taskId={}", taskId, e);
            }
        }
    }

    @Override
    public int updateSynthesisTaskStatus(Integer taskId, VideoSynthesisStatusEnum statusEnum, String errorMsg) {
        return taskInfoService.getBaseMapper().update(null,
                new LambdaUpdateWrapper<CmsTaskInfo>().set(CmsTaskInfo::getTaskStatus, statusEnum.getCode())
                        .set(StrUtil.isNotBlank(errorMsg), CmsTaskInfo::getErrorMessage, errorMsg)
                        .eq(CmsTaskInfo::getId, taskId));
    }

    @Override
    public VideoSynthesisVO getSynthesisTaskDetail(Integer taskId) {
        CmsTaskInfo taskInfo = taskInfoService.getById(taskId);
        if (taskInfo == null) {
            throw new BusinessException("任务不存在");
        }

        VideoSynthesisVO vo = new VideoSynthesisVO();
        vo.setTaskId(taskInfo.getId());
        vo.setTaskStatus(taskInfo.getTaskStatus());
        vo.setTaskStatusDesc(VideoSynthesisStatusEnum.getByCode(taskInfo.getTaskStatus()).getMsg());
        vo.setResultOssId(taskInfo.getResultFileIds());
        vo.setCreateTime(taskInfo.getCreateTime());
        vo.setUpdateTime(taskInfo.getUpdateTime());

        // 获取任务信息
        CmsTaskDetail taskDetail = taskDetailService.getTaskDetail(taskId, TaskDetailTypeEnum.INPUT.getCode(),
                TaskDetailDataTypeEnum.USER_SAVE.getCode());
        if (taskDetail != null) {
            String taskInfoData = taskDetail.getData();

            // 处理storyBoards中recommendVideos的签名URL
            try {
                JSONObject taskInfoJson = JSONUtil.parseObj(taskInfoData);

                // 如果有首帧图片，获取签名地址
                if (taskInfoJson.containsKey("firstFrameOssId")) {
                    String firstFrameOssId = taskInfoJson.getStr("firstFrameOssId");
                    if (StrUtil.isNotBlank(firstFrameOssId)) {
                        URL url = ossService.downloadPresignedUrl(objectStorageFlowProperties.getCms().getBucketName(),
                                firstFrameOssId, FileConstant.FILE_EXPIRE_TIME, 0);
                        vo.setFirstFrameOssUrl(url.toString());
                    }
                }

                // 处理storyBoards中的recommendVideos
                if (taskInfoJson.containsKey("storyBoards")) {
                    JSONArray storyBoards = taskInfoJson.getJSONArray("storyBoards");
                    for (int i = 0; i < storyBoards.size(); i++) {
                        JSONObject storyBoard = storyBoards.getJSONObject(i);

                        // Sign URL for storyBoard.objOssId and set it to storyBoard.url
                        if (storyBoard.containsKey("objOssId")) {
                            String storyBoardObjOssId = storyBoard.getStr("objOssId");
                            if (StrUtil.isNotBlank(storyBoardObjOssId)) {
                                String signedStoryBoardUrl = getSignedUrl(storyBoardObjOssId);
                                storyBoard.set("url", signedStoryBoardUrl);
                            }
                        }

                        if (storyBoard.containsKey("recommendVideos")) {
                            JSONArray recommendVideos = storyBoard.getJSONArray("recommendVideos");
                            for (int j = 0; j < recommendVideos.size(); j++) {
                                JSONObject video = recommendVideos.getJSONObject(j);

                                // 处理视频缩略图URL签名
                                if (video.containsKey("objOssId")) {
                                    String objOssId = video.getStr("objOssId");
                                    if (StrUtil.isNotBlank(objOssId)) {
                                        URL url = ossService.downloadPresignedUrl(
                                                objectStorageFlowProperties.getCms().getBucketName(), objOssId,
                                                FileConstant.FILE_EXPIRE_TIME, 0);
                                        video.set("videoUrl", url.toString());
                                    }
                                }

                                // 处理视频文件URL签名
                                if (video.containsKey("headObjOssId")) {
                                    String headObjOssId = video.getStr("headObjOssId");
                                    if (StrUtil.isNotBlank(headObjOssId)) {
                                        URL url = ossService.downloadPresignedUrl(
                                                objectStorageFlowProperties.getCms().getBucketName(), headObjOssId,
                                                FileConstant.FILE_EXPIRE_TIME, 0);
                                        video.set("headUrl", url.toString());
                                    }
                                }
                            }
                        }
                    }

                    // 更新处理后的taskInfoJson
                    taskInfoData = taskInfoJson.toString();
                }
            } catch (Exception e) {
                log.warn("处理storyBoards中recommendVideos的签名URL失败", e);
            }

            vo.setTaskInfo(taskInfoData);
        }

        // 如果有合成视频，获取签名地址
        if (StrUtil.isNotBlank(taskInfo.getResultFileIds())) {
            try {
                URL url = ossService.downloadPresignedUrl(objectStorageFlowProperties.getCms().getBucketName(),
                        taskInfo.getResultFileIds(), FileConstant.FILE_EXPIRE_TIME, 0);
                vo.setResultOssUrl(url.toString());
            } catch (Exception e) {
                log.warn("获取合成视频签名地址失败", e);
            }
        }

        return vo;
    }

    @Override
    public String getDownloadUrl(Integer taskId) {
        CmsTaskInfo taskInfo = taskInfoService.getById(taskId);
        if (taskInfo == null) {
            throw new BusinessException("任务不存在");
        }

        // 检查任务状态
        if (!VideoSynthesisStatusEnum.COMPLETED.getCode().equals(taskInfo.getTaskStatus())) {
            throw new BusinessException("视频合成任务未完成，无法下载");
        }

        // 检查结果文件ID
        String resultFileId = taskInfo.getResultFileIds();
        if (StrUtil.isBlank(resultFileId)) {
            throw new BusinessException("视频合成结果不存在");
        }

        try {
            // 获取下载链接
            URL url = ossService.downloadPresignedUrl(objectStorageFlowProperties.getCms().getBucketName(),
                    resultFileId, FileConstant.FILE_EXPIRE_TIME, 0);
            return url.toString();
        } catch (Exception e) {
            log.error("获取视频合成结果下载链接失败，taskId={}", taskId, e);
            throw new BusinessException("获取下载链接失败: " + e.getMessage());
        }
    }

    @Override
    public List<String> getSegmentVideoDownloadUrls(Integer taskId) {
        CmsTaskInfo taskInfo = taskInfoService.getById(taskId);
        if (taskInfo == null) {
            throw new BusinessException("任务不存在");
        }

        // 从 taskArg 字段获取视频OSS ID列表
        String taskArg = taskInfo.getTaskArg();
        if (StrUtil.isBlank(taskArg)) {
            throw new BusinessException("视频分段信息不存在");
        }

        try {
            // 解析JSON字符串为列表
            List<String> videoOssIds = JSONUtil.toList(JSONUtil.parseArray(taskArg), String.class);
            if (videoOssIds.isEmpty()) {
                throw new BusinessException("视频分段列表为空");
            }

            // 获取每个分段视频的下载链接
            List<String> downloadUrls = new ArrayList<>();
            for (int i = 0; i < videoOssIds.size(); i++) {
                String ossId = videoOssIds.get(i);
                URL url = ossService.downloadPresignedUrl(objectStorageFlowProperties.getCms().getBucketName(), ossId,
                        FileConstant.FILE_EXPIRE_TIME, 0);
                downloadUrls.add(url.toString());
            }

            return downloadUrls;
        } catch (Exception e) {
            log.error("获取分段视频下载链接失败，taskId={}", taskId, e);
            throw new BusinessException("获取分段视频下载链接失败: " + e.getMessage());
        }
    }

    @Override
    public Page<VideoSynthesisListItemVO> getSynthesisTaskList(VideoSynthesisListRequest request, Integer userId,
            Integer tenantId) {
        // 构建查询条件
        LambdaQueryWrapper<CmsTaskInfo> queryWrapper = new LambdaQueryWrapper<>();

        // 根据任务类型过滤，只查询视频合成任务
        queryWrapper.eq(CmsTaskInfo::getTaskType, TaskTypeEnum.VIDEO_SYNTHESIS.getCode());

        // 根据租户ID和用户ID过滤
        queryWrapper.eq(CmsTaskInfo::getTenantId, tenantId);

        // 根据任务状态过滤（如果指定了状态）
        if (request.getTaskStatus() != null) {
            queryWrapper.eq(CmsTaskInfo::getTaskStatus, request.getTaskStatus());
        }

        // 根据关键词过滤任务名称
        if (StrUtil.isNotBlank(request.getKeyword())) {
            queryWrapper.like(CmsTaskInfo::getName, request.getKeyword());
        }

        // 默认按创建时间降序排序
        queryWrapper.orderByDesc(CmsTaskInfo::getCreateTime);

        // 分页查询
        Page<CmsTaskInfo> page = new Page<>(request.getCurrent(), request.getPageSize());
        Page<CmsTaskInfo> taskInfoPage = taskInfoService.page(page, queryWrapper);

        // 转换为VO对象
        Page<VideoSynthesisListItemVO> resultPage = new Page<>();
        resultPage.setCurrent(taskInfoPage.getCurrent());
        resultPage.setSize(taskInfoPage.getSize());
        resultPage.setTotal(taskInfoPage.getTotal());
        resultPage.setPages(taskInfoPage.getPages());

        // 获取所有任务ID
        List<Integer> taskIds = taskInfoPage.getRecords().stream().map(CmsTaskInfo::getId).collect(Collectors.toList());

        // 如果任务ID列表为空，直接返回空的任务详情列表
        List<CmsTaskDetail> taskDetails = new ArrayList<>();
        if (!taskIds.isEmpty()) {
            // 批量查询任务详情
            LambdaQueryWrapper<CmsTaskDetail> detailQueryWrapper = new LambdaQueryWrapper<>();
            detailQueryWrapper.eq(CmsTaskDetail::getType, TaskDetailTypeEnum.INPUT.getCode())
                    .eq(CmsTaskDetail::getDataType, TaskDetailDataTypeEnum.USER_SAVE.getCode())
                    .in(CmsTaskDetail::getTaskId, taskIds);
            taskDetails = taskDetailService.list(detailQueryWrapper);
        }

        // 将任务详情按任务ID分组
        Map<Integer, String> taskIdToInfoMap = taskDetails.stream()
                .collect(Collectors.toMap(CmsTaskDetail::getTaskId, CmsTaskDetail::getData, (v1, v2) -> v1));

        // 构建返回对象
        List<VideoSynthesisListItemVO> records = new ArrayList<>();
        for (CmsTaskInfo taskInfo : taskInfoPage.getRecords()) {
            VideoSynthesisListItemVO vo = new VideoSynthesisListItemVO();
            vo.setTaskId(taskInfo.getId());
            vo.setTaskName(taskInfo.getName());
            vo.setTaskStatus(taskInfo.getTaskStatus());
            vo.setTaskStatusDesc(VideoSynthesisStatusEnum.getByCode(taskInfo.getTaskStatus()).getMsg());
            vo.setResultOssId(taskInfo.getResultFileIds());
            vo.setCreateTime(taskInfo.getCreateTime());
            vo.setUpdateTime(taskInfo.getUpdateTime());
            vo.setErrorMessage(taskInfo.getErrorMessage());

            // 从映射中获取任务信息
            String taskInfoData = taskIdToInfoMap.get(taskInfo.getId());
            vo.setTaskInfo(taskInfoData);

            // 如果有首帧图片，获取签名地址
            if (StrUtil.isNotBlank(taskInfoData)) {
                try {
                    JSONObject taskInfoJson = JSONUtil.parseObj(taskInfoData);
                    if (taskInfoJson.containsKey("firstFrameOssId")) {
                        String firstFrameOssId = taskInfoJson.getStr("firstFrameOssId");
                        if (StrUtil.isNotBlank(firstFrameOssId)) {
                            URL url = ossService.downloadPresignedUrl(objectStorageFlowProperties.getCms().getBucketName(),
                                    firstFrameOssId, FileConstant.FILE_EXPIRE_TIME, 0);
                            vo.setFirstFrameOssUrl(url.toString());
                        }
                    }
                } catch (Exception e) {
                    log.warn("获取首帧图片签名地址失败", e);
                }
            }

            // 如果有合成视频，获取签名地址
            if (StrUtil.isNotBlank(taskInfo.getResultFileIds())) {
                try {
                    URL url = ossService.downloadPresignedUrl(objectStorageFlowProperties.getCms().getBucketName(),
                            taskInfo.getResultFileIds(), FileConstant.FILE_EXPIRE_TIME,
                            0);
                    vo.setResultOssUrl(url.toString());
                } catch (Exception e) {
                    log.warn("获取合成视频签名地址失败", e);
                }
            }

            records.add(vo);
        }

        resultPage.setRecords(records);
        return resultPage;
    }

    @Override
    public void exportSynthesisScript(Integer taskId, HttpServletResponse response) throws IOException {
        // 查询任务信息
        CmsTaskInfo taskInfo = taskInfoService.getById(taskId);
        if (taskInfo == null) {
            throw new BusinessException("任务不存在");
        }

        // 检查任务类型
        if (TaskTypeEnum.VIDEO_SYNTHESIS.getCode() != taskInfo.getTaskType()) {
            throw new BusinessException("非视频合成任务");
        }

        // 查询任务详情
        CmsTaskDetail taskDetail = taskDetailService.getTaskDetail(taskId, TaskDetailTypeEnum.INPUT.getCode(),
                TaskDetailDataTypeEnum.USER_SAVE.getCode());
        if (taskDetail == null || StrUtil.isBlank(taskDetail.getData())) {
            throw new BusinessException("脚本信息不存在");
        }

        // 解析脚本信息
        String taskInfoStr = taskDetail.getData();
        JSONObject taskInfoJson = JSONUtil.parseObj(taskInfoStr);

        // 获取脚本列表
        List<VideoSynthesisScriptExcelVO> scriptList = new ArrayList<>();
        if (taskInfoJson.containsKey("storyBoards")) {
            JSONArray scriptArray = taskInfoJson.getJSONArray("storyBoards");
            for (int i = 0; i < scriptArray.size(); i++) {
                JSONObject scriptJson = scriptArray.getJSONObject(i);
                VideoSynthesisScriptExcelVO excelVO = new VideoSynthesisScriptExcelVO();

                // 设置镜头编号
                excelVO.setSceneNumber(i + 1);

                // 复制属性
                if (scriptJson.containsKey("sceneDescription")) {
                    excelVO.setSceneDescription(scriptJson.getStr("sceneDescription"));
                }
                if (scriptJson.containsKey("brandIntegration")) {
                    excelVO.setBrandIntegration(scriptJson.getStr("brandIntegration"));
                }
                if (scriptJson.containsKey("cameraType")) {
                    excelVO.setCameraType(scriptJson.getStr("cameraType"));
                }
                if (scriptJson.containsKey("cameraView")) {
                    excelVO.setCameraView(scriptJson.getStr("cameraView"));
                }
                if (scriptJson.containsKey("sceneLength")) {
                    excelVO.setDuration(scriptJson.getStr("sceneLength"));
                }
                if (scriptJson.containsKey("actors")) {
                    excelVO.setActors(scriptJson.getStr("actors"));
                }
                if (scriptJson.containsKey("actorActions")) {
                    excelVO.setActorActions(scriptJson.getStr("actorActions"));
                }
                if (scriptJson.containsKey("actorExpressions")) {
                    excelVO.setActorExpressions(scriptJson.getStr("actorExpressions"));
                }
                if (scriptJson.containsKey("dialogue")) {
                    excelVO.setDialogue(scriptJson.getStr("dialogue"));
                }
                if (scriptJson.containsKey("dialogueEmotion")) {
                    excelVO.setDialogueEmotion(scriptJson.getStr("dialogueEmotion"));
                }
                if (scriptJson.containsKey("costumeSuggestions")) {
                    excelVO.setCostumeSuggestions(scriptJson.getStr("costumeSuggestions"));
                }
                if (scriptJson.containsKey("props")) {
                    excelVO.setProps(scriptJson.getStr("props"));
                }
                if (scriptJson.containsKey("setRequirements")) {
                    excelVO.setSetRequirements(scriptJson.getStr("setRequirements"));
                }
                if (scriptJson.containsKey("bgm")) {
                    excelVO.setBgm(scriptJson.getStr("bgm"));
                }
                if (scriptJson.containsKey("lightColor")) {
                    excelVO.setLightColor(scriptJson.getStr("lightColor"));
                }
                if (scriptJson.containsKey("equipment")) {
                    excelVO.setEquipment(scriptJson.getStr("equipment"));
                }
                if (scriptJson.containsKey("contentStrategy")) {
                    excelVO.setContentStrategy(scriptJson.getStr("contentStrategy"));
                }
                if (scriptJson.containsKey("costumes")) {
                    excelVO.setCostumes(scriptJson.getStr("costumes"));
                }

                scriptList.add(excelVO);
            }
        }

        // 生成文件名
        final StringJoiner joiner = new StringJoiner("-", "", ".xlsx");
        joiner.add("视频合成脚本");
        joiner.add(taskInfo.getName());
        joiner.add(LocalDateTime.now().format(DateTimeFormatter.ofPattern(DatePattern.CHINESE_DATE_TIME_PATTERN)));
        final String filename = joiner.toString();

        try {
            // 设置响应头信息
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Pragma", "No-cache");
            response.setHeader("Cache-Control", "No-cache");
            response.setDateHeader("Expires", 0);
            response.setContentType("application/vnd.ms-excel");
            response.setHeader("Content-disposition",
                    "attachment;filename=" + URLEncoder.encode(filename, StandardCharsets.UTF_8));

            // 导出Excel
            ServletOutputStream outputStream = response.getOutputStream();
            EasyExcel.write(outputStream, VideoSynthesisScriptExcelVO.class).inMemory(true).autoCloseStream(true)
                    .sheet("视频合成脚本").doWrite(scriptList);
        } catch (Exception e) {
            log.error("导出视频合成脚本失败，taskId={}", taskId, e);
            throw new BusinessException("导出失败: " + e.getMessage());
        }
    }

    @Override
    public String getSignedUrl(String ossId) {
        if (StrUtil.isBlank(ossId)) {
            return null;
        }

        try {
            URL url = ossService.downloadPresignedUrl(objectStorageFlowProperties.getCms().getBucketName(),
                    ossId, FileConstant.FILE_EXPIRE_TIME, 0);
            return url.toString();
        } catch (Exception e) {
            log.warn("获取签名地址失败，ossId={}", ossId, e);
            return null;
        }
    }

    @Override
    public boolean updateSynthesisTaskDetail(VideoSynthesisDetailUpdateRequest request, Integer userId,
            Integer tenantId) {
        // 检查任务是否存在
        CmsTaskInfo taskInfo = taskInfoService.getById(request.getTaskId());
        if (taskInfo == null) {
            throw new BusinessException("任务不存在");
        }

        // 获取任务详情
        CmsTaskDetail taskDetail = taskDetailService.getTaskDetail(request.getTaskId(),
                TaskDetailTypeEnum.INPUT.getCode(), TaskDetailDataTypeEnum.USER_SAVE.getCode());

        if (taskDetail == null) {
            // 不存在则创建新的任务详情
            taskDetail = new CmsTaskDetail();
            taskDetail.setTaskId(request.getTaskId());
            taskDetail.setType(TaskDetailTypeEnum.INPUT.getCode());
            taskDetail.setDataType(TaskDetailDataTypeEnum.USER_SAVE.getCode());
            taskDetail.setData(request.getTaskInfo());
            return taskDetailService.save(taskDetail);
        } else {
            // 存在则更新数据
            taskDetail.setData(request.getTaskInfo());
            return taskDetailService.updateById(taskDetail);
        }
    }

}
