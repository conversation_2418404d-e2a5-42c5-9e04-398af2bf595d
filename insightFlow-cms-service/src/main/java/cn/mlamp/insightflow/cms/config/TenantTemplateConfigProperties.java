package cn.mlamp.insightflow.cms.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import cn.mlamp.insightflow.cms.auth.cms.bo.TenantTemplateResourceTypeBO;
import cn.mlamp.insightflow.cms.auth.cms.bo.TenantTmplateQuotaBO;

import java.util.Date;
import java.util.List;
import java.util.Set;

@Data
@Configuration
@ConfigurationProperties(prefix = "tenant-template")
public class TenantTemplateConfigProperties {

    private Boolean isForceCreateTenant;
    private Integer expiredDay;
    private Date expiredDate;
    private String remark;
    private List<TenantTemplateResourceTypeBO> resources;
    private Set<Integer> permissions;
    private List<TenantTmplateQuotaBO> quotas;
    private Integer userCountLimit;
    private List<String> tenantTagList;
}
