package cn.mlamp.insightflow.cms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 视频分析结果;
 * <AUTHOR> husper
 * @date : 2025-3-19
 */
@Data
@TableName("cms_video_result")
public class CmsVideoResult extends BaseEntity{
    /** 主键 自增id */
    @TableId(type = IdType.AUTO)
    private Integer id ;
    /** 1：AI解码；2：分镜；3：ASR */
    @TableField("`type`")
    private Integer type ;
    /** 分析任务ID */
    private Integer videoId ;
    /** 排序序号 */
    @TableField("`index`")
    private Integer index ;
    /** json格式 */
    @TableField("`data`")
    private String data ;

    /** 用户id */
    private Integer userId ;
    /** 租户id */
    private Integer tenantId ;

    @Data
    @AllArgsConstructor
    public static class Asr{
        private String asr;
    }




}