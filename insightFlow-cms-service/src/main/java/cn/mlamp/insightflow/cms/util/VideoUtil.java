package cn.mlamp.insightflow.cms.util;


import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.Base64;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

import cn.mlamp.insightflow.cms.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import net.bramp.ffmpeg.FFmpeg;
import net.bramp.ffmpeg.FFmpegExecutor;
import net.bramp.ffmpeg.FFprobe;
import net.bramp.ffmpeg.builder.FFmpegBuilder;
import net.bramp.ffmpeg.probe.FFmpegProbeResult;
import net.bramp.ffmpeg.probe.FFmpegStream;


/**
 * @Author: husuper
 * @CreateTime: 2024-08-23
 */
@Slf4j
public class VideoUtil {

    private static String ffmpegPath;

    private static String ffprobePath;

    private static String videoUrl;


    public static void main(String[] args) {
        VideoUtil.setFfmpegPath("/Users/<USER>/Documents/ffmpeg/ffmpeg");
        VideoUtil.setFfprobePath("/Users/<USER>/Documents/ffmpeg/ffprobe");
//        VideoUtil.cutImage2("/Users/<USER>/Documents/OSS/1111111.mp4","/Users/<USER>/Documents/OSS/1111.jpg",10000);
//        System.out.println(cutImageOfBase64("/Users/<USER>/Documents/OSS/1111111.mp4",10000));
//        System.out.println(getVideoLength("/Users/<USER>/Documents/OSS/1111111.mp4"));
        compressVideo("/Users/<USER>/Documents/OSS/1111111.mp4", "/Users/<USER>/Documents/OSS/ok1122.mp4");
//        compressVideoWithScaling("/Users/<USER>/Documents/OSS/1111111.mp4", "/Users/<USER>/Documents/OSS/22222.mp4");
//        compressVideoWithScalingAndCRF35("/Users/<USER>/Documents/OSS/1111111.mp4", "/Users/<USER>/Documents/OSS/33333.mp4");
//        convertVideoToAudio("/Users/<USER>/Documents/OSS/1111111.mp4", "/Users/<USER>/Documents/OSS/88.wav");


        String videoFilePath = "/Users/<USER>/Documents/OSS/1111111.mp4";
        long startTime = 1000; // 开始时间（毫秒）
        long endTime = 5000; // 结束时间（毫秒）
//
        String outputFilePath = VideoUtil.cutVideoSegment(videoFilePath,"/Users/<USER>/Documents/OSS/yasuohou222.mp4", startTime, endTime);
        System.out.println("截取后的视频文件路径: " + outputFilePath);

    }

    /**
     * 根据开始时间和结束时间截取视频片段
     *
     * @param videoFilePath 本地视频文件路径
     * @param startTime     开始时间（毫秒）
     * @param endTime       结束时间（毫秒）
     * @return 截取后的视频文件本地磁盘路径
     */
    public static String cutVideoSegment(String videoFilePath, String outputFilePath, long startTime, long endTime) {
        return cutVideoSegment(videoFilePath, outputFilePath, "mp4", startTime, endTime);
    }

    /**
     * 根据开始时间和结束时间截取视频片段
     *
     * @param videoFilePath 本地视频文件路径
     * @param startTime     开始时间（毫秒）
     * @param endTime       结束时间（毫秒）
     * @return 截取后的视频文件本地磁盘路径
     */
    public static String cutVideoSegment(String videoFilePath, String outputFilePath, String format, long startTime, long endTime) {
        try {
            FFmpeg ffmpeg = new FFmpeg(VideoUtil.ffmpegPath);
            FFmpegBuilder builder = new FFmpegBuilder()
                    .setInput(videoFilePath) // 输入视频文件
                    .overrideOutputFiles(true) // 覆盖输出文件
                    .addOutput(outputFilePath) // 输出视频文件
                    .setFormat(format) // 设置输出格式为MP4
                    .addExtraArgs("-ss", convertMillisecondsToSeconds(startTime)) // 设置开始时间
                    .addExtraArgs("-t", convertMillisecondsToSeconds(endTime - startTime)) // 设置持续时间
//                    .addExtraArgs("-threads", "4")
                    .done();

            FFmpegExecutor executor = new FFmpegExecutor(ffmpeg);
            executor.createJob(builder).run();

            return outputFilePath;
        } catch (Exception e) {
            log.error("视频截取失败", e);
            throw new BusinessException("视频截取失败");
        }
    }


    /**
     * 需要注意的就只有入参文件只支持.wav格式文件，可以用
     * ffmpeg -y -i {video_path} -vn -acodec pcm_s16le -ar 16000 -ac 1 {audio_path}
     * 命令生成
     *
     * @param videoPath
     * @param outputAudioPath
     */
    public static void convertVideoToAudio(String videoPath, String outputAudioPath) {
        try {
            FFmpeg ffmpeg = new FFmpeg(VideoUtil.ffmpegPath);
            FFmpegBuilder builder = new FFmpegBuilder()
                    .setInput(videoPath)
                    .overrideOutputFiles(true)
                    .addOutput(outputAudioPath)
//                    .setVideoCodec("copy") // 不处理视频流
                    .setFormat("wav") // 设置输出格式为WAV，若为MP3则改为 "mp3"
                    .disableVideo() // 只保留音频
                    .setAudioCodec("pcm_s16le")
                    .setAudioSampleRate(16000)
                    .setAudioChannels(1)
//                    .addExtraArgs("-threads", "4")
                    .done();
            FFmpegExecutor executor = new FFmpegExecutor(ffmpeg);
            executor.createJob(builder).run();
            log.info("完成");
        } catch (Exception e) {
            log.error("转换 WAV 文件失败", e);
            throw new BusinessException("转换 WAV 文件失败");
        }
    }


    //ffmpeg -i {video_path} -c:v libx264 -crf 28 {compress_mp4_path}  视频压缩
    public static void compressVideo(String videoFilePath, String compressMp4Path) {
        try {
            compressVideoWithScalingAndCRF35(videoFilePath, compressMp4Path);
        } catch (Exception e) {
            log.error("视频压缩失败", e.getMessage());
            //如果压缩失败了，采用原视频拷贝到压缩视频目录
            try {
                Path source = Paths.get(videoFilePath);
                Path target = Paths.get(compressMp4Path);

                // 使用 REPLACE_EXISTING 覆盖已有文件
                Files.copy(source, target, StandardCopyOption.REPLACE_EXISTING);
                log.info("视频压缩失败，已复制原文件到压缩视频目录");
            } catch (IOException e1) {
                log.error("复制原视频失败", e1);
                throw new BusinessException("视频压缩失败且无法复制原文件");
            }
        }
    }

    public static void compressVideoWithScaling(String videoFilePath, String compressMp4Path) {
        try {
            FFmpeg ffmpeg = new FFmpeg(VideoUtil.ffmpegPath);
            FFmpegBuilder builder = new FFmpegBuilder()
                    .setInput(videoFilePath)
                    .overrideOutputFiles(true)
                    .addOutput(compressMp4Path)
                    .setVideoCodec("libx264")
                    .setConstantRateFactor(28) // 设置 CRF 为 28
                    .addExtraArgs("-vf", "scale='if(gt(iw,ih),min(1080,iw),-1)':'if(gt(iw,ih),-1,min(1080,ih))'")
                    .setStrict(FFmpegBuilder.Strict.EXPERIMENTAL)
//                    .addExtraArgs("-threads", "4")
                    .done();
            FFmpegExecutor executor = new FFmpegExecutor(ffmpeg);
            executor.createJob(builder).run();
        } catch (Exception e) {
            log.error("视频压缩失败", e);
            throw new BusinessException("视频压缩失败");
        }
    }

    public static void compressVideoWithScalingAndCRF35(String videoFilePath, String compressMp4Path) {
        try {
            FFmpeg ffmpeg = new FFmpeg(VideoUtil.ffmpegPath);
            FFmpegBuilder builder = new FFmpegBuilder()
                    .setInput(videoFilePath)
                    .overrideOutputFiles(true)
                    .addOutput(compressMp4Path)
                    .setVideoCodec("libx264")
                    .setConstantRateFactor(35) // 设置 CRF 为 35
                    .addExtraArgs("-vf", "scale='if(gt(iw,ih),min(1080,iw),-1)':'if(gt(iw,ih),-1,min(1080,ih))'")
                    .setStrict(FFmpegBuilder.Strict.EXPERIMENTAL)
//                    .addExtraArgs("-hwaccel", "auto")
//                    .addExtraArgs("-c:v", "h264_nvenc") // 使用 NVIDIA GPU 加速
//                    .addExtraArgs("-threads", "4")
//                    .addExtraArgs("-preset", "fast")
                    .done();
            FFmpegExecutor executor = new FFmpegExecutor(ffmpeg);
            executor.createJob(builder).run();
        } catch (Exception e) {
            log.error("视频压缩失败", e.getMessage());
            throw new BusinessException("视频压缩失败");
        }
    }



    public static Long getVideoLength(String videoFilePath) {
        try {
            // 创建FFmpeg探测器
            FFprobe ffmpeg = new FFprobe(VideoUtil.ffprobePath);
            // 使用FFmpeg探测器获取视频信息
            FFmpegProbeResult probeResult = ffmpeg.probe(videoFilePath);
            // 获取视频流
            FFmpegStream stream = probeResult.getStreams().get(0);
            // 获取视频时长，单位为秒
            return (long) stream.duration;
        } catch (IOException e) {
            log.error("获取视频时长失败", e);
            throw new BusinessException("获取视频时长失败");
        }
    }

    public static Long getVideoLengthAsMs(String videoFilePath) {
        try {
            // 创建FFmpeg探测器
            FFprobe ffmpeg = new FFprobe(VideoUtil.ffprobePath);
            // 使用FFmpeg探测器获取视频信息
            FFmpegProbeResult probeResult = ffmpeg.probe(videoFilePath);
            // 获取视频流
            FFmpegStream stream = probeResult.getStreams().get(0);
            // 获取视频时长，单位为秒
            return (long) stream.duration * 1000;
        } catch (IOException e) {
            log.error("获取视频时长失败", e);
            throw new BusinessException("获取视频时长失败");
        }
    }

    public static void damCutImage(String videoFilePath, String picFilePath, long time) {
        try {
            // 指定FFmpeg的路径（如果已经配置到环境变量中可以忽略此步骤）
            FFmpeg ffmpeg = new FFmpeg(VideoUtil.ffmpegPath);
            // 创建FFmpegBuilder用于构建转换命令
            FFmpegBuilder builder = new FFmpegBuilder()
                    .setInput(videoFilePath) // 输入视频文件
                    .overrideOutputFiles(true) // 覆盖输出文件
                    .addOutput(picFilePath) // 输出图片文件
                    .setFormat("image2") // 设置输出格式为图片
                    .setFrames(1) // 只截取一帧
                    .addExtraArgs("-ss", convertMillisecondsToSeconds(time)) // 设置从指定时间开始截取
                    .addExtraArgs("-strict", "-1")
                    .done();

            // 创建执行器
            FFmpegExecutor executor = new FFmpegExecutor(ffmpeg);
            // 切图片
            executor.createJob(builder).run();
        } catch (Exception e) {
            log.error("视频切图片失败", e);
            throw new BusinessException("视频切图片失败", e);
        }
    }

    public static void damCutLastImage(String videoFilePath, String picFilePath, long time) {
        try {
            // 指定FFmpeg的路径（如果已经配置到环境变量中可以忽略此步骤）
            FFmpeg ffmpeg = new FFmpeg(VideoUtil.ffmpegPath);
            // 创建FFmpegBuilder用于构建转换命令
            FFmpegBuilder builder = new FFmpegBuilder()
                    .setInput(videoFilePath) // 输入视频文件
                    .overrideOutputFiles(true) // 覆盖输出文件
                    .addOutput(picFilePath) // 输出图片文件
                    .setFormat("image2") // 设置输出格式为图片
                    .setFrames(1) // 只截取一帧
                    .setStartOffset(time - 100, TimeUnit.MILLISECONDS)
                    .addExtraArgs("-strict", "-1")
                    .done();

            // 创建执行器
            FFmpegExecutor executor = new FFmpegExecutor(ffmpeg);
            // 切图片
            executor.createJob(builder).run();
        } catch (Exception e) {
            log.error("视频切图片失败", e);
            throw new BusinessException("视频切图片失败", e);
        }
    }

    public static String damCutImageOfBase64(String videoFilePath, Path tempDir, long time) {
        try {
            return FunctionUtils.withTempFile(tempDir, ".jpg", (tempFile) -> {
                damCutImage(videoFilePath, tempFile.toString(), time);
                return convertImageToBase64(tempFile.toString());
            });
        } catch (Exception e) {
            log.error("视频切图片失败", e);
            throw new BusinessException("视频切图片失败", e);
        }
    }

    public static String damCutLastImageOfBase64(String videoFilePath, Path tempDir, long time) {
        try {
            return FunctionUtils.withTempFile(tempDir, ".jpg", (tempFile) -> {
                damCutLastImage(videoFilePath, tempFile.toString(), time);
                return convertImageToBase64(tempFile.toString());
            });
        } catch (Exception e) {
            log.error("视频切图片失败", e);
            throw new BusinessException("视频切图片失败", e);
        }
    }

    public static void cutImage(String videoFilePath, String picFilePath, long time) {
        try {
            // 指定FFmpeg的路径（如果已经配置到环境变量中可以忽略此步骤）
            FFmpeg ffmpeg = new FFmpeg(VideoUtil.ffmpegPath);
            // 创建FFmpegBuilder用于构建转换命令
            FFmpegBuilder builder = new FFmpegBuilder()
                    .setInput(videoFilePath) // 输入视频文件
                    .overrideOutputFiles(true) // 覆盖输出文件
                    .addOutput(picFilePath) // 输出图片文件
                    .setFormat("image2") // 设置输出格式为图片
                    .setFrames(1) // 只截取一帧
                    .addExtraArgs("-ss", convertMillisecondsToSeconds(time)) // 设置从第10秒开始截取
//                    .addExtraArgs("-threads", "4")
                    .done();

            // 创建执行器
            FFmpegExecutor executor = new FFmpegExecutor(ffmpeg);
            // 切图片
            executor.createJob(builder).run();
        } catch (Exception e) {
            log.error("视频切图片失败", e);
            throw new BusinessException("视频切图片失败");
        }
    }

    public static void cutImage2(String videoFilePath, String picFilePath, long time) {
        try {
            // 指定FFmpeg的路径（如果已经配置到环境变量中可以忽略此步骤）
            FFmpeg ffmpeg = new FFmpeg(VideoUtil.ffmpegPath);
            // 创建FFmpegBuilder用于构建转换命令
            FFmpegBuilder builder = new FFmpegBuilder()
                    .setInput(videoFilePath) // 输入视频文件
                    .overrideOutputFiles(true) // 覆盖输出文件
                    .addOutput(picFilePath) // 输出图片文件
                    .setFormat("image2") // 设置输出格式为图片
                    .setFrames(1) // 只截取一帧
                    .addExtraArgs("-ss", convertMillisecondsToSeconds(time)) // 设置从指定时间开始截取
                    .addExtraArgs("-vf", "scale=480:-1") // 缩放图像宽度为480，高度自动调整以保持比例
//                    .addExtraArgs("-threads", "4")
                    .done();

            // 创建执行器
            FFmpegExecutor executor = new FFmpegExecutor(ffmpeg);
            // 切图片
            executor.createJob(builder).run();
        } catch (Exception e) {
            log.error("视频切图片失败", e);
            throw new BusinessException("视频切图片失败");
        }
    }

    /**
     * 视频切图片并返回base64
     *
     * @param videoFilePath
     * @param time          毫秒
     * @return
     */
    public static String cutImageOfBase64(String videoFilePath, long time) {
        String directoryPath = System.getProperty("user.dir");
        String picFileName = time + ".jpg";
        String picFilePath = directoryPath + "/" + picFileName;
        try {
            cutImage2(videoFilePath, picFilePath, time);
            return convertImageToBase64(picFilePath);
        } catch (Exception e) {
            log.error("视频切图片失败", e);
            throw new BusinessException("视频切图片失败");
        } finally {
            try {
                Files.deleteIfExists(Paths.get(picFilePath));
            } catch (Exception e) {
                log.error("删除图片失败", e);
            }
        }
    }

    public static String convertImageToBase64(String picFilePath) {
        try {
            // 读取图片文件的字节数据
            byte[] imageBytes = Files.readAllBytes(Paths.get(picFilePath));
            // 将字节数据转换为Base64字符串
            String base64String = Base64.getEncoder().encodeToString(imageBytes);
            return base64String;
        } catch (Exception e) {
            log.error("图片转Base64失败", e);
            throw new BusinessException("图片转Base64失败", e);
        }
    }

    private static String convertMillisecondsToSeconds(long milliseconds) {
        // 将毫秒转换为秒
        long seconds = milliseconds / 1000;
        // 计算剩余的毫秒
        long remainingMilliseconds = milliseconds % 1000;
        // 格式化为"秒.毫秒"的字符串
        return String.format("%d.%03d", seconds, remainingMilliseconds);
    }


    public static void setFfmpegPath(String ffmpeg) {
        if (VideoUtil.ffmpegPath != null) {
            throw new BusinessException("ffmpeg已经初始化");
        }
        VideoUtil.ffmpegPath = ffmpeg;
    }

    public static void setFfprobePath(String ffprobe) {
        if (VideoUtil.ffprobePath != null) {
            throw new BusinessException("ffprobe已经初始化");
        }
        VideoUtil.ffprobePath = ffprobe;
    }

    public static void setVideoUrl(String videoUrl) {
        if (VideoUtil.videoUrl != null) {
            throw new BusinessException("videoUrl已经初始化");
        }
        VideoUtil.videoUrl = videoUrl;
    }

    public static String getVideoUrl(Date datePublishedAt, String esId) {
        return new StringBuilder(videoUrl).append(DateUtil.getYYYYMMDD2(datePublishedAt)).append("/").append(esId).append(".mp4").toString();
    }

    public static String getESVideoOSSId(Date datePublishedAt, String esId) {
        return new StringBuilder("video-decode/videos/douyin/").append(DateUtil.getYYYYMMDD2(datePublishedAt)).append("/").append(esId).append(".mp4").toString();
    }

    public static String getVideoUrl(String datePublishedAt, String esId) {
        return new StringBuilder(videoUrl).append(datePublishedAt).append("/").append(esId).append(".mp4").toString();
    }

    // 获取视频画面比例, 支持多种视频格式:MP4/MOV/AVI/FLV
    // 画面比例: 9:16、16:9、1:1
    public static String getVideoAspectRatio(String videoPath) {
        try {
            // 使用FFprobe获取视频信息
            FFprobe ffprobe = new FFprobe(VideoUtil.ffprobePath);
            FFmpegProbeResult probeResult = ffprobe.probe(videoPath);

            // 获取视频流信息
            FFmpegStream videoStream = probeResult.getStreams().stream()
                    .filter(stream -> stream.codec_type == FFmpegStream.CodecType.VIDEO)
                    .findFirst()
                    .orElseThrow(() -> new BusinessException("无法获取视频流信息"));

            // 获取宽高
            int width = videoStream.width;
            int height = videoStream.height;

            // 计算最大公约数以简化比例
            int gcd = gcd(width, height);
            int widthRatio = width / gcd;
            int heightRatio = height / gcd;

            // 返回标准化的比例
            String ratio = widthRatio + ":" + heightRatio;

            // 转换为标准比例表示
            if (widthRatio == 16 && heightRatio == 9) {
                return "16:9";
            } else if (widthRatio == 9 && heightRatio == 16) {
                return "9:16";
            } else if (widthRatio == 1 && heightRatio == 1) {
                return "1:1";
            } else {
                return ratio; // 返回实际比例
            }
        } catch (IOException e) {
            log.error("获取视频画面比例失败", e);
            throw new BusinessException("获取视频画面比例失败");
        }
    }

    // 计算最大公约数
    private static int gcd(int a, int b) {
        return b == 0 ? a : gcd(b, a % b);
    }


    // 获取视频首帧图片
    public static String getVideoFirstFrame(String videoPath, String outputPath) {
        try {
            // 创建FFmpeg实例
            FFmpeg ffmpeg = new FFmpeg(VideoUtil.ffmpegPath);
            // 构建FFmpeg命令，提取视频第一帧
            FFmpegBuilder builder = new FFmpegBuilder()
                    .setInput(videoPath)
                    .overrideOutputFiles(true)
                    .addOutput(outputPath)
                    .setFrames(1)
                    .done();

            // 执行FFmpeg命令
            FFmpegExecutor executor = new FFmpegExecutor(ffmpeg);
            executor.createJob(builder).run();

            // 返回输出文件路径
            return outputPath;
        } catch (IOException e) {
            log.error("获取视频首帧图片失败", e);
            throw new BusinessException("获取视频首帧图片失败");
        }
    }

    // 获取视频首帧图片并转为Base64
    public static String getVideoFirstFrameAsBase64(String videoPath) {
        try {
            // 创建临时文件保存首帧图片
            String tempImagePath = System.getProperty("java.io.tmpdir") + "/frame_" + System.currentTimeMillis() + ".jpg";
            // 获取首帧图片
            getVideoFirstFrame(videoPath, tempImagePath);
            // 读取图片并转换为Base64
            byte[] imageBytes = Files.readAllBytes(Paths.get(tempImagePath));
            String base64Image = Base64.getEncoder().encodeToString(imageBytes);
            // 删除临时文件
            Files.deleteIfExists(Paths.get(tempImagePath));
            return base64Image;
        } catch (IOException e) {
            log.error("获取视频首帧图片并转为Base64失败", e);
            throw new BusinessException("获取视频首帧图片并转为Base64失败");
        }
    }

    // 获取视频文件格式, 支持多种视频格式:MP4/MOV/AVI/FLV
    public static String getVideoFormat(String videoPath) {
        try {
            FFprobe ffprobe = new FFprobe(VideoUtil.ffprobePath);
            FFmpegProbeResult probeResult = ffprobe.probe(videoPath);
            final String formatName = probeResult.getFormat().format_name;

            final List<String> formats = List.of("mp4", "avi", "mov", "flv");
            for (String format : formats) {
                if (formatName.contains(format)) {
                    return format;
                }
            }
            throw new RuntimeException("未知的视频格式: " + formatName);
        } catch (IOException e) {
            log.error("获取视频文件格式失败", e);
            throw new RuntimeException("获取视频文件格式失败", e);
        }
    }
}
