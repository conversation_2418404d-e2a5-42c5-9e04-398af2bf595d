package cn.mlamp.insightflow.cms.mapper.dam;

import cn.mlamp.insightflow.cms.entity.dam.DamTagValue;
import com.github.yulichang.base.MPJBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * DAM标签值表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
@Mapper
public interface DamTagValueMapper extends MPJBaseMapper<DamTagValue> {

    /**
     * 根据素材ID查询标签值
     *
     * @param assetId 素材ID
     * @return 标签值列表
     */
    List<DamTagValue> selectTagValuesByAssetId(@Param("assetId") Integer assetId);

    /**
     * 根据标签ID获取已使用的标签值列表
     *
     * @param tagId 标签ID
     * @return 标签值列表
     */
    List<String> selectValuesByTagId(@Param("tagId") Integer tagId);

    /**
     * 批量保存标签值
     *
     * @param tagValues 标签值列表
     * @return 影响行数
     */
    int batchInsert(@Param("list") List<DamTagValue> tagValues);

}
