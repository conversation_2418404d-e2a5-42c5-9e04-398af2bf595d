package cn.mlamp.insightflow.cms.auth.tcc.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ErrorCodeEnum {
    //---------------------------------------------------------------------||,
    //                         系统错误100开头                                |
    //---------------------------------------------------------------------\/
    SYSTEM_CODE_PRE(100, ""),

    /**
     * 系统错误
     */
    SYSTEM_ERROR(Integer.parseInt(SYSTEM_CODE_PRE.code + "0001"), "系统错误，请联系系统管理员！"),

    /**
     * Redis通用错误
     */
    REDIS_ERROR(Integer.parseInt(SYSTEM_CODE_PRE.code + "0002"), "查询缓存出错！"),

    /**
     * HTTP调用失败
     */
    HTTP_INVOKE_ERROR(Integer.parseInt(SYSTEM_CODE_PRE.code + "0003"), "HTTP调用失败！"),

    /**
     * 参数错误
     */
    PARAM_ERROR(Integer.parseInt(SYSTEM_CODE_PRE.code + "0004"), "客户端参数错误"),

    //---------------------------------------------------------------------||
    //                         登录相关错误码200开头                           |
    //---------------------------------------------------------------------\/
    LOGIN_CODE_PRE(200, ""),

    /**
     * 用户认证失败
     */
    AUTHENTICATION_FAIL(Integer.parseInt(LOGIN_CODE_PRE.code + "0001"), "该账号异常，请重新登录"),

    /**
     * 用户名为空
     */
    USERNAME_EMPTY(Integer.parseInt(LOGIN_CODE_PRE.code + "0002"), "用户名不能为空"),

    /**
     * 用户名或密码错误
     */
    USERNAME_PASSWORD_ERROR(Integer.parseInt(LOGIN_CODE_PRE.code + "0003"), "用户名或者密码错误"),

    /**
     * 租户信息为空
     */
    TENANT_EMPTY(Integer.parseInt(LOGIN_CODE_PRE.code + "0004"), "该账号无可用租户。"),

    /**
     * 该账号异常，请联系您的管理员
     */
    LOGIN_ACCOUNT_ERROR(Integer.parseInt(LOGIN_CODE_PRE.code + "0006"), "该账号异常，请联系您的管理员。"),

    /**
     * 登录失败
     */
    LOGIN_INVALID_ERROR(Integer.parseInt(LOGIN_CODE_PRE.code + "0007"), "登录失败，请重新登录"),

    /**
     * 密码已过期
     */
    PASSWORD_EXPIRE(Integer.parseInt(LOGIN_CODE_PRE.code + "0008"), "密码已过期，请修改密码后重试"),

    /**
     * 无产品权限
     */
    USER_NO_PRODUCT_PERMISSION_ERROR(Integer.parseInt(LOGIN_CODE_PRE.code + "0009"), "您没有登录权限，请联系管理员开通"),

    /**
     * 禁用
     */
    USER_FORBIDDEN_PERMISSION_ERROR(Integer.parseInt(LOGIN_CODE_PRE.code + "0010"), "该账号已被停用，请联系您的管理员。"),

    /**
     * 您的组织刚刚发生了变化，已为您更新。
     */
    NEED_RETRY_REFRESH(Integer.parseInt(LOGIN_CODE_PRE.code + "0011"), "您的组织刚刚发生了变化，已为您更新。"),

    /**
     * 授权失败
     */
    AUTHORIZATION_FAIL(Integer.parseInt(LOGIN_CODE_PRE.code + "0012"), "授权失败"),

    /**
     * passport登录过于频繁，已被限制，稍后再试
     */
    LOGIN_OFTEN_ERROR(Integer.parseInt(LOGIN_CODE_PRE.code + "0013"), "该账号频繁登录，请一小时后再试"),

    /**
     * passport已将账号冻结
     */
    LOGIN_ACCOUNT_FORBIDDEN_ERROR(Integer.parseInt(LOGIN_CODE_PRE.code + "0014"), "该账号已冻结，请联系passport"),

    /**
     * 无效的域名
     */
    DOMAIN_INVALID_ERROR(Integer.parseInt(LOGIN_CODE_PRE.code + "0015"), "无效的域名");


    public static ErrorCodeEnum getByCode(Integer code) {
        for (ErrorCodeEnum bizCode : values()) {
            if (bizCode.getCode().equals(code)) {
                return bizCode;
            }
        }
        return null;
    }

    private final Integer code;
    private final String message;

}
