package cn.mlamp.insightflow.cms.enums;

/**
 * @Author: husuper
 * @CreateTime: 2025-03-30
 */
public enum AnalysisStatusEnum {

    WAITING(0, "待分析"),


    PROCESSING(1, "分析中"),


    ANALYZING(2, "分析成功"),

    ERROR(3, "分析失败");


    private final int code;

    private final String msg;

    AnalysisStatusEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
