package cn.mlamp.insightflow.cms.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class TaskDetailImageGenRequest {

    @Schema(description = "任务id", required = false)
    private Integer taskId;

    @Schema(description = "分镜id", required = false)
    private Integer storyboardId;

    @Schema(description = "图片描述", required = false)
    private String imageDesc;

    @Schema(description = "图片提示词", required = false)
    private String imagePrompt;

    private Integer userId;
    private Integer tenantId;
}
