package cn.mlamp.insightflow.cms.model.vo;

import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import com.alibaba.excel.annotation.ExcelProperty;

/**
 * @Author: husuper
 * @CreateTime: 2025-04-02
 */
@Data
public class VideoInfoSceneSplitExcelVO {
    @ExcelProperty("片段")
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER,
            verticalAlignment = VerticalAlignmentEnum.CENTER)
    private String 片段;

    /*@ExcelProperty("片段视频")
    private String 片段视频;*/

    @ExcelProperty("台词")
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER,
            verticalAlignment = VerticalAlignmentEnum.CENTER)
    private String 台词;

    /*@ExcelProperty("分镜视频")
    private String 分镜视频;*/

    @ExcelProperty("分镜开始时间戳")
    private String start;

    @ExcelProperty("分镜结束时间戳")
    private String end;

    @ExcelProperty("时长")
    private String duration;
    /*@ExcelProperty("镜头参考")
    private String 镜头参考;*/

    @ExcelProperty("镜头描述")
    private String 镜头描述;

    @ExcelProperty("品牌植入")
    private String 品牌植入;

    @ExcelProperty("镜头类型")
    private String 镜头类型;

    @ExcelProperty("运镜方式")
    private String 运镜方式;

    @ExcelProperty("出现演员")
    private String 出现演员;

    @ExcelProperty("演员动作")
    private String 演员动作;

    @ExcelProperty("演员表情")
    private String 演员表情;

    @ExcelProperty("台词情绪")
    private String 台词情绪;

    @ExcelProperty("服装造型")
    private String 服装造型;

    @ExcelProperty("道具清单")
    private String 道具清单;

    @ExcelProperty("布景要求")
    private String 布景要求;

    /*@ExcelProperty("分镜图片")
    private String sceneDecodingSegmentPicOssId;*/
    @ExcelProperty("背景音乐/音效")
    @JSONField(name = "背景音乐/音效")
    private String 背景音乐音效;

    @ExcelProperty("光影与色彩要求")
    private String 光影与色彩要求;

    @ExcelProperty("摄影器材")
    private String 摄影器材;

    @ExcelProperty("视频内容策略")
    private String 视频内容策略;








}
