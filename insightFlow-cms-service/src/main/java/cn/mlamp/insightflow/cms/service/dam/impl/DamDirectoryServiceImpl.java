package cn.mlamp.insightflow.cms.service.dam.impl;

import cn.mlamp.insightflow.cms.common.resp.RespCode;
import cn.mlamp.insightflow.cms.entity.dam.DamAsset;
import cn.mlamp.insightflow.cms.entity.dam.DamDirectory;
import cn.mlamp.insightflow.cms.entity.dam.DamRecycleBin;
import cn.mlamp.insightflow.cms.entity.dam.DamRecycleBinDirectoryAssetMapping;
import cn.mlamp.insightflow.cms.enums.dam.DamDirectoryTypeEnum;
import cn.mlamp.insightflow.cms.enums.dam.DamRecycleBinObjectTypeEnum;
import cn.mlamp.insightflow.cms.exception.BusinessException;
import cn.mlamp.insightflow.cms.mapper.dam.DamDirectoryMapper;
import cn.mlamp.insightflow.cms.model.converter.dam.DamDirectoryConverter;
import cn.mlamp.insightflow.cms.model.dto.dam.DamDirectoryDTO;
import cn.mlamp.insightflow.cms.model.vo.dam.DamDirectoryVO;
import cn.mlamp.insightflow.cms.service.IUserService;
import cn.mlamp.insightflow.cms.service.dam.IDamAssetService;
import cn.mlamp.insightflow.cms.service.dam.IDamDirectoryService;
import cn.mlamp.insightflow.cms.service.dam.IDamRecycleBinDirectoryAssetMappingService;
import cn.mlamp.insightflow.cms.service.dam.IDamRecycleBinService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * DAM素材目录表 服务实现类
 *
 * <AUTHOR>
 */
@Service
public class DamDirectoryServiceImpl extends ServiceImpl<DamDirectoryMapper, DamDirectory>
        implements IDamDirectoryService {

    @Resource
    @Lazy
    private IDamAssetService assetService;

    @Resource
    @Lazy
    private IDamRecycleBinService recycleBinService;

    @Resource
    @Lazy
    private IDamRecycleBinDirectoryAssetMappingService recycleBinDirectoryAssetMappingService;

    @Resource
    private DamDirectoryConverter directoryConverter;

    /**
     * 用于 admin 判断
     * TODO: 需要更好的位置存放
     */
    @Resource
    private IUserService userService;

    @Override
    public DamDirectoryVO createDirectory(DamDirectoryDTO directoryDTO, Integer userId, Integer tenantId) {
        // 只有租户管理员可以新建租户文件夹
        if (DamDirectoryTypeEnum.TENANT.equals(directoryDTO.getType()) && !isTenantAdmin()) {
            throw new BusinessException(RespCode.FORBIDDEN.getCode(),
                    "only tenant administrator could create a tenant folder");
        }

        DamDirectory damDirectory = directoryConverter.toEntity(directoryDTO);
        damDirectory.setUserId(userId);
        damDirectory.setTenantId(tenantId);
        save(damDirectory);
        return directoryConverter.toVO(damDirectory);
    }

    @Override
    public List<DamDirectoryVO> getDirectoryList(DamDirectoryTypeEnum type, Integer userId, Integer tenantId) {
        LambdaQueryWrapper<DamDirectory> queryWrapper = baseQueryWrapper(userId, tenantId);

        if (type != null) {
            queryWrapper.eq(DamDirectory::getType, type);
        }

        return directoryConverter.toVOs(
                this.list(queryWrapper)
        );
    }

    // 获取用户可查看的文件列表
    // 可以查看个人(user_id相同&type=1&tenant_id相同) or 租户文件夹(tenant_id相同&type=2)
    @Override
    public List<DamDirectory> getViewableDirectory(Integer userId, Integer tenantId) {
        return baseMapper.getViewableDirectory(userId, tenantId);
    }

    // 获取个人AI视频上传文件id，如果没有就创建
    @Override
    public Integer getAiVideoUploadDictionaryId(Integer userId, Integer tenantId) {
        final String directoryName = "AI视频上传";
        LambdaQueryWrapper<DamDirectory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DamDirectory::getName, directoryName)
                .eq(DamDirectory::getType, DamDirectoryTypeEnum.PERSONAL)
                .eq(DamDirectory::getUserId, userId)
                .eq(DamDirectory::getTenantId, tenantId)
                .eq(DamDirectory::getIsDeleted, false);
        DamDirectory directory = getOne(queryWrapper);
        if (directory == null) {
            directory = new DamDirectory();
            directory.setName(directoryName);
            directory.setType(DamDirectoryTypeEnum.PERSONAL);
            directory.setUserId(userId);
            directory.setTenantId(tenantId);
            save(directory);
        }
        return directory.getId();
    }

    @Override
    public DamDirectoryVO getDirectoryDetail(Integer directoryId, Integer userId, Integer tenantId) {
        LambdaQueryWrapper<DamDirectory> queryWrapper = baseQueryWrapper(userId, tenantId)
                .eq(DamDirectory::getId, directoryId);
        DamDirectory directory = getOne(queryWrapper);
        if (directory == null) {
            throw new BusinessException(RespCode.NOT_FOUND.getCode(), "directory is not found");
        }
        return directoryConverter.toVO(directory);
    }

    /**
     * 修改文件夹名称 - 不允许变更文件夹类型
     * TODO: 确认
     *
     * @param directoryId  目录ID
     * @param directoryDTO 目录DTO
     * @param userId       用户ID
     * @param tenantId     租户ID
     * @return success or not
     */
    @Override
    public boolean updateDirectory(Integer directoryId,
                                   DamDirectoryDTO directoryDTO,
                                   Integer userId,
                                   Integer tenantId) {
        LambdaQueryWrapper<DamDirectory> queryWrapper = baseQueryWrapper(userId, tenantId)
                .eq(DamDirectory::getId, directoryId);
        DamDirectory directory = getOne(queryWrapper);
        if (directory == null) {
            throw new BusinessException(RespCode.NOT_FOUND.getCode(), "directory is not found");
        }
        if (DamDirectoryTypeEnum.TENANT.equals(directory.getType()) && !isTenantAdmin()) {
            throw new BusinessException(RespCode.FORBIDDEN.getCode(),
                    "only tenant administrator could update a tenant folder");
        }
        if (!directoryDTO.getName().equals(directory.getName())) {
            directory.setName(directoryDTO.getName());
            return updateById(directory);
        }
        return true;
    }

    /**
     * 删除目录
     * 1. 目标标记为 delete
     * 2. 目录下所有视频标记为 delete
     * 3. 回收站中创建新记录
     * 4. 为避免回收站展示混乱，不会再回收站中创建素材记录(TODO: 可以讨论)
     *
     * @param directoryId 目录ID
     * @param userId      用户ID
     * @param tenantId    租户ID
     * @return 是否完成删除
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteDirectory(Integer directoryId, Integer userId, Integer tenantId) {
        LambdaQueryWrapper<DamDirectory> queryWrapper = baseQueryWrapper(userId, tenantId)
                .eq(DamDirectory::getId, directoryId);
        DamDirectory directory = getOne(queryWrapper);
        if (directory == null) {
            throw new BusinessException(RespCode.NOT_FOUND.getCode(), "directory is not found");
        }
        // 只有租户管理员可以删除租户文件夹
        if (DamDirectoryTypeEnum.TENANT.equals(directory.getType()) && !isTenantAdmin()) {
            throw new BusinessException(RespCode.FORBIDDEN.getCode(),
                    "only tenant administrator could delete a tenant folder");
        }

        // 素材逻辑删除
        List<DamAsset> assets = assetService.list(
                new LambdaQueryWrapper<DamAsset>()
                        .eq(DamAsset::getDirectoryId, directoryId)
                        .eq(DamAsset::getIsDeleted, false)
        );
        assets.forEach(asset -> asset.setIsDeleted(true));
        assetService.updateBatchById(assets);

        // 文件夹逻辑删除
        directory.setIsDeleted(true);
        updateById(directory);

        // 添加到回收站，仅文件夹对象
        DamRecycleBin recycleBin = new DamRecycleBin();
        recycleBin.setUserId(userId);
        recycleBin.setTenantId(tenantId);
        recycleBin.setObjectId(directory.getId());
        recycleBin.setDirectoryType(directory.getType());
        recycleBin.setObjectType(DamRecycleBinObjectTypeEnum.DIRECTORY);
        recycleBinService.save(recycleBin);

        List<DamRecycleBinDirectoryAssetMapping> mappings = assets.stream().map(asset -> {
            DamRecycleBinDirectoryAssetMapping mapping = new DamRecycleBinDirectoryAssetMapping();
            mapping.setAssetId(asset.getId());
            mapping.setRecycleBinId(recycleBin.getId());
            return mapping;
        }).toList();
        recycleBinDirectoryAssetMappingService.saveBatch(mappings);
        return true;
    }

    /**
     * 根据 id 查询目录对象
     * 权限管理: 可以查看个人 & 租户文件夹
     *
     * @param userId   用户ID
     * @param tenantId 租户ID
     * @return LambdaQueryWrapper
     */
    private LambdaQueryWrapper<DamDirectory> baseQueryWrapper(Integer userId, Integer tenantId) {
        return new LambdaQueryWrapper<DamDirectory>()
                .eq(DamDirectory::getIsDeleted, false)
                .eq(DamDirectory::getTenantId, tenantId)
                .and(wrapper -> wrapper
                        .and(w -> w
                                .eq(DamDirectory::getType, DamDirectoryTypeEnum.PERSONAL)
                                .eq(DamDirectory::getUserId, userId))
                        .or(w -> w
                                .eq(DamDirectory::getType, DamDirectoryTypeEnum.TENANT)
                        )
                );
    }

    /**
     * 用于调用 ttc 检查是否租户管理员
     * TODO: 作为工具提供
     */
    boolean isTenantAdmin() {
        try {
            return userService.isTenantAdmin();
        } catch (org.apache.shiro.authz.AuthorizationException e) {
            return false;
        }
    }
}
