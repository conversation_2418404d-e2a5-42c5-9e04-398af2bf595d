package cn.mlamp.insightflow.cms.service;

import cn.mlamp.insightflow.cms.entity.CmsSubjectImages;
import cn.mlamp.insightflow.cms.model.query.SubjectCreateUploadImageRequest;
import cn.mlamp.insightflow.cms.model.vo.SubjectImageAnalyseVO;
import com.baomidou.mybatisplus.extension.service.IService;

public interface ISubjectImagesService extends IService<CmsSubjectImages> {
    SubjectImageAnalyseVO analyseImage(SubjectCreateUploadImageRequest subjectCreateUploadImageRequest);
}
