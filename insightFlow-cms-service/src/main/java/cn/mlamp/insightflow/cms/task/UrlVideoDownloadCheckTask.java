package cn.mlamp.insightflow.cms.task;

import cn.mlamp.insightflow.cms.config.TaskConfig;
import cn.mlamp.insightflow.cms.entity.CmsPullTaskDedupedData;
import cn.mlamp.insightflow.cms.enums.AnalysisVideoTypeEnum;
import cn.mlamp.insightflow.cms.enums.DocumentStatusEnum;
import cn.mlamp.insightflow.cms.enums.DownloadStatusEnum;
import cn.mlamp.insightflow.cms.enums.VideoInfoStatusEnum;
import cn.mlamp.insightflow.cms.model.query.AnalysisVideoCreateRequest;
import cn.mlamp.insightflow.cms.service.CmsPullTaskDedupedDataService;
import cn.mlamp.insightflow.cms.service.ICmsDocumentInfoService;
import cn.mlamp.insightflow.cms.service.IVideoInfoService;
import cn.mlamp.insightflow.cms.strategy.handle.VideoDownloadHandle;
import cn.mlamp.insightflow.cms.strategy.video.create.AnalysisVideoStrategyMap;
import cn.mlamp.insightflow.cms.util.VideoUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
@Slf4j
@AllArgsConstructor
public class UrlVideoDownloadCheckTask {

    private final StringRedisTemplate stringRedisTemplate;

    private final VideoDownloadHandle videoDownloadHandle;

    private final ICmsDocumentInfoService documentInfoService;

    private final CmsPullTaskDedupedDataService pullTaskDedupedDataService;

    private final IVideoInfoService videoInfoService;

    private final TaskConfig taskConfig;

    // 查询用户url上传视频的下载结果情况，如果成功就发起解析请求
    @Scheduled(cron = "0 0/1 * * * ?")
    public void urlVideoDownloadCheckJob() {
        if (taskConfig.isLocal()) {
            return;
        }
        // 分布式锁(防止同一时间多台服务器重复触发)，保证每次定时任务只有一台服务器在执行
        boolean result = Boolean.TRUE.equals(stringRedisTemplate.opsForValue().setIfAbsent(
                "videoDownloadCheckJob-user", "ImSyncJob-timer", 50, TimeUnit.SECONDS));
        if (result) {
            urlVideoDownloadCheck();
            stringRedisTemplate.delete("videoDownloadCheckJob-user");
        } else {
            log.warn("url下载检查job，多台服务器，防止同一个任务重复执行");
        }
    }

    // 查询sourceType=3（链接），downloadStatus=1（正在下载中的）的视频，检查是否下载成功
    // 1.下载成功就发起解析请求 2.下载失败就变更状态
    public void urlVideoDownloadCheck() {
        var downloadingUrlVideos = getDownloadingUrlVideos();
        Map<String, List<String>> downloadDataEsIdsMap = downloadingUrlVideos.stream()
                .collect(Collectors.groupingBy(CmsPullTaskDedupedData::getDownloadDate,
                        Collectors.mapping(CmsPullTaskDedupedData::getEsId, Collectors.toList())));
        Map<String, Integer> esIdTaskIdMap = downloadingUrlVideos.stream()
                .collect(Collectors.toMap(CmsPullTaskDedupedData::getEsId, CmsPullTaskDedupedData::getTaskId));

        // 分别分析成功和失败的视频，如果包含链接的视频，分别处理
        for (Map.Entry<String, List<String>> entry : downloadDataEsIdsMap.entrySet()) {
            String date = entry.getKey();
            List<String> esIds = entry.getValue(); // 待查询的esId
            try { // 下载成功的发送解析请求
                VideoDownloadHandle.ResultResponse successResponse = videoDownloadHandle.getDoneList(date);
                for (VideoDownloadHandle.ResultData resultData : successResponse.getData()) {
                    String esId = resultData.getResource_id();
                    if (esIds.contains(esId)) { // 下载成功
                        uploadVideoDownloadStatusSuccess(esId);
                        try {
                            urlVideoAnalyzeRequest(esId, date, esIdTaskIdMap.get(esId)); // 发送解析请求
                        } catch (Exception e) {
                            log.error("发送视频解析失败，esId：{}", esId);
                            // 发送失败改变状态下载中， 等待第二轮发送
                            pullTaskDedupedDataService.updateDownloadStatusByEsId(esId, DownloadStatusEnum.PENDING);
                        }
                    }
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                continue;
            }
            try { // 下载失败的改变状态失败
                VideoDownloadHandle.ResultResponse failResponse = videoDownloadHandle.getFailList(date);
                for (VideoDownloadHandle.ResultData resultData : failResponse.getData()) {
                    String esId = resultData.getResource_id();
                    if (esIds.contains(esId)) { // 下载失败
                        log.error("url链接下载失败，esId：{}", esId);
                        uploadVideoDownloadStatusFail(esId);
                        videoInfoService.updateStatusByDocId(esIdTaskIdMap.get(esId),
                                VideoInfoStatusEnum.ERROR, "url链接下载失败");
                    }
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }

    }

    // 下载成功发送解析请求
    public void urlVideoAnalyzeRequest(String esId, String uploadDateStr, Integer taskId) {
        log.info("开始解析视频信息：{}", esId);
        try {
            AnalysisVideoCreateRequest analysisVideoCreateRequest = new AnalysisVideoCreateRequest();
            analysisVideoCreateRequest.setEsId(esId);
            analysisVideoCreateRequest.setTypeName(AnalysisVideoTypeEnum.UPLOAD_VIDEO.getVideoType());
            analysisVideoCreateRequest.setVideoDownloadUrl(VideoUtil.getVideoUrl(uploadDateStr.replace("-", ""), esId));
            analysisVideoCreateRequest.setVideoInfoId(taskId);
            AnalysisVideoStrategyMap.process(analysisVideoCreateRequest);
        } catch (Exception e) {
            log.error("发送视频解析失败，esId：{}", esId);
            throw e;
        }
    }

    public List<CmsPullTaskDedupedData> getDownloadingUrlVideos() {
        return pullTaskDedupedDataService.list(new LambdaQueryWrapper<CmsPullTaskDedupedData>()
                .eq(CmsPullTaskDedupedData::getSourceType, 3) // url
                .eq(CmsPullTaskDedupedData::getDownloadStatus, DownloadStatusEnum.DOWNLOADING.getCode())); // downloading
    }

    public void uploadVideoDownloadStatusSuccess(String esId) {
        documentInfoService.updateStatusByEsId(esId, DocumentStatusEnum.ANALYZING);
        pullTaskDedupedDataService.updateDownloadStatusByEsId(esId, DownloadStatusEnum.SUCCESS);
    }

    public void uploadVideoDownloadStatusFail(String esId) {
        documentInfoService.updateStatusByEsId(esId, DocumentStatusEnum.FAIL);
        pullTaskDedupedDataService.updateDownloadStatusByEsId(esId, DownloadStatusEnum.FAILURE);
    }
}
