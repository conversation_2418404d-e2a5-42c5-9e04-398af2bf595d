package cn.mlamp.insightflow.cms.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.mlamp.insightflow.cms.entity.CmsTaskDetail;
import cn.mlamp.insightflow.cms.entity.CmsTaskInfo;
import cn.mlamp.insightflow.cms.entity.CmsTaskShareReport;
import cn.mlamp.insightflow.cms.entity.CmsUser;
import cn.mlamp.insightflow.cms.enums.TaskDetailDataTypeEnum;
import cn.mlamp.insightflow.cms.enums.TaskDetailTypeEnum;
import cn.mlamp.insightflow.cms.enums.TaskShareTypeEnum;
import cn.mlamp.insightflow.cms.enums.VideoSynthesisStatusEnum;
import cn.mlamp.insightflow.cms.exception.BusinessException;
import cn.mlamp.insightflow.cms.mapper.CmsTaskShareReportMapper;
import cn.mlamp.insightflow.cms.model.query.TaskShareRequest;
import cn.mlamp.insightflow.cms.model.vo.TaskShareDetailVO;
import cn.mlamp.insightflow.cms.model.vo.TaskShareVO;
import cn.mlamp.insightflow.cms.service.ICmsTaskDetailService;
import cn.mlamp.insightflow.cms.service.ICmsTaskInfoService;
import cn.mlamp.insightflow.cms.service.ITaskShareReportService;
import cn.mlamp.insightflow.cms.service.IUserService;
import cn.mlamp.insightflow.cms.service.IVideoSynthesisService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 任务分享报告服务实现
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
@Slf4j
@Service
public class TaskShareReportServiceImpl extends ServiceImpl<CmsTaskShareReportMapper, CmsTaskShareReport>
        implements ITaskShareReportService {

    @Autowired
    private ICmsTaskInfoService taskInfoService;

    @Autowired
    private ICmsTaskDetailService taskDetailService;

    @Autowired
    private IVideoSynthesisService videoSynthesisService;

    @Autowired
    private IUserService userService;

    @Value("${share.base-url:http://localhost:8080}")
    private String shareBaseUrl;

    @Override
    public TaskShareVO createTaskShare(TaskShareRequest request, Integer userId, Integer tenantId) {
        // 检查任务是否存在
        CmsTaskInfo taskInfo = taskInfoService.getById(request.getTaskId());
        if (taskInfo == null) {
            throw new BusinessException("任务不存在");
        }

        // 检查任务类型
        if (request.getTaskType().equals(TaskShareTypeEnum.VIDEO_SYNTHESIS.getCode())) {
            // 检查视频合成任务是否已完成
            if (!VideoSynthesisStatusEnum.COMPLETED.getCode().equals(taskInfo.getTaskStatus())) {
                throw new BusinessException("视频合成任务未完成，无法分享");
            }
        } else {
            throw new BusinessException("不支持的任务类型");
        }

        // 生成授权码
        String authorizeCode = generateAuthorizeCode();

        // 计算过期时间
        Date expireTime = DateUtil.offsetMinute(new Date(), request.getAuthorizeTime());

        // 获取用户信息
        CmsUser user = userService
                .getOne(new LambdaQueryWrapper<CmsUser>().eq(CmsUser::getUserId, userId).last("LIMIT 1"));

        // 获取任务详情
        CmsTaskDetail taskDetail = taskDetailService.getUserTaskDetail(request.getTaskId());

        // 构建分享内容
        Map<String, Object> contentMap = new HashMap<>();
        contentMap.put("taskName", taskInfo.getName());
        contentMap.put("sharerName", user != null ? user.getUserName() : "未知用户");
        contentMap.put("taskType", request.getTaskType());
        contentMap.put("taskTypeName", TaskShareTypeEnum.getByCode(request.getTaskType()).getMsg());
        contentMap.put("createTime", DateUtil.formatDateTime(new Date()));
        contentMap.put("expireTime", DateUtil.formatDateTime(expireTime));

        // 创建分享记录
        CmsTaskShareReport shareReport = new CmsTaskShareReport();
        shareReport.setTaskId(request.getTaskId());
        shareReport.setType(request.getTaskType());
        shareReport.setAuthorizeCode(authorizeCode);
        shareReport.setAuthorizeTime(request.getAuthorizeTime());
        shareReport.setContent(JSONUtil.toJsonStr(contentMap));
        shareReport.setData(taskDetail != null ? taskDetail.getData() : null);
        shareReport.setUserId(userId);
        shareReport.setTenantId(tenantId);
        shareReport.setExpireTime(expireTime);

        // 保存分享记录
        this.save(shareReport);

        // 构建分享链接
        String shareUrl = shareBaseUrl + "/share-page/" + authorizeCode;

        // 返回分享信息
        TaskShareVO vo = new TaskShareVO();
        vo.setId(shareReport.getId());
        vo.setTaskId(shareReport.getTaskId());
        vo.setType(shareReport.getType());
        vo.setAuthorizeCode(shareReport.getAuthorizeCode());
        vo.setExpireTime(shareReport.getExpireTime());
        vo.setShareUrl(shareUrl);

        return vo;
    }

    @Override
    public TaskShareDetailVO getShareDetailByCode(String authorizeCode) {
        // 查询分享记录
        CmsTaskShareReport shareReport = this.getOne(
                new LambdaQueryWrapper<CmsTaskShareReport>().eq(CmsTaskShareReport::getAuthorizeCode, authorizeCode)
                        .eq(CmsTaskShareReport::getIsDeleted, 0).last("LIMIT 1"));

        if (shareReport == null) {
            throw new BusinessException("分享链接不存在");
        }

        // 检查是否过期
        if (shareReport.getExpireTime().before(new Date())) {
            throw new BusinessException("分享链接已过期");
        }

        // 获取任务信息
        CmsTaskInfo taskInfo = taskInfoService.getById(shareReport.getTaskId());
        if (taskInfo == null) {
            throw new BusinessException("任务不存在");
        }

        // 解析分享内容
        JSONObject contentJson = JSONUtil.parseObj(shareReport.getContent());

        // 构建分享详情
        TaskShareDetailVO vo = new TaskShareDetailVO();
        vo.setId(shareReport.getId());
        vo.setTaskId(shareReport.getTaskId());
        vo.setType(shareReport.getType());
        vo.setTaskName(contentJson.getStr("taskName"));
        vo.setSharerName(contentJson.getStr("sharerName"));
        vo.setCreateTime(shareReport.getCreateTime());
        vo.setExpireTime(shareReport.getExpireTime());
        vo.setTaskInfo(shareReport.getData());

        // 根据任务类型获取不同的信息
        if (shareReport.getType().equals(TaskShareTypeEnum.VIDEO_SYNTHESIS.getCode())) {
            // 获取视频合成结果下载链接
            String videoDownloadUrl = videoSynthesisService.getDownloadUrl(shareReport.getTaskId());
            vo.setResultOssUrl(videoDownloadUrl);

            // 获取分段视频下载链接列表
            List<String> segmentVideoUrls = videoSynthesisService.getSegmentVideoDownloadUrls(shareReport.getTaskId());
            vo.setSegmentVideoUrls(segmentVideoUrls);

            // 处理taskInfo中的storyBoards和recommendVideos
            if (StrUtil.isNotBlank(shareReport.getData())) {
                try {
                    JSONObject taskInfoJson = JSONUtil.parseObj(shareReport.getData());

                    // 如果有首帧图片，获取签名地址
                    if (taskInfoJson.containsKey("firstFrameOssId")) {
                        String firstFrameOssId = taskInfoJson.getStr("firstFrameOssId");
                        if (StrUtil.isNotBlank(firstFrameOssId)) {
                            String firstFrameUrl = videoSynthesisService.getSignedUrl(firstFrameOssId);
                            vo.setFirstFrameOssUrl(firstFrameUrl);
                        }
                    }

                    // 处理storyBoards中的recommendVideos
                    if (taskInfoJson.containsKey("storyBoards")) {
                        JSONArray storyBoards = taskInfoJson.getJSONArray("storyBoards");
                        for (int i = 0; i < storyBoards.size(); i++) {
                            JSONObject storyBoard = storyBoards.getJSONObject(i);

                            // Sign URL for storyBoard.objOssId and set it to storyBoard.url
                            if (storyBoard.containsKey("objOssId")) {
                                String storyBoardObjOssId = storyBoard.getStr("objOssId");
                                if (StrUtil.isNotBlank(storyBoardObjOssId)) {
                                    String signedStoryBoardUrl = videoSynthesisService.getSignedUrl(storyBoardObjOssId);
                                    storyBoard.set("url", signedStoryBoardUrl);
                                }
                            }

                            if (storyBoard.containsKey("recommendVideos")) {
                                JSONArray recommendVideos = storyBoard.getJSONArray("recommendVideos");
                                for (int j = 0; j < recommendVideos.size(); j++) {
                                    JSONObject video = recommendVideos.getJSONObject(j);

                                    // 处理视频缩略图URL签名
                                    if (video.containsKey("objOssId")) {
                                        String objOssId = video.getStr("objOssId");
                                        if (StrUtil.isNotBlank(objOssId)) {
                                            String signedUrl = videoSynthesisService.getSignedUrl(objOssId);
                                            video.set("videoUrl", signedUrl);
                                        }
                                    }

                                    // 处理视频文件URL签名
                                    if (video.containsKey("headObjOssId")) {
                                        String headObjOssId = video.getStr("headObjOssId");
                                        if (StrUtil.isNotBlank(headObjOssId)) {
                                            String signedUrl = videoSynthesisService.getSignedUrl(headObjOssId);
                                            video.set("headUrl", signedUrl);
                                        }
                                    }
                                }
                            }
                        }

                        // 更新处理后的taskInfoJson
                        vo.setTaskInfo(taskInfoJson.toString());
                    }
                } catch (Exception e) {
                    log.warn("处理storyBoards中recommendVideos的签名URL失败", e);
                }
            }
        }

        return vo;
    }

    /**
     * 生成授权码
     *
     * @return 授权码
     */
    private String generateAuthorizeCode() {
        return RandomUtil.randomString(16);
    }
}
