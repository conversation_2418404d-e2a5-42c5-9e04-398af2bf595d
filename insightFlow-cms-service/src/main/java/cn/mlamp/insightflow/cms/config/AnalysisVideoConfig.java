package cn.mlamp.insightflow.cms.config;

import cn.mlamp.insightflow.cms.util.DeepanaSignUtil;
import cn.mlamp.insightflow.cms.util.VideoUtil;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @Author: husuper
 * @CreateTime: 2025-03-27
 */
@Component
public class AnalysisVideoConfig {

    @Value("${analysis.video.ffmpeg}")
    private  String ffmpegPath;

    @Value("${analysis.video.ffprobe}")
    private  String ffprobePath;

    @Value("${analysis.video.minio.url:https://ai-pc-cms2.oss-cn-beijing.aliyuncs.com/video-decode/videos/douyin/}")
    private String videoUrl;

    @Value("${analysis.video.minio.pic.path:video-decode/videos/douyin/pic}")
    private String videoPicPath;

    @Value("${analysis.video.url:http://10.10.100.228:8222}")
    private String analyzeUrl;

    @Value("${analysis.video.minio.domain:https://ai-pc-cms2.oss-cn-beijing.aliyuncs.com}")
    private String domain;


    @Value("${analysis.video.asr.url:http://asr-gpu.mlamp.cn/api/asr}")
    private String asrUrl;

//    @Value("${vidu.api-key}")
    private String viduApiKey="vda_822251437584031744_MquetfOWLqDgGPZ2egIB7PpmD72J15Yd"; // Vidu API密钥

    @PostConstruct
    public void init(){
        VideoUtil.setFfprobePath(ffprobePath);
        VideoUtil.setFfmpegPath(ffmpegPath);
        VideoUtil.setVideoUrl(videoUrl);
    }

    public String getVideoUrl() {
        return videoUrl;
    }

    public String getAnalyzeUrl() {
        return analyzeUrl;
    }

    public String getVideoPicPath() {
        return videoPicPath;
    }

    public String getDomain() {
        return domain;
    }

    public String getViduApiKey() {return viduApiKey;}

    public void setVideoPicPath(String videoPicPath) {
        this.videoPicPath = videoPicPath;
    }

    public void setAnalyzeUrl(String analyzeUrl) {
        this.analyzeUrl = analyzeUrl;
    }

    public void setAsrUrl(String asrUrl) {
        this.asrUrl = asrUrl;
    }

    public void setViduApiKey(String viduApiKey) {this.viduApiKey = viduApiKey;}


}
