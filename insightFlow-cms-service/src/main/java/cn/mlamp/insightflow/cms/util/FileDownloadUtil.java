/*
 *
 *                                  Apache License
 *                            Version 2.0, January 2004
 *                         https://www.apache.org/licenses/
 *
 *    TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION
 *
 *    1. Definitions.
 *
 *       "License" shall mean the terms and conditions for use, reproduction,
 *       and distribution as defined by Sections 1 through 9 of this document.
 *
 *       "Licensor" shall mean the copyright owner or entity authorized by
 *       the copyright owner that is granting the License.
 *
 *       "Legal Entity" shall mean the union of the acting entity and all
 *       other entities that control, are controlled by, or are under common
 *       control with that entity. For the purposes of this definition,
 *       "control" means (i) the power, direct or indirect, to cause the
 *       direction or management of such entity, whether by contract or
 *       otherwise, or (ii) ownership of fifty percent (50%) or more of the
 *       outstanding shares, or (iii) beneficial ownership of such entity.
 *
 *       "You" (or "Your") shall mean an individual or Legal Entity
 *       exercising permissions granted by this License.
 *
 *       "Source" form shall mean the preferred form for making modifications,
 *       including but not limited to software source code, documentation
 *       source, and configuration files.
 *
 *       "Object" form shall mean any form resulting from mechanical
 *       transformation or translation of a Source form, including but
 *       not limited to compiled object code, generated documentation,
 *       and conversions to other media types.
 *
 *       "Work" shall mean the work of authorship, whether in Source or
 *       Object form, made available under the License, as indicated by a
 *       copyright notice that is included in or attached to the work
 *       (an example is provided in the Appendix below).
 *
 *       "Derivative Works" shall mean any work, whether in Source or Object
 *       form, that is based on (or derived from) the Work and for which the
 *       editorial revisions, annotations, elaborations, or other modifications
 *       represent, as a whole, an original work of authorship. For the purposes
 *       of this License, Derivative Works shall not include works that remain
 *       separable from, or merely link (or bind by name) to the interfaces of,
 *       the Work and Derivative Works thereof.
 *
 *       "Contribution" shall mean any work of authorship, including
 *       the original version of the Work and any modifications or additions
 *       to that Work or Derivative Works thereof, that is intentionally
 *       submitted to Licensor for inclusion in the Work by the copyright owner
 *       or by an individual or Legal Entity authorized to submit on behalf of
 *       the copyright owner. For the purposes of this definition, "submitted"
 *       means any form of electronic, verbal, or written communication sent
 *       to the Licensor or its representatives, including but not limited to
 *       communication on electronic mailing lists, source code control systems,
 *       and issue tracking systems that are managed by, or on behalf of, the
 *       Licensor for the purpose of discussing and improving the Work, but
 *       excluding communication that is conspicuously marked or otherwise
 *       designated in writing by the copyright owner as "Not a Contribution."
 *
 *       "Contributor" shall mean Licensor and any individual or Legal Entity
 *       on behalf of whom a Contribution has been received by Licensor and
 *       subsequently incorporated within the Work.
 *
 *    2. Grant of Copyright License. Subject to the terms and conditions of
 *       this License, each Contributor hereby grants to You a perpetual,
 *       worldwide, non-exclusive, no-charge, royalty-free, irrevocable
 *       copyright license to reproduce, prepare Derivative Works of,
 *       publicly display, publicly perform, sublicense, and distribute the
 *       Work and such Derivative Works in Source or Object form.
 *
 *    3. Grant of Patent License. Subject to the terms and conditions of
 *       this License, each Contributor hereby grants to You a perpetual,
 *       worldwide, non-exclusive, no-charge, royalty-free, irrevocable
 *       (except as stated in this section) patent license to make, have made,
 *       use, offer to sell, sell, import, and otherwise transfer the Work,
 *       where such license applies only to those patent claims licensable
 *       by such Contributor that are necessarily infringed by their
 *       Contribution(s) alone or by combination of their Contribution(s)
 *       with the Work to which such Contribution(s) was submitted. If You
 *       institute patent litigation against any entity (including a
 *       cross-claim or counterclaim in a lawsuit) alleging that the Work
 *       or a Contribution incorporated within the Work constitutes direct
 *       or contributory patent infringement, then any patent licenses
 *       granted to You under this License for that Work shall terminate
 *       as of the date such litigation is filed.
 *
 *    4. Redistribution. You may reproduce and distribute copies of the
 *       Work or Derivative Works thereof in any medium, with or without
 *       modifications, and in Source or Object form, provided that You
 *       meet the following conditions:
 *
 *       (a) You must give any other recipients of the Work or
 *           Derivative Works a copy of this License; and
 *
 *       (b) You must cause any modified files to carry prominent notices
 *           stating that You changed the files; and
 *
 *       (c) You must retain, in the Source form of any Derivative Works
 *           that You distribute, all copyright, patent, trademark, and
 *           attribution notices from the Source form of the Work,
 *           excluding those notices that do not pertain to any part of
 *           the Derivative Works; and
 *
 *       (d) If the Work includes a "NOTICE" text file as part of its
 *           distribution, then any Derivative Works that You distribute must
 *           include a readable copy of the attribution notices contained
 *           within such NOTICE file, excluding those notices that do not
 *           pertain to any part of the Derivative Works, in at least one
 *           of the following places: within a NOTICE text file distributed
 *           as part of the Derivative Works; within the Source form or
 *           documentation, if provided along with the Derivative Works; or,
 *           within a display generated by the Derivative Works, if and
 *           wherever such third-party notices normally appear. The contents
 *           of the NOTICE file are for informational purposes only and
 *           do not modify the License. You may add Your own attribution
 *           notices within Derivative Works that You distribute, alongside
 *           or as an addendum to the NOTICE text from the Work, provided
 *           that such additional attribution notices cannot be construed
 *           as modifying the License.
 *
 *       You may add Your own copyright statement to Your modifications and
 *       may provide additional or different license terms and conditions
 *       for use, reproduction, or distribution of Your modifications, or
 *       for any such Derivative Works as a whole, provided Your use,
 *       reproduction, and distribution of the Work otherwise complies with
 *       the conditions stated in this License.
 *
 *    5. Submission of Contributions. Unless You explicitly state otherwise,
 *       any Contribution intentionally submitted for inclusion in the Work
 *       by You to the Licensor shall be under the terms and conditions of
 *       this License, without any additional terms or conditions.
 *       Notwithstanding the above, nothing herein shall supersede or modify
 *       the terms of any separate license agreement you may have executed
 *       with Licensor regarding such Contributions.
 *
 *    6. Trademarks. This License does not grant permission to use the trade
 *       names, trademarks, service marks, or product names of the Licensor,
 *       except as required for reasonable and customary use in describing the
 *       origin of the Work and reproducing the content of the NOTICE file.
 *
 *    7. Disclaimer of Warranty. Unless required by applicable law or
 *       agreed to in writing, Licensor provides the Work (and each
 *       Contributor provides its Contributions) on an "AS IS" BASIS,
 *       WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
 *       implied, including, without limitation, any warranties or conditions
 *       of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A
 *       PARTICULAR PURPOSE. You are solely responsible for determining the
 *       appropriateness of using or redistributing the Work and assume any
 *       risks associated with Your exercise of permissions under this License.
 *
 *    8. Limitation of Liability. In no event and under no legal theory,
 *       whether in tort (including negligence), contract, or otherwise,
 *       unless required by applicable law (such as deliberate and grossly
 *       negligent acts) or agreed to in writing, shall any Contributor be
 *       liable to You for damages, including any direct, indirect, special,
 *       incidental, or consequential damages of any character arising as a
 *       result of this License or out of the use or inability to use the
 *       Work (including but not limited to damages for loss of goodwill,
 *       work stoppage, computer failure or malfunction, or any and all
 *       other commercial damages or losses), even if such Contributor
 *       has been advised of the possibility of such damages.
 *
 *    9. Accepting Warranty or Additional Liability. While redistributing
 *       the Work or Derivative Works thereof, You may choose to offer,
 *       and charge a fee for, acceptance of support, warranty, indemnity,
 *       or other liability obligations and/or rights consistent with this
 *       License. However, in accepting such obligations, You may act only
 *       on Your own behalf and on Your sole responsibility, not on behalf
 *       of any other Contributor, and only if You agree to indemnify,
 *       defend, and hold each Contributor harmless for any liability
 *       incurred by, or claims asserted against, such Contributor by reason
 *       of your accepting any such warranty or additional liability.
 *
 *    END OF TERMS AND CONDITIONS
 *
 *    APPENDIX: How to apply the Apache License to your work.
 *
 *       To apply the Apache License to your work, attach the following
 *       boilerplate notice, with the fields enclosed by brackets "{}"
 *       replaced with your own identifying information. (Don't include
 *       the brackets!)  The text should be enclosed in the appropriate
 *       comment syntax for the file format. We also recommend that a
 *       file or class name and description of purpose be included on the
 *       same "printed page" as the copyright notice for easier
 *       identification within third-party archives.
 *
 *    Copyright 2024 onsamepage.ai
 *
 *    Licensed under the Apache License, Version 2.0 (the "License");
 *    you may not use this file except in compliance with the License.
 *    You may obtain a copy of the License at
 *
 *        https://www.apache.org/licenses/LICENSE-2.0
 *
 *    Unless required by applicable law or agreed to in writing, software
 *    distributed under the License is distributed on an "AS IS" BASIS,
 *    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *    See the License for the specific language governing permissions and
 *    limitations under the License.
 */
package cn.mlamp.insightflow.cms.util;


import cn.mlamp.insightflow.cms.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.util.StreamUtils;
import org.springframework.web.client.RequestCallback;
import org.springframework.web.client.ResponseExtractor;
import org.springframework.web.client.RestTemplate;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;

/**
 * @Author: husuper
 * @CreateTime: 2024-08-22
 */
@Slf4j
public class FileDownloadUtil {

    private static final RestTemplate restTemplate = new RestTemplate();

    public static String downloadFile(String url,String fileName ,String fileType){
       String localFilePath=getPath(fileName,fileType);
       downloadFile(url,localFilePath);
       return localFilePath;
    }



    public static void downloadFile(String url, String localFilePath){
        // 定义RequestCallback用于请求头设置等操作
        RequestCallback requestCallback = request -> {
            // 可以在这里设置请求头，如果有需要
        };

        // 定义ResponseExtractor用于处理响应
        ResponseExtractor<Void> responseExtractor = response -> {
            if (response.getStatusCode() == HttpStatus.OK) {
                // 创建本地文件
                File localFile = new File(localFilePath);
                if (!localFile.exists()) {
                    localFile.getParentFile().mkdirs();
                    localFile.createNewFile();
                }

                try (InputStream inputStream = response.getBody();
                     FileOutputStream outputStream = new FileOutputStream(localFile)) {
                    // 使用Spring的StreamUtils工具类将输入流复制到输出流
                    StreamUtils.copy(inputStream, outputStream);
                }
            }else{
                throw new IOException();
            }
            return null;
        };

        // 使用exchange方法流式下载文件
        restTemplate.execute(url, HttpMethod.GET, requestCallback, responseExtractor);
    }

    public static boolean fileExistenceChecker(String localFilePath){
        if(StringUtils.isBlank(localFilePath)){
            return false;
        }
        File file = new File(localFilePath);
        return file.exists();
    }


    //删除视频
    public static void deleteFile(String localFilePath){
        try {
            if(StringUtils.isBlank(localFilePath)){
                return;
            }
            File file = new File(localFilePath);
            if(file.exists()){
                file.delete();
            }
        }catch (Exception e){
            log.error("删除文件失败:{}", e.getMessage());
        }

    }



    public static String getPath(String name,String fileType){
        String fileName = name + "." + fileType;
        return getPath(fileName);
    }

    public static String getPath(String fileName){
        String directoryPath = System.getProperty("user.dir");
        String filePath = directoryPath + File.separator + fileName;
        return filePath;
    }


    public static void downloadFile2(String fileURL, String savePath)  {
        InputStream inputStream=null;
        FileOutputStream outputStream=null;
        HttpURLConnection httpConn=null;
        try {
            URL url = new URL(fileURL);
             httpConn = (HttpURLConnection) url.openConnection();
            httpConn.setRequestMethod("GET");

            // 检查响应代码
            int responseCode = httpConn.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                // 输入流
                inputStream = new BufferedInputStream(httpConn.getInputStream());
                // 输出流
                outputStream = new FileOutputStream(savePath);

                byte[] buffer = new byte[4096]; // 缓冲区
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
            } else {
                log.error("No file to download. Server replied HTTP code: " + responseCode);
            }
        }catch (Exception e){
            log.error("错误信息: ",e);
            throw new BusinessException("下载失败");
        }finally {
            try {
                if (outputStream != null) {
                    try {
                        outputStream.close();
                    }catch (Exception e){
                        log.error("Error closing output stream", e);
                    }
                }
                if (inputStream != null) {
                    try {
                        inputStream.close();
                    }catch (Exception e){
                        log.error("Error closing input stream", e);
                    }
                }
                if (httpConn != null) {
                    try {
                        httpConn.disconnect();
                    }catch (Exception e){
                        log.error("Error disconnecting HTTP connection", e);
                    }
                }
            } catch (Exception e) {
                log.error("Error closing streams", e);
            }
        }

    }


    public static void downloadFile3(String fileUrl, String localFilePath){
        //如果本地存在文件就不下载，没有就下载
        if(fileExistenceChecker(localFilePath)){
            return;
        }
        downloadFile2(fileUrl,localFilePath);
    }



    public static void main(String[] args) throws UnsupportedEncodingException {
        String fileUrl = "";
       String fileUrl1="https://mos-ex.intra.mlamp.cn/ai-pc-cms/video-decode/videos/douyin/20250323/31234ewwerwerr32ewr413.mp4";
       String fileUrl2="https://mos-ex.intra.mlamp.cn/ai-pc-image/test/insight_platform/file/_b917fe51-93b9-4d20-b813-fb7b60a67561.mp4?response-cache-control=max-age%3D0&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250407T081903Z&X-Amz-SignedHeaders=host&X-Amz-Expires=14399&X-Amz-Credential=Ff7O8C17XXKTrlPXXHH0%2F20250407%2Foss-cn-beijing%2Fs3%2Faws4_request&X-Amz-Signature=7a4a43d100119ae838fb454ff27af52f8ff195e42f5fac9c72316480b32e5636";
        String encodedUrl = URLEncoder.encode(fileUrl, "UTF-8");

        String localFilePath = getPath("test7.mp4");

        try {
            downloadFile(fileUrl1, localFilePath);
            System.out.println("File downloaded successfully to " + localFilePath);
            deleteFile(localFilePath);
        } catch (Exception e) {
            e.printStackTrace();
            System.err.println("Failed to download file.");
        }
    }


}