package cn.mlamp.insightflow.cms.model.query;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import cn.mlamp.insightflow.cms.validation.VerificationConstraint;

/**
 * 验证码Param
 *
 * <AUTHOR>
 * @since 2022-09-15 10:20:56
 */
@Data
@VerificationConstraint
public class VerificationCodeParam {

    /**
     * 行为：1-账号注册 2-重置密码
     */
    @NotNull(message = "override_message.verification_code_source_not_null")
    private Integer source;
    /**
     * 类型： 1-邮箱验证 2-手机号验证
     */
    @NotNull(message = "override_message.verification_type_not_null")
    private Integer verificationType;
    /**
     * 根据verificationType，填写 邮箱或手机号
     */
    @NotBlank(message = "override_message.verification_target_not_blank")
    private String verificationTarget;

    /**
     * 滑动验证码参数
     */
    private CaptchaParam captchaParam;

}
