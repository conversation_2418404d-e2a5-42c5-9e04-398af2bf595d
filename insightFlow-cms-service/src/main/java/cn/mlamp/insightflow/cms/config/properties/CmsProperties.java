package cn.mlamp.insightflow.cms.config.properties;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties("cms")
public class CmsProperties {

    @Value("${spring.profiles.active:prod}")
    private String profile = "prod";

    public boolean isProd() {
        return "prod".equalsIgnoreCase(profile);
    }

}
