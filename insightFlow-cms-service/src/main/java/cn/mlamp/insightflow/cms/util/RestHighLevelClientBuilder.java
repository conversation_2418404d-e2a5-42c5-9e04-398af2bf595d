package cn.mlamp.insightflow.cms.util;

import cn.hutool.core.collection.CollectionUtil;
import cn.mlamp.insightflow.cms.config.Es7Config;
import com.google.common.base.Preconditions;
import com.google.common.base.Splitter;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpHost;
import org.apache.http.HttpResponse;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.message.BasicHeader;
import org.apache.http.nio.conn.ssl.SSLIOSessionStrategy;
import org.apache.http.protocol.HttpContext;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.ssl.TrustStrategy;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;

import javax.net.ssl.SSLContext;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.X509Certificate;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * ES的RestHighLevelClient的Helper类
 */
@Slf4j
public class RestHighLevelClientBuilder {
    private static final String HEADER_AUTH = "Authorization";
    private static final String AUTH_KEY_NAME_PREFIX = "ApiKey ";
    private static final String NULL = "null";

    private static final Splitter SPLITTER = Splitter.on(";").omitEmptyStrings().trimResults();

    public static RestHighLevelClient build(Es7Config config)
            throws NoSuchAlgorithmException, KeyStoreException, KeyManagementException {
        log.info("start build RestHighLevelClient...");
        List<String> urlList = SPLITTER.splitToList(config.getHosts());
        Preconditions.checkArgument(CollectionUtil.isNotEmpty(urlList), "urls不能为空");

        SSLContext sslContext = new SSLContextBuilder().loadTrustMaterial(null, new TrustStrategy() {
            @Override
            public boolean isTrusted(X509Certificate[] chain, String authType) {
                return true;
            }
        }).build();
        SSLIOSessionStrategy sessionStrategy = new SSLIOSessionStrategy(sslContext, NoopHostnameVerifier.INSTANCE);

        HttpHost[] hosts = urlList.stream().map(HttpHost::create).toArray(HttpHost[]::new);
        RestClientBuilder builder = RestClient.builder(hosts);
        builder.setRequestConfigCallback(requestConfigBuilder -> {
            // 三次握手建立连接超时时间，默认1000
            requestConfigBuilder.setConnectTimeout(config.getConnectTimeout());
            // 数据包传输超时时间，默认30*1000，集群压力较大可以适当加大
            requestConfigBuilder.setSocketTimeout(config.getSocketTimeout());
            // 连接池获取连接超时时间
            requestConfigBuilder.setConnectionRequestTimeout(config.getConnectionRequestTimeout());
            return requestConfigBuilder;
        }).setHttpClientConfigCallback(httpAsyncClientBuilder -> {
                    httpAsyncClientBuilder
                            .setSSLStrategy(sessionStrategy)
                            // 连接池中最大总连接数，默认30,maxConnTotal是同时间正在使用的最多的连接数
                            .setMaxConnTotal(config.getMaxConnTotal())
                            // 每个路由的最大连接数，默认10,maxConnPerRoute是针对一个域名同时间正在使用的最多的连接数
                            .setMaxConnPerRoute(config.getMaxConnPerRoute())
                            // 设置长链接空闲时间
                            .setKeepAliveStrategy((HttpResponse response, HttpContext context) -> TimeUnit.MINUTES
                                    .toMinutes(config.getKeepAliveStrategyMinute()));
                    if (!NULL.equals(config.getApiKey())) {
                        // apikey方式
                        httpAsyncClientBuilder.setDefaultHeaders(Collections.singletonList(
                                new BasicHeader(HEADER_AUTH, AUTH_KEY_NAME_PREFIX + config.getApiKey())));
                    }
                    return httpAsyncClientBuilder;
                }

        );
        if (!NULL.equals(config.getPathPrefix())) {
            builder.setPathPrefix(config.getPathPrefix());
        }
        return new RestHighLevelClient(builder);
    }

}
