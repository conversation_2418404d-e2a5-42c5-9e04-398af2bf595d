//package cn.mlamp.insightflow.cms.strategy.video.create;
//
//import cn.mlamp.insightflow.cms.common.file.IS3FlowService;
//import cn.mlamp.insightflow.cms.config.AnalysisVideoConfig;
//import cn.mlamp.insightflow.cms.entity.CmsPullTaskDedupedData;
//import cn.mlamp.insightflow.cms.entity.CmsVideoAsr;
//import cn.mlamp.insightflow.cms.entity.CmsVideoInfo;
//import cn.mlamp.insightflow.cms.entity.CmsVideoResult;
//import cn.mlamp.insightflow.cms.enums.*;
//import cn.mlamp.insightflow.cms.exception.BusinessException;
//import cn.mlamp.insightflow.cms.model.query.AnalysisVideoCreateRequest;
//import cn.mlamp.insightflow.cms.model.query.AnalysisVideoQueryRequest;
//import cn.mlamp.insightflow.cms.model.vo.AnalysisVideoCreateVO;
//import cn.mlamp.insightflow.cms.model.vo.AnalysisVideoResultVO;
//import cn.mlamp.insightflow.cms.service.CmsPullTaskDedupedDataService;
//import cn.mlamp.insightflow.cms.service.IVideoAsrService;
//import cn.mlamp.insightflow.cms.service.IVideoInfoService;
//import cn.mlamp.insightflow.cms.service.IVideoResultService;
//import cn.mlamp.insightflow.cms.strategy.handle.GoldFiveHandle;
//import cn.mlamp.insightflow.cms.strategy.handle.VideoRecognitionHandle;
//import cn.mlamp.insightflow.cms.util.FileDownloadUtil;
//import cn.mlamp.insightflow.cms.util.VideoUtil;
//import com.alibaba.fastjson.JSONObject;
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import jakarta.annotation.PostConstruct;
//import jakarta.annotation.Resource;
//import lombok.Data;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.io.FileUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.stereotype.Component;
//import org.springframework.transaction.annotation.Transactional;
//
//import java.io.File;
//import java.io.FileOutputStream;
//import java.util.*;
//
//
///**
// * @Author: husuper
// * @CreateTime: 2025-02-18
// */
//@Slf4j
//@Component
//@RequiredArgsConstructor
//public class GoldFiveStrategy implements ProcessAnalysisVideoStrategyInterface {
//
//    private final CmsPullTaskDedupedDataService pullTaskDedupedDataService;
//
//    private final IVideoInfoService videoInfoService;
//
//    private final IVideoResultService videoResultService;
//
//    private final GoldFiveHandle goldFiveHandle;
//
//    private final IVideoAsrService videoAsrService;
//
//    @Resource(name = "cmsS3FlowService")
//    private IS3FlowService cmsS3FlowService;
//
//    private final AnalysisVideoConfig analysisVideoConfig;
//
//
//    @PostConstruct
//    public void init() {
//        AnalysisVideoStrategyMap.register(AnalysisVideoTypeEnum.GOLDEN_FIVE_SECONDS.getVideoType(), this);
//    }
//
//
//    @Override
//    @Transactional
//    public AnalysisVideoCreateVO process(AnalysisVideoCreateRequest request) {
//        String esId=request.getEsId();
//        if(StringUtils.isBlank(esId)){
//            throw new BusinessException("esId不能为空");
//        }
//        LambdaQueryWrapper<CmsPullTaskDedupedData> queryWrapper =new LambdaQueryWrapper<>();
//        queryWrapper.eq(CmsPullTaskDedupedData::getEsId,esId);
//        CmsPullTaskDedupedData dedupedData = pullTaskDedupedDataService.getOne(queryWrapper);
//        if(dedupedData==null){
//            throw new BusinessException("esId不存在");
//        }
//        if(dedupedData.getDownloadStatus()!= DownloadStatusEnum.SUCCESS.getCode()){
//            throw new BusinessException("视频未下载完成");
//        }
//        AnalysisVideoCreateVO analysisVideoCreateVO=new AnalysisVideoCreateVO();
//        //检查是否分析过了
//        List<CmsVideoInfo> list= videoInfoService.list(new LambdaQueryWrapper<CmsVideoInfo>().eq(CmsVideoInfo::getEsId, esId).eq(CmsVideoInfo::getType,AnalysisVideoTypeEnum.GOLDEN_FIVE_SECONDS.getVideoCode()));
//        if(list!=null && list.size()>0){
//            analysisVideoCreateVO.setId(list.get(0).getId());
//            return analysisVideoCreateVO;
//        }
//
//        boolean flag=false;
//        if(!FileDownloadUtil.fileExistenceChecker(request.getLocalFilePath())){
//            //下载视频
//            String videoUrl = VideoUtil.getVideoUrl(dedupedData.getDatePublishedAt(), dedupedData.getEsId());
//            String fileName= dedupedData.getEsId()+".mp4";
//            String localFilePath = FileDownloadUtil.getPath(fileName);
//            FileDownloadUtil.downloadFile(videoUrl, localFilePath);
//            request.setLocalFilePath(localFilePath);
//            flag=true;
//        }
//
//        List<String> images=new ArrayList<>();
//        images.add(VideoUtil.cutImageOfBase64(request.getLocalFilePath(),1000));
//        images.add(VideoUtil.cutImageOfBase64(request.getLocalFilePath(),2000));
//        images.add(VideoUtil.cutImageOfBase64(request.getLocalFilePath(),3000));
//        images.add(VideoUtil.cutImageOfBase64(request.getLocalFilePath(),4000));
//        images.add(VideoUtil.cutImageOfBase64(request.getLocalFilePath(),5000));
//
//        List<GoldFiveHandle.Sentence > sentences= new ArrayList<>();
//
//        List<CmsVideoAsr>  cmsVideoAsrList= videoAsrService.list(new LambdaQueryWrapper<CmsVideoAsr>().eq(CmsVideoAsr::getEsId, esId));
//
//        for (CmsVideoAsr cmsVideoAsr : cmsVideoAsrList){
//            if(getInteger(cmsVideoAsr.getStart()) < 5000){
//                GoldFiveHandle.Sentence sentence=new GoldFiveHandle.Sentence(cmsVideoAsr.getText(),Integer.parseInt(cmsVideoAsr.getStart()),Integer.parseInt(cmsVideoAsr.getEnd()));
//                sentences.add(sentence);
//            }
//        }
//
//       GoldFiveHandle.ApiResponse  apiResponse= goldFiveHandle.callImageDecoding5s("123",images,sentences,null,dedupedData.getTextContent());
//        if(apiResponse.getCode()!=200){
//            throw new BusinessException("调用接口失败");
//        }
//        Map<String,Object> data= apiResponse.getData();
//
//        CmsVideoInfo videoInfo = new CmsVideoInfo();
//        videoInfo.setEsId(esId);
//        videoInfo.setType(AnalysisVideoTypeEnum.GOLDEN_FIVE_SECONDS.getVideoCode());
//        videoInfo.setStatus(VideoInfoStatusEnum.SUCCESS.getCode());
//        videoInfo.setUserId(request.getUserId());
//        videoInfo.setTenantId(request.getTenantId());
//        Arg arg=new Arg();
//        arg.setSentences(sentences);
//        videoInfo.setArg(JSONObject.toJSONString(arg));
//        videoInfoService.save(videoInfo);
//
//        //存原始结果
//        CmsVideoResult videoResult=new CmsVideoResult();
//        videoResult.setVideoId(videoInfo.getId());
//        videoResult.setType(VideoResultTypeEnum.GOLD_FIVE.getCode());
//        //做一个list
//        List<String> pics=new ArrayList<>();
//        int i=1000;
//        for (String image : images){
//           String ossId= analysisVideoConfig.getVideoPicPath()+"/"+esId+"/"+i+".jpg";
//           uploadVideoPic(ossId,image,i+"");
//           pics.add(analysisVideoConfig.getDomain()+ossId);
//           i=i+1000;
//        }
//
//        videoResult.setData(JSONObject.toJSONString(pics));
//        videoResultService.save(videoResult);
//
//        CmsVideoResult videoResult6=new CmsVideoResult();
//        videoResult6.setVideoId(videoInfo.getId());
//        videoResult6.setType(VideoResultTypeEnum.PRE_GOLD_FIVE.getCode());
//        videoResult6.setData(JSONObject.toJSONString(data));
//        videoResultService.save(videoResult6);
//
//        images.clear();
//        if(flag){
//            FileDownloadUtil.deleteFile(request.getLocalFilePath());
//        }
//
//        analysisVideoCreateVO.setId(videoInfo.getId());
//        return analysisVideoCreateVO;
//    }
//
//
//    private static Integer getInteger(String str){
//        try{
//            return Integer.parseInt(str);
//        }catch (Exception e){
//            log.error("转换异常",e);
//            throw new BusinessException("转换异常");
//        }
//    }
//
//    @Data
//    public static class Arg{
//        //视频分析任务Id
//        private List<GoldFiveHandle.Sentence > sentences;
//
//    }
//
//
//    @Override
//    public AnalysisVideoResultVO queryResult(AnalysisVideoQueryRequest request) {
//        String esId=request.getEsId();
//        if(StringUtils.isBlank(esId)){
//            throw new BusinessException("esId不能为空");
//        }
//        CmsVideoInfo cmsVideoInfo= videoInfoService.getOne(new LambdaQueryWrapper<CmsVideoInfo>().eq(CmsVideoInfo::getEsId,esId).eq(CmsVideoInfo::getType,1).orderByDesc(CmsVideoInfo::getCreateTime).last("limit 1"));
//        if(cmsVideoInfo==null){
//            throw new BusinessException("没有查询到视频分析结果信息");
//        }else{
//            AnalysisVideoResultVO analysisVideoResultVO=new AnalysisVideoResultVO();
//            analysisVideoResultVO.setId(cmsVideoInfo.getId());
//            analysisVideoResultVO.setStatus(cmsVideoInfo.getStatus());
//            return analysisVideoResultVO;
//        }
//    }
//
//
//    public void uploadVideoPic(String ossId, String image,String time) {
//        // 解码BASE64编码的图片
//        byte[] imageBytes = Base64.getDecoder().decode(image);
//        // 创建临时文件
//        File tempFile = null;
//        FileOutputStream fos = null;
//        try {
//            tempFile = File.createTempFile(time, ".jpg");
//            fos = new FileOutputStream(tempFile);
//            fos.write(imageBytes);
//
//            // 调用上传接口
//            cmsS3FlowService.upload(ossId, tempFile);
//        } catch (Exception e) {
//            log.error("上传视频图片失败", e);
//        } finally {
//            // 关闭文件输出流
//            if (fos != null) {
//                try {
//                    fos.close();
//                } catch (Exception e) {
//                    log.error("关闭文件输出流失败", e);
//                }
//            }
//            // 删除临时文件
//            if (tempFile != null) {
//                tempFile.delete();
//            }
//        }
//    }
//}
