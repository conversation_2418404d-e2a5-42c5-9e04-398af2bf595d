package cn.mlamp.insightflow.cms.service;

import cn.mlamp.insightflow.cms.entity.CmsSearchHistory;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 用户查询历史记录Service接口
 * <AUTHOR>
 * @date 2025-05-28
 */
public interface CmsSearchHistoryService extends IService<CmsSearchHistory> {

    /**
     * 保存查询历史记录
     * @param userId 用户ID
     * @param tenantId 租户ID
     * @param keyword 关键词
     * @param searchType 查询类型
     * @param searchModule 查询模块
     */
    void saveSearchHistory(Integer userId, Integer tenantId, String keyword, String searchType, String searchModule);
}
