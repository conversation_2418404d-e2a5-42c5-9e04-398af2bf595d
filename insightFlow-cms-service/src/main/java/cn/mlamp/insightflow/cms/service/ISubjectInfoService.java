package cn.mlamp.insightflow.cms.service;

import cn.mlamp.insightflow.cms.entity.CmsSubjectInfo;
import cn.mlamp.insightflow.cms.model.query.SubjectCreateConfirmRequest;
import cn.mlamp.insightflow.cms.model.query.SubjectListRequest;
import cn.mlamp.insightflow.cms.model.vo.SubjectInfoVO;
import cn.mlamp.insightflow.cms.model.vo.SubjectListVO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

public interface ISubjectInfoService extends IService<CmsSubjectInfo> {
    Page<SubjectListVO> getList(SubjectListRequest subjectListRequest);

    SubjectInfoVO getSubjectDetail(Integer subjectId);

    void deleteBatchByIds(List<Integer> ids);

    void addOrUpdateSubjectConfirmDescription(SubjectCreateConfirmRequest subjectCreateConfirmRequest);

}
