package cn.mlamp.insightflow.cms.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;

@AllArgsConstructor
@Data
public class TaskUploadVO implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "文件在oss中的id", required = true)
    private String objOssId;

    @Schema(description = "文件上传url", required = true)
    private String uploadUrl;

    @Schema(description = "文件名", required = true)
    private String filename;
}
