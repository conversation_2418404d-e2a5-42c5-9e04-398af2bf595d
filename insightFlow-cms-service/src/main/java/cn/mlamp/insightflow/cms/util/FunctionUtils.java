package cn.mlamp.insightflow.cms.util;

import cn.hutool.core.date.StopWatch;
import cn.hutool.core.io.FileUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;

import javax.annotation.Nullable;
import javax.validation.constraints.NotNull;
import java.nio.file.Files;
import java.nio.file.Path;

@Slf4j
public class FunctionUtils {

    private FunctionUtils() {
    }

    public interface Action<T> {
        void accept(T param) throws Exception;
    }

    public interface ActionWithReturn<T, R> {
        R accept(T param) throws Exception;
    }

    public interface NoArgActionWithNoReturn {
        void accept() throws Exception;
    }

    public static <R> R withTempFile(
            @NotNull Path tempDir,
            @Nullable String suffix,
            @NotNull ActionWithReturn<Path, R> action
    ) {
        return withTempFile(tempDir, null, suffix, action);
    }

    public static <R> R withTempFile(
            @NotNull Path tempDir,
            @Nullable String prefix,
            @Nullable String suffix,
            @NotNull ActionWithReturn<Path, R> action
    ) {
        Path tempFile = null;
        try {
            tempFile = Files.createTempFile(
                    tempDir,
                    prefix,
                    suffix
            );
            return action.accept(tempFile);
        } catch (Throwable e) {
            return ExceptionUtils.rethrow(e);
        } finally {
            try {
                if (tempFile != null) {
                    FileUtil.del(tempFile);
                }
            } catch (Exception e) {
                log.warn("删除临时文件夹失败: {}", tempFile, e);
            }
        }
    }

    public static void withTempFile(
            @NotNull Path tempDir,
            @Nullable String suffix,
            @NotNull Action<Path> action
    ) {
        withTempFile(tempDir, null, suffix, action);
    }

    public static void withTempFile(
            @NotNull Path tempDir,
            @Nullable String prefix,
            @Nullable String suffix,
            @NotNull Action<Path> action
    ) {
        Path tempFile = null;
        try {
            tempFile = Files.createTempFile(
                    tempDir,
                    prefix,
                    suffix
            );
            action.accept(tempFile);
        } catch (Throwable e) {
            ExceptionUtils.rethrow(e);
        } finally {
            try {
                if (tempFile != null) {
                    FileUtil.del(tempFile);
                }
            } catch (Exception e) {
                log.warn("删除临时文件夹失败: {}", tempFile, e);
            }
        }
    }

    public static void withTempDirectory(@NotNull Action<Path> action) {
        withTempDirectory(null, action);
    }

    public static void withTempDirectory(@Nullable String prefix, @NotNull Action<Path> action) {
        Path tempDir = null;
        try {
            tempDir = Files.createTempDirectory(prefix);
            action.accept(tempDir);
        } catch (Throwable e) {
            ExceptionUtils.rethrow(e);
        } finally {
            try {
                if (tempDir != null) {
                    FileUtil.del(tempDir);
                }
            } catch (Exception e) {
                log.warn("删除临时文件夹失败: {}", tempDir, e);
            }
        }
    }

    public static void withStopWatch(
            StopWatch stopWatch,
            String taskName,
            @NotNull NoArgActionWithNoReturn function
    ) {
        try {
            stopWatch.start(taskName);
            function.accept();
        } catch (Throwable e) {
            ExceptionUtils.rethrow(e);
        } finally {
            stopWatch.stop();
        }
    }

}
