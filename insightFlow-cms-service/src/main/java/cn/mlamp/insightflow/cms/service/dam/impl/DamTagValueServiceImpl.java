package cn.mlamp.insightflow.cms.service.dam.impl;

import cn.mlamp.insightflow.cms.entity.dam.DamTagValue;
import cn.mlamp.insightflow.cms.mapper.dam.DamTagValueMapper;
import cn.mlamp.insightflow.cms.service.dam.IDamTagValueService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * DAM标签值表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
@Service
public class DamTagValueServiceImpl extends ServiceImpl<DamTagValueMapper, DamTagValue> implements IDamTagValueService {

}
