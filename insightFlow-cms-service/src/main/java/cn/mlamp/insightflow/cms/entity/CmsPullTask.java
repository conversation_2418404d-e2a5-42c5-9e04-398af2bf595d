package cn.mlamp.insightflow.cms.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("cms_pull_tasks")
public class CmsPullTask extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    private Date startTime; // 任务开始时间

    private Date endTime; // 任务结束时间

    private String status; // 任务状态（pending, running, pulled, completed, failed）

    private String failMessage; // 失败信息

    private String pullParams; // 拉取参数（JSON 格式）

    private String type; // 任务类型
}
