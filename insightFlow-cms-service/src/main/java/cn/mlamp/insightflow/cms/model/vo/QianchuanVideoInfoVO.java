package cn.mlamp.insightflow.cms.model.vo;

import cn.mlamp.insightflow.cms.entity.CmsPullTaskDedupedData;
import cn.mlamp.insightflow.cms.entity.QianchuanMaterialVideo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @Author: husuper
 * @CreateTime: 2025-04-21
 */
@Data
public class QianchuanVideoInfoVO implements Serializable {

    @Schema(description = "视频分析Id", required = true)
    private Integer id;

    @Schema(description = "1:视频分析;2：黄金5秒;3:圈层热点；4：上传视频；5：千川视频分析", required = true)
    private Integer type;

    @Schema(description = "原视频ID（千川的videoId）", required = true)
    private String esId;

    @Schema(description = "状态名称 1：待处理；2：处理中，3：完成，4：失败", required = true)
    private String statusName;

    @Schema(description = "状态 1：待处理；2：处理中，3：完成，4：失败", required = true)
    private Integer status;

    @Schema(description = "用户userID", required = false)
    private Integer userId;

    //黄金3秒类型
    @Schema(description = "黄金3秒类型", required = true)
    private String threeGoldType;

    //行业
    @Schema(description = "行业", required = true)
    private String industry;

    @Schema(description = "视频语音文字稿", required = true)
    private String asr;

    @Schema(description = "千川视频信息", required = false)
    private QianchuanMaterialVideo qianchuanMaterialVideo;

    @Schema(description = "ES的社媒数据", required = false)
    private CmsPullTaskDedupedData cmsPullTaskDedupedData;

//    @Schema(description = "分镜数据", required = true)
//    private List<SceneDecoding> sceneDecodings;

    @Schema(description = "分镜数据", required = true)
    private List<Map<String,Object>> sceneDecodings;

    @Data
    public static class SceneDecoding{

        @Schema(description = "结果Id", required = true)
        private Integer videoInfoReusltId;

        @Schema(description = "序号", required = true)
        private Integer index;

        @Schema(description = "分镜视频url", required = true)
        private String sceneDecodingUrl;

        @Schema(description = "分镜视频图片", required = true)
        private String sceneDecodingImageUrl;

        @Schema(description = "分镜视频Girl url", required = true)
        private String sceneDecodingGifUrl;

        @Schema(description = "分镜视频Girl头图 url", required = true)
        private String sceneDecodingGifImageUrl;

        @Schema(description = "是否黄金3秒", required = true)
        private Boolean threeGold;

        @Schema(description = "镜头描述", required = true)
        private String 镜头描述;

        @Schema(description = "视频内容策略", required = true)
        private String 视频内容策略;

        @Schema(description = "镜头参考", required = true)
        private String 镜头参考;

        @Schema(description = "品牌植入", required = true)
        private String 品牌植入;

        @Schema(description = "镜头类型", required = true)
        private String 镜头类型;

        @Schema(description = "运镜方式", required = true)
        private String 运镜方式;

        @Schema(description = "分镜开始时间戳", required = true)
        private String 分镜开始时间戳;

        @Schema(description = "分镜结束时间戳", required = true)
        private String 分镜结束时间戳;

        @Schema(description = "出现演员", required = true)
        private String 出现演员;

        @Schema(description = "演员动作", required = true)
        private String 演员动作;

        @Schema(description = "演员表情", required = true)
        private String 演员表情;

        @Schema(description = "台词", required = true)
        private String 台词;

        @Schema(description = "台词情绪", required = true)
        private String 台词情绪;

        @Schema(description = "服装造型", required = true)
        private String 服装造型;

        @Schema(description = "道具清单", required = true)
        private String 道具清单;

        @Schema(description = "布景要求", required = true)
        private String 布景要求;

        @Schema(description = "背景音乐/音效", required = true)
        private String 背景音乐音效;

        @Schema(description = "光影与色彩要求", required = true)
        private String 光影与色彩要求;

        @Schema(description = "摄影器材", required = true)
        private String 摄影器材;

    }


    @Schema(description = "视频整体分析信息", required = true)
    private VideoSummary videoSummary;


    @Data
    public static class VideoSummary {

        @Schema(description = "台词套路", required = true)
        private String dialogueRoutine;

        @Schema(description = "视频总结", required = true)
        private String videoSummary;

        @Schema(description = "产品名称", required = true)
        private String productName;

        @Schema(description = "卖点", required = true)
        private String cellingPoint;

        @Schema(description = "受众人群", required = true)
        private String aimingTribe;
    }







}
