package cn.mlamp.insightflow.cms.model.query;

import lombok.Data;
import lombok.ToString;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;

import cn.mlamp.insightflow.cms.constant.CommonConstant;
import cn.mlamp.insightflow.cms.validation.ObjectPropertiesMustBeConsistentConstraint;
import cn.mlamp.insightflow.cms.validation.VerificationConstraint;
import jakarta.validation.constraints.NotBlank;

/**
 * 验证码Param
 *
 * <AUTHOR>
 * @since 2022-09-16 14:05:08
 */
@Data
@ToString(onlyExplicitlyIncluded = true)
@VerificationConstraint
@ObjectPropertiesMustBeConsistentConstraint(properties = { "password",
        "passwordCheck" }, message = "override_message.password_not_equals_password_check")
public class ResetPasswordParam {

    /**
     * 类型： 1-邮箱验证 2-手机号验证
     */
    @ToString.Include
    @NotNull(message = "override_message.verification_type_not_null")
    private Integer verificationType;
    /**
     * 根据verificationType，填写 邮箱或手机号
     */
    @ToString.Include
    @NotBlank(message = "override_message.verification_target_not_blank")
    private String verificationTarget;
    /**
     * 验证码
     */
    @ToString.Include
    @Pattern(regexp = CommonConstant.VERIFICATION_CODE_REGX, message = "override_message.verification_code_invalid")
    private String verificationCode;
    /**
     * 密码
     */
    @ToString.Exclude
    @Pattern(regexp = CommonConstant.PASSWORD_REGX, message = "override_message.password_invalid")
    private String password;
    /**
     * 密码 - 二次
     */
    @ToString.Exclude
    @NotBlank(message = "override_message.password_check_not_blank")
    private String passwordCheck;

}
