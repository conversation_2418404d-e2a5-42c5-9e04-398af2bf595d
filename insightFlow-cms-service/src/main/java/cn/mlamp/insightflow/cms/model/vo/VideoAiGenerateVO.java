package cn.mlamp.insightflow.cms.model.vo;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

public class VideoAiGenerateVO {

    // 视频生成响应
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ViduCreateResponse {
        private String taskId;
        private List<String> ossid;
        private String theme;
        private String style;
        private String environment;
        private String condition;
        private Integer frequency;

        @Schema(description = "创建时间", required = true)
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date createTime;

        /**
         * 获取任务ID
         *
         * @return 任务ID
         */
        public String getId() {
            return this.taskId;
        }
    }


    //
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ViduQueryResponse {
        private String status;
        private VideoData videoData;
    }

    // 视频数据(视频查询响应)
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VideoData {
        private String taskId;
        private List<String> ossid;
        private String theme;
        private String style;
        private String environment;
        private String condition;

        @Schema(description = "更新时间", required = true)
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date updateTime;
        private String status;

        private List<OssidAndUrl> ossidAndUrl;
    }

    // 视频参数
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class OssidAndUrl {
        private String headObjOssId;
        private String objOssId;
        private String headUrl;
        private String videoUrl;
        private Integer videoId;


    }

    // 视频列表响应
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ViduListResponse {
        private List<VideoListData> listData;
        private Long total;
        private Integer pageSize;
        private Integer currentpage;
        private String status;

    }

    // 列表数据
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class VideoListData {
        private String taskId;

        @Schema(description = "创建时间", required = true)
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date createTime;
        private String count;
        private String name;
        private List<VideoItem> videoList;
        private Integer status;
    }

    // 视频列表项
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class VideoItem {
        private String videoUrl;
        private String coverUrl;
        private String videoOssid;
        private String coverOssId;
        private String videoName;
    }


}


