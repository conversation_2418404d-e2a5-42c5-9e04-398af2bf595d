package cn.mlamp.insightflow.cms.service;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;

import cn.mlamp.insightflow.cms.enums.AnalysisStatusEnum;
import cn.mlamp.insightflow.cms.enums.DownloadStatusEnum;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import cn.mlamp.insightflow.cms.entity.CmsPullTaskDedupedData;
import cn.mlamp.insightflow.cms.entity.CmsPullTaskRawData;
import cn.mlamp.insightflow.cms.model.query.RecommenListRequest;
import cn.mlamp.insightflow.cms.model.query.VideoHotspotQueryRequest;

public interface CmsPullTaskDedupedDataService extends IService<CmsPullTaskDedupedData> {
    List<CmsPullTaskDedupedData> getRecentDedupedData(Date startDate, Date endDate, Integer type);

    void saveDedupedDatas(List<CmsPullTaskRawData> finalData);

    IPage<CmsPullTaskDedupedData> getVideoHotspotList(VideoHotspotQueryRequest request);

    CmsPullTaskDedupedData getTopInteractiveDataOfToday();

    List<CmsPullTaskDedupedData> getRecommenListRequest(RecommenListRequest request);

    List<Map<String, Object>> getTribeStatisticsList();

    List<CmsPullTaskDedupedData> getTribeDataForAll(List<Integer> types, LocalDate startDate, LocalDate endDate);

    boolean updateBatch(List<CmsPullTaskDedupedData> updateList);

    long countRecentDedupedData(Date date, String kwKbIndustry, Integer type);

    CmsPullTaskDedupedData findFirstByEsId(String esId);

    void updateStatusByEsId(String esId, AnalysisStatusEnum analysisStatusEnum);

    void updateDownloadStatusByEsId(String esId, DownloadStatusEnum downloadStatusEnum);
}
