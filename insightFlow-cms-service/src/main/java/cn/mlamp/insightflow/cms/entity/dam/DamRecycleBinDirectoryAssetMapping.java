package cn.mlamp.insightflow.cms.entity.dam;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * DAM回收站表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
@Getter
@Setter
@TableName("cms_recycle_bin_directory_asset_mapping")
@Schema(name = "CmsRecycleBinDirectoryAssetMapping", description = "DAM回收站目录素材映射表")
public class DamRecycleBinDirectoryAssetMapping implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @Schema(description = "回收站ID")
    @TableField("recycle_bin_id")
    private Integer recycleBinId;

    @Schema(description = "素材ID")
    @TableField("asset_id")
    private Integer assetId;

    @Schema(description = "创建时间")
    @TableField("create_time")
    private Date createTime;

    @Schema(description = "逻辑删除：0-未删除，1-已删除")
    @TableField("is_deleted")
    private Boolean isDeleted;

}
