package cn.mlamp.insightflow.cms.enums;

/**
 * @Author: husuper
 * @CreateTime: 2025-03-30
 */
public enum DownloadStatusEnum {

//    下载状态0：待下载，1：下载中，2：成功，3，失败

    PENDING(0, "待下载"),
    DOWNLOADING(1, "下载中"),
    SUCCESS(2, "成功"),
    FAILURE(3, "失败");


    private final int code;

    private final String msg;

    DownloadStatusEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
