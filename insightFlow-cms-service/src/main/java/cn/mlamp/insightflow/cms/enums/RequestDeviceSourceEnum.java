package cn.mlamp.insightflow.cms.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 登陆源enum
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2022/6/17 11:53 上午
 */
@AllArgsConstructor
@Getter
public enum RequestDeviceSourceEnum {
    /**
     * 平台站点资源
     */
    PC("pc", "PC"),
    /**
     * 查询范围资源
     */
    MINI_PROGRAM("mini-program", "小程序");

    /**
     * code
     */
    private String code;
    /**
     * 名称
     */
    private String desc;

}
