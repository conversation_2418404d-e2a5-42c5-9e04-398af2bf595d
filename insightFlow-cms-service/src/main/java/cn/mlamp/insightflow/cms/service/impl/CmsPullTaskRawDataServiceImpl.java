package cn.mlamp.insightflow.cms.service.impl;

import cn.mlamp.insightflow.cms.entity.CmsPullTaskRawData;
import cn.mlamp.insightflow.cms.mapper.CmsPullTaskRawDataMapper;
import cn.mlamp.insightflow.cms.service.CmsPullTaskRawDataService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;

import org.springframework.stereotype.Service;

@Service
public class CmsPullTaskRawDataServiceImpl extends ServiceImpl<CmsPullTaskRawDataMapper, CmsPullTaskRawData>
        implements CmsPullTaskRawDataService {

    @Override
    public void batchInsertRawData(List<CmsPullTaskRawData> rawDataList) {
        if (rawDataList == null || rawDataList.isEmpty()) {
            return;
        }
        this.saveBatch(rawDataList); // MyBatis-Plus 提供的批量插入方法
    }

}
