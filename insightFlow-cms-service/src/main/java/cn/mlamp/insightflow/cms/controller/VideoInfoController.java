package cn.mlamp.insightflow.cms.controller;

import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.constant.RedisConstant;
import cn.mlamp.insightflow.cms.exception.BusinessException;
import cn.mlamp.insightflow.cms.model.query.*;
import cn.mlamp.insightflow.cms.model.vo.QianchuanVideoInfoVO;
import cn.mlamp.insightflow.cms.model.vo.VideoInfoResultVO;
import cn.mlamp.insightflow.cms.model.vo.VideoInfoVO;
import cn.mlamp.insightflow.cms.service.IVideoInfoService;
import cn.mlamp.insightflow.cms.task.QianchuanVideoAnalysisTask;
import com.alibaba.fastjson.JSONObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;


/**
 * @Author: husuper
 * @CreateTime: 2025-03-19
 */
@Slf4j
@RequestMapping("/video/info/")
@RestController
@RequiredArgsConstructor
@Tag(name = "视频分析关接口")
public class VideoInfoController {

    private final IVideoInfoService videoInfoService;

//    private final VideoDownloadTask videoDownloadTask;
//
//    private final VideoAnalysis2Task videoAnalysis2Task;

    private final QianchuanVideoAnalysisTask qianchuanVideoAnalysisTask;

    @Autowired
    private RedisTemplate<String, AsyncResultRequest> redisTemplate;

    @GetMapping("/reuslt")
    @Operation(summary = "获取视频分析结果接口")
    public RespBody<VideoInfoVO> getReuslt(VideoInfoResultRequest videoInfoDetailRequest) {
        boolean flag = false;
        if (videoInfoDetailRequest.getEsId() == null) {
            flag = true;
        }
        if (videoInfoDetailRequest.getId() == null) {
            if (flag) {
                throw new BusinessException("视频分析Id和帖子ID不能同时为空");
            }
        }

        return RespBody.ok(videoInfoService.getReuslt(videoInfoDetailRequest));
    }


    @GetMapping("/qianchuan/reuslt")
    @Operation(summary = "获取千川视频分析结果接口")
    public RespBody<QianchuanVideoInfoVO> getQianchuanReuslt(VideoInfoResultRequest videoInfoDetailRequest) {
        return RespBody.ok(videoInfoService.getQianchuanReuslt(videoInfoDetailRequest));
    }


    @GetMapping("reuslt/detail")
    @Operation(summary = "获取视频分析结果详情接口")
    public RespBody<VideoInfoResultVO> getReusltDetail(VideoInfoResultDetailRequest videoInfoResultDetailRequest) {
        if (videoInfoResultDetailRequest.getVideoInfoReusltId() == null) {
            throw new BusinessException("视频分析结果Id不能为空");
        }

        return RespBody.ok(videoInfoService.getReusltDetail(videoInfoResultDetailRequest));
    }


    @GetMapping("reuslt/detail/export")
    @Operation(summary = "分镜导出接口")
    public void exportReusltDetail(@RequestParam(value = "videoId", required = false) Integer videoId, HttpServletResponse response) {
        if (videoId == null) {
            throw new BusinessException("视频分析结果Id不能为空");
        }
        videoInfoService.exportReusltDetail(videoId, response);
    }

//    @GetMapping("videoDownloadJob")
//    @Operation(summary = "启动下载任务")
//    public RespBody<?> videoDownloadJob(String dateStr) {
//        videoDownloadTask.videoDownloadJob(dateStr);
//        return RespBody.ok();
//    }
//
//    @GetMapping("videoAnalysisJob")
//    @Operation(summary = "启动分析任务")
//    public RespBody<?> videoAnalysisJob(String dateStr) {
//        videoAnalysis2Task.videoAnalysisJob(dateStr);
//        return RespBody.ok();
//    }
//
    @GetMapping("qianchuanVideoAnalysisJob")
    @Operation(summary = "启动分析任务")
    public RespBody<?> qianchuanVideoAnalysisJob(String dateStr) {
        qianchuanVideoAnalysisTask.queryDataVideoAnalysisJob(dateStr);
        return RespBody.ok();
    }


    @GetMapping("handleErrorData")
    @Operation(summary = "处理错误数据")
    public RespBody<Boolean> handleErrorData() {
        videoInfoService.handleErrorData();
        return RespBody.ok(true);
    }


    @PostMapping("async/result")
    @Operation(summary = "接收算法任务结果推送")
    public RespBody<Boolean> asyncResult(@RequestBody AsyncResultRequest asyncResultRequest) {
        // 将请求体放入 Redis 队列
        try {
            log.info("接收算法任务结果推送: {}", JSONObject.toJSONString(asyncResultRequest));
            redisTemplate.opsForList().rightPush(RedisConstant.VIDEO_ASYNC_RESULT_QUEUE, asyncResultRequest);
            return RespBody.ok(true);
        }catch (Exception e){
            log.error("接收算法任务结果推送失败", e);
        }
        return RespBody.ok(false);
    }


}
