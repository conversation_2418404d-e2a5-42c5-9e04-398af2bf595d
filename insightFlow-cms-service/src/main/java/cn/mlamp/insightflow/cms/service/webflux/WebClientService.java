package cn.mlamp.insightflow.cms.service.webflux;

import cn.mlamp.insightflow.cms.config.properties.ThirdWebProperties;
import cn.mlamp.insightflow.cms.model.dto.DeepanaDySkuResponseDTO;
import cn.mlamp.insightflow.cms.model.dto.DeepanaResponseDataDTO;
import cn.mlamp.insightflow.cms.model.dto.DeepanaTikTokVideoDTO;
import cn.mlamp.insightflow.cms.model.dto.DeepanaTiktokRequest;
import cn.mlamp.insightflow.cms.util.DeepanaSignUtil;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.Date;

@Slf4j
@Service
public class WebClientService {

    private static final String DEEPANA_GET_TIKTOK_TITLE = "/get_title";
    private static final String DEEPANA_GET_TIKTOK_VIDEO_INFO = "/get_json";
    private static final String SUAN_FA_GEN_IMAGE = "/SD";
    private final WebClient deepanaWebClient;
    private final WebClient deepanaDySkuWebClient;
    private final WebClient suanFaWebClient;

    public WebClientService(ThirdWebProperties thirdWebProperties) {
        this.deepanaWebClient = WebClient.builder()
                .baseUrl(thirdWebProperties.deepana().baseUrl())
                .build();
        this.deepanaDySkuWebClient = WebClient.builder()
                .baseUrl(thirdWebProperties.deepanaDySku().baseUrl())
                .build();
        this.suanFaWebClient = WebClient.builder()
                .baseUrl(thirdWebProperties.suanFa().baseUrl())
                .build();
    }

    public Mono<DeepanaResponseDataDTO<String>> getTikTokTitle(String url) {
        return deepanaWebClient.post().uri(DEEPANA_GET_TIKTOK_TITLE)
                .body(BodyInserters.fromValue(new DeepanaTiktokRequest(url)))
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<DeepanaResponseDataDTO<String>>() {
                })
                .retry(3)
                .onErrorReturn(new DeepanaResponseDataDTO<>());
    }

    public Mono<DeepanaResponseDataDTO<DeepanaTikTokVideoDTO>> getTikTokVideoInfo(String url, String esId, Date documentCreateDate) {
        return deepanaWebClient.post().uri(DEEPANA_GET_TIKTOK_VIDEO_INFO)
                .body(BodyInserters.fromValue(new DeepanaTiktokRequest(url, esId, documentCreateDate)))
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<DeepanaResponseDataDTO<DeepanaTikTokVideoDTO>>() {
                })
                .retry(3)
                .onErrorReturn(new DeepanaResponseDataDTO<>());
    }

    public  Mono<DeepanaDySkuResponseDTO> getTikTokSkuInfo(String url){
        String requestId = DeepanaSignUtil.getRequestId();
        String time = DeepanaSignUtil.getTime();

        // 创建 MultiValueMap 实例
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();

        // 添加查询参数
        params.add("appId", DeepanaSignUtil.getAppId());
        params.add("requestId", requestId);
        params.add("sign", DeepanaSignUtil.sign(requestId, time));
        params.add("time", time);
        params.add("short_url", url);

        return deepanaDySkuWebClient.get()
                .uri(uriBuilder -> uriBuilder.path("/")
                        .queryParams(params)
                        .build())
                .retrieve()
                .bodyToMono(DeepanaDySkuResponseDTO.class);
    }

    public Mono<String> genImageBySD(String prompt) {
        return suanFaWebClient.post().uri(SUAN_FA_GEN_IMAGE)
                .body(BodyInserters.fromValue(new ImageGenRequest(prompt, null)))
                .retrieve()
                .bodyToMono(String.class)
                .timeout(Duration.ofMinutes(5))  // 添加5分钟超时
                .retry(3)
                .onErrorReturn("");
    }

    @Data
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class ImageGenRequest {
        private String prompt;
        @JsonProperty("negative_prompt")
        private String negativePrompt;
    }

    @Data
    public static class ImageGenResponse {
        private String code;
        @JsonProperty("image_base64")
        private String imageBase64;
        private String msg;
    }
}
