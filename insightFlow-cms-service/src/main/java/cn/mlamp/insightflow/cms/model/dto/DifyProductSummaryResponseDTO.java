package cn.mlamp.insightflow.cms.model.dto;

import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class DifyProductSummaryResponseDTO {
    @Schema(description = "品牌", required = true)
    private String brand;

    @Schema(description = "产品名称", required = true)
    private String productName;

    @Schema(description = "卖点", required = true)
    private String sellingPoint;

    public static DifyProductSummaryResponseDTO buildByText(String text) {
        if (StrUtil.isBlank(text)) {
            return null;
        }
        var dto = new DifyProductSummaryResponseDTO();
        String[] lines = text.split("\\r?\\n");
        for (String line : lines) {
            if (line.contains("品牌")) {
                var brand = line.substring(line.indexOf("：") + 1).trim();
                dto.setBrand(brand);
            } else if (line.contains("商品名")) {
                var productName = line.substring(line.indexOf("：") + 1).trim();
                dto.setProductName(productName);
            } else if (line.contains("卖点")) {
                var sellingPoint = line.substring(line.indexOf("：") + 1).trim();
                dto.setSellingPoint(sellingPoint);
            }
        }
        return dto;
    }
}
