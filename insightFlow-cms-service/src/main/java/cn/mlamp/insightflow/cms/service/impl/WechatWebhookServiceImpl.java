package cn.mlamp.insightflow.cms.service.impl;

import cn.mlamp.insightflow.cms.service.WechatWebhookService;
import cn.mlamp.insightflow.cms.util.HttpUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 微信企业号webhook服务实现类
 *
 * <AUTHOR>
 * @since 2024-10-15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WechatWebhookServiceImpl implements WechatWebhookService {

    @Value("${wechat.webhook.url:https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=ab8c98a8-36bb-4ad9-8ce7-e665cb94415c}")
    private String webhookUrl;

    /**
     * 发送文本消息到微信企业号
     *
     * @param content 消息内容
     * @return 是否发送成功
     */
    @Override
    public boolean sendTextMessage(String content) {
        try {
            Map<String, Object> contentMap = new HashMap<>();
            contentMap.put("content", content);

            Map<String, Object> messageMap = new HashMap<>();
            messageMap.put("msgtype", "text");
            messageMap.put("text", contentMap);

            log.info("发送文本消息到微信webhook，内容: {}", content);
            
            // 使用项目的HttpUtil工具类发送请求
            Map<String, String> headerMap = new HashMap<>();
            headerMap.put("Content-Type", "application/json");
            
            Map<String, Object> response = HttpUtil.postJsonParams(
                webhookUrl, 
                headerMap, 
                messageMap, 
                new TypeReference<Map<String, Object>>() {}
            );
            
            log.info("微信webhook响应: {}", response);
            
            return response != null && response.containsKey("errcode") && response.get("errcode").equals(0);
        } catch (Exception e) {
            log.error("发送文本消息到微信webhook失败", e);
            return false;
        }
    }

    /**
     * 发送markdown消息到微信企业号
     *
     * @param content markdown内容
     * @return 是否发送成功
     */
    @Override
    public boolean sendMarkdownMessage(String content) {
        try {
            Map<String, Object> contentMap = new HashMap<>();
            contentMap.put("content", content);

            Map<String, Object> messageMap = new HashMap<>();
            messageMap.put("msgtype", "markdown");
            messageMap.put("markdown", contentMap);

            log.info("发送markdown消息到微信webhook，内容长度: {}", content.length());
            
            // 使用项目的HttpUtil工具类发送请求
            Map<String, String> headerMap = new HashMap<>();
            headerMap.put("Content-Type", "application/json");
            
            Map<String, Object> response = HttpUtil.postJsonParams(
                webhookUrl, 
                headerMap, 
                messageMap, 
                new TypeReference<Map<String, Object>>() {}
            );
            
            log.info("微信webhook响应: {}", response);
            
            return response != null && response.containsKey("errcode") && response.get("errcode").equals(0);
        } catch (Exception e) {
            log.error("发送markdown消息到微信webhook失败", e);
            return false;
        }
    }
}
