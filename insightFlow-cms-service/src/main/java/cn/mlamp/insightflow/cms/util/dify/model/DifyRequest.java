package cn.mlamp.insightflow.cms.util.dify.model;//package cn.mlamp.insightflow.cms.util.dify.model;


import lombok.Builder;
import lombok.Data;

import java.util.Map;

@Builder
@Data
public class DifyRequest {
    private String appKey;
    private Map<String, Object> inputsParams;
    private String query;
    private String fileName;
    private byte[] fileBytes;
    private String fileType;
    private String baseUrl;
    private String observationId;
    private ViduImageFile[] files;




}
