package cn.mlamp.insightflow.cms.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class TaskDetailMagicRequest {

    @Schema(description = "任务id", required = true)
    private Integer taskId;

    @Schema(description = "分镜id", required = true)
    private Integer storyboardId;

    @Schema(description = "内容名称", required = true)
    private String contentKey;

    @Schema(description = "内容建议", required = false)
    private String contentValue;

    private Integer userId;
    private Integer tenantId;
}
