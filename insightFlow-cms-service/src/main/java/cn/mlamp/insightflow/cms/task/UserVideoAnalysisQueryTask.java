package cn.mlamp.insightflow.cms.task;

import cn.mlamp.insightflow.cms.config.TaskConfig;
import cn.mlamp.insightflow.cms.entity.CmsDocumentInfo;
import cn.mlamp.insightflow.cms.enums.*;
import cn.mlamp.insightflow.cms.model.query.AnalysisVideoQueryRequest;
import cn.mlamp.insightflow.cms.model.vo.AnalysisVideoResultVO;
import cn.mlamp.insightflow.cms.service.CmsPullTaskDedupedDataService;
import cn.mlamp.insightflow.cms.service.ICmsDocumentInfoService;
import cn.mlamp.insightflow.cms.service.IVideoInfoService;
import cn.mlamp.insightflow.cms.strategy.video.create.AnalysisVideoStrategyMap;
import cn.mlamp.insightflow.cms.util.FileDownloadUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
@AllArgsConstructor
public class UserVideoAnalysisQueryTask {

    public static final String VIDEO_ANALYSIS_USER_KEY = "videoAnalysisJob.analysis.user";

    private final StringRedisTemplate stringRedisTemplate;

    private final ICmsDocumentInfoService documentInfoService;

    private final CmsPullTaskDedupedDataService pullTaskDedupedDataService;

    private final IVideoInfoService videoInfoService;

    private final TaskConfig taskConfig;

    // 查询用户视频分析结果（document表状态为ANALYZING的是否成功）
    // 1.如果是本地上传，文件在minio中，不需要签名下载视频到本地。2.如果是url只需要拼接下载地址
    @Scheduled(cron = "0 0/3 * * * ?")
    public void userVideoAnalysisQuery() {
        if (taskConfig.isLocal()) {
            return;
        }
        // 分布式锁(防止同一时间多台服务器重复触发)，保证每次定时任务只有一台服务器在执行
        boolean result = Boolean.TRUE.equals(stringRedisTemplate.opsForValue().setIfAbsent("videoAnalysisJob-user:",
                "ImSyncJob-timer", 150, TimeUnit.SECONDS));
        if (result) {
            documentAnalysisJobQuery();
            stringRedisTemplate.delete("videoAnalysisJob-user");
        } else {
            log.warn("用户视频查询job，多台服务器，防止同一个任务重复执行");
        }
    }

    public void documentAnalysisJobQuery() {
        List<CmsDocumentInfo> analysisList = documentInfoService.listByUrlStatusIn(List.of(DocumentStatusEnum.ANALYZING));
        for (CmsDocumentInfo cmsDocumentInfo : analysisList) {
            // 视频处理超过2小时 直接判定失败
            if (cmsDocumentInfo.getCreateTime().getTime() + 2 * 60 * 60 * 1000 < System.currentTimeMillis()) {
                log.info("视频处理超过12小时，直接判定失败,esId:{}", cmsDocumentInfo.getEsId());
                updateAnalysisFailedByEsId(cmsDocumentInfo.getEsId(), cmsDocumentInfo.getId(), "任务超时");
                continue;
            }
            boolean result = Boolean.TRUE.equals(stringRedisTemplate.opsForValue().setIfAbsent(
                    VIDEO_ANALYSIS_USER_KEY + cmsDocumentInfo.getEsId(), "ImSyncJob-timer", 200, TimeUnit.SECONDS));
            if (result) {
                videoResult(cmsDocumentInfo);
                stringRedisTemplate.delete(VIDEO_ANALYSIS_USER_KEY + cmsDocumentInfo.getEsId());
            }
        }
    }

    private void videoResult(CmsDocumentInfo cmsDocumentInfo) {
        try {
            // 查询视频分析结果
            AnalysisVideoQueryRequest analysisVideoQueryRequest = new AnalysisVideoQueryRequest();
            analysisVideoQueryRequest.setEsId(cmsDocumentInfo.getEsId());
            String localFilePath = null;
            if (UploadSourceTypeEnum.from_url.name().equals(cmsDocumentInfo.getSourceType())) { // 如果是url，文件后缀为mp4
                String fileName = cmsDocumentInfo.getEsId() + ".mp4";
                localFilePath = FileDownloadUtil.getPath(fileName);
            } else if (UploadSourceTypeEnum.from_local.name().equals(cmsDocumentInfo.getSourceType())) { // 如果是本地上传，文件后缀直接取
                String filename = cmsDocumentInfo.getEsId() + "." + cmsDocumentInfo.getDocType();
                localFilePath = FileDownloadUtil.getPath(filename);
            }
            analysisVideoQueryRequest.setLocalFilePath(localFilePath);
            analysisVideoQueryRequest.setTypeName(AnalysisVideoTypeEnum.UPLOAD_VIDEO.getVideoType());
            AnalysisVideoResultVO analysisVideoResultVO = AnalysisVideoStrategyMap.queryResult(analysisVideoQueryRequest);

            if (analysisVideoResultVO.getStatus() == VideoInfoStatusEnum.SUCCESS.getCode()) {
                log.info("用户上传视频分析成功,esId:{}", cmsDocumentInfo.getEsId());
                updateAnalysisSuccessByEsId(cmsDocumentInfo.getEsId());
                FileDownloadUtil.deleteFile(localFilePath);
                return;
            }

            if (analysisVideoResultVO.getStatus() == VideoInfoStatusEnum.ERROR.getCode()) {
                updateAnalysisFailedByEsId(cmsDocumentInfo.getEsId(), null, null);
                //删除视频
                FileDownloadUtil.deleteFile(localFilePath);
            }

        } catch (Exception e) {
            log.error("用户上传视频分析失败", e);
            updateAnalysisFailedByEsId(cmsDocumentInfo.getEsId(), null, null);
        }
    }

    private void updateAnalysisSuccessByEsId(String esId) {
        documentInfoService.updateStatusByEsId(esId, DocumentStatusEnum.COMPLETED);
        pullTaskDedupedDataService.updateStatusByEsId(esId, AnalysisStatusEnum.ANALYZING);
    }

    private void updateAnalysisFailedByEsId(String esId, Integer docId, String errorMsg) {
        documentInfoService.updateStatusByEsId(esId, DocumentStatusEnum.FAIL);
        pullTaskDedupedDataService.updateStatusByEsId(esId, AnalysisStatusEnum.ERROR);
        if (docId != null) {
            videoInfoService.updateStatusByDocId(docId, VideoInfoStatusEnum.ERROR, errorMsg);
        }
    }

}
