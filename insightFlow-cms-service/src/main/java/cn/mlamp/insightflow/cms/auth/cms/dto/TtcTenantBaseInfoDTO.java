package cn.mlamp.insightflow.cms.auth.cms.dto;

import lombok.Data;

import java.util.List;

/**
 * ttc 租户基本信息
 * 
 * <AUTHOR>
 * @since 2023/4/20 11:39 下午
 */
@Data
public class TtcTenantBaseInfoDTO {

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 创建时间 例:2022.05.07 10:11:39
     */
    private String createTime;

    /**
     * 更新时间 例:2022.05.07 10:13:06
     */
    private String updateTime;

    /**
     * 过期时间 例:2023.05.05
     */
    private String expiredDate;

    /**
     * 管理员邮箱
     */
    private List<String> adminEmail;

    /**
     * 状态（0是有效，1是无效）
     */
    private Integer status;

    /**
     * 租户id
     */
    private Integer tenantId;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 租户标签
     */
    private List<String> tenantTagList;
}
