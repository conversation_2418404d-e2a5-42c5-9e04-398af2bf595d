package cn.mlamp.insightflow.cms.service;

import com.baomidou.mybatisplus.extension.service.IService;

import cn.mlamp.insightflow.cms.entity.CmsAsyncTask;
import cn.mlamp.insightflow.cms.model.dto.CmsAsyncTaskDTO;

import java.util.List;
import java.util.Map;

public interface CmsAsyncTaskService extends IService<CmsAsyncTask> {

    CmsAsyncTask save(String db_unique_id, Integer videoInfoType, Integer taskStatus, String esId, String taskType);

    CmsAsyncTask save(String db_unique_id, Integer videoInfoType, Integer taskStatus, String esId, String taskType,String data);


    Boolean  AllStatusSuccess(String esId,Integer videoInfoType);


}