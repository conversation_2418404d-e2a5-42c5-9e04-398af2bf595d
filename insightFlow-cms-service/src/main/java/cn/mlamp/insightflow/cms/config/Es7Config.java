package cn.mlamp.insightflow.cms.config;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2022-03-29 16:24:16
 */
@Configuration
@Getter
public class Es7Config {
    @Value("${es.client.es7.hosts:https://esproxy.mlamp.cn}")
    private String hosts;
    @Value("${es.client.es7.path-prefix:nb-whshdata}")
    private String pathPrefix;
    @Value("${es.client.es7.api-key:=}")
    private String apiKey;
    @Value("${es.client.es7.connect-timeout:5000}")
    private Integer connectTimeout;
    @Value("${es.client.es7.socket-timeout:300000}")
    private Integer socketTimeout;
    @Value("${es.client.es7.connection-request-timeout:10000}")
    private Integer connectionRequestTimeout;
    @Value("${es.client.es7.max-conn-total:30}")
    private Integer maxConnTotal;
    @Value("${es.client.es7.max-conn-per-route:10}")
    private Integer maxConnPerRoute;
    @Value("${es.client.es7.keep-alive-strategy-minute:3}")
    private Integer keepAliveStrategyMinute;
    @Value("${es.client.es7.bulk-timeout:120000}")
    private Long bulkTimeout;
}
