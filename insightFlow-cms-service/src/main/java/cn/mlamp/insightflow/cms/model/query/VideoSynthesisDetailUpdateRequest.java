package cn.mlamp.insightflow.cms.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 视频合成任务详情更新请求
 * 
 * <AUTHOR>
 * @date 2025-05-20
 */
@Data
public class VideoSynthesisDetailUpdateRequest {

    @Schema(description = "任务ID", required = true)
    private Integer taskId;

    @Schema(description = "任务详情数据（JSON格式）", required = true)
    private String taskInfo;
}
