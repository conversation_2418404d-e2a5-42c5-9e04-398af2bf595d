package cn.mlamp.insightflow.cms.model.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * @Author: husuper
 * @CreateTime: 2025-03-19
 */
@Data
public class VideoFiveGoldDetailVO {

    @Schema(description = "视频ID", required = true)
    private String videoId;

    @Schema(description = "标题", required = false)
    private String title;

    @Schema(description = "品牌", required = false)
    private String brand;

    @Schema(description = "曝光量", required = true)
    private Integer exposure;

    @Schema(description = "分享数", required = true)
    private Integer shares;

    @Schema(description = "评论数", required = true)
    private Integer comments;

    @Schema(description = "点赞数", required = true)
    private Integer likes;

    @Schema(description = "点击数", required = true)
    private Integer clicks;

    @Schema(description = "行业", required = false)
    private String industry;

    @Schema(description = "视频语音识别", required = false)
    private String kwVideoContent;

    @Schema(description = "榜单类型", required = false)
    private String rankingType;

    @Schema(description = "发布时间", required = true)
    private LocalDateTime publishTime;

    @Schema(description = "作者名称", required = false)
    private String authorName;

    @Schema(description = "作者头像", required = false)
    private String authorAvatar;

    @Schema(description = "封面图片", required = false)
    private String coverImage;

    @Schema(description = "时长", required = false)
    private Integer duration;

    @Schema(description = "完成率", required = false)
    private BigDecimal completionRate;

    @Schema(description = "创意分", required = false)
    private Float rating;

    @Schema(description = "产品名称", required = false)
    private String productName;

    @Schema(description = "卖点", required = false)
    private String cellingPoint;

    @Schema(description = "受众人群", required = false)
    private String aimingTribe;
}
