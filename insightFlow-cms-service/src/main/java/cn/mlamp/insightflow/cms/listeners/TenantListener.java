package cn.mlamp.insightflow.cms.listeners;

import cn.hutool.core.collection.CollectionUtil;
import cn.mlamp.insightflow.cms.entity.dam.DamTag;
import cn.mlamp.insightflow.cms.enums.dam.DamTagTypeEnum;
import cn.mlamp.insightflow.cms.events.TenantInitializeEvent;
import cn.mlamp.insightflow.cms.service.dam.IDamPublicTagService;
import cn.mlamp.insightflow.cms.service.dam.IDamTagService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 租户相关事件监听器
 * 用于响应租户创建等事件，执行相应的业务逻辑
 */
@Slf4j
@Component
public class TenantListener {

    @Autowired
    private IDamTagService tagService;

    @Autowired
    private IDamPublicTagService publicTagService;

    /**
     * 监听租户创建事件
     * 当新租户创建时，此方法会被调用
     *
     * @param event 租户创建事件
     */
    @EventListener(TenantInitializeEvent.class)
    public void onTenantInitialize(TenantInitializeEvent event) {
        Integer tenantId = event.getTenantId();
        Integer userId = event.getUserId();
        log.info("租户初始化事件被触发，租户ID: {}", tenantId);

        // 执行租户创建后的初始化操作
        initializeDam(tenantId, userId);
    }

    /**
     * 租户初始化操作
     * 可以在这里添加租户创建后需要执行的初始化逻辑
     * 例如：创建默认目录、设置默认配置等
     *
     * @param tenantId 租户ID
     */
    private void initializeDam(Integer tenantId, Integer userId) {
        try {
            log.info("开始初始化租户DAM资源 {}", tenantId);

            grantTenantTags(tenantId, userId);

            log.info("租户 {} 初始化完成", tenantId);
        } catch (Exception e) {
            log.error("租户 {} 初始化失败", tenantId, e);
            // 可以考虑是否需要进行回滚操作或发送通知
        }
    }

    private void grantTenantTags(Integer tenantId, Integer userId) {
        // 查找当前租户下所有标签
        final List<DamTag> existTags = tagService.list(
                new LambdaQueryWrapper<DamTag>()
                        .eq(DamTag::getTenantId, tenantId)
        );

        if (CollectionUtil.isNotEmpty(existTags)) {
            return;
        }
        // 获取所有公共标签
        var publicTags = publicTagService.list();

        var tags = publicTags.stream().map(item -> {
            var tag = DamTag.fromPublicTag(item);
            tag.setTenantId(tenantId);
            tag.setUserId(userId);
            return tag;
        }).collect(Collectors.toList());

        // 授权当前所有公共标签
        tagService.saveBatch(tags);

        log.info("租户 {} 授权公共标签完成", tenantId);
    }

}
