package cn.mlamp.insightflow.cms.model.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * @Author: husuper
 * @CreateTime: 2025-03-19
 */
@Data
public class VideoInfoResultVO {

    @Schema(description = "视频分析结果Id", required = true)
    private Integer videoInfoReusltId;

    @Schema(description = "视频分析结果类型", required = true)
    private Integer type;

    @Schema(description = "视频分析id", required = true)
    private Integer videoId;

    @Schema(description = "视频分析结果序号索引", required = true)
    private Integer index;

    @Schema(description = "视频分析结果数据,json格式", required = true)
    private String data;



    @Schema(description = "视频分析结果详情数据列表", required = true)
    private List<VideoInfoReusltDetail> videoInfoReusltDetails;


    @Data
    public static class VideoInfoReusltDetail implements Serializable {

        @Schema(description = "1：分镜结果详情", required = true)
        private Integer type;

        @Schema(description = "视频分析结果详情Id", required = true)
        private Integer videoInfoReusltDetailId;

        @Schema(description = "视频分析结果详情数据,json格式", required = true)
        private String data;

    }


}
