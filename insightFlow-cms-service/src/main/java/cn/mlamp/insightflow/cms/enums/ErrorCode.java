package cn.mlamp.insightflow.cms.enums;

import lombok.Getter;

/**
 * 错误码
 * 
 * @link <a href="https://conf.mlamp.cn/pages/viewpage.action?pageId=88708168"/>
 *       接口异常码定义
 * <AUTHOR>
 * @since 2019/9/29
 */
@Getter
public enum ErrorCode implements ResultCode {
    // ---------------------------------------------------------------------||
    // 通用错误码 |
    // ---------------------------------------------------------------------\/
    /**
     * 请求成功
     */
    SUCCESS(200, "success"),
    /**
     * 参数错误
     */
    PARAM_ERROR(400, "error_code.PARAM_ERROR"),

    /**
     * 用户认证失败
     */
    AUTHENTICATION_FAIL(401, "error_code.AUTHENTICATION_FAIL"),

    /**
     * 当前用户的权限发生变更，请重新登录
     */
    PRODUCT_PERMISSION_CHANGED_ERROR(401001, "error_code.PRODUCT_PERMISSION_CHANGED_ERROR"),

    /**
     * 您的账号在别处登录，您已被迫下线
     */
    LOGIN_FROM_A_DIFFERENT_LOCATION_ERROR(401002, "error_code.LOGIN_FROM_A_DIFFERENT_LOCATION_ERROR"),

    /**
     * 授权失败
     */
    AUTHORIZATION_FAIL(403, "error_code.AUTHORIZATION_FAIL"),

    /**
     * 资源未找到
     */
    NOT_FOUND(404, "error_code.NOT_FOUND"),

    /**
     * 用户名为空
     */
    USERNAME_EMPTY(406, "error_code.USERNAME_EMPTY"),

    /**
     * 用户名或密码错误
     */
    USERNAME_PASSWORD_ERROR(407, "error_code.USERNAME_PASSWORD_ERROR"),

    /**
     * 租户信息为空
     */
    TENANT_EMPTY(408, "error_code.TENANT_EMPTY"),

    /**
     * 当前用户未绑定角色信息
     */
    NOT_BIND_ROLE(409, "error_code.NOT_BIND_ROLE"),

    /**
     * 当前权限为空
     */
    PERMISSION_EMPTY(411, "error_code.PERMISSION_EMPTY"),

    /**
     * 登录已过期
     */
    LOGIN_INVALID_ERROR(412, "error_code.LOGIN_INVALID_ERROR"),

    /**
     * 密码已过期
     */
    PASSWORD_EXPIRE(413, "error_code.PASSWORD_EXPIRE"),

    /**
     * 无产品权限
     */
    USER_NO_PRODUCT_PERMISSION_ERROR(414, "error_code.USER_NO_PRODUCT_PERMISSION_ERROR"),

    /**
     * 禁用
     */
    USER_FORBIDDEN_PERMISSION_ERROR(415, "error_code.USER_FORBIDDEN_PERMISSION_ERROR"),

    /**
     * passport登录过于频繁，已被限制，稍后再试
     */
    TTC_LOGIN_OFTEN_ERROR(416, "error_code.TTC_LOGIN_OFTEN_ERROR"),

    /**
     * passport已将账号冻结
     */
    TTC_LOGIN_ACCOUNT_FORBIDDEN_ERROR(417, "error_code.TTC_LOGIN_ACCOUNT_FORBIDDEN_ERROR"),

    /**
     * 参数中的域名不在白名单中
     */
    DOMAIN_WHITE_LIST_NOT_MATCHED_ERROR(418, "error_code.DOMAIN_WHITE_LIST_NOT_MATCHED_ERROR"),

    /**
     * 参数错误，包含空值
     */
    PARAM_NULL_ERROR(400001, "error_code.PARAM_NULL_ERROR"),

    /**
     * 参数错误，超出合法范围
     */
    PARAM_RANGE_ERROR(400002, "error_code.PARAM_RANGE_ERROR"),

    /**
     * 未知异常
     */
    UNKNOW_ERROR(500, "error_code.COMMON_ERROR"),

    /**
     * 数据库异常
     */
    DATABASE_ERROR(500100, "error_code.DATABASE_ERROR"),

    /**
     * 数据库服务不可达
     */
    DATABASE_UNAVAILABLE_ERROR(500101, "error_code.DATABASE_UNAVAILABLE_ERROR"),

    /**
     * Redis通用错误
     */
    REDIS_ERROR(500200, "error_code.REDIS_ERROR"),

    /**
     * Redis服务不可达
     */
    REDIS_UNAVAILABLE_ERROR(500201, "error_code.REDIS_UNAVAILABLE_ERROR"),

    /**
     * Redis锁失败
     */
    REDIS_LOCK_ERROR(500202, "error_code.REDIS_LOCK_ERROR"),

    /**
     * es服务器异常
     */
    ES_SERVER_ERROR(500300, "error_code.ES_SERVER_ERROR"),

    /**
     * ES服务器PING超时
     */
    ES_SERVER_UNAVAILABLE_ERROR(500301, "error_code.ES_SERVER_UNAVAILABLE_ERROR"),

    /**
     * ES服务器负载过高
     */
    ES_SERVER_LOAD_HIGH(500303, "error_code.ES_SERVER_LOAD_HIGH"),

    /**
     * Es查询文本过长
     */
    RAW_DATA_ENTITY_TOO_LONG(500304, "error_code.RAW_DATA_ENTITY_TOO_LONG"),

    /**
     * 算法计算失败
     */
    ALGORITHM_ERROR(500400, "error_code.ALGORITHM_ERROR"),

    /**
     * 业务错误
     */
    BUSINESS_ERROR(500500, "error_code.BUSINESS_ERROR"),

    /**
     * HttpClient异常
     */
    HTTPCLIENT_ERROR(500600, "error_code.HTTPCLIENT_ERROR"),

    /**
     * 获取email token失败
     */
    EMAIL_TOKEN_ERROR(500601, "error_code.EMAIL_TOKEN_ERROR"),

    /**
     * 发送邮件失败
     */
    MAIL_SEND_ERROR(500602, "error_code.MAIL_SEND_ERROR"),
    /**
     * 邮件附件上传失败
     */
    MAIL_ATTACHMENT_UPLOAD_ERROR(500603, "error_code.MAIL_ATTACHMENT_UPLOAD_ERROR"),

    /**
     * 无权操作：例如跨租户操作方案和分组
     */
    NO_OPERATE_PERMISSION(500604, "error_code.NO_OPERATE_PERMISSION"),

    JWT_EXPIRED_ERROR(500700, "error_code.JWT_EXPIRED_ERROR"),

    JWT_PARSE_ERROR(500710, "error_code.JWT_PARSE_ERROR"),

    EMAIL_TEMPLATE_TRANSFORM_ERROR(500800, "error_code.EMAIL_TEMPLATE_TRANSFORM_ERROR"),

    // ---------------------------------------------------------------------||
    // 业务异常码 |
    // ---------------------------------------------------------------------\/
    /**
     * 参数校验错误(10000-19999) 10000 前端直接展示提示信息
     */
    GROUPNAMESIZE_ERROR(10000, "error_code.GROUPNAMESIZE_ERROR"),

    GROUPNAMESIZE0_ERROR(10000, "error_code.GROUPNAMESIZE0_ERROR"),

    TARGETNAMESIZE_ERROR(10000, "error_code.TARGETNAMESIZE_ERROR"),

    TARGETEMPTY_ERROR(10000, "error_code.TARGETEMPTY_ERROR"),

    TARGETNAMESIZE0_ERROR(10000, "error_code.TARGETNAMESIZE0_ERROR"),

    GROUPNAMEEXIST_ERROR(10000, "error_code.GROUPNAMEEXIST_ERROR"),

    TARGETNAMEEXIST_ERROR(10000, "error_code.TARGETNAMEEXIST_ERROR"),

    GROUPCOUNT_ERROR(10000, "error_code.GROUPCOUNT_ERROR"),

    TARGETOUTSCOUNTGT_OUT_OF_BOUND_ERROR(10000, "error_code.TARGETOUTSCOUNTGT_OUT_OF_BOUND_ERROR"),

    TARGETSCOUNTGT_OUT_OF_BOUND_ERROR(10000, "error_code.TARGETSCOUNTGT_OUT_OF_BOUND_ERROR"),

    TARGETGROUPIDSCOUNTGT15_ERROR(10000, "error_code.TARGETGROUPIDSCOUNTGT15_ERROR"),

    GROUPDELETED_ERROR(10000, "error_code.GROUPDELETED_ERROR"),

    TARGETDELETED_ERROR(10000, "error_code.TARGETDELETED_ERROR"),

    HAVETARGET_ERROR(10000, "error_code.HAVETARGET_ERROR"),

    ANALYSIS_OBJECT_ID_ERROR(10000, "error_code.ANALYSIS_OBJECT_ID_ERROR"),
    ANALYSIS_OBJECT_NAME_HAVE_CHANGED(10000, "error_code.ANALYSIS_OBJECT_NAME_HAVE_CHANGED"),
    ANALYSIS_IDS_NOT_IN_TENANT_ID_ERROR(10000, "error_code.ANALYSIS_IDS_NOT_IN_TENANT_ID_ERROR"),

    PLATFORM_EMPTY(10000, "error_code.PLATFORM_EMPTY"),
    AUDIENCE_PLATFORM_ERROR(10000, "error_code.AUDIENCE_PLATFORM_ERROR"),

    TAG_DELETED_ERROR(10000, "error_code.TAG_DELETED_ERROR"),

    /**
     * 不会返回给页面，除非直接走接口
     */
    TARGETANYSCOUNTGT_ERROR(10000, "error_code.TARGETANYSCOUNTGT_ERROR"),

    GROUPIDSNOTINTENANTID_ERROR(10000, "error_code.GROUPIDSNOTINTENANTID_ERROR"),

    TARGETUPDATEISNOTTENANTID_ERROR(10000, "error_code.TARGETUPDATEISNOTTENANTID_ERROR"),

    /**
     * 一个通用的兜底错误
     */
    COMMON_ERROR(20000, "error_code.COMMON_ERROR"),

    /**
     * 活动分析相关(25000-25999)
     */
    ACTIVITY_KOL_NOT_SUPPORT_MULTI_PLATFORM(25002, "error_code.ACTIVITY_KOL_NOT_SUPPORT_MULTI_PLATFORM"),

    /**
     * 配额相关错误(26000-26999)
     */
    GET_QUOTA_ERROR(26000, "error_code.GET_QUOTA_ERROR"),
    GET_QUOTA_REJECT_ERROR(26001, "error_code.GET_QUOTA_REJECT_ERROR"),
    SEARCH_QUOTA_OVERFLOW_ERROR(26002, "error_code.SEARCH_QUOTA_OVERFLOW_ERROR"),
    EXPORT_QUOTA_OVERFLOW_ERROR(26003, "error_code.EXPORT_QUOTA_OVERFLOW_ERROR"),
    OPINION_ANALYSIS_QUOTA_OVERFLOW_ERROR(26004, "error_code.OPINION_ANALYSIS_QUOTA_OVERFLOW_ERROR"),

    /**
     * kol分析相关(27000-27999) 获取
     */
    PLATFORM_INVALID(27001, "error_code.PLATFORM_INVALID"),
    QUERY_RANGE_OUT_OF_BOUND(27002, "error_code.QUERY_RANGE_OUT_OF_BOUND"),

    /**
     * 智库报告(28000-28999) 获取
     */
    DATA_FILE_SIZE_TOO_LARGE(28001, "error_code.DATA_FILE_SIZE_TOO_LARGE"),
    SLAB_REPORT_FILE_FORMATTER_INVALID(28002, "error_code.SLAB_REPORT_FILE_FORMATTER_INVALID"),
    SLAB_DUPLICATE_REPORT_TITLE_ERROR(28003, "error_code.SLAB_DUPLICATE_REPORT_TITLE_ERROR"),
    SLAB_REPORT_NOT_EXIST_ERROR(28004, "error_code.SLAB_REPORT_NOT_EXIST_ERROR"),
    SLAB_REPORT_TITLE_FORMATTER_ERROR(28005, "error_code.SLAB_REPORT_TITLE_FORMATTER_ERROR"),
    SLAB_REPORT_TITLE_NULL_ERROR(28006, "error_code.SLAB_REPORT_TITLE_NULL_ERROR"),
    SLAB_COVER_TITLE_NULL_ERROR(28007, "error_code.SLAB_COVER_TITLE_NULL_ERROR"),

    /**
     * 方案保存相关
     */
    PLAN_GROUP_SIZE_EXCEED(29001, "error_code.PLAN_GROUP_SIZE_EXCEED"),
    PLAN_QUOTA_EXCEED(29002, "error_code.PLAN_QUOTA_EXCEED"), PLAN_NOT_EXISTS(29003, "error_code.PLAN_NOT_EXISTS"),
    PLAN_GROUP_NOT_EXISTS(29003, "error_code.PLAN_GROUP_NOT_EXISTS"),
    PLAN_GROUP_DELETED_ERROR(29004, "error_code.PLAN_GROUP_DELETED_ERROR"),
    PLAN_DELETED_ERROR(29005, "error_code.PLAN_DELETED_ERROR"),
    PLAN_SEARCH_PARAM_EMPTY_ERROR(29006, "error_code.PLAN_SEARCH_PARAM_EMPTY_ERROR"),
    PLAN_SEARCH_ALERT_PARAM_ERROR(29007, "error_code.PLAN_SEARCH_ALERT_PARAM_ERROR"),
    PLAN_SEARCH_ALERT_EMAIL_ERROR(29008, "error_code.PLAN_SEARCH_ALERT_EMAIL_ERROR"),

    // 602 查询ES
    METADATA_ERROR_DB_PERMISSIONS(601403, "没有舆情数据库权限"), METADATA_SERVER_ERROR(601405, "调用字段管理中心失败"),
    SEARCH_ERROR(602000, "查询ES失败"), SEARCH_CONNECTOR_NOT_FOUND(602001, "未获取连接配置信息"), SEARCH_LOCK_FAILED(602003, "加锁失败"),
    SEARCH_ELASTICSEARCH_CLIENT_FAILED(602004, "构造客户端失败"), SEARCH_CONVERT_FAILED(602005, "数据转换失败"),
    SEARCH_NOT_GET_DICTIONARIES(602012, "未获取到字典数据"), SEARCH_START_IS_AFTER_END_ERROR(602020, "开始时间晚于结束时间"),

    /**
     * 聆媒洞察
     */
    AIGC_UNKNOWN_ERROR(701001, "error_code.AIGC_UNKNOWN_ERROR"),
    AIGC_ALGORITHM_NO_TARGET(701002, "error_code.AIGC_ALGORITHM_NO_TARGET_ERROR"),
    AIGC_NO_ES_INDUSTRY_PERMISSION(701003, "error_code.AIGC_NO_ES_INDUSTRY_PERMISSION"),
    AIGC_ALGORITHM_NO_PERMISSION_ERROR(701004, "error_code.AIGC_ALGORITHM_NO_PERMISSION_ERROR"),
    AIGC_DATA_BE_ZERO_ERROR(701005, "error_code.AIGC_DATA_BE_ZERO_ERROR"),
    AIGC_CONDITION_ERROR(701006, "error_code.AIGC_CONDITION_ERROR"),

    /**
     * 观点分析
     *
     */
    OPINION_ANALYSIS_SENTIMENT_ERROR(801001, "error_code.OPINION_ANALYSIS_SENTIMENT_ERROR"),
    OPINION_ANALYSIS_POST_ERROR(801002, "error_code.OPINION_ANALYSIS_POST_ERROR"),

    // ---------------------------------------------------------------------||
    // 前端特殊处理异常码 |
    // ---------------------------------------------------------------------\/
    /**
     * 您的组织刚刚发生了变化，已为您更新。
     */
    NEED_RETRY_REFRESH(600003, "error_code.NEED_RETRY_REFRESH"),

    ;

    private Integer code;
    private String message;

    ErrorCode(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public ErrorCode detail(String message) {
        this.message = message;
        return this;
    }
}
