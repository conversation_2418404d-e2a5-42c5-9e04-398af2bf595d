package cn.mlamp.insightflow.cms.enums;

import lombok.Getter;

/**
 * 视频合成任务状态
 * <AUTHOR>
 * @date 2025-05-20
 */
@Getter
public enum VideoSynthesisStatusEnum {

    /**
     * 排队中
     */
    QUEUING(0, "排队中"),

    /**
     * 下载中
     */
    DOWNLOADING(1, "下载中"),

    /**
     * 合成中
     */
    SYNTHESIZING(2, "合成中"),

    /**
     * 上传中
     */
    UPLOADING(3, "上传中"),

    /**
     * 已完成
     */
    COMPLETED(4, "已完成"),

    /**
     * 失败
     */
    FAILED(5, "失败"),

    /**
     * 已取消
     */
    CANCELED(6, "已取消"),
    ;

    private final Integer code;
    private final String msg;

    VideoSynthesisStatusEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static VideoSynthesisStatusEnum getByCode(Integer code) {
        for (VideoSynthesisStatusEnum statusEnum : VideoSynthesisStatusEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum;
            }
        }
        return null;
    }
}
