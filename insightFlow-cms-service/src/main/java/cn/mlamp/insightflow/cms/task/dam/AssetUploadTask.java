package cn.mlamp.insightflow.cms.task.dam;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IORuntimeException;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import cn.mlamp.insightflow.cms.common.file.IS3FlowService;
import cn.mlamp.insightflow.cms.common.redis.LockService;
import cn.mlamp.insightflow.cms.config.TaskConfig;
import cn.mlamp.insightflow.cms.config.properties.DamOSSProperties;
import cn.mlamp.insightflow.cms.entity.dam.*;
import cn.mlamp.insightflow.cms.enums.TokenTaskTypeEnum;
import cn.mlamp.insightflow.cms.enums.dam.DamDirectoryTypeEnum;
import cn.mlamp.insightflow.cms.enums.dam.DamTaskStatusEnum;
import cn.mlamp.insightflow.cms.model.dto.dam.DamAssetUploadTaskArg;
import cn.mlamp.insightflow.cms.service.TokenUseDetailService;
import cn.mlamp.insightflow.cms.service.dam.*;
import cn.mlamp.insightflow.cms.strategy.handle.VideoRecognition2Handle;
import cn.mlamp.insightflow.cms.strategy.handle.VideoRecognition2Handle.SceneDecodingRequest;
import cn.mlamp.insightflow.cms.util.FunctionUtils;
import cn.mlamp.insightflow.cms.util.VideoUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.util.retry.RetryBackoffSpec;
import reactor.util.retry.RetrySpec;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class AssetUploadTask {

    @Autowired
    private LockService lockService;

    @Autowired
    private IDamDirectoryService directoryService;

    @Autowired
    private IDamAssetService assetService;

    @Autowired
    private IDamTagService tagService;

    @Autowired
    private IDamTagValueService tagValueService;

    @Autowired
    @Lazy
    private IDamAssetUploadTaskService taskService;

    @Autowired
    private IDamAssetUploadTaskDetailService assetUploadTaskDetailService;

    @Autowired
    private TokenUseDetailService tokenUseDetailService;

    @Autowired
    private IS3FlowService s3Service;

    @Autowired
    private VideoRecognition2Handle videoRecognitionService;

    @Autowired
    private DamOSSProperties ossProperties;

    @Autowired
    private TaskConfig taskConfig;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    @Qualifier("assetUploadTaskThreadExecutor")
    private ThreadPoolExecutor assetUploadTaskThreadExecutor;

    private static final Integer BUFFER_SIZE = 5;

    protected int availableTaskCount() {
        // 如果线程池比较繁忙，则不执行
        if (assetUploadTaskThreadExecutor.getActiveCount() <= assetUploadTaskThreadExecutor.getMaximumPoolSize()) {
            return BUFFER_SIZE;
        }
        // 如果队列大小比较多，则不执行
        if (assetUploadTaskThreadExecutor.getQueue().size() >= assetUploadTaskThreadExecutor.getMaximumPoolSize() + BUFFER_SIZE) {
            log.warn("素材上传任务队列繁忙，跳过");
            return 0;
        }
        return assetUploadTaskThreadExecutor.getMaximumPoolSize() - assetUploadTaskThreadExecutor.getActiveCount() + BUFFER_SIZE;
    }

    protected String getLockKey(Integer taskId) {
        return "asset-upload-task-" + taskId;
    }

    // 每30秒执行一次, 用于查漏补缺
    @Scheduled(cron = "0 0/30 * * * ?")
    public void schedule() {
        if (taskConfig.isLocal()) {
            return;
        }
        // 获取所有正在运行的任务
        final List<DamAssetUploadTask> runningTasks = taskService.list(new LambdaQueryWrapper<DamAssetUploadTask>()
                .eq(DamAssetUploadTask::getStatus, DamTaskStatusEnum.RUNNING)
                .eq(DamAssetUploadTask::getIsDeleted, false));

        // 获取更新时间 - 当前时间 >= 30分钟
        final List<DamAssetUploadTask> needRerunTasks = runningTasks.stream()
                .filter(task -> {
                    final Date updateTime = task.getUpdateTime();
                    final long diff = System.currentTimeMillis() - updateTime.getTime();
                    return diff >= 30 * 60 * 1000;
                })
                .filter(task -> !lockService.getLock(getLockKey(task.getId())).isLocked())
                .toList();
        if (CollectionUtil.isNotEmpty(needRerunTasks)) {
            // 更新为待调度状态
            taskService.update(new LambdaUpdateWrapper<DamAssetUploadTask>()
                    .in(DamAssetUploadTask::getId, needRerunTasks.stream().map(DamAssetUploadTask::getId).toList())
                    .eq(DamAssetUploadTask::getIsDeleted, false)
                    .eq(DamAssetUploadTask::getStatus, DamTaskStatusEnum.RUNNING)
                    .set(DamAssetUploadTask::getStatus, DamTaskStatusEnum.PENDING));
        }
        // 获取所有未完成的任务
        final List<DamAssetUploadTask> queueTasks = taskService.list(new LambdaQueryWrapper<DamAssetUploadTask>()
                .eq(DamAssetUploadTask::getStatus, DamTaskStatusEnum.PENDING)
                .eq(DamAssetUploadTask::getIsDeleted, false));
        if (CollectionUtil.isEmpty(queueTasks)) {
            return;
        }
        // 如果线程池比较繁忙，则不执行
        final int availableTaskCount = availableTaskCount();
        if (availableTaskCount <= 0) {
            return;
        }

        for (int i = 0; i < availableTaskCount; i++) {
            final DamAssetUploadTask task = queueTasks.get(i);
            run(task.getId());
        }
    }

    public void submit(Integer taskId) {
        // 判断当前是否忙碌, 如果忙碌则不提交等待调度, 如果不忙碌则提交
        if (availableTaskCount() <= 0) {
            return;
        }
        assetUploadTaskThreadExecutor.submit(() -> run(taskId));
    }

    public void run(Integer taskId) {
        final String lockKey = getLockKey(taskId);
        if (!lockService.getLock(lockKey).tryLock()) {
            log.warn("任务 " + taskId + " 正在运行中，跳过...");
            return;
        }
        try {
            _run(taskId);
            completeTask(taskId);
        } catch (Exception e) {
            log.error("素材上传任务失败", e);
            errorTask(taskId, e);
            errorAllAssetUploadTask(taskId, e);
        }
    }

    private void _run(Integer taskId) throws IOException {
        final DamAssetUploadTask task = taskService.getById(taskId);
        if (task == null) {
            return;
        }
        if (Objects.equals(task.getStatus(), DamTaskStatusEnum.RUNNING)) {
            log.warn("素材上传任务: {} 正在执行，跳过", taskId);
            return;
        }
        if (Objects.equals(task.getStatus(), DamTaskStatusEnum.COMPLETED)) {
            log.warn("素材上传任务: {} 已完成，跳过", taskId);
            return;
        }
        if (Objects.equals(task.getStatus(), DamTaskStatusEnum.FAILED)) {
            log.warn("素材上传任务: {} 取消，跳过", taskId);
            return;
        }
        final DamAssetUploadTaskArg taskArg = objectMapper.readValue(task.getTaskArg(), DamAssetUploadTaskArg.class);
        final Map<Integer, DamAssetUploadTaskArg.Asset> assetId2ArgAssetMap = taskArg.getAssets().stream()
                .collect(Collectors.toMap(DamAssetUploadTaskArg.Asset::getAssetId, Function.identity()));

        final List<DamAssetUploadTaskDetail> taskDetails = assetUploadTaskDetailService.list(
                new LambdaQueryWrapper<>(DamAssetUploadTaskDetail.class)
                        .eq(DamAssetUploadTaskDetail::getTaskId, taskId));
        final List<Integer> assetIds = taskDetails.stream().map(DamAssetUploadTaskDetail::getAssetId).toList();
        if (CollectionUtil.isEmpty(assetIds)) {
            log.warn("素材上传任务: {} 跳过，素材ID为空", taskId);
            completeTask(taskId);
            return;
        }
        final Map<Integer, DamAssetUploadTaskDetail> assetIdTaskDetailMap = taskDetails.stream()
                .collect(Collectors.toMap(DamAssetUploadTaskDetail::getAssetId, Function.identity()));
        final List<DamAsset> assets = assetService.list(
                new LambdaQueryWrapper<DamAsset>()
                        .in(DamAsset::getId, assetIds)
                        .eq(DamAsset::getIsDeleted, false)
                        .eq(DamAsset::getIsStored, false));
        if (CollectionUtil.isEmpty(assets)) {
            log.warn("素材上传任务: {} 跳过，素材ID为空", taskId);
            completeTask(taskId);
            return;
        }

        runningTask(taskId);

        Path tempDir = null;
        try {
            // 创建临时目录
            tempDir = Files.createTempDirectory("asset-upload-task-");
            final RetryBackoffSpec retrySpec = RetrySpec.fixedDelay(3, Duration.ofSeconds(5));

            final Path finalTempDir = tempDir;
            Flux.fromIterable(assets)
                    .map(asset -> new TaskState(
                            task,
                            assetIdTaskDetailMap.get(asset.getId()),
                            taskArg,
                            asset,
                            assetId2ArgAssetMap.get(asset.getId()),
                            finalTempDir))
                    .parallel(10)
                    .runOn(Schedulers.fromExecutor(assetUploadTaskThreadExecutor))
                    .flatMap(state -> Mono.just(state)
                            .doOnNext(this::onRunning)
                            .flatMap(s -> Mono.just(s).map(this::downloadAsset).retryWhen(retrySpec))
                            .flatMap(s -> Mono.just(s).map(this::cutAsset).retryWhen(retrySpec))
                            .flatMap(s -> Mono.just(s).map(this::uploadAsset).retryWhen(retrySpec))
                            .flatMap(s -> Mono.just(s).map(this::uploadThumbnail).retryWhen(retrySpec))
                            .flatMap(s -> Mono.just(s).map(this::extractAspectRatio).retryWhen(retrySpec))
                            .flatMap(s -> Mono.just(s).map(this::prepareAsset).retryWhen(retrySpec))
                            .flatMap(s -> Mono.just(s).map(this::asrVideo).retryWhen(retrySpec))
                            .flatMap(s -> Mono.just(s).map(this::compressVideo).retryWhen(retrySpec))
                            .flatMap(s -> Mono.just(s).map(this::cutVideoSceneImages).retryWhen(retrySpec))
                            .flatMap(s -> Mono.just(s).map(this::sceneTag).retryWhen(retrySpec))
                            .onErrorResume(e -> {
                                onError(e, state);
                                return Mono.empty();
                            }))
                    .sequential()
                    .doOnNext(this::onSuccess)
                    .collectList()
                    .block();
        } finally {
            if (tempDir != null) {
                try {
                    FileUtil.del(tempDir);
                } catch (IORuntimeException e) {
                    log.warn("删除临时目录失败", e);
                }
            }
        }
    }

    private String getObservationId(Integer taskId) {
        return String.format("asset-upload-task-%d", taskId);
    }

    protected TaskState downloadAsset(TaskState state) {
        final DamAssetUploadTaskArg.Asset argAsset = state.getArgAsset();
        final String ossId = argAsset.getOssId();

        final StopWatch stopWatch = state.getStopWatch();
        final String originVideoPath = state.getOriginVideoPath();

        FileUtil.del(originVideoPath);

        FunctionUtils.withStopWatch(stopWatch, "下载视频", () -> FileUtil.writeFromStream(s3Service.download(ossId), originVideoPath));

        log.debug("下载视频耗时: {} ms", stopWatch.getLastTaskTimeMillis());
        return state;
    }

    protected TaskState cutAsset(TaskState state) {
        final DamAsset asset = state.getAsset();
        final Integer start = state.getArgAsset().getStart();
        final Integer end = state.getArgAsset().getEnd();

        final StopWatch stopWatch = state.getStopWatch();
        final String localVideoPath = state.getOriginVideoPath();

        final Long originVideoLength = VideoUtil.getVideoLengthAsMs(localVideoPath);

        final String cutVideoLocalPath = state.getVideoPath();
        FileUtil.del(cutVideoLocalPath);

        final int localVideoLength;
        if (start == 0 && originVideoLength <= end && "mp4".equals(asset.getExt())) {
            log.info("原始时长: {}, 小于截取时长, 且格式为mp4, 不进行剪辑", originVideoLength);
            FileUtil.copy(Path.of(localVideoPath), Path.of(cutVideoLocalPath));
            localVideoLength = originVideoLength.intValue();
        } else {
            FunctionUtils.withStopWatch(stopWatch, "剪辑视频", () -> {
                VideoUtil.cutVideoSegment(localVideoPath, cutVideoLocalPath, "mp4", start, end);
                log.debug("剪辑视频耗时: {} ms", stopWatch.getLastTaskTimeMillis());
            });
            localVideoLength = VideoUtil.getVideoLengthAsMs(cutVideoLocalPath).intValue();
        }
        asset.setDuration(localVideoLength);
        return state;
    }

    protected TaskState uploadAsset(TaskState state) {
        final DamAsset asset = state.getAsset();
        final StopWatch stopWatch = state.getStopWatch();
        final String cutVideoLocalPath = state.getVideoPath();

        final String assetBasePath = ossProperties.getAssetBasePath(asset.getTenantId(), asset.getId());
        final String assetVideoOssId = assetBasePath + "/" + UUID.fastUUID() + "_" + asset.getName() + ".mp4";

        FunctionUtils.withStopWatch(stopWatch, "上传视频到OSS", () -> {
            s3Service.upload(assetVideoOssId, new File(cutVideoLocalPath));
        });

        log.debug("上传视频到OSS耗时: {} ms", stopWatch.getLastTaskTimeMillis());

        asset.setOssId(assetVideoOssId);

        return state;
    }

    protected TaskState uploadThumbnail(TaskState state) {
        final DamAsset asset = state.getAsset();
        final StopWatch stopWatch = state.getStopWatch();
        final String cutVideoLocalPath = state.getVideoPath();

        final String assetThumbnailLocalPath = state.getThumbnailPath();
        FileUtil.del(assetThumbnailLocalPath);

        FunctionUtils.withStopWatch(stopWatch, "获取缩略图", () -> VideoUtil.getVideoFirstFrame(cutVideoLocalPath, assetThumbnailLocalPath));

        log.debug("获取缩略图耗时: {} ms", stopWatch.getLastTaskTimeMillis());

        final String assetBasePath = ossProperties.getAssetBasePath(asset.getTenantId(), asset.getId());
        final String assetThumbnailOssId = assetBasePath + "/" + state.getUuid() + "_" + asset.getName() + "_thumbnail"
                + ".jpg";
        FunctionUtils.withStopWatch(stopWatch, "上传缩略图到OSS", () -> s3Service.upload(assetThumbnailOssId, new File(assetThumbnailLocalPath)));
        log.debug("上传缩略图耗时: {} ms", stopWatch.getLastTaskTimeMillis());

        asset.setThumbnailOssId(assetThumbnailOssId);

        return state;
    }

    protected TaskState extractAspectRatio(TaskState state) {
        final DamAsset asset = state.getAsset();
        final StopWatch stopWatch = state.getStopWatch();
        final String cutVideoLocalPath = state.getVideoPath();

        FunctionUtils.withStopWatch(stopWatch, "获取画面比例", () -> {
            String videoAspectRatio = VideoUtil.getVideoAspectRatio(cutVideoLocalPath);
            asset.setAspectRatio(videoAspectRatio);
        });
        log.debug("获取画面比例耗时: {} ms", stopWatch.getLastTaskTimeMillis());

        return state;
    }

    protected TaskState prepareAsset(TaskState state) {
        final DamAsset asset = state.getAsset();
        final StopWatch stopWatch = state.getStopWatch();

        log.info("素材预处理: {}, 处理耗时: {}", asset.getName(), stopWatch.prettyPrint());

        assetService.updateById(asset);

        return state;
    }

    protected TaskState asrVideo(TaskState state) {
        final String audioPath = state.getAudioPath();
        FileUtil.del(audioPath);

        // 获取视频音频数据
        FunctionUtils.withStopWatch(state.getStopWatch(), "ASR", () -> {
            VideoUtil.convertVideoToAudio(state.getVideoPath(), audioPath);
            state.asr = videoRecognitionService.asrGpuService(audioPath,
                    getObservationId(state.getTask().getId()));
        });

        log.info("素材: {}, 音频转文字耗时: {} ms", state.asset.getId(), state.stopWatch.getLastTaskTimeMillis());
        return state;
    }

    protected TaskState compressVideo(TaskState state) {
        final String compressedVideoPath = state.getCompressedVideoPath();
        FileUtil.del(compressedVideoPath);

        FunctionUtils.withStopWatch(state.getStopWatch(), "压缩视频", () -> {
            VideoUtil.compressVideo(state.getVideoPath(), compressedVideoPath);
        });

        log.info("素材: {}, 视频压缩耗时: {} ms", state.asset.getId(), state.stopWatch.getLastTaskTimeMillis());
        return state;
    }

    protected TaskState cutVideoSceneImages(TaskState state) {
        final Integer duration = state.asset.getDuration();

        FunctionUtils.withStopWatch(state.getStopWatch(), "抽取视频图片", () -> {
            final String firstImage = VideoUtil.damCutImageOfBase64(state.getCompressedVideoPath(), state.getTempDir(), 0);
            final String middleImage = VideoUtil.damCutImageOfBase64(state.getCompressedVideoPath(), state.getTempDir(), duration / 2);
            final String lastImage = VideoUtil.damCutLastImageOfBase64(state.getCompressedVideoPath(), state.getTempDir(), duration);

            state.images = List.of(firstImage, middleImage, lastImage);
        });

        log.info("素材: {}, 视频剪辑耗时: {} ms", state.asset.getId(), state.stopWatch.getLastTaskTimeMillis());
        return state;
    }

    // 分镜打标接口
    protected TaskState sceneTag(TaskState state) {
        FunctionUtils.withStopWatch(state.getStopWatch(), "AI打标", () -> {

            final List<SceneDecodingRequest.Tag> tags = state.getTaskArg().getTags()
                    .stream()
                    .map(tag -> {
                        final SceneDecodingRequest.Tag requestedTag = new SceneDecodingRequest.Tag();
                        requestedTag.setName(tag.getName());
                        requestedTag.setDescription(tag.getDescription());
                        requestedTag.setExample(tag.getExample());
                        return requestedTag;
                    })
                    .toList();
            final SceneDecodingRequest request = new SceneDecodingRequest();
            request.setTitle("");
            request.setSentences(state.asr.getText());
            request.setContent("");
            request.setImages(state.getImages());
            request.setTags(tags);
            final VideoRecognition2Handle.SceneDecodingResponse response = videoRecognitionService.sceneDecoding(
                    request,
                    getObservationId(state.getTask().getId()));
            if (response.getCode() != 200) {
                throw new RuntimeException(String.format(
                        "分镜打标接口异常, Code: %d, 消息: %s, 数据: %s",
                        response.getCode(),
                        response.getMessage(),
                        response.getData()));
            }

            log.debug("素材: {}, 分镜打标结果: {}", state.asset.getId(), response.getData());
            state.tags = response.getData();
        });

        log.info("素材: {}, 分镜打标耗时: {} ms", state.asset.getId(), state.stopWatch.getLastTaskTimeMillis());

        return state;
    }

    protected void onRunning(TaskState state) {
        log.info("素材上传任务开始执行, 素材ID: {}, 素材名: {}", state.getAsset().getId(), state.getAsset().getName());
        assetUploadTaskDetailService.update(new LambdaUpdateWrapper<DamAssetUploadTaskDetail>()
                .eq(DamAssetUploadTaskDetail::getId, state.getTaskDetail().getId())
                .set(DamAssetUploadTaskDetail::getStatus, DamTaskStatusEnum.RUNNING)
                .set(DamAssetUploadTaskDetail::getUpdateTime, new Date()));
    }

    protected void onSuccess(TaskState state) {
        final Map<String, String> tags = state.getTags();
        log.info(
                "素材上传完成, 素材ID: {}, 素材名: {}, 耗时: {} ms, 结果: {}",
                state.getAsset().getId(),
                state.getAsset().getName(),
                state.getStopWatch().getTotalTimeMillis(),
                tags);
        try {
            final List<DamAssetUploadTaskArg.Tag> argTags = state.getTaskArg().getTags();
            final List<DamTagValue> tagValues = new ArrayList<>();

            final Set<Integer> tagIds = argTags.stream().map(DamAssetUploadTaskArg.Tag::getId).collect(Collectors.toSet());
            final Map<Integer, DamTag> tagId2tagMap = tagService.list(
                            new LambdaQueryWrapper<DamTag>()
                                    .in(DamTag::getId, tagIds)
                                    .eq(DamTag::getTenantId, state.getTask().getTenantId()))
                    .stream()
                    .collect(Collectors.toMap(DamTag::getId, Function.identity()));

            for (DamAssetUploadTaskArg.Tag tag : argTags) {
                final String tagName = tag.getName();

                final DamTagValue tagValue = new DamTagValue();
                final String value;
                if (tags.containsKey(tagName)) {
                    value = StrUtil.isBlank(tags.get(tagName)) ? "" : tags.get(tagName);
                } else {
                    value = "";
                }

                final DamTag currentTag = tagId2tagMap.get(tag.getId());

                tagValue.setTenantId(state.getTask().getTenantId());
                tagValue.setTagId(tag.getId());
                tagValue.setAssetId(state.getAsset().getId());
                tagValue.setValue(value);
                tagValue.setCreateTime(new Date());
                tagValue.setUpdateTime(new Date());
                tagValue.setIsDeleted(currentTag.getIsDeleted());
                tagValues.add(tagValue);
            }
            if (CollectionUtil.isNotEmpty(tagValues)) {
                tagValueService.saveBatch(tagValues);
            }
            assetUploadTaskDetailService.update(new LambdaUpdateWrapper<DamAssetUploadTaskDetail>()
                    .eq(DamAssetUploadTaskDetail::getId, state.getTaskDetail().getId())
                    .set(DamAssetUploadTaskDetail::getStatus, DamTaskStatusEnum.COMPLETED)
                    .set(DamAssetUploadTaskDetail::getResult, objectMapper.writeValueAsString(tags))
                    .set(DamAssetUploadTaskDetail::getMetrics, objectMapper.writeValueAsString(state.getMetrics()))
                    .set(DamAssetUploadTaskDetail::getUpdateTime, new Date()));
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    protected void onError(Throwable t, TaskState state) {
        log.error(
                "任务执行异常，素材ID: {}, 素材名: {}",
                state.getAsset().getId(),
                state.getAsset().getName(),
                t);

        final DamAssetUploadTaskDetail taskDetail = state.getTaskDetail();
        try {
            assetUploadTaskDetailService.update(
                    new LambdaUpdateWrapper<DamAssetUploadTaskDetail>()
                            .eq(DamAssetUploadTaskDetail::getId, taskDetail.getId())
                            .set(DamAssetUploadTaskDetail::getStatus, DamTaskStatusEnum.FAILED)
                            .set(DamAssetUploadTaskDetail::getError, t.getMessage())
                            .set(DamAssetUploadTaskDetail::getMetrics,
                                    objectMapper.writeValueAsString(state.getMetrics()))
                            .set(DamAssetUploadTaskDetail::getUpdateTime, new Date()));
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    protected void errorTask(Integer taskId, Exception e) {
        taskService.update(new LambdaUpdateWrapper<DamAssetUploadTask>()
                .eq(DamAssetUploadTask::getId, taskId)
                .set(DamAssetUploadTask::getStatus, DamTaskStatusEnum.FAILED)
                .set(DamAssetUploadTask::getError, e.getMessage())
                .set(DamAssetUploadTask::getUpdateTime, new Date()));
    }

    protected void errorAllAssetUploadTask(Integer taskId, Exception e) {
        assetUploadTaskDetailService.update(
                new LambdaUpdateWrapper<DamAssetUploadTaskDetail>()
                        .eq(DamAssetUploadTaskDetail::getTaskId, taskId)
                        .ne(DamAssetUploadTaskDetail::getStatus, DamTaskStatusEnum.COMPLETED)
                        .set(DamAssetUploadTaskDetail::getStatus, DamTaskStatusEnum.FAILED)
                        .set(DamAssetUploadTaskDetail::getError, e.getMessage())
                        .set(DamAssetUploadTaskDetail::getUpdateTime, new Date()));
    }

    protected void runningTask(Integer taskId) {
        taskService.update(new LambdaUpdateWrapper<DamAssetUploadTask>()
                .eq(DamAssetUploadTask::getId, taskId)
                .set(DamAssetUploadTask::getStatus, DamTaskStatusEnum.RUNNING.getCode())
                .set(DamAssetUploadTask::getUpdateTime, new Date()));
    }

    protected void completeTask(Integer taskId) {
        final DamAssetUploadTask task = taskService.getById(taskId);
        taskService.update(new LambdaUpdateWrapper<DamAssetUploadTask>()
                .eq(DamAssetUploadTask::getId, taskId)
                .set(DamAssetUploadTask::getStatus, DamTaskStatusEnum.COMPLETED)
                .set(DamAssetUploadTask::getUpdateTime, new Date()));
        try {
            final DamAssetUploadTaskArg taskArg = objectMapper.readValue(task.getTaskArg(),
                    DamAssetUploadTaskArg.class);
            final Integer directoryId = taskArg.getDirectoryId();
            final DamDirectory directory = directoryService.getById(directoryId);

            final String taskName = new StringBuilder(TokenTaskTypeEnum.ASSET_UPLOAD_TASK.getShowTaskType())
                    .append("_")
                    .append(DamDirectoryTypeEnum.PERSONAL.equals(directory.getType()) ? "个人文件夹" : "租户文件夹")
                    .append("_")
                    .append(directory.getName())
                    .append("_")
                    .append(taskArg.getAssets().size())
                    .toString();
            // 统计用户Token明细
            tokenUseDetailService.countTokenUse(
                    getObservationId(taskId),
                    taskId,
                    TokenTaskTypeEnum.ASSET_UPLOAD_TASK.getTaskType(),
                    taskName,
                    task.getTenantId(),
                    task.getUserId().toString(),
                    null);
        } catch (Exception e) {
            log.warn("统计用户Token明细失败", e);
        }
    }

    @Data
    public static class TaskState {

        private final String uuid;

        private final DamAssetUploadTask task;

        private final DamAssetUploadTaskDetail taskDetail;

        private final DamAssetUploadTaskArg taskArg;

        private final DamAsset asset;

        private final DamAssetUploadTaskArg.Asset argAsset;

        private final Path tempDir;

        private final StopWatch stopWatch;

        private VideoRecognition2Handle.ASR asr;

        private List<String> images;

        private Map<String, String> tags;

        public TaskState(
                DamAssetUploadTask task,
                DamAssetUploadTaskDetail taskDetail,
                DamAssetUploadTaskArg taskArg,
                DamAsset asset,
                DamAssetUploadTaskArg.Asset argAsset,
                Path tempDir) {
            this.uuid = UUID.fastUUID().toString();
            this.task = task;
            this.taskDetail = taskDetail;
            this.taskArg = taskArg;
            this.asset = asset;
            this.argAsset = argAsset;
            this.tempDir = tempDir;
            this.stopWatch = new StopWatch();
        }

        public String getObservationId() {
            return String.format("asset-upload-task-%d", task.getId());
        }

        public String getOriginVideoPath() {
            return this.tempDir + File.separator + "origin-" + uuid + "-" + asset.getName() + "." + asset.getExt();
        }

        public String getVideoPath() {
            return this.tempDir + File.separator + uuid + "-" + asset.getName() + ".mp4";
        }

        public String getCompressedVideoPath() {
            return this.tempDir + File.separator + "compressed-" + uuid + "-" + asset.getName() + ".mp4";
        }

        public String getAudioPath() {
            return this.tempDir + File.separator + uuid + "-" + asset.getName() + ".wav";
        }

        public String getThumbnailPath() {
            return this.tempDir + File.separator + uuid + "-" + asset.getName() + "_thumbnail" + ".jpg";
        }

        public Map<String, Long> getMetrics() {
            final int taskCount = stopWatch.getTaskCount();
            if (taskCount == 0) {
                return Map.of();
            }
            final Map<String, Long> metrics = new HashMap<>(stopWatch.getTaskCount());
            for (StopWatch.TaskInfo taskInfo : stopWatch.getTaskInfo()) {
                metrics.put(taskInfo.getTaskName(), taskInfo.getTimeMillis());
            }
            metrics.put("total", stopWatch.getTotalTimeMillis());
            return metrics;
        }

    }

}
