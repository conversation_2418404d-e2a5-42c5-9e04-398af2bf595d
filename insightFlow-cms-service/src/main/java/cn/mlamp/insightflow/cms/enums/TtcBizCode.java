package cn.mlamp.insightflow.cms.enums;

import lombok.Getter;

/**
 * TTC请求业务异常码
 *
 * <AUTHOR>
 * @since 2022-09-15 19:05:53
 **/
@Getter
public enum TtcBizCode implements ResultCode {
    /**
     * 产品管理通用异常码
     */
    TTC_SUCCESS("0", 0, "success"),
    /**
     * 参数错误
     */
    PARAM_ERROR("700014", 31000, "ttc_code.param_error"),
    /**
     * 发送验证码，邮箱已注册
     */
    ACCOUNT_HAS_BEEN_EXIST_ERROR("700015", 31011, "ttc_code.passport_account_has_been_exist_error"),
    /**
     * 发送验证码，邮箱已注册，且租户已关联
     */
    ACCOUNT_AND_TTC_TENANT_HAVE_BEEN_EXIST_ERROR("700018", 31012,
            "ttc_code.account_and_ttc_tenant_have_been_exist_error"),
    /**
     * 发送验证码，获取验证码需间隔60s
     */
    GET_VERIFICATION_CODE_LIMIT_ERROR("700016", 31013, "ttc_code.passport_get_verification_code_limit_error"),
    /**
     * 发送验证码，非注册用户
     */
    UNREGISTER_ACCOUNT_ERROR("700017", 31014, "ttc_code.passport_unregister_account_error"),
    /**
     * 发送验证码，手机号已注册
     */
    MOBILE_HAS_BEEN_EXIST_ERROR("700015", 31015, "ttc_code.passport_mobile_has_been_exist_error"),
    /**
     * 租户名称已存在，请重新输入
     */
    TENANT_EXISTS_ERROR("600300", 31021, "ttc_code.tenant_exists_error"),
    /**
     * 用户已拥有关联租户
     */
    USER_HAS_OWN_TENANT_ERROR("300002", 31022, "ttc_code.user_has_own_tenant_error"),
    /**
     * 邮箱、手机号验证码错误
     */
    EMAIL_AND_MOBILE_CODE_INVALID("700019", 31023, "ttc_code.email_and_mobile_code_invalid"),
    /**
     * 邮箱验证码错误
     */
    EMAIL_CODE_INVALID("700020", 31024, "ttc_code.email_code_invalid"),
    /**
     * 手机号验证码错误
     */
    MOBILE_CODE_INVALID("700021", 31025, "ttc_code.mobile_code_invalid"),

    /**
     * 目前内部用户不支持标品修改密码，提示：请至企业微信工作台 - 明略统一认证 - 更改密码，修改账号密码
     */
    INTERNAL_USER_RESET_PASSWORD_ERROR("700022", 31026, "ttc_code.internal_user_reset_password_error"),

    ;

    /**
     * Ttc响应给SocialX Code码
     */
    private String ttcCode;
    /**
     * SocialX响应给前端Code码
     */
    private Integer code;
    /**
     * SocialX响应给前端message
     */
    private String message;

    TtcBizCode(String ttcCode, Integer code, String message) {
        this.ttcCode = ttcCode;
        this.code = code;
        this.message = message;
    }

    public TtcBizCode detail(String message) {
        this.message = message;
        return this;
    }
}
