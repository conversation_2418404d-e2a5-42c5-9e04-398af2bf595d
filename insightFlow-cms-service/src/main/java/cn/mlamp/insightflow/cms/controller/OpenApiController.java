package cn.mlamp.insightflow.cms.controller;

import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.model.vo.TaskShareDetailVO;
import cn.mlamp.insightflow.cms.service.ITaskShareReportService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 开放API接口
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/open-api")
@Tag(name = "开放API接口")
public class OpenApiController {

    private final ITaskShareReportService taskShareReportService;

    @GetMapping("/share")
    @Operation(summary = "获取分享详情")
    public RespBody<TaskShareDetailVO> getShareDetail(@RequestParam("code") String authorizeCode) {
        TaskShareDetailVO result = taskShareReportService.getShareDetailByCode(authorizeCode);
        return RespBody.ok(result);
    }
}
