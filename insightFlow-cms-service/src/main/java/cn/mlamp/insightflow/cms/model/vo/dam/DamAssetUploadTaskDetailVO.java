package cn.mlamp.insightflow.cms.model.vo.dam;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import javax.annotation.Nullable;

import com.fasterxml.jackson.annotation.JsonIgnore;

import cn.mlamp.insightflow.cms.entity.CmsUser;
import cn.mlamp.insightflow.cms.entity.TokenUseDetail;
import cn.mlamp.insightflow.cms.entity.dam.DamAssetUploadTask;
import cn.mlamp.insightflow.cms.enums.dam.DamTaskStatusEnum;
import cn.mlamp.insightflow.cms.enums.dam.DamTaskTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 任务详情，包含素材列表
 */
@Data
@Schema(description = "任务详情")
public class DamAssetUploadTaskDetailVO {
    @Schema(description = "任务ID")
    private Integer id;

    @Schema(description = "任务类型")
    private DamTaskTypeEnum type;

    @Schema(description = "素材列表")
    private List<DamAssetUploadItemVO> assets;

    @JsonIgnore
    @Schema(description = "消耗token数")
    private Integer tokens;

    @Schema(description = "是否带入库")
    private Boolean isStored;

    @Schema(description = "失败原因")
    private String error;

    @Schema(description = "任务状态：1-待处理，2-处理中，3-完成，4-失败")
    private DamTaskStatusEnum status;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;
    
    @Schema(description = "消耗点数")
    public Integer getPoints() {
        return tokens != null ? Math.round(tokens / 1000.f) : 0;
    }

    public static DamAssetUploadTaskDetailVO from(DamAssetUploadTask task) {
        return from(task, null, null, null);
    }

    public static DamAssetUploadTaskDetailVO from(
            DamAssetUploadTask task,
            @Nullable List<DamAssetUploadItemVO> assets
    ) {
        return from(task, assets, null, null);
    }

    public static DamAssetUploadTaskDetailVO from(
            DamAssetUploadTask task,
            @Nullable List<DamAssetUploadItemVO> assets,
            @Nullable TokenUseDetail tokenUseDetail,
            @Nullable CmsUser user
    ) {
        final DamAssetUploadTaskDetailVO vo = new DamAssetUploadTaskDetailVO();
        vo.setId(task.getId());
        vo.setType(task.getType());
        vo.setAssets(Objects.requireNonNullElse(assets, Collections.emptyList()));
        vo.setTokens(tokenUseDetail != null && tokenUseDetail.getTokens() != null ? tokenUseDetail.getTokens() : null);
        vo.setError(task.getError());
        vo.setIsStored(task.getIsStored());
        vo.setStatus(task.getStatus());
        vo.setCreateTime(task.getCreateTime());
        vo.setUpdateTime(task.getUpdateTime());
        vo.setCreateBy(user != null ? user.getUserName() : null);
        return vo;
    }
}
