package cn.mlamp.insightflow.cms.util.excel;

import com.alibaba.excel.write.handler.RowWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class StoryboardExport {
    // 自定义图片写入处理器
    @Setter
    public static class CellImageWriteHandler implements RowWriteHandler {

        // 图片所在列索引（根据实际情况调整）
        private static final int IMAGE_COL_INDEX = 2;

        private Map<String, byte[]> base64Map = new HashMap<>();

        @Override
        public void afterRowDispose(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder,
                                    Row row, Integer relativeRowIndex, Boolean isHead) {
            if (isHead) return;

            Sheet sheet = writeSheetHolder.getSheet();
            Workbook workbook = sheet.getWorkbook();

            // 假设图片在最后一列（根据实际情况调整）
            Cell imageCell = row.getCell(IMAGE_COL_INDEX);

            if (imageCell != null && imageCell.getStringCellValue() != null
                    && base64Map.containsKey(imageCell.getStringCellValue())) {
                try {
                    // 1.获取ossId上的图片
                    // 使用s3获取数据
                    byte[] imageBytes = base64Map.get(imageCell.getStringCellValue());

                    // 2. 获取图片原始尺寸（像素）
                    BufferedImage originalImage = ImageIO.read(new ByteArrayInputStream(imageBytes));
                    int originalWidth = originalImage.getWidth();
                    int originalHeight = originalImage.getHeight();

                    // 3. 添加图片到工作簿（保持原始像素）
                    int pictureIdx = workbook.addPicture(imageBytes, Workbook.PICTURE_TYPE_PNG);

                    // 4. 创建绘图对象
                    Drawing<?> drawing = sheet.createDrawingPatriarch();
                    CreationHelper helper = workbook.getCreationHelper();
                    ClientAnchor anchor = helper.createClientAnchor();

                    // 5. 设置图片位置（保持原始尺寸）
                    anchor.setCol1(IMAGE_COL_INDEX);
                    anchor.setRow1(row.getRowNum());
                    // 根据图片像素计算占用的列数和行数
                    anchor.setCol2(IMAGE_COL_INDEX + (int) Math.ceil(originalWidth / 960.0)); // 96DPI转换
                    anchor.setRow2(row.getRowNum() + (int) Math.ceil(originalHeight / 960.0));

                    // 6. 插入图片
                    drawing.createPicture(anchor, pictureIdx);

                    // 7. 调整单元格尺寸（精确匹配图片像素）
                    adjustCellSize(sheet, row, IMAGE_COL_INDEX, originalWidth, originalHeight);

                    // 8. 清空原始Base64文本
                    imageCell.setCellValue("");
                } catch (Exception e) {
                    // 图片处理失败时保留原始文本
                    log.warn("插入图片失败", e);
                }
            }
        }


        /**
         * 调整单元格大小以适应图片
         */
        private void adjustCellSize(Sheet sheet, Row row, int colIndex, int widthPx, int heightPx) {
            // 列宽调整（Excel单位：1/256字符宽度）
            int columnWidth = (int) (widthPx * 256 / 8.43 / 8.0); // 8.43是默认字符宽度
            sheet.setColumnWidth(colIndex, Math.min(columnWidth, 255 * 256)); // 最大255个字符

            // 行高调整（Excel单位：磅）
            float rowHeight = heightPx * 72f / 96f / 8f; // 96DPI转换为磅
            row.setHeightInPoints(Math.max(rowHeight, sheet.getDefaultRowHeightInPoints()));
        }
    }
}
