package cn.mlamp.insightflow.cms.enums.dam;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import javax.annotation.Nullable;

/**
 * DAM回收站对象类型枚举
 */
@Getter
@AllArgsConstructor
public enum DamRecycleBinObjectTypeEnum implements IEnum<Integer> {

    DIRECTORY(1, "目录"),
    ASSET(2, "素材");

    @JsonValue
    private final Integer code;
    private final String desc;

    @Override
    public Integer getValue() {
        return code;
    }

    @JsonCreator
    @Nullable
    public static DamRecycleBinObjectTypeEnum getByCode(@Nullable Integer code) {
        if (code == null) {
            return null;
        }
        for (DamRecycleBinObjectTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new IllegalArgumentException("Invalid value '" + code + "'");
    }
}