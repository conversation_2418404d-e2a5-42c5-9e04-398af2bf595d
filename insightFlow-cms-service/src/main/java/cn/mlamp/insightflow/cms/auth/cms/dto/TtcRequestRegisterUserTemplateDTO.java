package cn.mlamp.insightflow.cms.auth.cms.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import cn.mlamp.insightflow.cms.auth.cms.bo.TenantTemplateBO;
import cn.mlamp.insightflow.cms.constant.CommonConstant;
import jakarta.validation.constraints.Size;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;

/**
 * Ttc注册用户DTO PS: 1、以下正则均为Passport提供
 *
 * <AUTHOR>
 * @since 2022-09-08 15:20:16
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(onlyExplicitlyIncluded = true, callSuper = true)
public class TtcRequestRegisterUserTemplateDTO extends TenantTemplateBO {

    /**
     * 租户名称
     */
    @NotBlank(message = "override_message.tenant_name_not_blank")
    @Size(max = 20, message = "override_message.tenant_name_out_of_length")
    private String tenantName;
    /**
     * 密码
     */
    @ToString.Exclude
    @NotBlank(message = "override_message.password_not_blank")
    @Pattern(regexp = CommonConstant.PASSWORD_REGX, message = "override_message.password_invalid")
    private String password;
    /**
     * 密码 - 二次
     */
    @ToString.Exclude
    @NotBlank(message = "override_message.password_check_not_blank")
    private String passwordCheck;
    /**
     * 邮箱
     */
    @ToString.Include
    @NotBlank(message = "override_message.email_not_empty")
    @Pattern(regexp = "^([A-Za-z0-9_\\-\\.])+\\@([A-Za-z0-9_\\-\\.])+\\.([A-Za-z]{2,10})$", message = "override_message.email_invalid")
    private String email;
    /**
     * 邮箱验证码
     */
    @ToString.Include
    @Pattern(regexp = CommonConstant.VERIFICATION_CODE_REGX, message = "override_message.email_verification_code_invalid")
    private String emailCode;
    /**
     * 手机号
     */
    @ToString.Include
    @NotBlank(message = "override_message.mobile_not_empty")
    @Pattern(regexp = CommonConstant.MOBILE_REGX, message = "override_message.mobile_invalid")
    private String mobile;
    /**
     * 手机号验证码
     */
    @ToString.Include
    @Pattern(regexp = CommonConstant.VERIFICATION_CODE_REGX, message = "override_message.mobile_verification_code_invalid")
    private String mobileCode;

}
