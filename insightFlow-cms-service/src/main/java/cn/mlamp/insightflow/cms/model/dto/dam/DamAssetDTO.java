package cn.mlamp.insightflow.cms.model.dto.dam;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.util.List;

/**
 * DAM素材DTO
 */
@Data
@Schema(description = "DAM素材DTO")
public class DamAssetDTO {

    @Schema(description = "素材ID")
    private Integer id;

    @NotBlank(message = "素材名称不能为空")
    @Size(max = 20, message = "素材名称不能超过20个字符")
    @Pattern(regexp = "^[^\\\\/:*?\"<>|]*$",
            message = "目录名称不能包含以下字符：\\、/、:、*、?、\"、<、>、|")
    @Schema(description = "素材名称",
            requiredMode = RequiredMode.REQUIRED)
    private String name;

    @NotNull(message = "所属目录ID不能为空")
    @Schema(description = "所属目录ID",
            requiredMode = RequiredMode.REQUIRED)
    private Integer directoryId;

    @Schema(description = "视频时长（秒）")
    private Integer duration;

    @Schema(description = "缩略图URL")
    private String thumbnail;

    @Schema(description = "画面比率 16:9、9:16、1:1")
    private String aspectRatio;

    @Schema(description = "OSS存储URL")
    private String ossUrl;

    @Schema(description = "素材标签")
    private List<DamTagValueDTO> tags;
} 