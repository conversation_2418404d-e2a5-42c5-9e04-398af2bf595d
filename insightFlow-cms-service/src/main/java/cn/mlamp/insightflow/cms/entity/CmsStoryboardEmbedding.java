package cn.mlamp.insightflow.cms.entity;

import cn.mlamp.insightflow.cms.util.mybatis.pg.FloatArrayTypeHandler;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * CMS Storyboard Embeddings entity class
 */
@Data
@TableName("cms_storyboard_embeddings")
public class CmsStoryboardEmbedding {
    @TableId(value = "id", type = IdType.INPUT)
    private Integer id;

    private Integer taskId;

    private String content;

    @TableField(typeHandler = FloatArrayTypeHandler.class)
    private Float[] embedding; // pgvector field

    private Integer tenantId;

    private Integer userId;

    @TableField(exist = false)
    private Float distance;
}
