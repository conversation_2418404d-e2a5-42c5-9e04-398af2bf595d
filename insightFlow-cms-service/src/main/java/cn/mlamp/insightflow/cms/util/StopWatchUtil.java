package cn.mlamp.insightflow.cms.util;

import org.springframework.util.StopWatch;

import java.util.Objects;

/**
 * StopWatch Util 用于线程中打印时间
 *
 * <AUTHOR>
 * @since 2022-08-08
 */
public class StopWatchUtil {
    private static ThreadLocal<StopWatch> stopWatchLocal = new ThreadLocal<>();

    /**
     * 创建StopWatch并放入线程变量中
     *
     * @param stopWatchName
     */
    public static void create(String stopWatchName) {
        StopWatch stopWatch = new StopWatch(stopWatchName);
        stopWatchLocal.set(stopWatch);
    }

    /**
     * 创建StopWatch并放入线程变量中
     *
     * @param stageName
     */
    public static void start(String stageName) {
        StopWatch stopWatch = stopWatchLocal.get();
        if (Objects.nonNull(stopWatch)) {
            if (stopWatch.isRunning()) {
                stopWatch.stop();
            }
            stopWatch.start(stageName);
        }
    }

    /**
     * 停止计时
     */
    public static void stop() {
        StopWatch stopWatch = stopWatchLocal.get();
        if (Objects.nonNull(stopWatch) && stopWatch.isRunning()) {
            stopWatch.stop();
        }
    }

    /**
     * 打印
     */
    public static String prettyPrint() {
        return prettyPrint(false);
    }

    /**
     * 打印
     */
    public static String prettyPrint(boolean clear) {
        StopWatch stopWatch = stopWatchLocal.get();
        if (Objects.isNull(stopWatch)) {
            return "";
        }
        if (clear) {
            clear();
        }
        return stopWatch.prettyPrint();
    }

    public static String shortSummary() {
        return shortSummary(false);
    }

    public static String shortSummary(boolean clear) {
        StopWatch stopWatch = stopWatchLocal.get();
        if (Objects.isNull(stopWatch)) {
            return "";
        }
        if (clear) {
            clear();
        }
        return stopWatch.shortSummary();
    }

    public static long getTotalTimeMillis() {
        return getTotalTimeMillis(false);
    }

    public static long getTotalTimeMillis(boolean clear) {
        StopWatch stopWatch = stopWatchLocal.get();

        if (Objects.isNull(stopWatch)) {
            return 0L;
        }
        if (clear) {
            clear();
        }
        return stopWatch.getTotalTimeMillis();
    }

    /**
     * 清理
     */
    public static void clear() {
        stopWatchLocal.remove();
    }
}
