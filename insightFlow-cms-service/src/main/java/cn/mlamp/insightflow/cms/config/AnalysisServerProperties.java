package cn.mlamp.insightflow.cms.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import lombok.Data;

@ConfigurationProperties("analysis-server")
@Configuration
@Data
public class AnalysisServerProperties {
    private String clusterAddress;
    private String ascribeAddress;
    private String modelClusterAddress;
    private String modelClusterApi;
    private String markingAddress;
    private String markingUsername;
    private String markingPassword;
    private String tokenUseAddress;
}
