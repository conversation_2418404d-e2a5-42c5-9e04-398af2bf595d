package cn.mlamp.insightflow.cms.strategy.handle;

import cn.mlamp.insightflow.cms.config.AnalysisVideoConfig;
import cn.mlamp.insightflow.cms.exception.BusinessException;
import cn.mlamp.insightflow.cms.util.FileDownloadUtil;
import cn.mlamp.insightflow.cms.util.VideoUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.Map;

/**
 * @Author: husuper
 * @CreateTime: 2025-03-25
 */
@Service
@Slf4j
public class VideoRecognition3Handle {


    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private AnalysisVideoConfig analysisVideoConfig;

    public static void main(String[] args) {
        VideoRecognition3Handle videoRecognition2Handle = new VideoRecognition3Handle();
        videoRecognition2Handle.restTemplate = new RestTemplate();
        videoRecognition2Handle.analysisVideoConfig = new AnalysisVideoConfig();
        videoRecognition2Handle.analysisVideoConfig.setAnalyzeUrl("http://10.10.100.228:8222");

        VideoUtil.setFfmpegPath("/Users/<USER>/Documents/ffmpeg/ffmpeg");
        VideoUtil.setFfprobePath("/Users/<USER>/Documents/ffmpeg/ffprobe");

//        String url="https://ai-pc-test.oss-cn-beijing.aliyuncs.com/sample/video/-CQEDpYBd6YxY-31wA2Z.mp4";
        String url = "https://ai-pc-test.oss-cn-beijing.aliyuncs.com/test/yasuohou.mp4";
        String videoFilePath = "/Users/<USER>/Documents/OSS/yasuohou.mp4";


//        //1:获取ASR
        ASR response= videoRecognition2Handle.asrGpuService("/Users/<USER>/Documents/OSS/88.wav", "observationId");


        //2：视频分析
        RecognitionArg recognitionArg= new RecognitionArg();
        recognitionArg.setVideo_url(url);
        recognitionArg.setTitle("美妆");
        recognitionArg.setContent("测试视频");
        recognitionArg.setIndustry("美妆");
        recognitionArg.setAsr_text(response.getText());
        recognitionArg.setTask_queue("customer");

        videoRecognition2Handle.processVideo(recognitionArg, "observationId");


        //3：查询结果 17164
//        VideoContent videoContent = videoRecognition2Handle.queryVideo("18165", "observationId");
//        Map<String, Object> industryDecoding = JSONObject.parseObject(videoContent.getData().getResponse_body(), Map.class);

//        System.out.println(industryDecoding.get("创意得分"));

        // 4: 图片打标接口（黄金3s）
        ImageDecodingRequest request = new ImageDecodingRequest();
        request.setImages(List.of(VideoUtil.cutImageOfBase64(videoFilePath, 1000),
                VideoUtil.cutImageOfBase64(videoFilePath, 2000),
                VideoUtil.cutImageOfBase64(videoFilePath, 3000),
                VideoUtil.cutImageOfBase64(videoFilePath, 4000),
                VideoUtil.cutImageOfBase64(videoFilePath, 5000)
        ));
        request.setSentences("");
        request.setTitle("美妆");
        request.setContent("测试内容");

//        ImageDecodingResponse imageDecodingResponse = videoRecognition2Handle.imageDecoding3s(request, "observationId");


        // 5: 分镜图片打标接口
        SceneDecodingRequest sceneDecodingRequest = new SceneDecodingRequest();
        sceneDecodingRequest.setImages(List.of(
                VideoUtil.cutImageOfBase64(videoFilePath, 1000),
                VideoUtil.cutImageOfBase64(videoFilePath, 2000),
                VideoUtil.cutImageOfBase64(videoFilePath, 3000)
        ));
        sceneDecodingRequest.setSentences("");
        sceneDecodingRequest.setTitle("美妆");
        sceneDecodingRequest.setContent("测试内容");

//        SceneDecodingResponse sceneDecodingResponse = videoRecognition2Handle.sceneDecoding(sceneDecodingRequest, "observationId");
//        log.info("场景打标接口返回{}", sceneDecodingResponse);

        // 6: 视频分割接口
        VideoSplitRequest videoSplitRequest = new VideoSplitRequest();
        videoSplitRequest.setVideo_url(url);

//        videoSplitRequest.setVideo_asr(response);

//        VideoSplitResponse videoSplitResponse = videoRecognition2Handle.videoSplit(videoSplitRequest, "observationId");


    }


    /**
     * ASR-gpu服务_copy
     * 创建人：lijingsong
     * 状态：已完成
     * 更新时间：2025-04-22 17:29:53
     * 接口路径：POST /api/asr_1745314095577
     * Mock地址：https://yapi.mlamp.cn/mock/920/api/asr_1745314095577
     * 请求参数：
     * Headers：
     * 参数名称	参数值	是否必须	示例	备注
     * Content-Type	multipart/form-data	是
     * Body:
     * 参数名称	参数类型	是否必须	示例	备注
     * files	文件	是
     * files={"file":wav_bytes}
     * 二进制wav_bytes数据
     * 用file.read()读取.wav文件获取
     * 返回参数
     * Response<ASR>
     *
     * @param wavFilePath
     * @param observationId
     * @return
     */
    public ASR asrGpuService(String wavFilePath, String observationId) {
        String url = "http://asr-gpu.mlamp.cn/api/asr";

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        headers.set("observation-id", observationId);

        FileSystemResource resource = new FileSystemResource(wavFilePath);
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add("file", resource);

        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, requestEntity, String.class);
        log.info("调用 ASR-gpu 服务返回{}", responseEntity.getBody());

        ASR asrResponse = JSONObject.parseObject(responseEntity.getBody(), new TypeReference<ASR>() {
        });
        if(asrResponse.getCode()!=0){
            FileDownloadUtil.deleteFile(wavFilePath);
            throw new BusinessException("识别ASR失败");
        }

        return asrResponse;
    }


    /**
     * 异步视频整体识别请求接口
     * @param recognitionArg
     * @param observationId
     * @return
     */
    public ProcessVideo processVideo(RecognitionArg recognitionArg, String observationId) {
        String url = analysisVideoConfig.getAnalyzeUrl() + "/async_video_flow_v2";

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("observation-id", observationId);
        log.info("调用视频整体分析服务请求{},observation-id:{}", recognitionArg, observationId);
        HttpEntity<RecognitionArg> requestEntity = new HttpEntity<>(recognitionArg, headers);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, requestEntity, String.class);
        log.info("调用视频整体分析服务返回{}", responseEntity.getBody());
        ProcessVideo processVideo = JSONObject.parseObject(responseEntity.getBody(), ProcessVideo.class);
        if(processVideo.getCode()!=200){
            throw new BusinessException(processVideo.getMessage());
        }
        return processVideo;
    }



    @Data
    public static class ImageDecodingRequest {
        private List<String> images;
        private String sentences;
        private String title;
        private String content;
        //队列 daily/customer
        private String task_queue;

        @Override
        public String toString() {
            return "ImageDecodingRequest{" +
                    "sentences='" + sentences + '\'' +
                    ", title='" + title + '\'' +
                    ", content='" + content + '\'' +
                    '}';
        }
    }

    /**
     * 图片打标接口（黄金3s）
     *
     * @param request       请求参数
     * @param observationId
     * @return
     */
    public ImageDecodingResponse imageDecoding3s(ImageDecodingRequest request, String observationId) {
        String url = analysisVideoConfig.getAnalyzeUrl() + "/async_image_decoding_3s";

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("observation-id", observationId);
        log.info("调用图片打标（黄金3s）接口请求{},observation-id:{}", request,observationId);
        HttpEntity<ImageDecodingRequest> requestEntity = new HttpEntity<>(request, headers);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, requestEntity, String.class);
        log.info("调用图片打标（黄金3s）接口返回{}", responseEntity.getBody());
        ImageDecodingResponse imageDecodingResponse = JSONObject.parseObject(responseEntity.getBody(), ImageDecodingResponse.class);
        return imageDecodingResponse;
    }

    /**
     * 分镜图片打标接口
     *
     * @param request       请求参数
     * @param observationId
     * @return
     */
    public SceneDecodingResponse sceneDecoding(SceneDecodingRequest request, String observationId) {
        String url = analysisVideoConfig.getAnalyzeUrl() + "/async_scene_decoding";

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("observation-id", observationId);
        log.info("调用分镜图片打标接口请求{},observation-id:{}", request, observationId);
        HttpEntity<SceneDecodingRequest> requestEntity = new HttpEntity<>(request, headers);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, requestEntity, String.class);
        log.info("调用分镜图片打标接口返回{}", responseEntity.getBody());
        SceneDecodingResponse sceneDecodingResponse = JSONObject.parseObject(responseEntity.getBody(), SceneDecodingResponse.class);
        return sceneDecodingResponse;
    }

    /**
     * 视频分割接口
     *
     * @param request       请求参数
     * @param observationId
     * @return
     */
    public VideoSplitResponse videoSplit(VideoSplitRequest request, String observationId) {
        String url = analysisVideoConfig.getAnalyzeUrl() + "/async_video_split";

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("observation-id", observationId);
        log.info("调用视频分割接口请求{},observation-id:{}", JSONObject.toJSONString(request),observationId);
        HttpEntity<VideoSplitRequest> requestEntity = new HttpEntity<>(request, headers);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, requestEntity, String.class);
        log.info("调用视频分割接口返回{}", responseEntity.getBody());
        VideoSplitResponse videoSplitResponse = JSONObject.parseObject(responseEntity.getBody(), VideoSplitResponse.class);
        if(videoSplitResponse.getCode()!=200){
            throw new BusinessException(videoSplitResponse.getMsg());
        }
        return videoSplitResponse;
    }


    @Data
    public static class VideoSplitRequest {
        private String video_url;
        private ASR video_asr;
        //队列 daily/customer
        private String task_queue;
    }

    @Data
    public static class VideoSplitResponse {
        private int code;
        private String msg;
        private AsyncData data;
    }

    @Data
    public static class AsyncData {
        private String db_unique_id;
    }



    @Data
    public static class ASRFragment {
        private String text;
        private List<Integer> time_stamp;
    }


    @Data
    public static class SceneDecodingResponse {
        private AsyncData data;
        private int code;
        private String message;
    }


    @Data
    public static class SceneDecodingRequest {
        private List<String> images; // 头start中(start+end)/2尾end这3张图片base64
        private String sentences; // 分镜对应ASR结果，sentence start>=分镜start，<分镜end
        private String title; // 标题
        private String content; // 内容

        //队列 daily/customer
        private String task_queue;

        @Override
        public String toString() {
            return "SceneDecodingRequest{" +
                    "sentences='" + sentences + '\'' +
                    ", title='" + title + '\'' +
                    ", content='" + content + '\'' +
                    '}';
        }
    }


    @Data
    public static class ImageDecodingResponse {
        private AsyncData data;
        private int code;
        private String message;
    }



    @Data
    public static class CountPendingData {
        private int pending_count;
    }


    @Data
    public static class RecognitionArg {

        //视频URL
        private String video_url;

        //标题
        private String title;

        //内容
        private String content;

        //行业
        private String asr_text;

        //默认美妆
        private String industry;

        //队列 daily/customer
        private String task_queue;

        //ASR清洗
        private ASR  video_asr;

    }

    @Data
    public static class ProcessVideo {
        // 消息内容
        private String message;

        // 响应代码
        private int code;

        private AsyncData data;

    }

    @Data
    public static class ProcessVideoData {
        private String db_unique_id;
    }




    @Data
    public static class Response<T> {

        private Integer code;

        private String message;

        private T data;
    }


    @Data
    public static class ASR {

        private Integer code;

        // 视频台词完整拼接
        private String text;

        // 带时间戳的句子
        private List<Sentences> sentences;

    }

    @Data
    public static class Sentences {
        // 台词
        private String text;

        // 开始时间(毫秒)
        private Integer start;

        // 结束时间(毫秒)
        private Integer end;

    }

    @Data
    public static class VideoSplitData {
        //转json的key定义为ASR
        @JsonProperty("ASR")
        private Map<String, VideoRecognition3Handle.ASRFragment> ASR;
        private List<List<Integer>> 画面分镜;
        private boolean Use_ASR;
    }


}
