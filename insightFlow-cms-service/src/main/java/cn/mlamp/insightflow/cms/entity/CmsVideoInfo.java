package cn.mlamp.insightflow.cms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 视频分析信息表;
 * <AUTHOR> husper
 * @date : 2025-3-19
 */
@Data
@TableName("cms_video_info")
public class CmsVideoInfo extends BaseEntity{
    /** 主键 自增id */
    @TableId(type = IdType.AUTO)
    private Integer id ;
    /** 1:视频分析;2：黄金5秒;3:圈层热点；4：上传视频 */
    @TableField("`type`")
    private Integer type ;
    /** 帖子ID */
    private String esId ;
    /** 黄金5秒表id */
    private String fiveGoldId ;
    /** 视频源文件Id */
    private Integer sourceFileId ;
    /** 1：待处理；2：处理中，3：完成，4：失败 */
    @TableField("`status`")
    private Integer status ;
    /** 失败原因 */
    private String errorMessage ;
    /** 四有三好打分 */
    private String rating ;
    /** 用户id */
    private Integer userId ;
    /** 租户id */
    private Integer tenantId ;

    private String arg ;

    //黄金3秒类型
    private String threeGoldType;

    //行业
    private String industry;

}
