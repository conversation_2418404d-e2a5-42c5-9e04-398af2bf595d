package cn.mlamp.insightflow.cms.service.impl;

import cn.mlamp.insightflow.cms.entity.CmsDocumentInfo;
import cn.mlamp.insightflow.cms.enums.DocumentStatusEnum;
import cn.mlamp.insightflow.cms.enums.UploadSourceTypeEnum;
import cn.mlamp.insightflow.cms.mapper.CmsDocumentInfoMapper;
import cn.mlamp.insightflow.cms.service.ICmsDocumentInfoService;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@AllArgsConstructor
public class CmsDocumentInfoServiceImpl extends ServiceImpl<CmsDocumentInfoMapper, CmsDocumentInfo>
        implements ICmsDocumentInfoService {

    @Override
    public int updateDocumentInfoStatus(DocumentStatusEnum statusEnum, Integer taskId, Integer userId, Integer tenantId) {
        return this.baseMapper.update(new LambdaUpdateWrapper<CmsDocumentInfo>()
                .set(CmsDocumentInfo::getStatus, statusEnum.name())
                .eq(CmsDocumentInfo::getId, taskId)
//                .eq(CmsDocumentInfo::getUserId, userId)
                .eq(CmsDocumentInfo::getTenantId, tenantId)
        );
    }

    @Override
    public void updateDocumentInfoStatus(DocumentStatusEnum statusEnum, Integer taskId) {
        this.baseMapper.update(new LambdaUpdateWrapper<CmsDocumentInfo>()
                .set(CmsDocumentInfo::getStatus, statusEnum.name())
                .eq(CmsDocumentInfo::getId, taskId)
        );
    }

    @Override
    public List<CmsDocumentInfo> listByUrlStatusIn(List<DocumentStatusEnum> status) {
        return this.baseMapper.selectList(new LambdaUpdateWrapper<CmsDocumentInfo>()
                .in(CmsDocumentInfo::getStatus, status.stream().map(DocumentStatusEnum::name).toList())
//                .eq(CmsDocumentInfo::getSourceType, UploadSourceTypeEnum.from_url.name())
                .eq(CmsDocumentInfo::getIsDeleted, 0));

    }

    @Override
    public void updateStatusByEsId(String esId, DocumentStatusEnum statusEnum) {
        this.baseMapper.update(new LambdaUpdateWrapper<CmsDocumentInfo>()
                .set(CmsDocumentInfo::getStatus, statusEnum.name())
                .eq(CmsDocumentInfo::getEsId, esId));
    }
}
