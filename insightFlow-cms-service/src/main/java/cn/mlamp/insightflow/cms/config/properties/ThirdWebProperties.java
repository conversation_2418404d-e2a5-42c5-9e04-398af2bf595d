package cn.mlamp.insightflow.cms.config.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;

@ConfigurationProperties("third-web")
public record ThirdWebProperties(Deepana deepana, DeepanaDySku deepanaDySku, SuanFa suanFa) {
    public record Deepana(String baseUrl) {

    }

    public record DeepanaDySku(String baseUrl) {

    }

    public record SuanFa(String baseUrl) {

    }
}
