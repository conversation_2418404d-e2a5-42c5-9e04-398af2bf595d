package cn.mlamp.insightflow.cms.util;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import jakarta.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/11
 **/
@Slf4j
public class IpUtil {
    /**
     * 未知ip
     */
    private static final String UNKNOWN = "unknown";
    private static final String LOCAL_IP = "127.0.0.1";

    public static String getClientIp(HttpServletRequest request) {
        // 只有通过了 HTTP 代理或者负载均衡服务器时才会添加该项，格式为X-Forwarded-For: client1, proxy1, proxy2
        // 一般情况下，第一个ip为客户端真实ip，后面的为经过的代理服务器ip
        String ipAddress = request.getHeader("x-forwarded-for");
        if (StringUtils.isBlank(ipAddress) || UNKNOWN.equalsIgnoreCase(ipAddress)) {
            // apache http服务器做代理时回加上该项
            ipAddress = request.getHeader("Proxy-Client-IP");
        }
        if (StringUtils.isBlank(ipAddress) || UNKNOWN.equalsIgnoreCase(ipAddress)) {
            // apache http代理的weblogic插件添加该项
            ipAddress = request.getHeader("WL-Proxy-Client-IP");
        }
        if (StringUtils.isBlank(ipAddress) || UNKNOWN.equalsIgnoreCase(ipAddress)) {
            // 某些代理服务器会加上该项
            ipAddress = request.getHeader("HTTP_CLIENT_IP");
        }
        if (StringUtils.isBlank(ipAddress) || UNKNOWN.equalsIgnoreCase(ipAddress)) {
            // nginx代理一般会加上该项
            ipAddress = request.getHeader("X-Real-IP");
        }
        if (StringUtils.isBlank(ipAddress) || UNKNOWN.equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getRemoteAddr();
        }

        log.info("getClientIp ori ipAddress:{}", ipAddress);

        // 通过,分割IP,取出第一个IP,即为真实IP
        if (ipAddress != null && ipAddress.indexOf(StrUtil.COMMA) > 0) {
            ipAddress = ipAddress.substring(0, ipAddress.indexOf(StrUtil.COMMA));
        }
        if (UNKNOWN.equalsIgnoreCase(ipAddress) || LOCAL_IP.equals(ipAddress)) {
            ipAddress = "";
        }
        log.info("getClientIp ipAddress:{}", ipAddress);
        return ipAddress;
    }
}
