package cn.mlamp.insightflow.cms.service;

import cn.mlamp.insightflow.cms.entity.CmsVideoInfo;
import cn.mlamp.insightflow.cms.enums.VideoInfoStatusEnum;
import cn.mlamp.insightflow.cms.model.query.VideoInfoResultDetailRequest;
import cn.mlamp.insightflow.cms.model.query.VideoInfoResultRequest;
import cn.mlamp.insightflow.cms.model.vo.QianchuanVideoInfoVO;
import cn.mlamp.insightflow.cms.model.vo.VideoInfoResultVO;
import cn.mlamp.insightflow.cms.model.vo.VideoInfoVO;
import cn.mlamp.insightflow.cms.model.vo.VideoTaskListInfoVO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import jakarta.servlet.http.HttpServletResponse;

import java.util.List;

/**
 * @Author: husuper
 * @CreateTime: 2025-03-21
 */
public interface IVideoInfoService extends IService<CmsVideoInfo> {

    VideoInfoVO getReuslt(VideoInfoResultRequest videoInfoDetailRequest);


    VideoInfoResultVO getReusltDetail(VideoInfoResultDetailRequest videoInfoResultDetailRequest);


    void exportReusltDetail(Integer videoId, HttpServletResponse response);

    /**
     * 查询符合条件的视频数据
     *
     * @param type          视频类型
     * @param status        视频状态
     * @param threeGoldType 黄金五秒标签
     * @param industry      行业
     * @param limit         查询数量限制
     * @return 符合条件的视频数据列表
     */
    List<CmsVideoInfo> findVideosByCondition(Integer type, Integer status, String threeGoldType, String industry,
            Integer limit);


    QianchuanVideoInfoVO getQianchuanReuslt(VideoInfoResultRequest videoInfoDetailRequest);

    List<CmsVideoInfo> getProcessingVideoInfosByToday() ;


    CmsVideoInfo findOneByEsId(String esId);

    void updateStatusByDocId(Integer docId, VideoInfoStatusEnum videoInfoStatusEnum,String errorMsg);

    void handleErrorData();


    Page<VideoTaskListInfoVO> pageUserVideoTask(Integer current, Integer pageSize, Integer tenantId);

//    void updateStatusByDocId(Integer docId, VideoInfoStatusEnum videoInfoStatusEnum);


    void updateVideoInfoStatusFailed(Integer id, String message);

    void updateESVideoInfoStatusFailed(Integer id, String message);

}