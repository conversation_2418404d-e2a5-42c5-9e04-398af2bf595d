package cn.mlamp.insightflow.cms.service.dam;

import cn.mlamp.insightflow.cms.entity.dam.DamAsset;
import cn.mlamp.insightflow.cms.entity.dam.DamDirectory;
import cn.mlamp.insightflow.cms.model.dto.dam.DamAssertUpdateDTO;
import cn.mlamp.insightflow.cms.model.dto.dam.DamTagValueDTO;
import cn.mlamp.insightflow.cms.model.query.dam.DamAssetQueryParam;
import cn.mlamp.insightflow.cms.model.query.dam.DamAssetSemanticSearchParam;
import cn.mlamp.insightflow.cms.model.vo.dam.DamAssetVO;
import cn.mlamp.insightflow.cms.model.vo.dam.DamPageResult;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Stream;

/**
 * <p>
 * DAM素材表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
public interface IDamAssetService extends IService<DamAsset> {

    /**
     * 获取素材列表
     *
     * @param queryParam 查询参数
     * @param userId     用户ID
     * @param tenantId   租户ID
     * @return 素材分页结果
     */
    DamPageResult<DamAssetVO> getAssetList(DamAssetQueryParam queryParam, Integer userId, Integer tenantId);

    /**
     * 获取素材详情
     *
     * @param assetId  素材ID
     * @param userId   用户ID
     * @param tenantId 租户ID
     * @return 素材详情
     */
    DamAssetVO getAssetDetail(Integer assetId, Integer userId, Integer tenantId);

    /**
     * 更新素材标签
     *
     * @param assetId   素材ID
     * @param tagValues 标签值列表
     * @param userId    用户ID
     * @param tenantId  租户ID
     * @return 是否成功
     */
    boolean updateAsset(Integer assetId, DamAssertUpdateDTO tagValues, Integer userId, Integer tenantId);

    /**
     * 删除素材（移入回收站）
     *
     * @param assetIds 素材ID列表
     * @param userId   用户ID
     * @param tenantId 租户ID
     * @return 是否成功
     */
    boolean deleteAssets(List<Integer> assetIds, Integer userId, Integer tenantId);

    /**
     * 拷贝素材
     *
     * @param assetIds 素材ID列表
     * @param userId   用户ID
     * @param tenantId 租户ID
     * @return 是否成功
     */
    boolean copyAssets(List<Integer> assetIds, Integer userId, Integer tenantId);

    /**
     * 移动素材
     *
     * @param assetIds          素材ID列表
     * @param targetDirectoryId 目标目录ID
     * @param userId            用户ID
     * @param tenantId          租户ID
     * @return 是否成功
     */
    boolean moveAssets(List<Integer> assetIds, Integer targetDirectoryId, Integer userId, Integer tenantId);

    /**
     * 下载素材（生成链接）
     *
     * @param assetId  素材ID
     * @param userId   用户ID
     * @param tenantId 租户ID
     * @return 下载链接
     */
    String getAssetDownloadUrl(Integer assetId, Integer userId, Integer tenantId);

    /**
     * 语义检索素材
     *
     * @param searchParam 检索参数
     * @param userId      用户ID
     * @param tenantId    租户ID
     * @return 素材分页结果
     */
    DamPageResult<DamAssetVO> semanticSearchAssets(DamAssetSemanticSearchParam searchParam, Integer userId,
                                                   Integer tenantId);

    /**
     * 获取用户可见的目录下的素材流
     *
     * @param assets                 素材列表
     * @param userVisibleDirectories 用户可见的目录
     * @return 素材流
     */
    Stream<DamAssetVO> streamBy(List<DamAsset> assets, Map<Integer, DamDirectory> userVisibleDirectories);

    /**
     * 更新素材使用次数
     *
     * @param assetIds 素材ID列表
     * @param userId   用户ID
     * @param tenantId 租户ID
     * @return 是否成功
     */
    boolean updateUsedNum(List<Integer> assetIds, Integer userId, Integer tenantId);

    List<DamAssetVO> topKAssets(String aspectRatio, String content, Float[] embedding,
                                Integer topK, Float threshold, Integer userId, Integer tenantId);

    Map<Integer, List<DamTagValueDTO>> getTagsByAssertIds(Set<Integer> assetIds);
}
