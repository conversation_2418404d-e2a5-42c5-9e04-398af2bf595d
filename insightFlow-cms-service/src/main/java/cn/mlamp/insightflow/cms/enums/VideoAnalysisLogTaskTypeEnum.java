package cn.mlamp.insightflow.cms.enums;


public enum VideoAnalysisLogTaskTypeEnum {
    UPLOAD(1, "文件上传"),
    COMPRESS(2, "文件压缩"),
    DOWNLOAD(3, "文件下载"),
    AUDIO_CONVERT(4, "WAV转码"),
    SEGMENTATION(5, "视频分割"),
    FRAME_CUTTING(6, "切帧处理"),
    ALGORITHM(7, "算法接口调用");

    private final int code;
    private final String desc;

    VideoAnalysisLogTaskTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
