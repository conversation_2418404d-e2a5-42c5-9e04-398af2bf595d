package cn.mlamp.insightflow.cms.model.query;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import com.github.yulichang.toolkit.StrUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * 分页参数基类
 */
@Data
@Schema(description = "分页参数")
public class PageParam {

    @Schema(description = "页码", defaultValue = "1")
    private Integer pageNum = 1;

    @Schema(description = "每页大小", defaultValue = "10")
    private Integer pageSize = 10;

    // TODO: 这里好像不生效
    @Pattern(regexp = "^(asc|desc)$", message = "排序方式只能是 asc 或 desc")
    @Schema(description = "排序字段")
    private String sortField;

    @Schema(description = "排序方式(asc/desc)")
    private String sortOrder;

    public <T> Page<T> toPage(Class<T> entityClass) {
        final Page<T> page = new Page<>(pageNum, pageSize);
        if ("createTime".equals(sortField)) {
            sortField = "id";
        }

        if (StringUtils.isNotBlank(sortField)) {
            sortField = StrUtils.camelToUnderline(sortField);
            if ("asc".equalsIgnoreCase(sortOrder) || sortOrder == null) {
                page.addOrder(OrderItem.asc(sortField));
            } else if ("desc".equalsIgnoreCase(sortOrder)) {
                page.addOrder(OrderItem.desc(sortField));
            }
        }
        return page;
    }
} 