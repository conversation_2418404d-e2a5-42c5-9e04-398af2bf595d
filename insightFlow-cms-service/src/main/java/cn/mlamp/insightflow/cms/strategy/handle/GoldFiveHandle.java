//package cn.mlamp.insightflow.cms.strategy.handle;
//
//import cn.mlamp.insightflow.cms.config.AnalysisVideoConfig;
//import cn.mlamp.insightflow.cms.util.VideoUtil;
//import com.alibaba.fastjson.JSONObject;
//import lombok.AllArgsConstructor;
//import lombok.Data;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.http.HttpEntity;
//import org.springframework.http.HttpHeaders;
//import org.springframework.http.HttpMethod;
//import org.springframework.http.ResponseEntity;
//import org.springframework.stereotype.Service;
//import org.springframework.web.client.RestTemplate;
//
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Map;
//
///**
// * @Author: husuper
// * @CreateTime: 2025-03-25
// */
//@Service
//@Slf4j
//public class GoldFiveHandle {
//
//    @Autowired
//    private RestTemplate restTemplate;
//
//    @Autowired
//    private AnalysisVideoConfig analysisVideoConfig;
//
//    public static void main(String[] args) {
//        VideoUtil.setFfmpegPath("/Users/<USER>/Documents/ffmpeg/ffmpeg");
//        VideoUtil.setFfprobePath("/Users/<USER>/Documents/ffmpeg/ffprobe");
//        GoldFiveHandle goldFiveHandle = new GoldFiveHandle();
//        goldFiveHandle.restTemplate = new RestTemplate();
//        goldFiveHandle.analysisVideoConfig=new AnalysisVideoConfig();
//        goldFiveHandle.analysisVideoConfig.setAnalyzeUrl("http://10.10.100.228:8354");
//        List<String> images=new ArrayList<>();
////        images.add(VideoUtil.convertImageToBase64("/Users/<USER>/Documents/OSS/未命名文件夹/0_0a4e3517c07ca9b00a544892611f8405_005.jpg"));
//        images.add(VideoUtil.cutImageOfBase64("/Users/<USER>/Documents/OSS/122354564654565643.mp4",1000));
//        images.add(VideoUtil.cutImageOfBase64("/Users/<USER>/Documents/OSS/122354564654565643.mp4",2000));
////        images.add(VideoUtil.cutImageOfBase64("/Users/<USER>/Documents/OSS/122354564654565643.mp4",3000));
////        images.add(VideoUtil.cutImageOfBase64("/Users/<USER>/Documents/OSS/122354564654565643.mp4",4000));
////        images.add(VideoUtil.cutImageOfBase64("/Users/<USER>/Documents/OSS/122354564654565643.mp4",5000));
//        List<Sentence> sentences=new ArrayList<>();
////        sentences.add(new Sentence("你喜欢什么？",1000,2000));
////        sentences.add(new Sentence("什么？",2000,3000));
//        ApiResponse    apiResponse= goldFiveHandle.callImageDecoding5s("123",images,sentences,null,"你看到的个别大胃王，都有怎么起号的？");
////        System.out.println(apiResponse);
//
//    }
//
//    public ApiResponse callImageDecoding5s(String observationId, List<String> images, List<Sentence> sentences, String title, String content) {
//        String url = analysisVideoConfig.getAnalyzeUrl()+"/image_decoding_5s";
//
//
//        HttpHeaders headers = new HttpHeaders();
//        headers.set("Content-Type", "application/json");
//        headers.set("observation-id", observationId);
//
//        RequestPayload payload = new RequestPayload(images, sentences, title, content);
////        log.info("调用黄金5秒服务请求{}",JSONObject.toJSONString(payload));
//        HttpEntity<RequestPayload> requestEntity = new HttpEntity<>(payload, headers);
//        ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, requestEntity,String.class);
//        log.info("调用黄金5秒服务返回{}",response.getBody());
//        return JSONObject.parseObject(response.getBody(),ApiResponse.class);
//    }
//
//    @Data
//    @AllArgsConstructor
//    public static class Sentence {
//        //台词文本
//        private String text;
//        //开始时间戳
//        private int start;
//        //结束时间戳
//        private int end;
//
//    }
//
//    @Data
//    public static class RequestPayload {
//        //前5秒图片base64
//        private List<String> images;
//        //前5秒ASR结果
//        private List<Sentence> sentences;
//        private String title;
//        private String content;
//
//        public RequestPayload(List<String> images, List<Sentence> sentences, String title, String content) {
//            this.images = images;
//            this.sentences = sentences;
//            this.title = title;
//            this.content = content;
//        }
//
//    }
//
//    @Data
//    public static class ApiResponse {
//        private String message;
//        private int code;
//        private Map<String,Object> data;
//
//    }
//}