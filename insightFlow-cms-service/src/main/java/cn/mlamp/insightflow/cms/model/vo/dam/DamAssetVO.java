package cn.mlamp.insightflow.cms.model.vo.dam;

import java.util.Date;
import java.util.List;

import cn.mlamp.insightflow.cms.entity.dam.DamAsset;
import cn.mlamp.insightflow.cms.entity.dam.DamDirectory;
import cn.mlamp.insightflow.cms.enums.dam.DamDirectoryTypeEnum;
import cn.mlamp.insightflow.cms.model.dto.dam.DamTagValueDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * DAM素材VO
 */
@Data
@Schema(description = "DAM素材VO")
public class DamAssetVO {

    @Schema(description = "素材ID")
    private Integer id;

    @Schema(description = "素材名称")
    private String name;

    @Schema(description = "创建者")
    private String createBy;

    @Schema(description = "所属目录ID")
    private Integer directoryId;

    @Schema(description = "目录名称")
    private String directoryName;

    @Schema(description = "目录类型")
    private DamDirectoryTypeEnum directoryType;

    @Schema(description = "视频时长（秒）")
    private Integer duration;

    @Schema(description = "缩略图OSS ID")
    private String thumbnailOssId;

    @Schema(description = "缩略图URL")
    private String thumbnailUrl;

    @Schema(description = "画面比率 16:9、9:16、1:1")
    private String aspectRatio;

    @Schema(description = "扩展名")
    private String ext;

    @Schema(description = "OSS ID")
    private String ossId;

    @Schema(description = "OSS存储URL")
    private String ossUrl;

    @Schema(description = "标签列表")
    private List<DamTagValueDTO> tags;

    @Schema(description = "是否入库")
    private Boolean isStored;

    @Schema(description = "入库时间")
    private Date storageTime;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;

    // 换算为秒
    public int getDuration() {
        return duration == null ? 0 : duration / 1000;
    }

    public static DamAssetVO from(
            DamAsset asset,
            DamDirectory directory,
            String ossUrl,
            String thumbnailUrl
    ) {
        final DamAssetVO vo = new DamAssetVO();
        vo.setId(asset.getId());
        vo.setName(asset.getName());
        vo.setDirectoryId(asset.getDirectoryId());
        if (directory != null) {
            vo.setDirectoryName(directory.getName());
            vo.setDirectoryType(directory.getType());
        }
        vo.setDuration(asset.getDuration());
        vo.setThumbnailUrl(thumbnailUrl);
        vo.setThumbnailOssId(asset.getThumbnailOssId());
        vo.setAspectRatio(asset.getAspectRatio());
        vo.setExt(asset.getExt());
        vo.setOssUrl(ossUrl);
        vo.setOssId(asset.getOssId());
        vo.setIsStored(asset.getIsStored());
        vo.setStorageTime(asset.getStorageTime());
        vo.setCreateTime(asset.getCreateTime());
        vo.setUpdateTime(asset.getUpdateTime());
        if (asset.getCreateBy() != null) {
            vo.setCreateBy(asset.getCreateBy());
        }
        return vo;
    }

} 