package cn.mlamp.insightflow.cms.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@AllArgsConstructor
@Data
@NoArgsConstructor
public class SubjectInfoVO implements java.io.Serializable {

    @Schema(description = "主体id", required = true)
    private Integer subjectId;

    @Schema(description = "主体姓名", required = true)
    private String subjectName;

    @Schema(description = "主体标签", required = false)
    private String subjectLabel;

    @Schema(description = "主体可见性: 0 个人可见 1 租户可见", required = true)
    private Integer visibility;

    @Schema(description = "主体风格", required = true)
    private String subjectStyle;

    @Schema(description = "主体描述", required = true)
    private String subjectDescription;

    @Schema(description = "主体图片对象集合", required = true)
    private List<SubjectImageInfo> subjectImages;

    @Data
    public static class SubjectImageInfo implements Serializable {

        @Schema(description = "主体图片url", required = true)
        private String imageUrl;

        @Schema(description = "主体图片ossId", required = true)
        private String imageOssId;


        @Schema(description = "主体图片排序", required = true)
        private Integer imageOrder;
    }
}
