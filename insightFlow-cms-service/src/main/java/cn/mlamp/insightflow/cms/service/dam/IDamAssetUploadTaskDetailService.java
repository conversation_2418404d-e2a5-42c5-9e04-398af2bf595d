package cn.mlamp.insightflow.cms.service.dam;

import java.util.List;

import cn.mlamp.insightflow.cms.model.vo.dam.DamAssetUploadTaskDetailVO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;

import cn.mlamp.insightflow.cms.entity.dam.DamAssetUploadTaskDetail;
import cn.mlamp.insightflow.cms.enums.dam.DamTaskTypeEnum;
import cn.mlamp.insightflow.cms.model.dto.dam.DamAssetUploadTaskDTO;
import cn.mlamp.insightflow.cms.model.dto.dam.DamTagValueDTO;
import cn.mlamp.insightflow.cms.model.query.PageParam;
import cn.mlamp.insightflow.cms.model.vo.dam.DamAssetUploadTaskVO;
import cn.mlamp.insightflow.cms.model.vo.dam.DamPageResult;

/**
 * <p>
 * DAM素材上传任务表-记录细节 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
public interface IDamAssetUploadTaskDetailService extends IService<DamAssetUploadTaskDetail> {

    List<DamAssetUploadTaskDetail> listByTaskId(Integer taskId);

    List<DamAssetUploadTaskDetail> listByTaskIds(List<Integer> taskIds);

    List<DamAssetUploadTaskDetail> listByTaskIdAndAssetIds(Integer taskId, List<Integer> assetIds);

}
