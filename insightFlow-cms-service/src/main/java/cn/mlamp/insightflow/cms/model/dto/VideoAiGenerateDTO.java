package cn.mlamp.insightflow.cms.model.dto;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

public class VideoAiGenerateDTO {
    // 视频生成请求体
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ViduCreateRequest {
        @NotNull
        private List<String> ossId;
        @NotNull
        private String theme;
        @NotNull
        private String style;
        @NotNull
        private String environment;
        @NotNull
        private String condition;
        @NotNull
        private Integer duration;
        @NotNull
        private String movementAmplitude;
        @NotNull
        private String aspectRatio;
        @NotNull
        private String resolution;
        @NotNull
        private Integer frequency;
    }
}
