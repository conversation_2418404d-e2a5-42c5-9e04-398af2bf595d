package cn.mlamp.insightflow.cms.model.converter.dam;

import cn.mlamp.insightflow.cms.entity.dam.DamDirectory;
import cn.mlamp.insightflow.cms.model.dto.dam.DamDirectoryDTO;
import cn.mlamp.insightflow.cms.model.vo.dam.DamDirectoryVO;

import java.util.List;

/**
 * DamDirectory 转换类
 *
 * <AUTHOR> liuyuan
 **/
@org.mapstruct.Mapper(componentModel = "spring")
public interface DamDirectoryConverter {

    DamDirectory toEntity(DamDirectoryDTO directoryDTO);

    /**
     * 对象转换方法
     *
     * @param entity 实体
     * @return VO
     */
    DamDirectoryVO toVO(DamDirectory entity);

    /**
     * 对象集合转换方法
     *
     * @param entities 实体列表
     * @return VO 列表
     */
    List<DamDirectoryVO> toVOs(List<DamDirectory> entities);

}
