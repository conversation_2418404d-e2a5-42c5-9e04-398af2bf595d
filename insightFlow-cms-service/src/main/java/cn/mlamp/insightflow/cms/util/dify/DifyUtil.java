package cn.mlamp.insightflow.cms.util.dify;//package cn.mlamp.insightflow.cms.util.dify;


import cn.mlamp.insightflow.cms.exception.BusinessException;
import cn.mlamp.insightflow.cms.util.dify.model.DifyRequest;
import cn.mlamp.insightflow.cms.util.dify.model.ViduImageFile;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Dify调用工具类
 */
@Slf4j
public class DifyUtil {

    private static final String user = "insight-flow-cms";
    private static final MediaType JSON = MediaType.get("application/json; charset=utf-8");
    private static final ObjectMapper objectMapper = new ObjectMapper();
    private final OkHttpClient httpClient = new OkHttpClient.Builder()
            .connectTimeout(500, TimeUnit.SECONDS) // 连接超时时间
            .readTimeout(500, TimeUnit.SECONDS)    // 读取超时时间
            .writeTimeout(500, TimeUnit.SECONDS)   // 写入超时时间
            .build();

    private static final OkHttpClient client = new OkHttpClient().newBuilder().connectTimeout(60, TimeUnit.SECONDS)
            .readTimeout(60, TimeUnit.SECONDS).writeTimeout(60, TimeUnit.SECONDS).build();





    public String blockingChatRequest(DifyRequest difyRequest, String observationId) {
        if (difyRequest == null) {
            return null;
        }
        // 应用key
        final String appKey = difyRequest.getAppKey();
        // 输入参数
        final Map<String, Object> inputsParams = difyRequest.getInputsParams();
        // query
        final String query = difyRequest.getQuery();
        String baseUrl = difyRequest.getBaseUrl();
        // 由输入参数构造inputs json
        JSONObject inputs = new JSONObject();

        // 存在输入参数 添加进inputs json中
        if (inputsParams != null) {
            for (Map.Entry<String, Object> entry : inputsParams.entrySet()) {
                try {
                    inputs.put(entry.getKey(), entry.getValue());
                } catch (JSONException e) {
                    log.error("发起dify聊天请求失败:json转换失败", e);
                    return null;
                }
            }
        }

        // 构造请求json
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("query", query);
            jsonObject.put("inputs", inputs);
            jsonObject.put("response_type", "blocking");
            jsonObject.put("user", user);
            jsonObject.put("file", new JSONArray());
        } catch (JSONException e) {
            log.error("发起dify聊天请求失败:json转换失败", e);
            throw new BusinessException("发起dify聊天请求失败:json转换失败");
        }
        final Request req = new Request.Builder()
                .url(baseUrl + "/chat-messages").addHeader("Authorization", "Bearer " + appKey)
                .addHeader("Content-Type", "application/json")
                .addHeader("observation-id", observationId)
                .post(RequestBody.create(MediaType.parse("application/json"), jsonObject.toString())).build();
        Response response = null;
        try {
            try {
                response = client.newCall(req).execute();
            } catch (IOException e) {
                log.error("发起dify聊天请求失败:网络请求失败", e);
                throw new BusinessException("发起dify聊天请求失败:网络请求失败");
            }
            if (response.isSuccessful()) {
                try {
                    String responseBody = response.body().string();
                    JSONObject jsonObject1 = new JSONObject(responseBody);
                    //todo

                    return jsonObject1.get("answer").toString();
                } catch (Exception e) {
                    log.error("发起dify聊天请求失败:json解析失败", e);
                    throw new BusinessException("发起dify聊天请求失败:json解析失败");
                }

            } else {
                try {
                    throw new BusinessException(new Gson().fromJson(response.body().string(), JsonObject.class)
                            .get("message").getAsString());
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return null;
    }



    /**
     * 发送阻塞聊天请求
     *
     * @param difyRequest 请求参数
     * @return 返回聊天结果
     */
    public String blockingImageChatRequest(DifyRequest difyRequest, String observationId) {
        if (difyRequest == null) {
            return null;
        }
        // 应用key
        final String appKey = difyRequest.getAppKey();
        // 输入参数
        final Map<String, Object> inputsParams = difyRequest.getInputsParams();
        // query
        final String query = difyRequest.getQuery();
        String baseUrl = difyRequest.getBaseUrl();
        // 由输入参数构造inputs json
        JSONObject inputs = new JSONObject();

     /*   final ViduImageFile[] files = difyRequest.getFiles();
        JSONObject[] jsonObjects = new JSONObject[files.length];
        for (int i = 0; i < files.length; i++) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("type", files[i].getType());
            jsonObject.put("transfer_method", files[i].getTransfer_method());
            jsonObject.put("url", files[i].getUrl());
            jsonObjects[i] = jsonObject;
        }*/

        // 处理文件数组
        JSONArray filesArray = new JSONArray();
        final ViduImageFile[] files = difyRequest.getFiles();
        if (files != null) {
            for (ViduImageFile file : files) {
                JSONObject fileJson = new JSONObject();
                try {
                    fileJson.put("type", file.getType());
                    fileJson.put("transfer_method", file.getTransfer_method());
                    fileJson.put("url", file.getUrl());
                    filesArray.put(fileJson);
                } catch (JSONException e) {
                    log.error("文件参数转换失败", e);
                    return null;
                }
            }
        }

        // 存在输入参数 添加进inputs json中
        if (inputsParams != null) {
            for (Map.Entry<String, Object> entry : inputsParams.entrySet()) {
                try {
                    inputs.put(entry.getKey(), entry.getValue());
                } catch (JSONException e) {
                    log.error("发起dify聊天请求失败:json转换失败", e);
                    return null;
                }
            }
        }

        // 构造请求json
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("query", query);
            jsonObject.put("inputs", inputs);
            jsonObject.put("response_mode", "blocking");
            jsonObject.put("user", user);

            jsonObject.put("file",new JSONArray());

            jsonObject.put("files", filesArray);
        } catch (JSONException e) {
            log.error("发起dify聊天请求失败:json转换失败", e);
            throw new BusinessException("发起dify聊天请求失败:json转换失败");
        }
        final Request req = new Request.Builder()
                .url(baseUrl + "/chat-messages").addHeader("Authorization", "Bearer " + appKey)
                .addHeader("Content-Type", "application/json")
                .addHeader("observation-id", observationId)
                .post(RequestBody.create(MediaType.parse("application/json"), jsonObject.toString())).build();
        Response response = null;
        try {
            try {
                response = client.newCall(req).execute();
            } catch (IOException e) {
                log.error("发起dify聊天请求失败:网络请求失败", e);
                throw new BusinessException("发起dify聊天请求失败:网络请求失败");
            }
            if (response.isSuccessful()) {
                try {
                    String responseBody = response.body().string();
                    JSONObject jsonObject1 = new JSONObject(responseBody);
                    //todo

                    return jsonObject1.get("answer").toString();
                } catch (Exception e) {
                    log.error("发起dify聊天请求失败:json解析失败", e);
                    throw new BusinessException("发起dify聊天请求失败:json解析失败");
                }

            } else {
                try {
                    throw new BusinessException(new Gson().fromJson(response.body().string(), JsonObject.class)
                            .get("message").getAsString());
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return null;
    }

    /**
     * 执行workflow（流式模式）
     *
     * @param callback 流式响应回调
     * @throws IOException 网络或API错误
     */
    public void executeWorkflowStreaming(DifyRequest difyRequest,
                                         StreamingCallback callback) throws IOException {
        Request request = buildRequest(difyRequest);
        log.info("发起API请求: {}", request.url());
        httpClient.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                callback.onError(e);
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                if (!response.isSuccessful()) {
                    callback.onError(new IOException("API请求失败: " + response.code()));
                    return;
                }

                try (ResponseBody body = response.body()) {
                    if (body != null) {
                        String line;
                        while ((line = body.source().readUtf8Line()) != null) {
                            if (line.startsWith("data:")) {
                                String json = line.substring(5).trim();
                                if (!json.isEmpty()) {
                                    callback.onEvent(objectMapper.readValue(json, Map.class));
                                }
                            }
                        }
                    }
                }
            }
        });
    }

    private Request buildRequest(DifyRequest difyRequest) throws IOException {
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("inputs", difyRequest.getInputsParams());
        requestBody.put("response_mode", "streaming");
        requestBody.put("user", user);
        RequestBody body = RequestBody.create(objectMapper.writeValueAsString(requestBody), JSON);

        return new Request.Builder()
                .url(difyRequest.getBaseUrl() + "/workflows/run")
                .addHeader("Authorization", "Bearer " + difyRequest.getAppKey())
                .addHeader("Content-Type", "application/json")
                .addHeader("observation-id", difyRequest.getObservationId())
                .post(body)
                .build();
    }

    public interface StreamingCallback {
        void onEvent(Map<String, Object> event);

        void onError(Exception e);
    }

    /**
     * 流式调用并合并结果(流式转阻塞)
     *
     * @return 合并后的完整结果
     * @throws IOException          网络或API错误
     * @throws InterruptedException 线程中断异常
     */
    public Map<String, Object> executeAndMergeStreaming(DifyRequest difyRequest) throws IOException, InterruptedException {
        // 创建结果合并容器
        final Map<String, Object> mergedResult = new ConcurrentHashMap<>();
        final CountDownLatch latch = new CountDownLatch(1);
        final AtomicReference<Exception> error = new AtomicReference<>();

        // 执行流式调用
        executeWorkflowStreaming(difyRequest, new StreamingCallback() {
            @Override
            public void onEvent(Map<String, Object> event) {
                // 处理不同类型的事件
                String eventType = (String) event.get("event");
                switch (eventType) {
                    case "workflow_finished":
                        // 合并最终输出
                        Map<String, Object> data = (Map<String, Object>) event.get("data");
                        if (data.containsKey("outputs")) {
                            mergedResult.putAll((Map<? extends String, ?>) data.get("outputs"));
                        }
                        latch.countDown();
                        break;

                    case "error":
                        error.set(new IOException((String) event.get("error")));
                        latch.countDown();
                        break;

                    case "tts_message":
                        // 处理音频数据
                        mergedResult.put("audio_data", event.get("audio"));
                        break;
                }
            }

            @Override
            public void onError(Exception e) {
                error.set(e);
                latch.countDown();
            }
        });

        // 等待工作流完成
        latch.await();

        // 检查是否有错误
        if (error.get() != null) {
            throw new IOException("流式调用出错", error.get());
        }
        log.info("dify流式调用成功");
        return mergedResult;
    }

}
