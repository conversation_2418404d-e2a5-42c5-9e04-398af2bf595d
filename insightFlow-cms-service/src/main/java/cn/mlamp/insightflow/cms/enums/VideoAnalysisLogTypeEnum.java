package cn.mlamp.insightflow.cms.enums;


public enum VideoAnalysisLogTypeEnum {
    ASR_RECOGNITION(1, "ASR识别"),
    OVERALL_ANALYSIS(2, "整体理解"),
    VIDEO_SEGMENTATION(3, "视频分割"),
    GOLDEN_3_SECOND(4, "黄金3秒"),
    SHOT_MARKING(5, "分镜打标");

    private final int code;
    private final String desc;

    VideoAnalysisLogTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
