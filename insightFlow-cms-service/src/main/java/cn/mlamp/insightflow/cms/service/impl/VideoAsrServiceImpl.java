package cn.mlamp.insightflow.cms.service.impl;

import cn.mlamp.insightflow.cms.entity.CmsVideoAsr;
import cn.mlamp.insightflow.cms.mapper.VideoAsrMapper;
import cn.mlamp.insightflow.cms.service.IVideoAsrService;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * @Author: husuper
 * @CreateTime: 2025-03-21
 */
@RequiredArgsConstructor
@Service
public class VideoAsrServiceImpl extends ServiceImpl<VideoAsrMapper, CmsVideoAsr> implements IVideoAsrService {

    private final VideoAsrMapper videoAsrMapper;

    @Override
    public CmsVideoAsr getByEsIdWithMinStart(String esId) {
        LambdaQueryWrapper<CmsVideoAsr> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CmsVideoAsr::getEsId, esId).orderByAsc(CmsVideoAsr::getStart).last("LIMIT 1"); // 只查询一条数据，确保返回唯一值

        return videoAsrMapper.selectOne(queryWrapper);
    }

}
