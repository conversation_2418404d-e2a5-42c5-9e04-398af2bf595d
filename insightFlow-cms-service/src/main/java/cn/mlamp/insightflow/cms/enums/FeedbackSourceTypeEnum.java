package cn.mlamp.insightflow.cms.enums;


import lombok.Getter;

/**
 * 文件的状态信息
 */


@Getter
public enum FeedbackSourceTypeEnum {

    /**
     * 通用
     */
    NORMAL(0, "通用"),

    /**
     * 视频分析
     */
    VIDEO_ANALYSIS(1, "视频分析"),

    /**
     * AI仿写
     */
    AI_WRITE(2, "AI仿写"),

    ;

    private final int code;
    private final String msg;

    FeedbackSourceTypeEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static FeedbackSourceTypeEnum getByCode(int code) {
        for (FeedbackSourceTypeEnum type : FeedbackSourceTypeEnum.values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        return null;
    }
}
