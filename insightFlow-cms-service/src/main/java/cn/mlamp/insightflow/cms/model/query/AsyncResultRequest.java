package cn.mlamp.insightflow.cms.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Author: husuper
 * @CreateTime: 2025-05-13
 */
@Data
public class AsyncResultRequest  implements java.io.Serializable{

    @Schema(description = "日常任务、用户任务 daily/customer", required = true)
    private String taskQueue;

    @Schema(description = "任务Id", required = true)
    private Integer dbUniqueId;

    @Schema(description = "具体数据", required = true)
    private Object data;

    private Integer code;

    private String message;


}
