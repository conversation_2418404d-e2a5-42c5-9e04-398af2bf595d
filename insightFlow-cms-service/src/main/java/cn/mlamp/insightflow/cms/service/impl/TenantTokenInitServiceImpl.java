package cn.mlamp.insightflow.cms.service.impl;

import cn.mlamp.insightflow.cms.entity.TenantToken;
import cn.mlamp.insightflow.cms.mapper.TenantTokenMapper;
import cn.mlamp.insightflow.cms.service.TenantTokenInitService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 租户Token初始化服务实现类
 *
 * <AUTHOR>
 * @since 2024-10-15
 */
@Slf4j
@Service
public class TenantTokenInitServiceImpl implements TenantTokenInitService {

    @Autowired
    private TenantTokenMapper tenantTokenMapper;

    @Override
    public void initTenantToken(Integer tenantId) {
        if (tenantId == null) {
            log.warn("租户ID为空，无法初始化租户Token记录");
            return;
        }

        // 查询租户Token记录是否存在
        TenantToken tenantToken = tenantTokenMapper.selectOne(
                new LambdaQueryWrapper<TenantToken>().eq(TenantToken::getTenantId, tenantId));

        // 如果不存在，创建一条新记录
        if (tenantToken == null) {
            tenantToken = new TenantToken();
            tenantToken.setTenantId(tenantId);
            tenantToken.setBalance(100000000);
            tenantToken.setAccumulatedRecharge(100000000);
            tenantToken.setAccumulatedExpenses(0);
            tenantToken.setStatisticalTime(new Date());
            tenantTokenMapper.insert(tenantToken);
            log.info("新租户Token记录已创建，tenantId: {}, 初始余额: {}", tenantId, 100000000);
        }
    }
}
