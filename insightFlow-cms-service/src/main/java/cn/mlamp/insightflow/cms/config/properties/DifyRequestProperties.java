package cn.mlamp.insightflow.cms.config.properties;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Configuration
@ConfigurationProperties(prefix = "dify")
public class DifyRequestProperties {

    // 请求路径
    private String baseUrl;

    // 产品信息总结 API Key
    private String productSummaryKey;

    // 分镜信息修改 API Key
    private String storyboardModifyKey;

    // 分镜推荐 API Key
    private String storyboardRecommendKey;

    // 脚本生成 API Key
    private String scriptGenKey;

    // AI仿写 API Key
    private String aiWriteKey;

    // 图片提示词翻译 API Key
    private String imagePromptTranslateKey;

    // 黄金五秒脚本生成 API Key
    private String goldFiveSecondKey;

    // 图片风格和描述 API Key
    private String imageStyleAndDescKey;

}
