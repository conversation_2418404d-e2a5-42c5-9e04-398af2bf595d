package cn.mlamp.insightflow.cms.helper;

import cn.hutool.core.util.ObjectUtil;
import cn.mlamp.insightflow.cms.constant.CommonConstant;
import cn.mlamp.insightflow.cms.enums.RequestDeviceSourceEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;

import jakarta.servlet.http.HttpServletRequest;

/**
 * 请求端来源辅助类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/13
 **/
@Slf4j
public class RequestDeviceHelper {

    public static String getRequestDeviceFromHeader(HttpServletRequest request) {
        if (request == null) {
            return StringUtils.EMPTY;
        } else {
            String requestDevice = request.getHeader(CommonConstant.REQUEST_HEADER_DEVICE_SOURCE);
            return Strings.isNotEmpty(requestDevice) ? requestDevice : StringUtils.EMPTY;
        }

    }

    /**
     * 是否小程序的请求
     *
     * @param request
     * @return
     */
    public static Boolean isMinProgramRequest(HttpServletRequest request) {
        String requestDeviceFromHeader = getRequestDeviceFromHeader(request);
        return ObjectUtil.equal(requestDeviceFromHeader, RequestDeviceSourceEnum.MINI_PROGRAM.getCode());
    }
}
