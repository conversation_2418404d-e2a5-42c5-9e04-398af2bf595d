package cn.mlamp.insightflow.cms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;

import java.util.Date;



@Data
public class BaseEntity implements java.io.Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    protected Integer id;

    protected Date createTime;

    protected Date updateTime;

    @TableLogic
    protected Integer isDeleted;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
