package cn.mlamp.insightflow.cms.service;

import cn.mlamp.insightflow.cms.entity.CmsTaskInfo;
import cn.mlamp.insightflow.cms.enums.VideoTaskStatusEnum;
import cn.mlamp.insightflow.cms.model.query.TaskDetailImageGenRequest;
import cn.mlamp.insightflow.cms.model.query.TaskDetailMagicRequest;
import cn.mlamp.insightflow.cms.model.query.VideoScriptGenRequest;
import cn.mlamp.insightflow.cms.model.vo.DocTaskUploadVO;
import cn.mlamp.insightflow.cms.model.vo.VideoStoryBoardVO;
import cn.mlamp.insightflow.cms.model.vo.VideoTaskListInfoVO;
import cn.mlamp.insightflow.cms.model.vo.VideoTaskScriptDetailVO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.scheduling.annotation.Async;

import java.io.IOException;
import java.util.List;

public interface ICmsTaskInfoService extends IService<CmsTaskInfo> {

    Integer videoTaskAiImitate(VideoScriptGenRequest request);

    @Async("commonTaskExecutor")
        // 异步生成视频脚本
    void videoTaskAiImitateAsync(VideoScriptGenRequest request, Integer taskId);

    Page<VideoTaskListInfoVO> videoTaskPage(Integer current, Integer pageSize, Integer type, Integer userId, Integer tenantId);

    List<DocTaskUploadVO> videoTaskStatus(Integer userId, Integer tenantId, List<Integer> taskIds);

    VideoTaskScriptDetailVO videoTaskDetail(Integer taskId, Integer userId, Integer tenantId);

    void exportVideoTask(Integer taskId, Integer storyboardId, HttpServletResponse response) throws IOException;

    void videoTaskCancel(Integer taskId, Integer userId, Integer tenantId);

    VideoTaskStatusEnum videoTaskRetry(Integer taskId, Integer userId, Integer tenantId);

    @Async("commonTaskExecutor")
    void retryVideoTaskAsync(Integer taskId, VideoTaskStatusEnum taskStatusEnum);

    int updateVideoTaskStatus(VideoTaskStatusEnum statusEnum, String errorMsg, Integer taskId, Integer userId, Integer tenantId);

    void deleteVideoTask(Integer taskId, Integer userId, Integer tenantId);

    List<Integer> storyBoardRecommend(VideoScriptGenRequest request);

    VideoTaskScriptDetailVO updateVideoTaskDetail(VideoTaskScriptDetailVO videoTaskScriptDetailVO, Integer userId, Integer tenantId);

    String videoTaskDetailMagic(TaskDetailMagicRequest request);

    VideoTaskScriptDetailVO videoTaskDetailReverse(Integer taskId, Integer userId, Integer tenantId);

    VideoStoryBoardVO videoTaskDetailImageGen(TaskDetailImageGenRequest request);

    VideoTaskScriptDetailVO recommendVideo(Integer taskId, String aspectRatio, Integer userId, Integer tenantId);
}
