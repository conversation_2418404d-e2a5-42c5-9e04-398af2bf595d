package cn.mlamp.insightflow.cms.config;


import java.util.Map;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import com.google.common.collect.Maps;

import cn.hutool.core.util.StrUtil;
import cn.mlamp.insightflow.cms.common.file.IS3FlowProperties;
import cn.mlamp.insightflow.cms.common.file.IS3FlowService;
import cn.mlamp.insightflow.cms.common.file.IS3FlowServiceImpl;
import cn.mlamp.insightflow.cms.config.properties.ObjectStorageFlowProperties;
import cn.mlamp.insightflow.cms.util.FilePathBuilder;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;

/**
 * S3Config
 */
@Configuration
public class S3FlowConfig {



    @Value("${spring.profiles.active:prod}")
    public String profile;

    @PostConstruct
    private void init(){
        FilePathBuilder.setProfile(profile);
    }

    private IS3FlowService createService(IS3FlowProperties is3Properties) {
        IS3FlowServiceImpl is3Service = new IS3FlowServiceImpl(is3Properties, null, null);
        CLIENT_CACHE.put(is3Properties.getBucketName(), is3Service);
        String aliasBucketName = is3Properties.getAliasBucketName();
        if (StrUtil.isNotBlank(aliasBucketName)) {
            CLIENT_CACHE.put(aliasBucketName, is3Service);
        }
        return is3Service;
    }

    @Resource
    private ObjectStorageFlowProperties properties;


    private static final Map<String, IS3FlowService> CLIENT_CACHE = Maps.newHashMap();

//    @Bean("pictureS3FlowService")
//    @Primary
//    public IS3FlowService pictureS3FLowService() {
//        IS3FlowProperties pic = properties.getPic();
//        if (Objects.nonNull(pic)) {
//            return createService(pic);
//        } else {
//            return null;
//        }
//
//    }

    @Bean("cmsS3FlowService")
    @Primary
    public IS3FlowService videoDecodeS3FlowService() {
        IS3FlowProperties pic = properties.getCms();
        if (Objects.nonNull(pic)) {
            return createService(pic);
        } else {
            return null;
        }

    }
    /**
     * 获取s3 客户端
     *
     * @param bucketName
     * @return
     */
    public static IS3FlowService getByBucket(String bucketName) {
        return CLIENT_CACHE.get(bucketName);
    }

    public String getProfile() {
        return profile;
    }
}
