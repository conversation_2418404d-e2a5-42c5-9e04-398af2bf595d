package cn.mlamp.insightflow.cms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 视频分析结果详情表;
 * <AUTHOR> husper
 * @date : 2025-3-19
 */
@Data
@TableName("cms_video_result_detail")
public class CmsVideoResultDetail extends BaseEntity{
    /** 主键 自增id */
    @TableId(type = IdType.AUTO)
    private Integer id ;
    /** 1：分镜详情 */
    @TableField("`type`")
    private Integer type ;
    /** 视频信息id */
    private Integer videoId ;
    /** 视频分析结果id */
    private Integer videoResultId ;
    /** json格式 */
    @TableField("`data`")
    private String data ;

}