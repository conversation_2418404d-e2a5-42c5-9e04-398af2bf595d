package cn.mlamp.insightflow.cms.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class VideoExistCheckRequestDTO {

    @Schema(description = "视频ID", example = "7123456789")
    private String videoId;

    @Schema(description = "视频标题", example = "示例视频标题")
    private String title;

    @Schema(description = "视频时长（秒）", example = "60")
    private Integer duration;
}
