package cn.mlamp.insightflow.cms.service.impl;

import cn.mlamp.insightflow.cms.config.properties.DifyRequestProperties;
import cn.mlamp.insightflow.cms.entity.CmsVideoFiveGold;
import cn.mlamp.insightflow.cms.entity.CmsVideoResult;
import cn.mlamp.insightflow.cms.entity.CmsVideoThreeGoldRelation;
import cn.mlamp.insightflow.cms.entity.QianchuanMaterialVideo;
import cn.mlamp.insightflow.cms.exception.BusinessException;
import cn.mlamp.insightflow.cms.mapper.QianchuanMaterialVideoMapper;
import cn.mlamp.insightflow.cms.mapper.VideoFiveGoldMapper;
import cn.mlamp.insightflow.cms.mapper.VideoThreeGoldRelationMapper;
import cn.mlamp.insightflow.cms.model.dto.DifyGoldFiveSecondRequestDTO;
import cn.mlamp.insightflow.cms.model.query.VideoFiveGoldDetailRequest;
import cn.mlamp.insightflow.cms.model.query.VideoFiveGoldRequest;
import cn.mlamp.insightflow.cms.model.query.VideoFiveGoldRetryScriptRequest;
import cn.mlamp.insightflow.cms.model.vo.VideoFiveGoldDetailPageVO;
import cn.mlamp.insightflow.cms.model.vo.VideoFiveGoldDetailVO;
import cn.mlamp.insightflow.cms.model.vo.VideoFiveGoldVO;
import cn.mlamp.insightflow.cms.service.IVideoFiveGoldService;
import cn.mlamp.insightflow.cms.service.IVideoResultService;
import cn.mlamp.insightflow.cms.util.dify.DifyUtil;
import cn.mlamp.insightflow.cms.util.dify.model.DifyRequest;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;

/**
 * @Author: husuper
 * @CreateTime: 2025-03-21
 */
@Slf4j
@Service
public class VideoFiveGoldServiceImpl extends ServiceImpl<VideoFiveGoldMapper, CmsVideoFiveGold> implements IVideoFiveGoldService {

    @Autowired
    private VideoFiveGoldMapper videoFiveGoldMapper;

    @Autowired
    private VideoThreeGoldRelationMapper videoThreeGoldRelationMapper;

    @Autowired
    private QianchuanMaterialVideoMapper qianchuanMaterialVideoMapper;

    @Autowired
    private IVideoResultService videoResultService;

    @Autowired
    private DifyRequestProperties difyRequestProperties;

    private final DifyUtil difyUtil = new DifyUtil();

    @Override
    public Page<VideoFiveGoldVO> getList(VideoFiveGoldRequest videoFiveGoldRequest) {
        QueryWrapper<CmsVideoFiveGold> queryWrapper = new QueryWrapper<>();

        // 添加行业筛选条件
        if (StringUtils.isNotBlank(videoFiveGoldRequest.getIndustry())) {
            queryWrapper.eq("industry", videoFiveGoldRequest.getIndustry());
        }

        // 添加黄金标签筛选条件
        if (StringUtils.isNotBlank(videoFiveGoldRequest.getTag())) {
            queryWrapper.eq("tag", videoFiveGoldRequest.getTag());
        }

        // 添加排序条件
        if (StringUtils.isNotBlank(videoFiveGoldRequest.getSortField())) {
            String sortField = videoFiveGoldRequest.getSortField();
            String sortOrder = videoFiveGoldRequest.getSortOrder();

            // 根据排序字段和排序方式添加排序条件
            switch (sortField) {
            case "videoNum":
                queryWrapper.orderBy(true, "asc".equals(sortOrder), "video_num");
                break;
            case "exposureCount":
                queryWrapper.orderBy(true, "asc".equals(sortOrder), "exposure_count");
                break;
            case "interactCount":
                queryWrapper.orderBy(true, "asc".equals(sortOrder), "interact_count");
                break;
            case "likeCount":
                queryWrapper.orderBy(true, "asc".equals(sortOrder), "like_count");
                break;
            case "commentCount":
                queryWrapper.orderBy(true, "asc".equals(sortOrder), "comment_count");
                break;
            case "originalityNum":
                queryWrapper.orderBy(true, "asc".equals(sortOrder), "originality_num");
                break;
            default:
                // 默认按更新时间降序排序
                queryWrapper.orderByDesc("update_time");
                break;
            }
        } else {
            // 默认按更新时间降序排序
            queryWrapper.orderByDesc("update_time");
        }
        Page<CmsVideoFiveGold> page =this.baseMapper.selectPage(new Page<>(videoFiveGoldRequest.getCurrent(), videoFiveGoldRequest.getPageSize()), queryWrapper);

        List<CmsVideoFiveGold> videoFiveGoldList = page.getRecords();
        List<VideoFiveGoldVO> videoFiveGoldVOList = new ArrayList<>();
        for (CmsVideoFiveGold videoFiveGold : videoFiveGoldList){
            VideoFiveGoldVO videoFiveGoldVO = new VideoFiveGoldVO();
            BeanUtils.copyProperties(videoFiveGold,videoFiveGoldVO);
            videoFiveGoldVOList.add(videoFiveGoldVO);
        }
        Page<VideoFiveGoldVO> page2=new Page<>();
        page2.setRecords(videoFiveGoldVOList);
        page2.setTotal(page.getTotal());
        page2.setSize(page.getSize());
        page2.setCurrent(page.getCurrent());
        page2.setPages(page.getPages());
        return page2;
    }



    @Override
    public VideoFiveGoldDetailPageVO getListDetail(VideoFiveGoldDetailRequest videoFiveGoldDetailRequest) {
        // 检查黄金5秒ID是否存在
        CmsVideoFiveGold fiveGold = this.getById(videoFiveGoldDetailRequest.getFiveGoledId());
        if (fiveGold == null) {
            throw new BusinessException("黄金5秒ID不存在");
        }

        // 创建分页对象
        Page<QianchuanMaterialVideo> page = new Page<>();
        page.setCurrent(videoFiveGoldDetailRequest.getCurrent());
        page.setSize(videoFiveGoldDetailRequest.getPageSize());

        // 1. 查询黄金5秒关联的视频ID列表
        LambdaQueryWrapper<CmsVideoThreeGoldRelation> relationQueryWrapper = new LambdaQueryWrapper<>();
        relationQueryWrapper
                .eq(CmsVideoThreeGoldRelation::getVideoThreeGoldId, videoFiveGoldDetailRequest.getFiveGoledId())
                .eq(CmsVideoThreeGoldRelation::getIsDeleted, 0);
        List<CmsVideoThreeGoldRelation> relationList = videoThreeGoldRelationMapper.selectList(relationQueryWrapper);

        // 创建返回结果对象
        VideoFiveGoldDetailPageVO result = new VideoFiveGoldDetailPageVO();
        result.setTag(fiveGold.getTag());
        result.setDialogueRoutine(fiveGold.getDialogueRoutine());

        if (relationList.isEmpty()) {
            // 如果没有关联视频，返回空结果
            result.setPage(page);
            return result;
        }

        // 提取视频ID列表
        List<String> videoIds = relationList.stream().map(CmsVideoThreeGoldRelation::getVideoId)
                .collect(Collectors.toList());

        // 2. 查询千川素材视频数据
        LambdaQueryWrapper<QianchuanMaterialVideo> videoQueryWrapper = new LambdaQueryWrapper<>();
        videoQueryWrapper.in(QianchuanMaterialVideo::getVideoId, videoIds).eq(QianchuanMaterialVideo::getIsDeleted, 0);

        // 添加排序条件
        if (StringUtils.isNotBlank(videoFiveGoldDetailRequest.getSortField())) {
            String sortField = videoFiveGoldDetailRequest.getSortField();
            String sortOrder = videoFiveGoldDetailRequest.getSortOrder();

            // 根据排序字段和排序方式添加排序条件
            switch (sortField) {
            case "exposure":
                videoQueryWrapper.orderBy(true, "asc".equals(sortOrder), QianchuanMaterialVideo::getExposure);
                break;
            case "shares":
                videoQueryWrapper.orderBy(true, "asc".equals(sortOrder), QianchuanMaterialVideo::getShares);
                break;
            case "comments":
                videoQueryWrapper.orderBy(true, "asc".equals(sortOrder), QianchuanMaterialVideo::getComments);
                break;
            case "likes":
                videoQueryWrapper.orderBy(true, "asc".equals(sortOrder), QianchuanMaterialVideo::getLikes);
                break;
            case "clicks":
                videoQueryWrapper.orderBy(true, "asc".equals(sortOrder), QianchuanMaterialVideo::getClicks);
                break;
            case "rating":
                videoQueryWrapper.orderBy(true, "asc".equals(sortOrder), QianchuanMaterialVideo::getRating);
                break;
            case "consume_range_weight":
                videoQueryWrapper.orderBy(true, "asc".equals(sortOrder), QianchuanMaterialVideo::getConsumeRangeWeight);
                break;
            default:
                // 默认按发布时间降序排序
                videoQueryWrapper.orderByDesc(QianchuanMaterialVideo::getPublishTime);
                break;
            }
        } else {
            // 默认按发布时间降序排序
            videoQueryWrapper.orderByDesc(QianchuanMaterialVideo::getPublishTime);
        }

        // 执行分页查询
        Page<QianchuanMaterialVideo> videoPage = new Page<>(page.getCurrent(), page.getSize());
        Page<QianchuanMaterialVideo> resultVideoPage = qianchuanMaterialVideoMapper.selectPage(videoPage,
                videoQueryWrapper);

        // 设置分页数据
        result.setPage(resultVideoPage);

        return result;
    }

    @Override
    public boolean retryScript(VideoFiveGoldRetryScriptRequest request) {
        // 1. 查询黄金5秒记录
        CmsVideoFiveGold fiveGold = this.getById(request.getFiveGoldId());
        if (fiveGold == null) {
            throw new BusinessException("黄金5秒ID不存在");
        }

        // 2. 查询黄金5秒关联的视频ID列表
        LambdaQueryWrapper<CmsVideoThreeGoldRelation> relationQueryWrapper = new LambdaQueryWrapper<>();
        relationQueryWrapper.eq(CmsVideoThreeGoldRelation::getVideoThreeGoldId, request.getFiveGoldId())
                .eq(CmsVideoThreeGoldRelation::getIsDeleted, 0);
        List<CmsVideoThreeGoldRelation> relationList = videoThreeGoldRelationMapper.selectList(relationQueryWrapper);

        if (relationList.isEmpty()) {
            throw new BusinessException("黄金5秒没有关联的视频");
        }

        // 3. 提取视频ID列表
        List<String> videoIds = relationList.stream().map(CmsVideoThreeGoldRelation::getVideoId)
                .collect(Collectors.toList());

        // 4. 查询视频的ASR 5秒结果
        Map<String, String> esIdToAsr5Map = videoResultService
                .getVideoAsr5Results(videoIds.stream().map(Integer::valueOf).collect(Collectors.toList()));

        if (esIdToAsr5Map.isEmpty()) {
            throw new BusinessException("没有查询到视频的ASR 5秒结果");
        }

        // 5. 将ASR 5秒结果转换为列表
        List<String> asr5List = new ArrayList<>(esIdToAsr5Map.values());

        try {
            // 6. 调用Dify工作流API生成脚本
            String script = generateScriptByDify(asr5List);

            // 7. 更新黄金5秒记录的台词套路
            fiveGold.setDialogueRoutine(script);
            this.updateById(fiveGold);

            return true;
        } catch (Exception e) {
            log.error("重试台词套路失败，黄金5秒ID：{}", request.getFiveGoldId(), e);
            throw new BusinessException("重试台词套路失败：" + e.getMessage());
        }
    }

    /**
     * 调用Dify工作流API生成脚本
     *
     * @param asr5List ASR 5秒值列表
     * @return 生成的脚本
     * @throws Exception 异常信息
     */
    private String generateScriptByDify(List<String> asr5List) throws Exception {
        // 构建Dify请求
        DifyRequest difyRequest = DifyRequest.builder().appKey(difyRequestProperties.getGoldFiveSecondKey())
                .baseUrl(difyRequestProperties.getBaseUrl())
                .inputsParams(DifyGoldFiveSecondRequestDTO.buildDifyParams(asr5List)).build();

        log.info("调用Dify工作流API生成脚本中...");

        // 执行请求
        Map<String, Object> difyResult = difyUtil.executeAndMergeStreaming(difyRequest);

        // 检查结果
        if (!difyResult.containsKey("text")) {
            throw new Exception("Dify生成脚本失败");
        }

        // 返回生成的脚本
        return difyResult.get("text").toString();
    }
}
