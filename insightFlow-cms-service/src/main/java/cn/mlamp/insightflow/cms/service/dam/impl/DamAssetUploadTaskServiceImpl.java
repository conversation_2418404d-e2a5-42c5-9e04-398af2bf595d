package cn.mlamp.insightflow.cms.service.dam.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import cn.mlamp.insightflow.cms.common.file.IS3FlowService;
import cn.mlamp.insightflow.cms.config.properties.DamOSSProperties;
import cn.mlamp.insightflow.cms.config.properties.ObjectStorageFlowProperties;
import cn.mlamp.insightflow.cms.constant.FileConstant;
import cn.mlamp.insightflow.cms.entity.CmsUser;
import cn.mlamp.insightflow.cms.entity.TokenUseDetail;
import cn.mlamp.insightflow.cms.entity.dam.*;
import cn.mlamp.insightflow.cms.enums.ErrorCode;
import cn.mlamp.insightflow.cms.enums.TokenTaskTypeEnum;
import cn.mlamp.insightflow.cms.enums.VideoTaskStatusEnum;
import cn.mlamp.insightflow.cms.enums.dam.DamTaskStatusEnum;
import cn.mlamp.insightflow.cms.enums.dam.DamTaskTypeEnum;
import cn.mlamp.insightflow.cms.exception.BusinessException;
import cn.mlamp.insightflow.cms.mapper.dam.DamAssetUploadTaskDetailMapper;
import cn.mlamp.insightflow.cms.mapper.dam.DamAssetUploadTaskMapper;
import cn.mlamp.insightflow.cms.model.converter.dam.DamAssetUploadTaskDetailConverter;
import cn.mlamp.insightflow.cms.model.dto.dam.DamAssetUploadTaskArg;
import cn.mlamp.insightflow.cms.model.dto.dam.DamAssetUploadTaskDTO;
import cn.mlamp.insightflow.cms.model.dto.dam.DamAssetUploadTaskDTO.DamAssetUploadItemDTO;
import cn.mlamp.insightflow.cms.model.dto.dam.DamAssetUploadTaskStorageDTO;
import cn.mlamp.insightflow.cms.model.dto.dam.DamTagValueDTO;
import cn.mlamp.insightflow.cms.model.query.PageParam;
import cn.mlamp.insightflow.cms.model.vo.dam.DamAssetUploadItemVO;
import cn.mlamp.insightflow.cms.model.vo.dam.DamAssetUploadTaskDetailVO;
import cn.mlamp.insightflow.cms.model.vo.dam.DamPageResult;
import cn.mlamp.insightflow.cms.service.IUserService;
import cn.mlamp.insightflow.cms.service.TenantTokenService;
import cn.mlamp.insightflow.cms.service.TokenUseDetailService;
import cn.mlamp.insightflow.cms.service.VideoAiGenerationService;
import cn.mlamp.insightflow.cms.service.dam.*;
import cn.mlamp.insightflow.cms.task.dam.AssetUploadTask;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * DAM素材上传任务表-记录细节 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
@Slf4j
@Service
public class DamAssetUploadTaskServiceImpl
        extends ServiceImpl<DamAssetUploadTaskMapper, DamAssetUploadTask>
        implements IDamAssetUploadTaskService {

    @Autowired
    private IDamAssetService assetService;

    @Autowired
    private IDamTagService tagService;

    @Autowired
    private IDamDirectoryService directoryService;

    @Autowired
    private IDamAssetUploadTaskDetailService assetUploadTaskDetailService;

    @Autowired
    private IDamTagValueService tagValueService;

    @Autowired
    private IUserService userService;

    @Autowired
    private IDamAssetEmbeddingService embeddingService;

    @Autowired
    private DamAssetUploadTaskDetailConverter assetUploadTaskDetailConverter;

    @Autowired
    private TokenUseDetailService tokenUseDetailService;

    @Autowired
    private IS3FlowService s3Service;

    @Autowired
    private AssetUploadTask assetUploadTask;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private ObjectStorageFlowProperties storageProperties;

    @Autowired
    private DamOSSProperties ossProperties;

    @Autowired
    @Qualifier("assetUploadTaskThreadExecutor")
    private ExecutorService assetUploadTaskThreadExecutor;

    @Autowired
    private DamAssetUploadTaskDetailMapper assetUploadTaskDetailMapper;

    @Autowired
    private VideoAiGenerationService videoAiGenerationService;

    @Autowired
    private TenantTokenService tenantTokenService;

    private DamAssetUploadTask safeGetTaskInfoById(Integer taskId, Integer userId, Integer tenantId) {
        final DamAssetUploadTask taskInfo = getById(taskId);
        if (taskInfo == null) {
            throw new BusinessException(ErrorCode.NOT_FOUND);
        }
        if (!taskInfo.getUserId().equals(userId) || !taskInfo.getTenantId().equals(tenantId)) {
            throw new BusinessException(ErrorCode.AUTHORIZATION_FAIL, "没有权限操作该任务");
        }
        return taskInfo;
    }

    @Override
    public Integer createAssetUploadTask(DamAssetUploadTaskDTO taskDTO, Integer userId, Integer tenantId) {
        final DamTaskTypeEnum type = taskDTO.getType();
        final List<DamAssetUploadItemDTO> uploadedAssets = taskDTO.getAssets();
        // AI分镜视频上传,需要有videoTaskId
        if (type == DamTaskTypeEnum.AI_SEGMENT && taskDTO.getVideoTaskId() == null) {
            throw new BusinessException(ErrorCode.PARAM_ERROR);
        }
        if (CollectionUtil.isEmpty(uploadedAssets)) {
            throw new BusinessException(ErrorCode.PARAM_ERROR);
        }

        final DamDirectory directory = directoryService.getById(taskDTO.getDirectoryId());
        if (directory == null || !directory.getTenantId().equals(tenantId)
                || directory.getIsDeleted()) {
            throw new BusinessException(ErrorCode.PARAM_ERROR);
        }

        final List<DamAssetUploadTaskArg.Asset> argAssets = new ArrayList<>(uploadedAssets.size());
        final List<DamAsset> assets = new ArrayList<>(uploadedAssets.size());
        for (DamAssetUploadItemDTO item : uploadedAssets) {
            final String ext = FileUtil.getSuffix(item.getName());
            final String name = item.getName().replace("." + ext, "");
            final DamAsset asset = new DamAsset();
            asset.setTenantId(tenantId);
            asset.setUserId(userId);
            asset.setDirectoryId(taskDTO.getDirectoryId());
            asset.setName(name);
            asset.setExt(ext);
            asset.setIsStored(false);
            asset.setCreateTime(new Date());
            asset.setUpdateTime(new Date());
            assets.add(asset);

            final DamAssetUploadTaskArg.Asset argAsset = new DamAssetUploadTaskArg.Asset();
            argAsset.setOssId(item.getOssId());
            argAsset.setStart(item.getStart());
            argAsset.setEnd(item.getEnd());
            argAssets.add(argAsset);
        }

        assetService.saveBatch(assets);

        for (int i = 0; i < assets.size(); i++) {
            final DamAsset asset = assets.get(i);
            final DamAssetUploadTaskArg.Asset argAsset = argAssets.get(i);
            argAsset.setAssetId(asset.getId());
        }

        // 获取当前时刻租户内所有标签
        final List<DamTag> tags = tagService.list(
                new LambdaQueryWrapper<DamTag>()
                        .eq(DamTag::getTenantId, tenantId)
                        .eq(DamTag::getIsDeleted, false));

        final DamAssetUploadTaskArg taskArg = new DamAssetUploadTaskArg();
        taskArg.setDirectoryId(taskDTO.getDirectoryId());
        // AI分镜视频上传,需要保存videoTaskId
        if (type == DamTaskTypeEnum.AI_SEGMENT) {
            taskArg.setVideoTaskId(taskDTO.getVideoTaskId());
        }
        taskArg.setAssets(argAssets);
        taskArg.setTags(assetUploadTaskDetailConverter.toArgTags(tags));

        // 创建任务
        final DamAssetUploadTask task = new DamAssetUploadTask();
        task.setUserId(userId);
        task.setTenantId(tenantId);
        task.setType(type);
        task.setStatus(DamTaskStatusEnum.PENDING);
        try {
            task.setTaskArg(objectMapper.writeValueAsString(taskArg));
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        task.setCreateTime(new Date());
        task.setUpdateTime(new Date());
        save(task);

        // 创建任务详情
        final List<DamAssetUploadTaskDetail> taskDetails = assets.stream().map(asset -> {
            final DamAssetUploadTaskDetail taskDetail = new DamAssetUploadTaskDetail();
            taskDetail.setTaskId(task.getId());
            taskDetail.setAssetId(asset.getId());
            taskDetail.setStatus(DamTaskStatusEnum.PENDING);
            taskDetail.setCreateTime(new Date());
            taskDetail.setUpdateTime(new Date());
            return taskDetail;
        }).toList();

        assetUploadTaskDetailService.saveBatch(taskDetails);

        if (type == DamTaskTypeEnum.AI_SEGMENT) {
            // 更新AI分镜任务状态为入库中
            videoAiGenerationService.updateTaskStatus(taskDTO.getVideoTaskId(),
                    VideoTaskStatusEnum.VIDU_AI_VIDEO_ENTERSTOREHOUSE_ENTERING, userId, tenantId);
        }

        if (tenantTokenService.checkBalance(tenantId) < 2 * uploadedAssets.size()) {
            throw new BusinessException(ErrorCode.PARAM_ERROR, "租户点数不足");
        }

        // 立刻异步执行任务
        assetUploadTask.submit(task.getId());

        return task.getId();
    }

    private Map<Integer, List<DamAssetUploadItemVO>> getUploadItemVOs(List<Integer> taskIds, Integer userId, Integer tenantId) {
        if (CollUtil.isEmpty(taskIds)) {
            return Collections.emptyMap();
        }
        final List<DamAssetUploadItemVO> itemVOs = baseMapper.selectJoinList(
                DamAssetUploadItemVO.class,
                new MPJLambdaWrapper<DamAssetUploadTask>()
                        .selectAll(DamAsset.class)
                        .selectAs(DamDirectory::getId, "directoryId")
                        .selectAs(DamDirectory::getName, "directoryName")
                        .selectAs(DamDirectory::getType, "directoryType")
                        .selectAs(DamAssetUploadTaskDetail::getTaskId, "taskId")
                        .selectAs(DamAssetUploadTaskDetail::getId, "taskDetailId")
                        .selectAs(DamAssetUploadTaskDetail::getStatus, "status")
                        .selectAs(CmsUser::getUserName, "createBy")
                        .leftJoin(DamAssetUploadTaskDetail.class, DamAssetUploadTaskDetail::getTaskId, DamAssetUploadTask::getId)
                        .leftJoin(DamAsset.class, DamAsset::getId, DamAssetUploadTaskDetail::getAssetId, on -> on.eq(DamAsset::getTenantId, tenantId))
                        .leftJoin(DamDirectory.class, DamDirectory::getId, DamAsset::getDirectoryId, on -> on.eq(DamDirectory::getTenantId, tenantId))
                        .leftJoin(CmsUser.class, CmsUser::getUserId, DamAsset::getUserId)
                        .in(DamAssetUploadTask::getId, taskIds)
        );
        return itemVOs.stream()
                .collect(Collectors.groupingBy(DamAssetUploadItemVO::getTaskId));
    }

    private Map<Integer, List<DamTagValueDTO>> getTagValues(List<Integer> taskIds, Integer userId, Integer tenantId) {
        final List<DamTagValueDTO> tagValues = baseMapper.selectJoinList(
                DamTagValueDTO.class,
                new MPJLambdaWrapper<DamAssetUploadTask>()
                        .selectAll(DamTagValue.class)
                        .selectAs(DamTagValue::getAssetId, "assetId")
                        .selectAs(DamTag::getName, "name")
                        .selectAs(DamTag::getType, "tagType")
                        .leftJoin(DamAssetUploadTaskDetail.class, DamAssetUploadTaskDetail::getTaskId, DamAssetUploadTask::getId)
                        .leftJoin(DamAsset.class, DamAsset::getId, DamAssetUploadTaskDetail::getAssetId)
                        .leftJoin(DamTagValue.class, DamTagValue::getAssetId, DamAsset::getId, on -> on.eq(DamTagValue::getIsDeleted, false))
                        .leftJoin(DamTag.class, DamTag::getId, DamTagValue::getTagId)
                        .in(DamAssetUploadTask::getId, taskIds)
                        .eq(DamAssetUploadTask::getUserId, userId)
                        .eq(DamAssetUploadTask::getTenantId, tenantId)
                        .eq(DamAssetUploadTask::getIsDeleted, false)
        );
        return tagValues.stream()
                .collect(Collectors.groupingBy(DamTagValueDTO::getAssetId));
    }

    private DamAssetUploadTaskDetailVO fillAssetUploadTaskDetailVO(
            DamAssetUploadTaskDetailVO taskDetailVO,
            List<DamAssetUploadItemVO> itemVOs,
            Map<Integer, List<DamTagValueDTO>> assetId2TagValuesMap
    ) {
        return fillAssetUploadTaskDetailVO(taskDetailVO, itemVOs, assetId2TagValuesMap, Collections.emptyMap());
    }

    private DamAssetUploadTaskDetailVO fillAssetUploadTaskDetailVO(
            DamAssetUploadTaskDetailVO taskDetailVO,
            List<DamAssetUploadItemVO> itemVOs,
            Map<Integer, List<DamTagValueDTO>> assetId2TagValuesMap,
            Map<Integer, TokenUseDetail> taskId2TokenUseDetailMap
    ) {
        final Integer taskId = taskDetailVO.getId();
        if (taskId2TokenUseDetailMap.containsKey(taskId)) {
            taskDetailVO.setTokens(taskId2TokenUseDetailMap.get(taskId).getTokens());
        }
        for (DamAssetUploadItemVO itemVO : itemVOs) {
            try {
                final String ossUrl = itemVO.getOssId() != null ? s3Service.downloadPresignedUrl(
                        storageProperties.getCms().getBucketName(), itemVO.getOssId(),
                        FileConstant.OSS_PRESIGNED_URL_EXPIRE_TIME, 0).toString()
                        : null;
                final String thumbnailUrl = itemVO.getThumbnailOssId() != null ? s3Service.downloadPresignedUrl(
                        storageProperties.getCms().getBucketName(), itemVO.getThumbnailOssId(),
                        FileConstant.OSS_PRESIGNED_URL_EXPIRE_TIME, 0).toString()
                        : null;

                itemVO.setOssUrl(ossUrl);
                itemVO.setThumbnailUrl(thumbnailUrl);
            } catch (Exception e) {
                log.warn("获取素材预签名URL失败, assetId: {}, ossId: {}, thumbnailObjId: {}", itemVO.getId(), itemVO.getOssId(), itemVO.getThumbnailOssId(), e);
            }

            final List<DamTagValueDTO> tagValues = assetId2TagValuesMap.getOrDefault(itemVO.getId(), Collections.emptyList());
            itemVO.setTags(tagValues);
        }
        taskDetailVO.setAssets(itemVOs);
        return taskDetailVO;
    }

    public DamPageResult<DamAssetUploadTaskDetailVO> getAssetUploadTaskListV2(DamTaskTypeEnum type,
                                                                              PageParam pageParam,
                                                                              Integer userId, Integer tenantId) {
        final Page<DamAssetUploadTaskDetailVO> page = baseMapper.selectJoinPage(
                pageParam.toPage(DamAssetUploadTaskDetailVO.class),
                DamAssetUploadTaskDetailVO.class,
                new MPJLambdaWrapper<DamAssetUploadTask>()
                        .selectAll(DamAssetUploadTask.class)
                        .selectAs(CmsUser::getUserName, "createBy")
                        .leftJoin(CmsUser.class, CmsUser::getUserId, DamAssetUploadTask::getUserId)
                        .eq(DamAssetUploadTask::getTenantId, tenantId)
                        .eq(DamAssetUploadTask::getUserId, userId)
                        .eq(type != null, DamAssetUploadTask::getType, type)
                        .eq(DamAssetUploadTask::getIsDeleted, false)
        );
        if (CollUtil.isEmpty(page.getRecords())) {
            return DamPageResult.emptyResult();
        }
        final List<DamAssetUploadTaskDetailVO> taskDetailVOs = page.getRecords();
        final List<Integer> taskIds = taskDetailVOs.stream().map(DamAssetUploadTaskDetailVO::getId).toList();

        final Map<Integer, List<DamTagValueDTO>> assetId2TagValuesMap = getTagValues(taskIds, userId, tenantId);
        final Map<Integer, TokenUseDetail> taskId2TokenUseDetailMap = tokenUseDetailService.list(
                        new LambdaQueryWrapper<TokenUseDetail>()
                                .in(TokenUseDetail::getTaskId, taskIds)
                                .eq(TokenUseDetail::getTaskType, TokenTaskTypeEnum.ASSET_UPLOAD_TASK.getTaskType())
                ).stream()
                .collect(Collectors.toMap(TokenUseDetail::getTaskId, Function.identity()));

        final Map<Integer, List<DamAssetUploadItemVO>> taskId2ItemVOsMap = getUploadItemVOs(taskIds, userId, tenantId);
        for (DamAssetUploadTaskDetailVO taskDetailVO : taskDetailVOs) {
            fillAssetUploadTaskDetailVO(
                    taskDetailVO,
                    taskId2ItemVOsMap.getOrDefault(taskDetailVO.getId(),
                            Collections.emptyList()),
                    assetId2TagValuesMap,
                    taskId2TokenUseDetailMap
            );
        }

        return DamPageResult.of(
                page.getTotal(),
                taskDetailVOs,
                (int) page.getCurrent(), (int) page.getPages());
    }

    @Deprecated
    @Override
    public DamPageResult<DamAssetUploadTaskDetailVO> getAssetUploadTaskList(DamTaskTypeEnum type,
                                                                            PageParam pageParam,
                                                                            Integer userId, Integer tenantId) {
        final Page<DamAssetUploadTask> page = page(
                pageParam.toPage(DamAssetUploadTask.class),
                new LambdaQueryWrapper<>(DamAssetUploadTask.class)
                        .eq(DamAssetUploadTask::getTenantId, tenantId)
                        .eq(DamAssetUploadTask::getUserId, userId)
                        .eq(type != null, DamAssetUploadTask::getType, type)
                        .eq(DamAssetUploadTask::getIsDeleted, false));

        final List<DamAssetUploadTask> tasks = page.getRecords();
        if (CollectionUtil.isEmpty(tasks)) {
            return DamPageResult.emptyResult();
        }

        final List<Integer> taskIds = tasks.stream().map(DamAssetUploadTask::getId).toList();
        final List<Integer> userIds = tasks.stream().map(DamAssetUploadTask::getUserId).toList();
        final List<CmsUser> users = userService.listByUserIds(userIds);
        final List<DamAssetUploadTaskDetail> taskDetails = assetUploadTaskDetailService.listByTaskIds(taskIds);
        final List<TokenUseDetail> tokenUseDetails = tokenUseDetailService.list(
                new LambdaQueryWrapper<>(TokenUseDetail.class)
                        .eq(TokenUseDetail::getTaskType, TokenTaskTypeEnum.ASSET_UPLOAD_TASK.getTaskType())
                        .in(TokenUseDetail::getTaskId, taskIds)
                        .eq(TokenUseDetail::getTenantId, tenantId)
                        .eq(TokenUseDetail::getIsDeleted, false)
        );
        final Map<Integer, CmsUser> userId2UserMap = users.stream()
                .collect(Collectors.toMap(CmsUser::getUserId, Function.identity()));
        final Map<Integer, TokenUseDetail> taskId2TokenUseDetailMap = tokenUseDetails.stream()
                .collect(Collectors.toMap(TokenUseDetail::getTaskId, Function.identity()));
        final Map<Integer, List<DamAssetUploadTaskDetail>> taskId2TaskDetailsMap = taskDetails.stream()
                .collect(Collectors.groupingBy(DamAssetUploadTaskDetail::getTaskId));

        final List<DamAsset> assets = assetService
                .listByIds(taskDetails.stream().map(DamAssetUploadTaskDetail::getAssetId).toList());
        final List<DamDirectory> directories = directoryService
                .listByIds(assets.stream().map(DamAsset::getDirectoryId).toList());
        final List<DamTag> tags = tagService.list(new LambdaQueryWrapper<DamTag>()
                .eq(DamTag::getTenantId, tenantId)
                .eq(DamTag::getIsDeleted, false));
        final List<DamTagValue> tagValues = tagValueService.list(new LambdaQueryWrapper<DamTagValue>()
                .in(DamTagValue::getAssetId, assets.stream().map(DamAsset::getId).toList())
                .eq(DamTagValue::getIsDeleted, false));

        final Map<Integer, DamAsset> id2AssetMap = assets.stream()
                .collect(Collectors.toMap(DamAsset::getId, Function.identity()));
        final Map<Integer, DamDirectory> id2DirectoryMap = directories.stream()
                .collect(Collectors.toMap(DamDirectory::getId, Function.identity()));
        final Map<Integer, DamTag> id2TagMap = tags.stream()
                .collect(Collectors.toMap(DamTag::getId, Function.identity()));
        final Map<Integer, List<DamTagValue>> assetId2TagValuesMap = tagValues.stream()
                .collect(Collectors.groupingBy(DamTagValue::getAssetId, Collectors.toList()));

        final List<DamAssetUploadTaskDetailVO> taskDetailVOs = new ArrayList<>();
        for (DamAssetUploadTask task : tasks) {
            final List<DamAssetUploadTaskDetail> taskDetailList = taskId2TaskDetailsMap.get(task.getId());
            if (CollectionUtil.isEmpty(taskDetailList)) {
                taskDetailVOs.add(DamAssetUploadTaskDetailVO.from(task, null));
                continue;
            }
            final List<DamAssetUploadItemVO> items = new ArrayList<>();
            for (DamAssetUploadTaskDetail taskDetail : taskDetailList) {
                final DamAsset asset = id2AssetMap.get(taskDetail.getAssetId());
                final DamDirectory directory = id2DirectoryMap.get(asset.getDirectoryId());
                final List<DamTagValue> values = assetId2TagValuesMap.getOrDefault(asset.getId(),
                        Collections.emptyList());
                final List<DamTagValueDTO> valueDTOs = values.stream()
                        .filter(tagValue -> id2TagMap.containsKey(tagValue.getTagId()))
                        .map(tagValue -> DamTagValueDTO.from(tagValue,
                                id2TagMap.get(tagValue.getTagId())))
                        .toList();

                String ossUrl = null;
                String thumbnailUrl = null;

                try {
                    ossUrl = asset.getOssId() != null ? s3Service.downloadPresignedUrl(
                            storageProperties.getCms().getBucketName(), asset.getOssId(),
                            FileConstant.OSS_PRESIGNED_URL_EXPIRE_TIME, 0).toString()
                            : null;
                    thumbnailUrl = asset.getThumbnailOssId() != null ? s3Service.downloadPresignedUrl(
                            storageProperties.getCms().getBucketName(), asset.getThumbnailOssId(),
                            FileConstant.OSS_PRESIGNED_URL_EXPIRE_TIME, 0).toString()
                            : null;
                } catch (Exception e) {
                    log.warn("获取素材预签名URL失败, assetId: {}, ossId: {}, thumbnailObjId: {}", asset.getId(), asset.getOssId(), asset.getThumbnailOssId(), e);
                }

                final DamAssetUploadItemVO itemVO = DamAssetUploadItemVO.from(
                        asset,
                        directory,
                        taskDetail,
                        ossUrl,
                        thumbnailUrl,
                        valueDTOs,
                        userId2UserMap.get(task.getUserId())
                );
                items.add(itemVO);
            }
            final TokenUseDetail tokenUseDetail = taskId2TokenUseDetailMap.get(task.getId());
            taskDetailVOs.add(DamAssetUploadTaskDetailVO.from(task, items, tokenUseDetail, userId2UserMap.get(task.getUserId())));
        }

        return DamPageResult.of(
                page.getTotal(),
                taskDetailVOs,
                (int) page.getCurrent(),
                (int) page.getPages());
    }

    @Override
    public DamAssetUploadTaskDetailVO getAssetUploadTaskDetailV2(Integer taskId, Integer userId, Integer tenantId) {
        final DamAssetUploadTaskDetailVO detailVO = baseMapper.selectJoinOne(
                DamAssetUploadTaskDetailVO.class,
                new MPJLambdaWrapper<DamAssetUploadTask>()
                        .selectAll(DamAssetUploadTask.class)
                        .selectAs(CmsUser::getUserName, "createBy")
                        .leftJoin(CmsUser.class, CmsUser::getUserId, DamAssetUploadTask::getUserId)
                        .eq(DamAssetUploadTask::getTenantId, tenantId)
                        .eq(DamAssetUploadTask::getUserId, userId)
                        .eq(DamAssetUploadTask::getId, taskId)
                        .eq(DamAssetUploadTask::getIsDeleted, false)
        );
        if (detailVO == null) {
            return null;
        }
        final TokenUseDetail tokenUseDetail = tokenUseDetailService.getOne(
                new LambdaQueryWrapper<TokenUseDetail>()
                        .eq(TokenUseDetail::getTaskId, taskId)
                        .eq(TokenUseDetail::getTaskType, TokenTaskTypeEnum.ASSET_UPLOAD_TASK.getTaskType())
        );
        if (tokenUseDetail != null) {
            detailVO.setTokens(tokenUseDetail.getTokens());
        }
        final List<DamAssetUploadItemVO> itemVOs = getUploadItemVOs(Collections.singletonList(taskId), userId, tenantId).get(taskId);
        final Map<Integer, List<DamTagValueDTO>> assetId2TagValuesMap = getTagValues(Collections.singletonList(taskId), userId, tenantId);
        fillAssetUploadTaskDetailVO(detailVO, itemVOs, assetId2TagValuesMap);
        return detailVO;
    }

    @Deprecated
    @Override
    public DamAssetUploadTaskDetailVO getAssetUploadTaskDetail(Integer taskId, Integer userId, Integer tenantId) {
        final DamAssetUploadTask task = safeGetTaskInfoById(taskId, userId, tenantId);
        final List<DamAssetUploadTaskDetail> taskDetails = assetUploadTaskDetailService.listByTaskId(taskId);
        if (CollectionUtil.isEmpty(taskDetails)) {
            return DamAssetUploadTaskDetailVO.from(task);
        }
        final CmsUser user = userService.getById(task.getUserId());
        final TokenUseDetail tokenUseDetail = tokenUseDetailService.getOne(
                new LambdaQueryWrapper<TokenUseDetail>()
                        .eq(TokenUseDetail::getTaskId, taskId)
                        .eq(TokenUseDetail::getTaskType, TokenTaskTypeEnum.ASSET_UPLOAD_TASK.getTaskType())
                        .eq(TokenUseDetail::getTenantId, tenantId)
                        .eq(TokenUseDetail::getIsDeleted, false)
        );
        final List<DamAsset> assets = assetService
                .listByIds(taskDetails.stream().map(DamAssetUploadTaskDetail::getAssetId).toList());
        final List<DamDirectory> directories = directoryService
                .listByIds(assets.stream().map(DamAsset::getDirectoryId).toList());
        final List<DamTag> tags = tagService.list(new LambdaQueryWrapper<DamTag>()
                .eq(DamTag::getTenantId, tenantId)
                .eq(DamTag::getIsDeleted, false));
        final List<DamTagValue> tagValues = tagValueService.list(new LambdaQueryWrapper<DamTagValue>()
                .in(DamTagValue::getAssetId, assets.stream().map(DamAsset::getId).toList())
                .eq(DamTagValue::getIsDeleted, false));

        final Map<Integer, DamAsset> id2AssetMap = assets.stream()
                .collect(Collectors.toMap(DamAsset::getId, Function.identity()));
        final Map<Integer, DamDirectory> id2DirectoryMap = directories.stream()
                .collect(Collectors.toMap(DamDirectory::getId, Function.identity()));
        final Map<Integer, DamTag> id2TagMap = tags.stream()
                .collect(Collectors.toMap(DamTag::getId, Function.identity()));
        final Map<Integer, List<DamTagValue>> assetId2TagValuesMap = tagValues.stream()
                .collect(Collectors.groupingBy(DamTagValue::getAssetId, Collectors.toList()));

        final List<DamAssetUploadItemVO> items = new ArrayList<>();
        for (DamAssetUploadTaskDetail taskDetail : taskDetails) {
            final DamAsset asset = id2AssetMap.get(taskDetail.getAssetId());
            final DamDirectory directory = id2DirectoryMap.get(asset.getDirectoryId());
            final List<DamTagValue> values = assetId2TagValuesMap.getOrDefault(asset.getId(),
                    Collections.emptyList());
            final List<DamTagValueDTO> valueDTOs = values.stream()
                    .filter(tagValue -> id2TagMap.containsKey(tagValue.getTagId()))
                    .map(tagValue -> DamTagValueDTO.from(tagValue,
                            id2TagMap.get(tagValue.getTagId())))
                    .toList();

            String ossUrl = null;
            String thumbnailUrl = null;
            try {
                ossUrl = asset.getOssId() != null ? s3Service.downloadPresignedUrl(
                        storageProperties.getCms().getBucketName(), asset.getOssId(),
                        FileConstant.OSS_PRESIGNED_URL_EXPIRE_TIME, 0).toString()
                        : null;
                thumbnailUrl = asset.getThumbnailOssId() != null ? s3Service.downloadPresignedUrl(
                        storageProperties.getCms().getBucketName(), asset.getThumbnailOssId(),
                        FileConstant.OSS_PRESIGNED_URL_EXPIRE_TIME, 0).toString()
                        : null;
            } catch (Exception e) {
                log.warn("获取素材预签名URL失败, assetId: {}, ossId: {}, thumbnailObjId: {}", asset.getId(), asset.getOssId(), asset.getThumbnailOssId(), e);
            }

            final DamAssetUploadItemVO itemVO = DamAssetUploadItemVO.from(
                    asset,
                    directory,
                    taskDetail,
                    ossUrl,
                    thumbnailUrl,
                    valueDTOs,
                    user
            );
            items.add(itemVO);
        }

        return DamAssetUploadTaskDetailVO.from(
                task,
                items,
                tokenUseDetail,
                user
        );
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean storageAssets(Integer taskId, DamAssetUploadTaskStorageDTO request,
                                 Integer userId, Integer tenantId) throws JsonMappingException, JsonProcessingException {
        final Map<Integer, DamAssetUploadTaskStorageDTO.Asset> assetId2DTOMap = request.getAssets().stream()
                .collect(Collectors.toMap(DamAssetUploadTaskStorageDTO.Asset::getId,
                        Function.identity()));
        final List<Integer> assetIds = new ArrayList<>(assetId2DTOMap.keySet());

        final List<DamAssetUploadTaskDetail> taskDetails = assetUploadTaskDetailService.listByTaskIdAndAssetIds(
                taskId,
                assetIds);
        if (CollectionUtil.isEmpty(taskDetails)) {
            return true;
        }
        final DamAssetUploadTask taskInfo = safeGetTaskInfoById(taskId, userId, tenantId);
        if (!Objects.equals(taskInfo.getStatus(), DamTaskStatusEnum.COMPLETED)) {
            throw new BusinessException(ErrorCode.PARAM_ERROR, "任务还未完成, 无法入库");
        }
        if (taskInfo.getIsStored()) {
            throw new BusinessException(ErrorCode.PARAM_ERROR, "任务已入库, 无法重复入库");
        }
        if (!Objects.equals(taskInfo.getUserId(), userId) || !Objects.equals(taskInfo.getTenantId(), tenantId)) {
            throw new BusinessException(ErrorCode.AUTHORIZATION_FAIL, "没有权限操作该任务");
        }
        final DamAssetUploadTaskArg taskArg = objectMapper.readValue(taskInfo.getTaskArg(),
                DamAssetUploadTaskArg.class);
        if (taskArg == null) {
            throw new BusinessException(ErrorCode.PARAM_ERROR, "任务参数错误");
        }
        final List<DamAssetUploadTaskArg.Tag> taskTags = taskArg.getTags();
        if (CollectionUtil.isEmpty(taskTags)) {
            assetService.update(
                    new LambdaUpdateWrapper<DamAsset>()
                            .in(DamAsset::getId, assetIds)
                            .set(DamAsset::getIsStored, true)
                            .set(DamAsset::getStorageTime, new Date())
                            .set(DamAsset::getUpdateTime, new Date()));
            update(
                    new LambdaUpdateWrapper<DamAssetUploadTask>()
                            .eq(DamAssetUploadTask::getId, taskId)
                            .set(DamAssetUploadTask::getIsStored, true)
            );
            return true;
        }

        final List<DamAsset> assets = assetService.listByIds(assetIds);
        final Map<Integer, DamAsset> id2AssetMap = assets
                .stream()
                .collect(Collectors.toMap(DamAsset::getId, Function.identity()));
        final Map<Integer, List<DamTagValue>> assetId2TagValuesMap = tagValueService.list(
                        new LambdaQueryWrapper<DamTagValue>()
                                .in(DamTagValue::getAssetId, assetIds)
                                .eq(DamTagValue::getIsDeleted, false))
                .stream()
                .collect(Collectors.groupingBy(DamTagValue::getAssetId, Collectors.toList()));

        final List<DamTagValue> tagValues = new ArrayList<>();
        for (DamAssetUploadTaskDetail taskDetail : taskDetails) {
            if (taskDetail.getStatus() != DamTaskStatusEnum.COMPLETED) {
                // 打标失败的素材, 不进行入库
                continue;
            }
            final DamAsset asset = id2AssetMap.get(taskDetail.getAssetId());
            if (asset == null) {
                throw new BusinessException(ErrorCode.NOT_FOUND, "未找到任务对应的素材");
            }

            final DamAssetUploadTaskStorageDTO.Asset assetDTO = assetId2DTOMap.get(taskDetail.getAssetId());

            final Map<Integer, DamTagValue> tagId2TagValueMap = assetId2TagValuesMap.getOrDefault(asset.getId(),
                    Collections.emptyList()).stream().collect(Collectors.toMap(DamTagValue::getId, Function.identity()));

            final List<DamTagValueDTO> tagValueDTOs = assetDTO.getTags();
            for (DamTagValueDTO tagValueDTO : tagValueDTOs) {
                final DamTagValue tagValue = tagId2TagValueMap.get(tagValueDTO.getId());
                if (tagValue == null) {
                    continue;
                }
                tagValue.setValue(tagValueDTO.getValue() != null ? tagValueDTO.getValue() : "");
                tagValue.setUpdateTime(new Date());
                tagValues.add(tagValue);
            }
        }

        tagValueService.updateBatchById(tagValues);
        assetService.update(
                new LambdaUpdateWrapper<DamAsset>()
                        .in(DamAsset::getId, assetIds)
                        .set(DamAsset::getIsStored, true)
                        .set(DamAsset::getStorageTime, new Date())
                        .set(DamAsset::getUpdateTime, new Date()));
        // 保存isUpdate传false
        embeddingService.saveOrUpdateByAssets(assets, userId, tenantId, false);
        update(
                new LambdaUpdateWrapper<DamAssetUploadTask>()
                        .eq(DamAssetUploadTask::getId, taskId)
                        .set(DamAssetUploadTask::getIsStored, true)
        );
        // 如果时AI分镜视频入库,需要更新videoTaskId，状态已入库
        if (taskInfo.getType() == DamTaskTypeEnum.AI_SEGMENT) {
            // 更新AI分镜任务状态为入库中
            videoAiGenerationService.updateTaskStatus(taskArg.getVideoTaskId(),
                    VideoTaskStatusEnum.VIDU_AI_VIDEO_ENTERSTOREHOUSE_SUCCESS, userId, tenantId);
        }
        return true;
    }

}
