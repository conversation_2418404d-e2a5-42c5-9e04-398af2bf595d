package cn.mlamp.insightflow.cms.model.vo;

import lombok.Data;

import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;

@Data
public class RechargeAllVO implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "租户名称", required = false)
    private String tenantName;

    @Schema(description = "当前租户余额", required = false)
    private Integer balance;

    @Schema(description = "最近充值时间", required = false)
    private Date userName;
}
