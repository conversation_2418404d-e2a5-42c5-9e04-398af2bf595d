package cn.mlamp.insightflow.cms.vector;

import cn.mlamp.insightflow.cms.entity.dam.DamAssetEmbedding;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface DamAssetEmbeddingMapper extends BaseMapper<DamAssetEmbedding> {
    long totalEmbeddingCount(@Param("aspectRatio") String aspectRatio,
                             @Param("tenantId") Integer tenantId,
                             @Param("userId") Integer userId,
                             @Param("directoryIds") List<Integer> directoryIds);

    /**
     * 分页查询embedding相似度排序结果
     *
     * @param embedding   查询向量
     * @param pageSize    每页数量
     * @param offset      偏移量
     * @param tenantId    租户ID
     * @param userId      用户ID
     * @param directoryIds 目录ID
     * @param aspectRatio 宽高比
     * @return 结果列表
     */
    List<DamAssetEmbedding> pageEmbedding(@Param("embedding") float[] embedding,
                                          @Param("offset") int offset,
                                          @Param("pageSize") int pageSize,
                                          @Param("aspectRatio") String aspectRatio,
                                          @Param("tenantId") Integer tenantId,
                                          @Param("userId") Integer userId,
                                          @Param("directoryIds") List<Integer> directoryIds);

    List<DamAssetEmbedding> topKEmbedding(@Param("embedding") float[] embedding,
                                          @Param("limit") int limit,
                                          @Param("threshold") Float threshold,
                                          @Param("aspectRatio") String aspectRatio,
                                          @Param("tenantId") Integer tenantId,
                                          @Param("userId") Integer userId,
                                          @Param("directoryIds") List<Integer> directoryIds);

    int updateEmbedding(@Param("id") Integer id,
                        @Param("content") String content,
                        @Param("embedding") Float[] embedding,
                        @Param("updateTime") Date updateTime);

    int updateByEntity(DamAssetEmbedding entity);
}
