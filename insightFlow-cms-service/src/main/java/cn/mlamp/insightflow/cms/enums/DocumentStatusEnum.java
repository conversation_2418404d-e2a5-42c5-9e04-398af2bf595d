package cn.mlamp.insightflow.cms.enums;


import lombok.Getter;

/**
 * 文件的状态信息
 */


@Getter
public enum DocumentStatusEnum {

    /**
     * 处理中（url爬取）
     */
    PROCESSING(0, "处理中"),

    /**
     * 等待中（等待解析）
     */
    WAITING(1, "等待中"),

    /**
     * 分析中
     */
    ANALYZING(2, "分析中"),

    /**
     * 完成
     */
    COMPLETED(3, "完成"),

    /**
     * 失败
     */
    FAIL(4, "失败"),

    /**
     * 已取消
     */
    CANCEL(5, "已取消"),
    ;

    private final int code;
    private final String msg;

    DocumentStatusEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
