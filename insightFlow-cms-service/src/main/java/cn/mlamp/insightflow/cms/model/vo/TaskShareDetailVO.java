package cn.mlamp.insightflow.cms.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 任务分享详情响应
 * <AUTHOR>
 * @date 2025-05-20
 */
@Data
public class TaskShareDetailVO {

    @Schema(description = "分享ID")
    private Integer id;

    @Schema(description = "任务ID")
    private Integer taskId;

    @Schema(description = "任务类型：1-视频合成任务")
    private Integer type;

    @Schema(description = "任务名称")
    private String taskName;

    @Schema(description = "分享人姓名")
    private String sharerName;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "过期时间")
    private Date expireTime;

    @Schema(description = "合成视频下载链接")
    private String resultOssUrl;

    @Schema(description = "视频首帧图片链接")
    private String firstFrameOssUrl;

    @Schema(description = "分段视频下载链接列表")
    private List<String> segmentVideoUrls;

    @Schema(description = "任务信息")
    private String taskInfo;
}
