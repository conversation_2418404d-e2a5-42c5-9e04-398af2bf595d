package cn.mlamp.insightflow.cms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("cms_video_analysis_log")
public class CmsVideoAnalysisLog extends BaseEntity {
    /** 主键ID */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /** 视频ID */
    @TableField("video_id")
    private String videoId;

    /** 视频分析任务类型（字典：ASR识别，整体理解，视频分割，黄金3秒，分镜打标） */
    @TableField("video_info_type")
    private String videoInfoType;

    /** 任务类型（字典：上传，压缩，下载，wav转码，分割，切帧，算法接口） */
    @TableField("task_type")
    private String taskType;

    /** 耗时（毫秒） */
    @TableField("time")
    private Integer time;

    /** 异步任务唯一标识 */
    @TableField("db_unique_id")
    private Long dbUniqueId;

}