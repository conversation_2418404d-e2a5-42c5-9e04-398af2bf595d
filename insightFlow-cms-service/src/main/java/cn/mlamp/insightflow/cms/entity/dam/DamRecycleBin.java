package cn.mlamp.insightflow.cms.entity.dam;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import cn.mlamp.insightflow.cms.enums.dam.DamDirectoryTypeEnum;
import cn.mlamp.insightflow.cms.enums.dam.DamRecycleBinObjectTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * DAM回收站表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
@Getter
@Setter
@TableName("cms_recycle_bin")
@Schema(name = "CmsRecycleBin", description = "DAM回收站表")
public class DamRecycleBin implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @Schema(description = "租户ID")
    @TableField("tenant_id")
    private Integer tenantId;

    @Schema(description = "操作用户ID")
    @TableField("user_id")
    private Integer userId;

    @Schema(description = "目录类型：1-个人文件夹，2-租户文件夹")
    @TableField("directory_type")
    private DamDirectoryTypeEnum directoryType;

    @Schema(description = "对象ID")
    @TableField("object_id")
    private Integer objectId;

    @Schema(description = "对象类型：1-目录，2-素材")
    @TableField("object_type")
    private DamRecycleBinObjectTypeEnum objectType;

    @Schema(description = "恢复时间")
    @TableField("recover_time")
    private Date recoverTime;

    @Schema(description = "创建时间")
    @TableField("create_time")
    private Date createTime;

    @Schema(description = "逻辑删除：0-未删除，1-已删除")
    @TableField("is_deleted")
    private Boolean isDeleted;

}
