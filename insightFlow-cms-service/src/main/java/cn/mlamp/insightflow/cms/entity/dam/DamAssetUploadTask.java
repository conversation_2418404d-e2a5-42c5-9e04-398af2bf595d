package cn.mlamp.insightflow.cms.entity.dam;

import cn.mlamp.insightflow.cms.enums.dam.DamTaskStatusEnum;
import cn.mlamp.insightflow.cms.enums.dam.DamTaskTypeEnum;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * DAM素材上传任务表-记录细节
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
@Getter
@Setter
@TableName(value = "cms_asset_upload_task", autoResultMap = true)
@Schema(name = "CmsAssetUploadTask", description = "DAM素材上传任务表")
public class DamAssetUploadTask implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @Schema(description = "租户ID（租户素材）")
    @TableField("tenant_id")
    private Integer tenantId;

    @Schema(description = "上传用户ID")
    @TableField("user_id")
    private Integer userId;

    @Schema(description = "任务类型 1: 素材上传任务, 2: AI分镜镜头入库")
    @TableField("type")
    private DamTaskTypeEnum type;

    @Schema(description = "任务参数")
    @TableField("task_arg")
    private String taskArg;

    @Schema(description = "是否已入库")
    @TableField("is_stored")
    private Boolean isStored;

    @Schema(description = "状态：1-待处理，2-处理中，3-完成，4-失败")
    @TableField("status")
    private DamTaskStatusEnum status;

    @Schema(description = "失败原因")
    @TableField("error")
    private String error;

    @Schema(description = "创建时间")
    @TableField("create_time")
    private Date createTime;

    @Schema(description = "更新时间")
    @TableField("update_time")
    private Date updateTime;

    @Schema(description = "逻辑删除：0-未删除，1-已删除")
    @TableField("is_deleted")
    private Boolean isDeleted = false;
}
