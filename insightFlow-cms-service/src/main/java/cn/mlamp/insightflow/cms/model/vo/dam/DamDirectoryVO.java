package cn.mlamp.insightflow.cms.model.vo.dam;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

import cn.mlamp.insightflow.cms.entity.dam.DamDirectory;
import cn.mlamp.insightflow.cms.enums.dam.DamDirectoryTypeEnum;

/**
 * DAM目录VO
 */
@Data
@Schema(description = "DAM目录VO")
public class DamDirectoryVO {

    @Schema(description = "目录ID")
    private Integer id;

    @Schema(description = "目录名称")
    private String name;

    @Schema(description = "目录类型：1-个人文件夹，2-租户文件夹")
    private DamDirectoryTypeEnum type;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;

    @Schema(description = "是否删除")
    private Boolean isDeleted;

    public static DamDirectoryVO from(DamDirectory directory) {
        final DamDirectoryVO vo = new DamDirectoryVO();
        vo.setId(directory.getId());
        vo.setName(directory.getName());
        vo.setType(directory.getType());
        vo.setCreateTime(directory.getCreateTime());
        vo.setUpdateTime(directory.getUpdateTime());
        vo.setIsDeleted(directory.getIsDeleted());
        return vo;
    }
} 