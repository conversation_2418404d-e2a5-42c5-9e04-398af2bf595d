package cn.mlamp.insightflow.cms.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("cms_pull_task_deduped_datas")
public class CmsPullTaskDedupedData extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer taskId; // 任务 ID，关联 cms_pull_tasks

    private String esId; // ES 唯一 ID（外部系统唯一标识）

    private String textContent; // 内容

    private String textTitle; // 标题

    private String kwKbIndustry; // 圈成关键词行业

    private String kwUrl; // 帖子链接

    private Date datePublishedAt; // 发布时间

    private Boolean boolIsDeleted; // 是否删除

    private Long longRepostCount; // 转发数

    private Long longCommentCount; // 评论数

    private Long longInteractCount; // 互动数

    private Long longLikeCount; // 点赞数

    private String textNickName; // 用户昵称

    private String kwProfileImageUrl; // 用户头像链接

    private String kwUserUrl; // 用户主页链接

    private Long longVideoDuration; // 视频时长

    private String kwHeadImage; // 视频头图

    private String kwVideoUrl; // 视频链接

    private String kwVideoContent; // 视频语音识别

    private String kwCommonSentimentPlus; // 通用情感 Plus 版

    private String kwDataTagPlus; // 数据标签 Plus 版

    private Long longViewCount; // 阅读数

    private Long longFollowerCount; // 发帖用户粉丝数

    private Long longCollectCount; // 收藏数

    private String kwSource; // 来源

    private Integer type; // 类型

    private String kwTwoLevelTribeTag; // 圈成二级标签

    private Integer sourceType; // 来源类型

    private Integer userId; // 用户 ID

    private Integer tenantId; // 租户 ID

    private Float rating; // 创意分

    //下载状态0：待下载，1：下载中，2：成功，3，失败
    private Integer downloadStatus;


    private String downloadDate;

    //0:待分析；1：分析中，2：分析成功，3：分析失败
    private Integer analysisStatus;

    //视频分析任务Id
    private String dbUniqueId;

    //创意原因
    private String creativeReasons;

    //产品名称
    private String productName;

    //卖点
    private String cellingPoint;

    //受众人群
    private String aimingTribe;

    //是否高光0：没有1：高光
    private Integer highlight;

    //视频OSS ID
    private String videoOssId;

    //视频头图OSS ID
    private String videoHeadPicOssId;

}
