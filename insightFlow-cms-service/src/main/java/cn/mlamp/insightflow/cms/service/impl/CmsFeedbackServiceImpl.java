package cn.mlamp.insightflow.cms.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.mlamp.insightflow.cms.entity.CmsFeedback;
import cn.mlamp.insightflow.cms.enums.FeedbackSourceTypeEnum;
import cn.mlamp.insightflow.cms.exception.BusinessException;
import cn.mlamp.insightflow.cms.mapper.CmsFeedbackMapper;
import cn.mlamp.insightflow.cms.model.query.FeedbackRequest;
import cn.mlamp.insightflow.cms.service.CmsFeedbackService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * @description 针对表【cms_feedback(反馈意见表)】的数据库操作Service实现
 */
@Service
public class CmsFeedbackServiceImpl extends ServiceImpl<CmsFeedbackMapper, CmsFeedback>
        implements CmsFeedbackService {

    @Override
    public void userFeedback(FeedbackRequest uploadVO, Integer userId, Integer tenantId) {
        var feedbackType = FeedbackSourceTypeEnum.getByCode(uploadVO.getSourceType());
        // 构造用户反馈文本
        StringBuilder sb = new StringBuilder();
        if (!uploadVO.getOptions().isEmpty()) {
            sb.append("反馈选项：");
            uploadVO.getOptions().forEach(item -> sb.append(item).append(","));
            sb.deleteCharAt(sb.length() - 1);
            // 换行
            sb.append("\n");
        }
        if (feedbackType != null && uploadVO.getSourceId() != null) {
            sb.append("反馈类型：").append(feedbackType.getMsg());
            // 换行
            sb.append("\n");
            sb.append("反馈ID：").append(uploadVO.getSourceId());
            // 换行
            sb.append("\n");
        }
        if (StrUtil.isNotBlank(uploadVO.getContent())) {
            sb.append("反馈内容：").append(uploadVO.getContent());
            // 换行
            sb.append("\n");
        }
        if (sb.isEmpty()) {
            throw new BusinessException("反馈内容不能为空");
        }
        var entity = new CmsFeedback();
        entity.setUserId(userId);
        entity.setTenantId(tenantId);
        entity.setType(uploadVO.getSourceType());
        entity.setText(sb.toString());
        this.save(entity);
    }
}




