package cn.mlamp.insightflow.cms.model.vo;

import cn.mlamp.insightflow.cms.entity.QianchuanMaterialVideo;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 黄金5秒详情分页VO，包含分页数据和额外信息
 * 
 * @Author: yangzhibo
 * @CreateTime: 2025-5-15
 */
@Data
public class VideoFiveGoldDetailPageVO {

    @Schema(description = "分页数据", required = true)
    private Page<QianchuanMaterialVideo> page;

    @Schema(description = "黄金5秒标签", required = true)
    private String tag;

    @Schema(description = "台词套路", required = true)
    private String dialogueRoutine;
}
