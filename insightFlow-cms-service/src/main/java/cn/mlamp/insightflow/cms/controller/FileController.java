package cn.mlamp.insightflow.cms.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.common.resp.RespCode;
import cn.mlamp.insightflow.cms.config.UserContext;
import cn.mlamp.insightflow.cms.entity.CmsVideoInfo;
import cn.mlamp.insightflow.cms.exception.BusinessException;
import cn.mlamp.insightflow.cms.model.query.*;
import cn.mlamp.insightflow.cms.model.vo.DocTaskUploadVO;
import cn.mlamp.insightflow.cms.model.vo.LinkFileVO;
import cn.mlamp.insightflow.cms.model.vo.TaskUploadVO;
import cn.mlamp.insightflow.cms.service.FileService;
import cn.mlamp.insightflow.cms.task.UrlVideoDownloadCheckTask;
import cn.mlamp.insightflow.cms.task.UserVideoAnalysisQueryTask;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;

@RestController
@RequestMapping("/file")
@Tag(name = "文件相关接口")
public class FileController {

    @Resource
    private FileService fileService;

    @Resource
    private UrlVideoDownloadCheckTask urlVideoDownloadCheckTask;

    @Resource
    private UserVideoAnalysisQueryTask userVideoAnalysisQueryTask;

    @PostMapping("/download")
    @Operation(summary = "下载文件")
    public RespBody<List<String>> downloadFile(@RequestBody DownloadRequest objOssIds) {
        if (objOssIds == null) {
            throw new BusinessException(RespCode.BAD_REQUEST, "请求体为null");
        }
        List<String> ossIds = objOssIds.getOssIds();
        if (CollectionUtil.isEmpty(ossIds)) {
            return RespBody.ok(Collections.emptyList());
        }
        final List<String> downloadSignatureUrl = fileService.getDownloadSignatureUrl(ossIds);
        return RespBody.ok(downloadSignatureUrl);
    }

    @PostMapping("/upload/presign")
    @Operation(summary = "获取上传文件的url(签名)")
    public RespBody<TaskUploadVO> uploadFile(@RequestBody TaskUploadRequest request) {
        if (request == null) {
            throw new BusinessException(RespCode.BAD_REQUEST, "请求体为null");
        }
        final TaskUploadVO TaskUploadVO = fileService.getUploadSignatureUrl(request);
        return RespBody.ok(TaskUploadVO);
    }

    /**
     * 上传网页链接-校验并获取标题
     */
    @PostMapping("/upload/link/check")
    @Operation(summary = "上传网页链接-校验并获取标题")
    public RespBody<List<LinkFileVO>> uploadLink(@Valid @RequestBody LinkUploadRequest request) {
        return RespBody.ok(fileService.uploadLinkCheck(request));
    }

    /**
     * 批量上传任务
     */
    @PostMapping("/upload/batch")
    @Operation(summary = "批量上传视频任务")
    public RespBody<List<Integer>> batchUploadVideo(@Valid @RequestBody BatchUploadRequest request) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();
        request.setUserId(userId);
        request.setTenantId(tenantId);
        var videoInfos = fileService.batchUploadVideo(request);
        fileService.decodeUploadVideo(videoInfos, request.getSourceType());
        return RespBody.ok(videoInfos.stream().map(CmsVideoInfo::getSourceFileId).toList());
    }

    /**
     * 上传任务历史（分页）
     */
    @GetMapping("/upload/history")
    @Operation(summary = "上传任务历史（分页）")
    public RespBody<Page<DocTaskUploadVO>> uploadHistory(@RequestParam(value = "current", defaultValue = "1") Integer current,
                                                         @RequestParam(value = "pageSize", defaultValue = "5") Integer pageSize) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();
        return RespBody.ok(fileService.uploadHistory(current, pageSize, userId, tenantId));
    }

    /**
     * 右上角任务列表（批量查询任务状态）
     */
    @PostMapping("/upload/status")
    @Operation(summary = "右上角任务列表（批量查询任务状态）")
    public RespBody<List<DocTaskUploadVO>> uploadTaskStatus(@Valid @RequestBody UploadTaskQueryRequest request) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();
        request.setUserId(userId);
        request.setTenantId(tenantId);
        return RespBody.ok(fileService.uploadTaskStatus(request));
    }

    /**
     * 取消上传
     */
    @PostMapping("/upload/cancel/{taskId}")
    @Operation(summary = "取消上传")
    public RespBody<Void> uploadCancel(@PathVariable("taskId") Integer taskId) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();
        fileService.uploadCancel(taskId, userId, tenantId);
        return RespBody.ok();
    }

    /**
     * 上传重试（智能分镜重试）
     */
    @PostMapping("/upload/retry/{taskId}")
    @Operation(summary = "上传重试")
    public RespBody<Void> uploadRetry(@PathVariable("taskId") Integer taskId) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();
        fileService.uploadRetry(taskId, userId, tenantId);
        return RespBody.ok();
    }

    /**
     * 智能分镜上传任务删除
     */
    @DeleteMapping("/upload/delete/{taskId}")
    @Operation(summary = "删除分镜上传任务")
    public RespBody<Void> uploadDelete(@PathVariable("taskId") Integer taskId) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();
        fileService.uploadTaskDelete(taskId, userId, tenantId);
        return RespBody.ok();
    }


    /**
     * 查询用户url上传视频的下载结果情况，如果成功就发起解析请求
     */
    @GetMapping("/urlVideoDownloadCheck")
    @Operation(summary = "查询用户url上传视频的下载结果情况，如果成功就发起解析请求）")
    public RespBody<?> urlVideoDownloadCheck() {
        urlVideoDownloadCheckTask.urlVideoDownloadCheck();
        return RespBody.ok();
    }

    /**
     * 查询用户视频分析结果
     */
    @GetMapping("/documentAnalysisJobQuery")
    @Operation(summary = "查询用户视频分析结果")
    public RespBody<?> documentAnalysisJobQuery() {
        userVideoAnalysisQueryTask.documentAnalysisJobQuery();
        return RespBody.ok();
    }



}
