package cn.mlamp.insightflow.cms.model.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Author: husuper
 * @CreateTime: 2025-03-19
 */
@Data
public class VideoFiveGoldVO {

    /**
     * 主键 自增id
     */
    @Schema(description = "id", required = true)
    private Integer id;
    /**
     * 行业分类
     */
    @Schema(description = "行业分类", required = true)
    private String industry;

    @Schema(description = "视频标签", required = true)
    private String tag;

    /**
     * 封面视频URL
     */
    @Schema(description = "封面视频URL", required = true)
    private String coverVideoUrl;
    /**
     * 关联素材（视频）
     */
    @Schema(description = "关联素材（视频）", required = true)
    private Integer videoNum;
    /**
     * 平均曝光量
     */
    @Schema(description = "平均曝光量", required = true)
    private Integer exposureCount;
    /**
     * 平均互动数
     */
    @Schema(description = "平均互动数", required = true)
    private Integer interactCount;
    /**
     * 平均点赞数
     */
    @Schema(description = "平均点赞数", required = true)
    private Integer likeCount;
    /**
     * 平均评论数
     */
    @Schema(description = "平均评论数", required = true)
    private Integer commentCount;
    /**
     * 创意分值
     */
    @Schema(description = "创意分值", required = true)
    private String originalityNum;

    /**
     * 台词套路
     */
    @Schema(description = "台词套路", required = true)
    private String dialogueRoutine;

//    /** 1：待处理；2：处理中，3：完成，4：失败 */
    @Schema(description = "1：待处理；2：处理中，3：完成，4：失败", required = true)
    private Integer status ;

}
