package cn.mlamp.insightflow.cms.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 管理员配置
 *
 * <AUTHOR>
 * @since 2024-10-15
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "admin")
public class AdminConfig {

    /**
     * 管理员租户ID
     */
    private Integer tenantId;
}
