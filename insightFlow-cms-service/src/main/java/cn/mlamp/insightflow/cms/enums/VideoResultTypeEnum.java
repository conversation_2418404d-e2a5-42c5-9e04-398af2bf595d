package cn.mlamp.insightflow.cms.enums;

/**
 * @Author: husuper
 * @CreateTime: 2025-03-31
 */
public enum VideoResultTypeEnum {


    INDUSTRY_DECODING(1, "AI解码"),


    scene_split(2, "分镜"),


    ASR(3, "ASR"),


    GOLD_FIVE(4, "黄金5秒"),


    PRE_INDUSTRY_DECODING(5, "AI解码原始数据"),

    PRE_GOLD_FIVE(6, "黄金5秒原始数据"),
            

    ASR5(7, "黄金5秒ASR"),

    ALL_ASR(8, "ASR全量信息"),

    INDUSTRY_DECODING2(9, "0.2版本视频整体分析"),

    GOLD_FIVE2(10, "0.2版本黄金3秒（视频前5秒）"),

    SCENE_SPLIT2(11, "0.2版本分镜"),

    VIDEO_SPLIT(12, "0.2视频分割"),

    VIDEO_SPLIT_ASR(13, "0.2视频分割ASR分割信息"),



    USER_SCENE_SPLIT2(1101, "0.2版本y用户编辑后的分镜"),


    ;

    private final int code;
    private final String msg;

    VideoResultTypeEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
