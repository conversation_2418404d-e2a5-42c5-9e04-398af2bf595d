package cn.mlamp.insightflow.cms.constant;

/**
 * <AUTHOR>
 * @since 2022/3/7 5:59 下午
 */
public class CommonConstant {

    /**
     * 左括号
     */
    public static final String LEFT_BRACKETS = "(";
    /**
     * 右括号
     */
    public static final String RIGHT_BRACKETS = ")";
    /**
     * 晚于某个时间的标志
     */
    public static final Integer AFTER_FLAG = 1;
    /**
     * 零，Long类型
     */
    public static final Long ZERO_LONG = 0L;
    /**
     * 零，Double类型
     */
    public static final Double ZERO_DOUBLE = 0.0d;
    /**
     * 微博站点在ES中的值
     */
    public static final String WEIBO_SOURCE_ES_SOURCE = "weibo.com";
    /**
     * 微博对应的i18n的key
     */
    public static final String WEIBO_NAME_I18N = "dict.platform.weibo";

    /**
     * 平台微博CODE
     */
    public static final String PLATFORM_CODE_WEIBO = "weibo";

    /**
     * 平台微信CODE
     */
    public static final String PLATFORM_CODE_WEIXIN = "weixin";

    /**
     * 平台晒单CODE
     */
    public static final String PLATFORM_CODE_SHARE = "share";

    /**
     * 平台视频CODE
     */
    public static final String PLATFORM_CODE_VIDEO = "video";

    /**
     * 平台新闻CODE
     */
    public static final String PLATFORM_CODE_NEWS = "news";

    /**
     * 平台论坛CODE
     */
    public static final String PLATFORM_CODE_BBS = "bbs";

    /**
     * 平台博客CODE
     */
    public static final String PLATFORM_CODE_BLOG = "blog";

    /**
     * 站点小红薯CODE
     */
    public static final String SOURCE_CODE_XIAOHONGSHU = "xiaohongshu";

    /**
     * 站点B站CODE
     */
    public static final String SOURCE_CODE_BILIBILI = "bilibili";

    /**
     * 站点抖音CODE
     */
    public static final String SOURCE_CODE_DOUYIN = "douyin";

    /**
     * 帖子类型维度配置中，默认站点key名称，即非明确指明的站点都按照默认站点的配置执行
     */
    public static final String DEFAULT_WEBSITE_KEY = "default";

    /**
     * 去水权限Key
     */
    public static final String AUTH_PERMISSION_IS_DE_WATER = "isDeWater";

    /**
     * 观点分析权限Key
     */
    public static final String AUTH_PERMISSION_OPINION_ANALYSIS = "opinion_analysis";

    /**
     * 查询范围：图片/语音/字幕识别 的code
     */
    public static final String SEARCH_FIELD_OCR_ASR = "ocr_asr";

    public static final String DOC_TYPE_COMMENT = "comment";

    /**
     * 英文逗号分割符
     */
    public static final String COMMA = ",";

    /**
     * 邮箱正则
     */
    public static final String EMAIL_REGX = "^([A-Za-z0-9_\\-\\.])+\\@([A-Za-z0-9_\\-\\.])+\\.([A-Za-z]{2,10})$";
    /**
     * 手机号正则
     */
    public static final String MOBILE_REGX = "^[1][3,4,5,6,7,8,9][0-9]{9}$";
    /**
     * 中文字符正则
     */
    public static final String CHINESE_REGX = "^[\\u4e00-\\u9fa5]{0,}$";
    /**
     * ttc 密码正则
     */
    public static final String PASSWORD_REGX = "^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\\W_]+$)(?![a-z0-9]+$)(?![a-z\\W_]+$)(?![0-9\\W_]+$)[\\S]{8,20}$";
    /**
     * 验证码 正则
     */
    public static final String VERIFICATION_CODE_REGX = "^[0-9]{6}$";

    /**
     * 请求设备源
     */
    public static final String REQUEST_HEADER_DEVICE_SOURCE = "_request_device";

    /**
     * 高级筛选中需要特殊处理的条件类型 - searchFields
     */
    public static final String OPERATION_CONDITION_SEARCH_FIELDS = "searchFields";

    /**
     * 高级筛选中需要特殊处理的条件类型 - docType
     */
    public static final String OPERATION_CONDITION_DOCTYPE = "docType";

    /**
     * 高级筛选中需要特殊处理的条件类型 - sentiment
     */
    public static final String OPERATION_CONDITION_SENTIMENT = "sentiment";
    /**
     * 高级筛选中需要特殊处理的条件类型 - isDeWater
     */
    public static final String OPERATION_CONDITION_IS_DE_WATER = "isDeWater";

    /**
     * 高级筛选中需要特殊处理的条件类型 - age
     */
    public static final String OPERATION_CONDITION_AGE = "ageGroup";

    /**
     * 高级筛选中需要特殊处理的条件类型 - city
     */
    public static final String OPERATION_CONDITION_CITY = "city";

    /**
     * 城市名称后缀
     */
    public static final String CITY_NAME_SUFFIX = "市";

    /**
     * 标识该条件是否受最多10个条件的查询范围限制 0-是筛选条件，需要做校验，1-是下钻，不需要校验
     */
    public static Integer OPERATION_CONDITION_FLAG_0 = 0;

    /**
     * ES通用大库Tag
     */
    public static final String ES_TAG_COMMON = "common";
    /**
     * 欧莱雅库
     */
    public static final String ES_TAG_LOREAL = "lorealv2";

    public static final String NORM_DATETIME_MINUTE_PATTERN = "yyyy-MM-dd\nHH:mm";

    public static final String NORM_DATETIME_HOUR_PATTERN = "mm:ss";

    /**
     * 美妆原始实体标签维度
     */
    public static final String ES_TAG_BEAUTY_DIMENSION_STANDARD = "entity_non_standard";

    /**
     * 美妆标签维度
     */
    public static final String ES_TAG_BEAUTY_DIMENSION_TAG = "tag";

    /**
     * 行业知识库标签树，顶级节点的parentKey值
     */
    public static final String INDUSTRY_KNOWLEDGE_TAG_TREE_ROOT_NODE_PARENT_KEY = "0";

    /**
     * 行业知识库标签是否是树结构标志，标签类型：0:普通标签，1：树标签
     */
    public static final Integer INDUSTRY_KNOWLEDGE_TAG_TREE = 1;
    public static final Integer INDUSTRY_KNOWLEDGE_TAG_LIST = 0;

    /**
     * 原贴默认头像
     */

    public static final String DEFAULT_AVATAR = "https://mz-social-ttc-1255521909.cos.ap-shanghai.myqcloud.com/socialx/prod/profile.png";

    /**
     * 算法推荐类型：1:全量推荐，2：猜你想搜(智能推荐)
     */
    public static final Integer FULL_RECOMMENDATION = 1;
    public static final Integer INTELLIGENT_RECOMMENDATION = 2;

    public static final String PERCENT = "%";
    /**
     * (?<=.)(?!$) 表示匹配一个字符，该字符的前面必须有一个字符存在，并且它的后面不能是字符串的末尾。
     */
    public static final String REGX_SEARCH_KEYWORD = "(?<=.)(?!$)";

    public static final String GT = ">";

}
