package cn.mlamp.insightflow.cms.util.dify.model;//package cn.mlamp.insightflow.cms.util.dify.model;

public enum DifyFileTypeEnum {
    // Document types
    DOCUMENT("document", new String[]{"TXT", "MD", "MARKDOWN", "PDF", "HTML", "XLSX", "XLS", "DOCX", "CSV", "EML", "MSG", "PPTX", "PPT", "XML", "EPUB"}),

    // Image types
    IMAGE("image", new String[]{"JPG", "JPEG", "PNG", "GIF", "WEBP", "SVG"}),

    // Audio types
    AUDIO("audio", new String[]{"MP3", "M4A", "WAV", "WEBM", "AMR"}),

    // Video types
    VIDEO("video", new String[]{"MP4", "MOV", "MPEG", "MPGA"});

    private final String category;
    private final String[] specificTypes;

    DifyFileTypeEnum(String category, String[] specificTypes) {
        this.category = category;
        this.specificTypes = specificTypes;
    }

    public static String getCategory(String fileType) {
        for (DifyFileTypeEnum type : DifyFileTypeEnum.values()) {
            for (String specificType : type.getSpecificTypes()) {
                if (specificType.equalsIgnoreCase(fileType)) {
                    return type.getCategory();
                }
            }
        }
        return null;
    }

    public String getCategory() {
        return category;
    }

    public String[] getSpecificTypes() {
        return specificTypes;
    }

}
