package cn.mlamp.insightflow.cms.model.dto.dam;

import cn.mlamp.insightflow.cms.enums.dam.DamDirectoryTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * DAM目录DTO
 */
@Data
@Schema(description = "DAM目录DTO")
public class DamDirectoryDTO {

    @Schema(description = "目录ID")
    private Integer id;

    @NotBlank(message = "目录名称不能为空")
    @Pattern(regexp = "^[^\\\\/:*?\"<>|]*$",
            message = "目录名称不能包含以下字符：\\、/、:、*、?、\"、<、>、|")
    @Size(max = 20, message = "目录名称不能超过20个字符")
    @Schema(description = "目录名称",
            requiredMode = RequiredMode.REQUIRED)
    private String name;

    @NotNull(message = "目录类型不能为空")
    @Schema(description = "目录类型：1-个人文件夹，2-租户文件夹",
            requiredMode = RequiredMode.REQUIRED)
    private DamDirectoryTypeEnum type;
} 