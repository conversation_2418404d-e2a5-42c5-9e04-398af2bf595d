package cn.mlamp.insightflow.cms.model.query;

import lombok.Data;

import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;

@Data
public class VideoHotspotQueryRequest {

    @Schema(description = "搜索关键词（可选）", required = false)
    private String keyword;

    @Schema(description = "开始时间（可选）", required = false)
    private Date startTime;

    @Schema(description = "结束时间（可选）", required = false)
    private Date endTime;

    @Schema(description = "排序依据：点赞数、互动数、评论数、创意分值", required = false)
    private String sortBy;

    @Schema(description = "排序方式：asc 或 desc", required = false)
    private String sortOrder;

    @Schema(description = "类型 1: 视频热点, 2: 圈层热点, 3: 上传视频", required = false)
    private Integer type = 1; // 默认值为 1，视频热点

    @Schema(description = "类型 0,1,2,3, 综合，名字，内容，视频", required = false)
    private String selectType;

    @Schema(description = "品类筛选", required = false)
    private String kwKbIndustry; // 品类筛选（例如：3c,beauty）

    @Schema(description = "圈层筛选", required = false)
    private String kwTwoLevelTribeTag; // 圈层筛选（例如：某些圈层标签）

    @Schema(description = "数据来源类型1 es，2 上传，3 链接", required = false)
    private Integer dataSource;

    @Schema(description = "视频名称", required = false)
    private String videoName;

    @Schema(description = "页码（默认 1）", required = false)
    private int page = 1;

    @Schema(description = "每页大小（默认 10）", required = false)
    private int size = 10;
}
