package cn.mlamp.insightflow.cms.service.impl;

import cn.mlamp.insightflow.cms.auth.cms.service.QuotaService;
import cn.mlamp.insightflow.cms.auth.cms.service.TtcAdaptor;
import cn.mlamp.insightflow.cms.enums.TtcBizCode;
import cn.mlamp.insightflow.cms.enums.VerificationTypeEnum;
import cn.mlamp.insightflow.cms.exception.BusinessException;
import cn.mlamp.insightflow.cms.model.dto.TtcRequestVerificationCodeDTO;
import cn.mlamp.insightflow.cms.model.query.VerificationCodeParam;
import cn.mlamp.insightflow.cms.service.VerificationCodeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 验证码Service
 *
 * <AUTHOR>
 * @since 2022-09-08 15:20:16
 **/
@Slf4j
@Service
public class VerificationCodeServiceImpl implements VerificationCodeService {

    @Autowired
    private TtcAdaptor ttcAdaptor;

    @Autowired
    private QuotaService quotaService;

    @Override
    public void send(VerificationCodeParam verificationCodeParam) {
        log.info("执行TTC验证码发送");
        // 内部用户校验,Source = 1-账号注册 2-重置密码, 重置密码时发送验证码提示'企业微信修改'
        if (VerificationTypeEnum.TYPE_EMAIL.getCode().equals(verificationCodeParam.getVerificationType())
                && verificationCodeParam.getSource() == 2) {
            String email = verificationCodeParam.getVerificationTarget();
            if (quotaService.checkIsInternalUsersByUserName(email)) {
                throw new BusinessException(TtcBizCode.INTERNAL_USER_RESET_PASSWORD_ERROR);
            }
        }
        TtcRequestVerificationCodeDTO requestDTO = new TtcRequestVerificationCodeDTO();
        requestDTO.setSource(verificationCodeParam.getSource());
        requestDTO.setVerificationType(verificationCodeParam.getVerificationType());
        requestDTO.setVerificationTarget(verificationCodeParam.getVerificationTarget());
        ttcAdaptor.sendVerificationCode(requestDTO);
        log.info("TTC验证码发送成功，verificationTarget=[{}]", verificationCodeParam.getVerificationTarget());
    }

}
