package cn.mlamp.insightflow.cms.model.converter.dam;

import javax.annotation.Nullable;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import cn.mlamp.insightflow.cms.entity.dam.DamRecycleBin;
import cn.mlamp.insightflow.cms.model.vo.dam.DamAssetVO;
import cn.mlamp.insightflow.cms.model.vo.dam.DamDirectoryVO;
import cn.mlamp.insightflow.cms.model.vo.dam.DamRecycleBinVO;

@Mapper(componentModel = "spring")
public interface DamRecycleBinConverter {

    @Mapping(source = "damRecycleBin.id", target = "id")
    @Mapping(source = "damRecycleBin.createTime", target = "createTime")
    @Mapping(source = "damRecycleBin.tenantId", target = "tenantId")
    @Mapping(source = "damRecycleBin.userId", target = "userId")
    @Mapping(source = "damRecycleBin.objectType", target = "objectType")
    @Mapping(source = "damRecycleBin.recoverTime", target = "recoverTime")
    @Mapping(source = "asset", target = "asset")
    @Mapping(source = "directory", target = "directory")
    DamRecycleBinVO toVO(DamRecycleBin damRecycleBin, DamDirectoryVO directory, @Nullable DamAssetVO asset);

}
