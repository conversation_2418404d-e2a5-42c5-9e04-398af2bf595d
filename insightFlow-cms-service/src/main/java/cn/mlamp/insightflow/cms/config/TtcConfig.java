package cn.mlamp.insightflow.cms.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * TTC 接口API配置
 *
 * <AUTHOR>
 * @since 2022-09-15 16:03:41
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "auth-center")
public class TtcConfig {

    /**
     * 服务OpenApi地址
     */
    private String serverOpenApiUrl;
    /**
     * 服务OpenApi ClientId
     */
    private String serverOpenApiClientId;
    /**
     * 服务OpenApi签名
     */
    private String serverOpenApiSign;
    /**
     * 校验租户名是否存在api
     */
    private String checkTenantNameExistUrl;
    /**
     * 注册用户api
     */
    private String registerUrl;
    /**
     * 激活用户api
     */
    private String activateUrl;
    /**
     * 发送验证码api
     */
    private String sendVerificationCodeUrl;
    /**
     * 重置密码api
     */
    private String resetPasswordUrl;
    /**
     * 租户列表
     */
    private String tenantListUrl;
    /**
     * 租户详细
     */
    private String tenantDetailUrl;

    private String productKey;

}
