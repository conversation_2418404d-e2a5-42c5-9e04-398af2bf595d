package cn.mlamp.insightflow.cms.strategy.video.create;


import cn.mlamp.insightflow.cms.entity.CmsAsyncTask;
import cn.mlamp.insightflow.cms.exception.BusinessException;
import cn.mlamp.insightflow.cms.model.query.AnalysisVideoCreateRequest;
import cn.mlamp.insightflow.cms.model.query.AnalysisVideoQueryRequest;
import cn.mlamp.insightflow.cms.model.query.AsyncResultRequest;
import cn.mlamp.insightflow.cms.model.vo.AnalysisVideoCreateVO;
import cn.mlamp.insightflow.cms.model.vo.AnalysisVideoResultVO;

public interface ProcessAnalysisVideoStrategyInterface {

    AnalysisVideoCreateVO process(AnalysisVideoCreateRequest request);

    AnalysisVideoResultVO queryResult(AnalysisVideoQueryRequest request);

    default void processAsyncTask(CmsAsyncTask cmsAsyncTask, AsyncResultRequest asyncResultRequest) {
        // 处理 async_video_flow_v2 类型任务
        throw new BusinessException("暂不支持该任务类型");
    }



}
