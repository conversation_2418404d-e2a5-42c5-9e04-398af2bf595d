package cn.mlamp.insightflow.cms.mapper.dam;

import cn.mlamp.insightflow.cms.entity.dam.DamRecycleBin;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * DAM回收站表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
@Mapper
public interface DamRecycleBinMapper extends BaseMapper<DamRecycleBin> {

    /**
     * 获取回收站列表，包括对象详情
     *
     * @param type     对象类型 1-个人文件夹, 2-租户文件夹
     * @param tenantId 租户ID
     * @param userId   用户ID
     * @return 回收站列表
     */
    List<DamRecycleBin> selectRecycleBinListWithDetails(
            @Param("type") Integer type,
            @Param("tenantId") Integer tenantId,
            @Param("userId") Integer userId);

    /**
     * 更新恢复时间
     *
     * @param id 回收站ID
     * @return 影响行数
     */
    int updateRecoverTime(@Param("id") Integer id);

    /**
     * 批量删除回收站记录（物理删除）
     *
     * @param ids ID列表
     * @return 影响行数
     */
    int batchDeletePhysically(@Param("list") List<Integer> ids);

    /**
     * 清空回收站（物理删除全部）
     *
     * @param tenantId 租户ID
     * @param userId   用户ID
     * @return 影响行数
     */
    int emptyRecycleBin(@Param("tenantId") Integer tenantId, @Param("userId") Integer userId);

}
