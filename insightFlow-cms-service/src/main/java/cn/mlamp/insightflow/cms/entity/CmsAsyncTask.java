package cn.mlamp.insightflow.cms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 异步任务表
 */
@Data
@TableName("cms_async_task")
public class CmsAsyncTask {

    @TableId(type = IdType.AUTO)
    // 主键ID
    private Long id;

    // 异步任务唯一标识
    private Long dbUniqueId;

    // 任务类型
    private String taskType;

    // 任务处理状态：1：已推送；2：处理成功；3：处理失败
    private Integer taskStatus;

    // ES业务ID
    private String esId;

    // 视频分析任务类型
    private Integer videoInfoType;

    // 任务创建时间
    private LocalDateTime createTime;

    // 任务最后更新时间
    private LocalDateTime updateTime;

    // 数据逻辑删除标记 0:未删除 1:已删除
    private Integer isDeleted;

    //json数据
    private String data;
}
