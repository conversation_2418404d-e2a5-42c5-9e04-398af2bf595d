package cn.mlamp.insightflow.cms.mapper;

import cn.mlamp.insightflow.cms.entity.CmsVideoInfo;
import cn.mlamp.insightflow.cms.model.vo.VideoTaskListInfoVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * @Author: husuper
 * @CreateTime: 2025-03-21
 */
public interface VideoInfoMapper extends BaseMapper<CmsVideoInfo> {

    Page<VideoTaskListInfoVO> pageVideoInfo(Page<VideoTaskListInfoVO> page,
                                            @Param("type") Integer type,
                                            @Param("tenantId") Integer tenantId,
                                            @Param("userId") Integer userId);

    @Select({
            "SELECT vi.*",
            "FROM cms_qianchuan_material_video qm",
            "JOIN cms_video_info vi ON qm.video_id = vi.es_id",
            "WHERE qm.analysis_status = 3 AND vi.status = 3 AND vi.is_deleted=0 AND qm.is_deleted=0"
    })
    List<CmsVideoInfo> selectQianchuanAndVideoInfo();


}
