package cn.mlamp.insightflow.cms.service.dam.impl;

import cn.mlamp.insightflow.cms.entity.dam.DamPublicTag;
import cn.mlamp.insightflow.cms.mapper.dam.DamPublicTagMapper;
import cn.mlamp.insightflow.cms.service.dam.IDamPublicTagService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * DAM公共标签表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
@Service
public class DamPublicTagServiceImpl extends ServiceImpl<DamPublicTagMapper, DamPublicTag> implements IDamPublicTagService {

}
