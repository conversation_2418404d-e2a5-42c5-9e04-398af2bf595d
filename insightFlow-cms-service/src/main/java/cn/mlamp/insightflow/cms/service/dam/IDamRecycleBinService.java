package cn.mlamp.insightflow.cms.service.dam;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;

import cn.mlamp.insightflow.cms.entity.dam.DamRecycleBin;
import cn.mlamp.insightflow.cms.enums.dam.DamDirectoryTypeEnum;
import cn.mlamp.insightflow.cms.model.dto.dam.DamRecycleBinRecoverDTO;
import cn.mlamp.insightflow.cms.model.vo.dam.DamRecycleBinRecoverItem;
import cn.mlamp.insightflow.cms.model.vo.dam.DamRecycleBinVO;

/**
 * <p>
 * DAM回收站表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
public interface IDamRecycleBinService extends IService<DamRecycleBin> {

    /**
     * 获取回收站列表
     *
     * @param type     类型 1: 个人, 2: 租户
     * @param userId   用户ID
     * @param tenantId 租户ID
     * @return 回收站列表
     */
    List<DamRecycleBinVO> getRecycleBinList(DamDirectoryTypeEnum type, Integer userId, Integer tenantId);

    /**
     * 恢复回收站对象
     *
     * @param recycleBinDTOList 恢复对象列表
     * @param userId            用户ID
     * @param tenantId          租户ID
     * @return 恢复结果列表
     */
    List<DamRecycleBinRecoverItem> recoverObjects(List<DamRecycleBinRecoverDTO> recycleBinDTOList, Integer userId,
            Integer tenantId);

    /**
     * 删除回收站对象
     *
     * @param recycleBinIds 回收站对象ID列表
     * @param userId        用户ID
     * @param tenantId      租户ID
     * @return 是否成功
     */
    boolean deleteObjects(List<Integer> recycleBinIds, Integer userId, Integer tenantId);

    /**
     * 清空回收站
     *
     * @param userId   用户ID
     * @param tenantId 租户ID
     * @return 是否成功
     */
    boolean emptyRecycleBin(Integer userId, Integer tenantId);

}
