
package cn.mlamp.insightflow.cms.service;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;

import cn.mlamp.insightflow.cms.entity.QianchuanMaterialVideo;
import cn.mlamp.insightflow.cms.model.dto.QianchuanVideoHotspotQueryDTO;
import cn.mlamp.insightflow.cms.model.query.RecommenListRequest;
import cn.mlamp.insightflow.cms.model.vo.BrandProductRecommendVO;

public interface QianchuanVideoHotspotService {
    IPage<QianchuanMaterialVideo> listVideos(QianchuanVideoHotspotQueryDTO queryDTO);

    QianchuanMaterialVideo getTopExposureVideo();

    List<QianchuanMaterialVideo> getRecommenListRequest(RecommenListRequest request);

    /**
     * 获取品牌和产品名称推荐列表
     * 
     * @param request 请求参数
     * @return 品牌和产品名称推荐列表
     */
    List<BrandProductRecommendVO> getBrandProductRecommendList(RecommenListRequest request);
}
