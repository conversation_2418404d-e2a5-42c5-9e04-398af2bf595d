package cn.mlamp.insightflow.cms.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;

import cn.hutool.json.JSONUtil;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 黄金五秒Dify请求DTO
 */
@Data
public class DifyGoldFiveSecondRequestDTO {

    private String line;

    /**
     * 构建Dify请求参数
     *
     * @param asr5List ASR 5秒值列表
     * @return Dify请求参数
     */
    public static Map<String, Object> buildDifyParams(List<String> asr5List) {
        var dto = new DifyGoldFiveSecondRequestDTO();

        // 将ASR 5秒值列表转换为 Map<String, String> 对象
        Map<String, String> lineMap = new HashMap<>();
        for (int i = 0; i < asr5List.size(); i++) {
            // 使用从1开始的索引作为键
            lineMap.put(String.valueOf(i + 1), asr5List.get(i));
        }
        dto.setLine(JSONUtil.toJsonStr(lineMap));

        // 转换为Map
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        @SuppressWarnings("unchecked")
        Map<String, Object> result = objectMapper.convertValue(dto, Map.class);
        return result;
    }
}
