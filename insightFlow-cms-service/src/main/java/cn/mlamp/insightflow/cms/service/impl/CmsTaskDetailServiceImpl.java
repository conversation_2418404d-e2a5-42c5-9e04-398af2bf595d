package cn.mlamp.insightflow.cms.service.impl;

import cn.mlamp.insightflow.cms.entity.CmsTaskDetail;
import cn.mlamp.insightflow.cms.enums.TaskDetailDataTypeEnum;
import cn.mlamp.insightflow.cms.enums.TaskDetailTypeEnum;
import cn.mlamp.insightflow.cms.mapper.CmsTaskDetailMapper;
import cn.mlamp.insightflow.cms.service.ICmsTaskDetailService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@AllArgsConstructor
public class CmsTaskDetailServiceImpl extends ServiceImpl<CmsTaskDetailMapper, CmsTaskDetail>
        implements ICmsTaskDetailService {

    @Override
    public CmsTaskDetail saveTaskDetail(Integer taskId, Integer type, String dataType, String data) {
        CmsTaskDetail cmsTaskDetail = new CmsTaskDetail();
        cmsTaskDetail.setTaskId(taskId);
        cmsTaskDetail.setType(type);
        cmsTaskDetail.setDataType(dataType);
        cmsTaskDetail.setData(data);
        this.save(cmsTaskDetail);
        return cmsTaskDetail;
    }

    @Override
    public List<CmsTaskDetail> getTaskResult(Integer taskId) {
        return this.list(new LambdaQueryWrapper<CmsTaskDetail>()
                .eq(CmsTaskDetail::getType, TaskDetailTypeEnum.OUTPUT.getCode())
                .eq(CmsTaskDetail::getTaskId, taskId));
    }

    @Override
    public CmsTaskDetail getUserTaskDetail(Integer taskId) {
        return this.getOne(new LambdaQueryWrapper<CmsTaskDetail>()
                .eq(CmsTaskDetail::getTaskId, taskId)
                .eq(CmsTaskDetail::getDataType, TaskDetailDataTypeEnum.USER_SAVE.getCode())
        );
    }

    @Override
    public CmsTaskDetail getTaskDetail(Integer taskId, Integer type, String dataType) {
        return this.getOne(new LambdaQueryWrapper<CmsTaskDetail>()
                .eq(CmsTaskDetail::getTaskId, taskId)
                .eq(CmsTaskDetail::getType, type)
                .eq(CmsTaskDetail::getDataType, dataType));
    }

    @Override
    public void deleteByTaskId(Integer taskId) {
        this.remove(new LambdaQueryWrapper<CmsTaskDetail>()
                .eq(CmsTaskDetail::getTaskId, taskId));
    }
}
