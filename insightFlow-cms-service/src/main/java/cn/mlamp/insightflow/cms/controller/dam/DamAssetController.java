package cn.mlamp.insightflow.cms.controller.dam;

import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.common.resp.RespCode;
import cn.mlamp.insightflow.cms.config.UserContext;
import cn.mlamp.insightflow.cms.exception.BusinessException;
import cn.mlamp.insightflow.cms.model.dto.dam.DamAssertUpdateDTO;
import cn.mlamp.insightflow.cms.model.query.dam.DamAssetQueryParam;
import cn.mlamp.insightflow.cms.model.query.dam.DamAssetSemanticSearchParam;
import cn.mlamp.insightflow.cms.model.vo.dam.DamAssetVO;
import cn.mlamp.insightflow.cms.model.vo.dam.DamPageResult;
import cn.mlamp.insightflow.cms.service.dam.IDamAssetService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * DAM 素材 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/dam/assets")
public class DamAssetController {

    @Autowired
    private IDamAssetService assetService;

    private static Set<String> orderFieldsSet = Set.of("createBy", "usedNum", "duration", "storageTime");

    /**
     * 获取素材列表
     */
    @PostMapping
    public RespBody<DamPageResult<DamAssetVO>> getAssetList(@RequestBody DamAssetQueryParam queryParam,
                                                            HttpServletRequest request) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();

        if (StringUtils.isNotBlank(queryParam.getSortField())
                && !orderFieldsSet.contains(queryParam.getSortField())) {
            throw new BusinessException(RespCode.BAD_REQUEST, "unsupported sortField: " + queryParam.getSortField());
        }

        DamPageResult<DamAssetVO> result = assetService.getAssetList(queryParam, userId, tenantId);
        return RespBody.ok(result);
    }

    /**
     * 获取素材详情
     */
    @GetMapping("/{assetId}")
    public RespBody<DamAssetVO> getAssetDetail(@PathVariable Integer assetId,
                                               HttpServletRequest request) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();

        DamAssetVO assetVO = assetService.getAssetDetail(assetId, userId, tenantId);
        return RespBody.ok(assetVO);
    }

    /**
     * 更新素材标签
     */
    @PutMapping("/{assetId}")
    public RespBody<Void> updateAsset(@PathVariable Integer assetId,
                                      @RequestBody DamAssertUpdateDTO assertUpdateDTO,
                                      HttpServletRequest request) {
        if (!assertUpdateDTO.validate()) {
            throw new BusinessException(RespCode.BAD_REQUEST.getCode(), "request body is invalid");
        }

        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();

        boolean result = assetService.updateAsset(assetId, assertUpdateDTO, userId, tenantId);
        return result ? RespBody.ok() : RespBody.fail("更新素材标签失败");
    }

    /**
     * 删除素材（移入回收站）
     */
    @DeleteMapping("/{assetId}")
    public RespBody<Void> deleteAsset(@PathVariable Integer assetId,
                                      HttpServletRequest request) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();

        boolean result = assetService.deleteAssets(List.of(assetId), userId, tenantId);
        return result ? RespBody.ok() : RespBody.fail("删除素材失败");
    }

    /**
     * 删除素材（移入回收站）
     * TODO: 最大支持数量
     */
    @PostMapping("/delete")
    public RespBody<Void> deleteAssets(@RequestBody Map<String, Object> params,
                                       HttpServletRequest request) {
        @SuppressWarnings("unchecked")
        List<Integer> assetIds = (List<Integer>) params.get("assetIds");
        if (CollectionUtils.isEmpty(assetIds)) {
            throw new BusinessException(RespCode.BAD_REQUEST, "asset ids can't be empty");
        }

        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();

        boolean result = assetService.deleteAssets(assetIds, userId, tenantId);
        return result ? RespBody.ok() : RespBody.fail("删除素材失败");
    }

    /**
     * 拷贝素材
     */
    @PostMapping("/copy")
    public RespBody<Void> copyAssets(@RequestBody Map<String, Object> params,
                                     HttpServletRequest request) {
        @SuppressWarnings("unchecked")
        List<Integer> assetIds = (List<Integer>) params.get("assetIds");
        if (CollectionUtils.isEmpty(assetIds)) {
            throw new IllegalArgumentException("asset ids can't be empty");
        }

        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();

        boolean result = assetService.copyAssets(assetIds, userId, tenantId);
        return result ? RespBody.ok() : RespBody.fail("拷贝素材失败");
    }

    /**
     * 移动素材
     */
    @PostMapping("/move")
    public RespBody<Void> moveAssets(@RequestBody Map<String, Object> params,
                                     HttpServletRequest request) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();

        @SuppressWarnings("unchecked")
        List<Integer> assetIds = (List<Integer>) params.get("assetIds");
        Integer targetDirectoryId = (Integer) params.get("targetDirectoryId");

        if (CollectionUtils.isEmpty(assetIds)) {
            throw new IllegalArgumentException("asset ids can't be empty");
        }
        if (targetDirectoryId == null) {
            throw new IllegalArgumentException("targetDirectoryId be null");
        }

        boolean result = assetService.moveAssets(assetIds, targetDirectoryId, userId, tenantId);
        return result ? RespBody.ok() : RespBody.fail("移动素材失败");
    }

    /**
     * 下载素材（生成链接）
     */
    @GetMapping("/{assetId}/download")
    public RespBody<String> getAssetDownloadUrl(@PathVariable Integer assetId,
                                                HttpServletRequest request) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();

        String downloadUrl = assetService.getAssetDownloadUrl(assetId, userId, tenantId);
        return RespBody.ok(downloadUrl);
    }

    /**
     * 语义检索素材
     */
    @PostMapping("/search/semantic")
    public RespBody<DamPageResult<DamAssetVO>> semanticSearchAssets(@RequestBody @Valid DamAssetSemanticSearchParam searchParam,
                                                                    HttpServletRequest request) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();

        DamPageResult<DamAssetVO> result = assetService.semanticSearchAssets(searchParam, userId, tenantId);
        return RespBody.ok(result);
    }
}