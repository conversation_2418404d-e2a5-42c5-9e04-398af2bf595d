package cn.mlamp.insightflow.cms.service.dam.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import cn.hutool.core.collection.CollectionUtil;
import cn.mlamp.insightflow.cms.common.file.IS3FlowService;
import cn.mlamp.insightflow.cms.config.properties.ObjectStorageFlowProperties;
import cn.mlamp.insightflow.cms.entity.dam.DamAsset;
import cn.mlamp.insightflow.cms.entity.dam.DamDirectory;
import cn.mlamp.insightflow.cms.entity.dam.DamRecycleBin;
import cn.mlamp.insightflow.cms.entity.dam.DamRecycleBinDirectoryAssetMapping;
import cn.mlamp.insightflow.cms.enums.dam.DamDirectoryTypeEnum;
import cn.mlamp.insightflow.cms.enums.dam.DamRecycleBinObjectTypeEnum;
import cn.mlamp.insightflow.cms.mapper.dam.DamRecycleBinMapper;
import cn.mlamp.insightflow.cms.model.converter.dam.DamDirectoryConverter;
import cn.mlamp.insightflow.cms.model.converter.dam.DamRecycleBinConverter;
import cn.mlamp.insightflow.cms.model.dto.dam.DamRecycleBinRecoverDTO;
import cn.mlamp.insightflow.cms.model.vo.dam.DamAssetVO;
import cn.mlamp.insightflow.cms.model.vo.dam.DamRecycleBinRecoverItem;
import cn.mlamp.insightflow.cms.model.vo.dam.DamRecycleBinVO;
import cn.mlamp.insightflow.cms.service.dam.IDamAssetEmbeddingService;
import cn.mlamp.insightflow.cms.service.dam.IDamAssetService;
import cn.mlamp.insightflow.cms.service.dam.IDamDirectoryService;
import cn.mlamp.insightflow.cms.service.dam.IDamRecycleBinDirectoryAssetMappingService;
import cn.mlamp.insightflow.cms.service.dam.IDamRecycleBinService;
import cn.mlamp.insightflow.cms.service.dam.IDamTagService;
import cn.mlamp.insightflow.cms.service.dam.IDamTagValueService;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * DAM回收站表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
@Slf4j
@Service
public class DamRecycleBinServiceImpl extends ServiceImpl<DamRecycleBinMapper, DamRecycleBin>
        implements IDamRecycleBinService {

    @Lazy
    @Autowired
    private IDamDirectoryService directoryService;

    @Autowired
    @Lazy
    private IDamAssetService assetService;

    @Autowired
    private IDamTagService tagService;

    @Autowired
    private IDamTagValueService tagValueService;

    @Autowired
    private DamDirectoryConverter directoryConverter;

    @Autowired
    private ObjectStorageFlowProperties storageProperties;

    @Autowired
    private IDamRecycleBinDirectoryAssetMappingService recycleBinDirectoryAssetMappingService;

    @Autowired
    private IDamAssetEmbeddingService embeddingService;

    @Autowired
    private IS3FlowService s3Service;

    @Autowired
    private DamRecycleBinConverter recycleBinConverter;

    @Override
    public List<DamRecycleBinVO> getRecycleBinList(
            DamDirectoryTypeEnum type,
            Integer userId,
            Integer tenantId) {
        final List<DamRecycleBin> recycleBinList = list(
                new LambdaQueryWrapper<DamRecycleBin>()
                        .eq(type != null, DamRecycleBin::getDirectoryType, type)
                        .eq(DamRecycleBin::getUserId, userId)
                        .eq(DamRecycleBin::getTenantId, tenantId)
                        .eq(DamRecycleBin::getIsDeleted, false));

        // 获取objectType为素材的垃圾桶对象Ids
        final Set<Integer> assetIds = recycleBinList.stream()
                .filter(recycleBin -> DamRecycleBinObjectTypeEnum.ASSET
                        .equals(recycleBin.getObjectType()))
                .map(DamRecycleBin::getObjectId)
                .collect(Collectors.toSet());

        final List<DamAsset> assets = CollectionUtil.isNotEmpty(assetIds) ? assetService.listByIds(assetIds) : Collections.emptyList();
        final Set<Integer> assetDirectoryIds = assets.stream().map(DamAsset::getDirectoryId)
                .collect(Collectors.toSet());

        // 获取objectType为目录的垃圾桶对象Ids
        final Set<Integer> allDirectoryIds = recycleBinList.stream()
                .filter(recycleBin -> DamRecycleBinObjectTypeEnum.DIRECTORY
                        .equals(recycleBin.getObjectType()))
                .map(DamRecycleBin::getObjectId).collect(Collectors.toSet());
        allDirectoryIds.addAll(assetDirectoryIds);

        // 获取目录对象
        final Map<Integer, DamDirectory> directoryMap = CollectionUtil.isNotEmpty(allDirectoryIds) ? directoryService.listByIds(allDirectoryIds).stream()
                .collect(Collectors.toMap(DamDirectory::getId, Function.identity())) : Collections.emptyMap();

        // 获取素材对象
        final Map<Integer, DamAssetVO> assetMap = CollectionUtil.isNotEmpty(assetIds) ? assetService
                .streamBy(assetService.listByIds(assetIds), directoryMap)
                .collect(Collectors.toMap(DamAssetVO::getId, Function.identity())) : Collections.emptyMap();

        return recycleBinList.stream().map(recycleBin -> {
            if (DamRecycleBinObjectTypeEnum.DIRECTORY.equals(recycleBin.getObjectType())) {
                return recycleBinConverter.toVO(recycleBin,
                        directoryConverter.toVO(directoryMap.get(recycleBin.getObjectId())),
                        null);
            } else {
                final DamAssetVO asset = assetMap.get(recycleBin.getObjectId());
                return recycleBinConverter.toVO(recycleBin,
                        directoryConverter.toVO(directoryMap.get(asset.getDirectoryId())),
                        asset);
            }
        }).toList();
    }

    @Override
    public List<DamRecycleBinRecoverItem> recoverObjects(
            List<DamRecycleBinRecoverDTO> recycleBinDTOList,
            Integer userId,
            Integer tenantId) {
        if (CollectionUtil.isEmpty(recycleBinDTOList)) {
            return Collections.emptyList();
        }
        final List<Integer> recycleBinIds = recycleBinDTOList.stream()
                .map(DamRecycleBinRecoverDTO::getRecycleBinId)
                .toList();

        final Map<Integer, Integer> targetDirectoryIdMap = recycleBinDTOList.stream()
                .filter(item -> item.getTargetDirectoryId() != null)
                .collect(Collectors.toMap(DamRecycleBinRecoverDTO::getRecycleBinId,
                        DamRecycleBinRecoverDTO::getTargetDirectoryId));

        // 获取所有需要恢复的回收站对象
        final List<DamRecycleBin> recycleBinList = list(new LambdaQueryWrapper<DamRecycleBin>()
                .eq(DamRecycleBin::getTenantId, tenantId)
                .in(DamRecycleBin::getId, recycleBinIds)
                .eq(DamRecycleBin::getUserId, userId)
                .eq(DamRecycleBin::getIsDeleted, false));
        final List<DamRecycleBin> directoryRecycleBins = recycleBinList.stream()
                .filter(recycleBin -> DamRecycleBinObjectTypeEnum.DIRECTORY
                        .equals(recycleBin.getObjectType()))
                .toList();

        final List<DamRecycleBin> assetRecycleBins = recycleBinList.stream()
                .filter(recycleBin -> DamRecycleBinObjectTypeEnum.ASSET
                        .equals(recycleBin.getObjectType()))
                .toList();

        if (directoryRecycleBins.isEmpty() && assetRecycleBins.isEmpty()) {
            return Collections.emptyList();
        }

        final List<DamRecycleBinRecoverItem> result = new ArrayList<>();
        if (!directoryRecycleBins.isEmpty()) {
            result.addAll(recoverDirectories(directoryRecycleBins, userId, tenantId));
        }

        if (!assetRecycleBins.isEmpty()) {
            result.addAll(recoverAssets(assetRecycleBins, targetDirectoryIdMap, userId, tenantId));
        }

        // 补齐未处理的回收站对象
        final Set<Integer> validRecycleBinIds = result.stream()
                .map(DamRecycleBinRecoverItem::getRecycleBinId)
                .collect(Collectors.toSet());

        for (DamRecycleBinRecoverDTO recycleBinDTO : recycleBinDTOList) {
            if (validRecycleBinIds.contains(recycleBinDTO.getRecycleBinId())) {
                continue;
            }
            final DamRecycleBinRecoverItem recoverItem = new DamRecycleBinRecoverItem(recycleBinDTO.getRecycleBinId(), false);
            result.add(recoverItem);
        }

        return result;
    }

    private List<DamRecycleBinRecoverItem> recoverDirectories(List<DamRecycleBin> recycleBins, Integer userId,
                                                              Integer tenantId) {
        final Set<Integer> directoryIds = recycleBins.stream().map(DamRecycleBin::getObjectId)
                .collect(Collectors.toSet());
        final Map<Integer, DamDirectory> directoryMap = CollectionUtil.isNotEmpty(directoryIds) ? directoryService.listByIds(directoryIds)
                .stream().collect(Collectors.toMap(DamDirectory::getId, Function.identity())) : Collections.emptyMap();

        final List<Integer> removedRecycleBinIds = new ArrayList<>();
        for (DamRecycleBin recycleBin : recycleBins) {
            final DamDirectory directory = directoryMap.get(recycleBin.getObjectId());

            directory.setIsDeleted(false);
            directory.setUpdateTime(new Date());

            // 查找需要被还原的素材id
            final List<Integer> assetIds = recycleBinDirectoryAssetMappingService
                    .list(new LambdaQueryWrapper<DamRecycleBinDirectoryAssetMapping>()
                            .eq(DamRecycleBinDirectoryAssetMapping::getRecycleBinId,
                                    recycleBin.getId())
                            .eq(DamRecycleBinDirectoryAssetMapping::getIsDeleted, false))
                    .stream().map(DamRecycleBinDirectoryAssetMapping::getAssetId).toList();

            // 还原素材
            final List<DamAsset> assets = CollectionUtil.isNotEmpty(assetIds) ? assetService.listByIds(assetIds) : Collections.emptyList();
            for (DamAsset asset : assets) {
                asset.setIsDeleted(false);
                asset.setUpdateTime(new Date());
            }
            directoryService.updateById(directory);

            if (!assets.isEmpty()) {
                assetService.updateBatchById(assets);
                // 恢复素材使用更新
                embeddingService.saveOrUpdateByAssets(assets, userId, tenantId, true);
            }
            removedRecycleBinIds.add(recycleBin.getId());
        }

        if (!removedRecycleBinIds.isEmpty()) {
            // 删除回收站对象
            update(
                    new LambdaUpdateWrapper<>(DamRecycleBin.class)
                            .in(DamRecycleBin::getId, removedRecycleBinIds)
                            .eq(DamRecycleBin::getIsDeleted, false)
                            .set(DamRecycleBin::getIsDeleted, true)
                            .set(DamRecycleBin::getRecoverTime, new Date())
            );
        }

        return recycleBins.stream().map(recycleBin -> new DamRecycleBinRecoverItem(recycleBin.getId(), true)).toList();
    }

    private List<DamRecycleBinRecoverItem> recoverAssets(
            List<DamRecycleBin> recycleBins,
            Map<Integer, Integer> targetDirectoryIdMap,
            Integer userId,
            Integer tenantId) {
        final Set<Integer> assetIds = recycleBins.stream().map(DamRecycleBin::getObjectId)
                .collect(Collectors.toSet());
        final List<DamAsset> assets = CollectionUtil.isNotEmpty(assetIds) ? assetService.listByIds(assetIds) : Collections.emptyList();
        final Map<Integer, DamAsset> assetMap = assets.stream()
                .collect(Collectors.toMap(DamAsset::getId, Function.identity()));

        // 获取所有涉及到的目录id
        final Set<Integer> directoryIds = assets.stream().map(DamAsset::getDirectoryId)
                .collect(Collectors.toSet());
        directoryIds.addAll(targetDirectoryIdMap.values());

        // 获取所有涉及到的目录对象
        final Map<Integer, DamDirectory> directoryMap = CollectionUtil.isNotEmpty(directoryIds) ? directoryService.listByIds(directoryIds)
                .stream().collect(Collectors.toMap(DamDirectory::getId, Function.identity())) : Collections.emptyMap();

        final List<DamAsset> recoverAssets = new ArrayList<>();
        final List<Integer> removedRecycleBinIds = new ArrayList<>();
        final List<DamRecycleBinRecoverItem> result = new ArrayList<>();
        for (DamRecycleBin recycleBin : recycleBins) {
            final DamAsset asset = assetMap.get(recycleBin.getObjectId());

            // 查找需要被还原的目录id
            final Integer targetDirectoryId = targetDirectoryIdMap.get(recycleBin.getId());
            if (targetDirectoryId != null) {
                final DamDirectory targetDirectory = directoryMap.get(targetDirectoryId);
                // 如果目标目录已经被删除，则恢复失败
                if (targetDirectory.getIsDeleted()) {
                    log.warn("目标目录已经被删除，无法恢复素材，recycleBinId: {}, targetDirectoryId: {}", recycleBin.getId(), targetDirectoryId);
                    result.add(new DamRecycleBinRecoverItem(recycleBin.getId(), false));
                    continue;
                }

                asset.setDirectoryId(targetDirectoryId);
            } else {
                final DamDirectory sourceDirectory = directoryMap.get(asset.getDirectoryId());
                // 如果源目录已经被删除，则恢复失败
                if (sourceDirectory.getIsDeleted()) {
                    log.warn("源目录已经被删除，无法恢复素材，recycleBinId: {}, sourceDirectoryId: {}", recycleBin.getId(), asset.getDirectoryId());
                    result.add(new DamRecycleBinRecoverItem(recycleBin.getId(), false));
                    continue;
                }
            }

            // 还原素材
            asset.setIsDeleted(false);
            asset.setUpdateTime(new Date());
            recoverAssets.add(asset);

            // 删除回收站对象
            removedRecycleBinIds.add(recycleBin.getId());

            result.add(new DamRecycleBinRecoverItem(recycleBin.getId(), true));
        }

        if (!recoverAssets.isEmpty()) {
            // 还原素材
            assetService.updateBatchById(recoverAssets);

            // 恢复embedding素材使用更新
            embeddingService.saveOrUpdateByAssets(recoverAssets, userId, tenantId, true);
        }

        if (!removedRecycleBinIds.isEmpty()) {
            // 删除回收站对象
            update(
                    new LambdaUpdateWrapper<>(DamRecycleBin.class)
                            .in(DamRecycleBin::getId, removedRecycleBinIds)
                            .eq(DamRecycleBin::getIsDeleted, false)
                            .set(DamRecycleBin::getIsDeleted, true)
                            .set(DamRecycleBin::getRecoverTime, new Date())
            );
        }

        return result;
    }

    @Override
    public boolean deleteObjects(List<Integer> recycleBinIds, Integer userId, Integer tenantId) {
        update(new LambdaUpdateWrapper<DamRecycleBin>()
                .eq(DamRecycleBin::getTenantId, tenantId)
                .eq(DamRecycleBin::getUserId, userId)
                .in(DamRecycleBin::getId, recycleBinIds)
                .eq(DamRecycleBin::getIsDeleted, false)
                .set(DamRecycleBin::getIsDeleted, true));
        return true;
    }

    @Override
    public boolean emptyRecycleBin(Integer userId, Integer tenantId) {
        update(new LambdaUpdateWrapper<DamRecycleBin>()
                .eq(DamRecycleBin::getTenantId, tenantId)
                .eq(DamRecycleBin::getUserId, userId)
                .eq(DamRecycleBin::getIsDeleted, false)
                .set(DamRecycleBin::getIsDeleted, true));
        return true;
    }
}
