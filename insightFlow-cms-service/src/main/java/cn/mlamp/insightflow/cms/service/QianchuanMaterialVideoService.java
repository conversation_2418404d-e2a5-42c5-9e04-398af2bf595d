package cn.mlamp.insightflow.cms.service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.IService;

import cn.mlamp.insightflow.cms.entity.QianchuanMaterialVideo;
import cn.mlamp.insightflow.cms.model.dto.QianchuanMaterialVideoDTO;
import cn.mlamp.insightflow.cms.model.dto.VideoExistCheckRequestDTO;

public interface QianchuanMaterialVideoService extends IService<QianchuanMaterialVideo> {
    Map<String, Object> batchInsertOrUpdate(List<QianchuanMaterialVideoDTO> videoList);

    /**
     * 查询不存在于数据库中的视频ID
     *
     * @param videoIds  要检查的视频ID列表
     * @param startTime 开始时间（可为空）
     * @param endTime   结束时间（可为空）
     * @return 不存在于数据库中的视频ID列表
     */
    List<String> findNonExistingVideoIds(List<String> videoIds, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 检查视频是否已存在
     *
     * @param request 包含视频ID或标题和时长的请求
     * @return 如果视频已存在返回true，否则返回false
     */
    boolean checkVideoExists(VideoExistCheckRequestDTO request);
}
