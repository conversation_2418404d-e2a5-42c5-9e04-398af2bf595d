package cn.mlamp.insightflow.cms.service;

/**
 * 微信企业号webhook服务接口
 *
 * <AUTHOR>
 * @since 2024-10-15
 */
public interface WechatWebhookService {

    /**
     * 发送文本消息到微信企业号
     *
     * @param content 消息内容
     * @return 是否发送成功
     */
    boolean sendTextMessage(String content);

    /**
     * 发送markdown消息到微信企业号
     *
     * @param content markdown内容
     * @return 是否发送成功
     */
    boolean sendMarkdownMessage(String content);
}
