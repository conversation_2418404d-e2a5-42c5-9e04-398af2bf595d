package cn.mlamp.insightflow.cms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 视频ASR表;
 * <AUTHOR> husper
 * @date : 2025-3-19
 */
@Data
@TableName("cms_video_asr")
public class CmsVideoAsr extends BaseEntity{
    /** 主键 自增id */
    @TableId(type = IdType.AUTO)
    private Integer id ;
    /** 帖子Id */
    private String esId ;
    /** 视频id */
    private Integer videoId ;
    /** 开始时间(毫秒) */
    private String start ;
    /** 结束时间(毫秒) */
    private String end ;
    /** 台词 */
    private String text ;


}