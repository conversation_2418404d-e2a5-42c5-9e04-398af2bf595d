package cn.mlamp.insightflow.cms.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("cms_qianchuan_material_video")
public class QianchuanMaterialVideo extends BaseEntity {

    @TableId(type = IdType.AUTO)
    private Integer id;

    private String ossid;

    @TableField("video_id")
    private String videoId;

    @TableField("consume_range")
    private String consumeRange;

    @TableField("consume_range_weight")
    private Integer consumeRangeWeight;

    private String title;
    private String brand;
    private Integer exposure;
    private Integer shares;
    private Integer comments;
    private Integer likes;
    private Integer clicks;
    private String industry;
    private String kwVideoContent; // 视频语音识别

    @TableField("ranking_type")
    private String rankingType;

    @TableField("publish_time")
    private LocalDateTime publishTime;

    @TableField("author_name")
    private String authorName;

    @TableField("author_avatar")
    private String authorAvatar;

    @TableField("cover_image")
    private String coverImage;

    private Integer duration;

    @TableField("completion_rate")
    private BigDecimal completionRate;

    @TableLogic
    @TableField("is_deleted")
    private Integer isDeleted;

    private Float rating; // 创意分

    //产品名称
    private String productName;

    //卖点
    private String cellingPoint;

    //受众人群
    private String aimingTribe;

    //是否高光0：没有1：高光
    private Integer highlight;

    //0:待分析；1：分析中，2：分析成功，3：分析失败
    private Integer analysisStatus;
}
