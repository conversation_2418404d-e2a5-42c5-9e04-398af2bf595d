package cn.mlamp.insightflow.cms.auth.tcc.model;

import lombok.Data;

import java.util.Collections;
import java.util.List;

@Data
public class User {

    /**
     * 用户id
     */
    private Integer id;

    /**
     * 用户名称
     */
    private String name;

    /**
     * 用户邮箱
     */
    private String email;

    /**
     * 用户手机号
     */
    private String phoneNumber;

    /**
     * 当前租户Id
     */
    private Integer currentTenantId;

    /**
     * 当前租户权限列表
     */
    private List<String> permissions = Collections.emptyList();

    /**
     * 用户关联的租户列表
     */
    private List<Tenant> tenants;

}
