package cn.mlamp.insightflow.cms.model.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.util.Date;

/**
 * 消费记录导出DTO
 */
@Data
public class ConsumptionExportDTO {

    @ColumnWidth(30)
    @ExcelProperty("文件名")
    private String taskName;

    @ColumnWidth(20)
    @ExcelProperty("任务类型")
    private String taskType;

    @ColumnWidth(25)
    @ExcelProperty("创建时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date usageTime;

    @ColumnWidth(15)
    @ExcelProperty("任务消耗")
    private Integer tokens;

    @ColumnWidth(20)
    @ExcelProperty("创建人")
    private String userName;
}
