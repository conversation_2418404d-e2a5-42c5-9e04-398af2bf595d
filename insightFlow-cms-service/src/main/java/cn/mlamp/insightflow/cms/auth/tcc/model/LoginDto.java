package cn.mlamp.insightflow.cms.auth.tcc.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

//import javax.validation.constraints.NotBlank;
//import javax.validation.constraints.Pattern;

@Data
public class LoginDto {
    //    @NotBlank(message = "请输入用户名")
    //    @Pattern(regexp="^[A-Za-z0-9]+([_\\.][A-Za-z0-9]+)*@([A-Za-z0-9\\-]+\\.)+[A-Za-z]{2,6}$", message = "请输入正确的邮箱")
    @Schema(description = "用户账号", required = true)
    private String username;

    //    @NotBlank(message = "请输入密码")
    @Schema(description = "用户密码", required = true)
    private String password;
}
