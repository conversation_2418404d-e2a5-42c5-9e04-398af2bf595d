package cn.mlamp.insightflow.cms.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @Author: husuper
 * @CreateTime: 2025-04-07
 */
@Component
public class TaskConfig {

    @Value("${spring.profiles.active}")
    private String activeProfiles;


    public boolean isLocal() {
        return "local".equals(activeProfiles);
    }



}
