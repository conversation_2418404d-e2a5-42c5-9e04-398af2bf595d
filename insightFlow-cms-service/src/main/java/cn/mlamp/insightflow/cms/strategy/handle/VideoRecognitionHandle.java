package cn.mlamp.insightflow.cms.strategy.handle;

import cn.mlamp.insightflow.cms.config.AnalysisVideoConfig;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import java.util.List;
import java.util.Map;

/**
 * @Author: husuper
 * @CreateTime: 2025-03-25
 */
@Service
@Slf4j
public class VideoRecognitionHandle {


    @Autowired
    private  RestTemplate restTemplate;

    @Autowired
    private AnalysisVideoConfig analysisVideoConfig;

    public static void main(String[] args) {
        VideoRecognitionHandle videoRecognitionHandle = new VideoRecognitionHandle();
        videoRecognitionHandle.restTemplate = new RestTemplate();
        videoRecognitionHandle.analysisVideoConfig=new AnalysisVideoConfig();
        videoRecognitionHandle.analysisVideoConfig.setAnalyzeUrl("http://10.10.100.228:8354");

        RecognitionArg recognitionArg= new RecognitionArg();

//        recognitionArg.setVideo_url("https://mos-ex.intra.mlamp.cn/ai-pc-cms/video-decode/videos/douyin/20250327/1213.mp4");
//        recognitionArg.setVideo_url("https://mos-ex.intra.mlamp.cn/ai-pc-cms/video-decode/videos/douyin/20250326/4356547586t3434976gtretfdgfd.mp4");
//        recognitionArg.setVideo_url("https://mos-ex.intra.mlamp.cn/ai-pc-cms/video-decode/videos/douyin/20250326/4356547586976gfdgfd.mp4");
        recognitionArg.setVideo_url("https://mos-ex.intra.mlamp.cn/ai-pc-cms/video-decode/videos/douyin/20250323/31234ewwerwerr32ewr413.mp4");
        recognitionArg.setTitle("测试视频");
        recognitionArg.setContent("测试视频");
        recognitionArg.setIndustry("美妆");
        recognitionArg.setIndustry_prompt("美妆");
//        ProcessVideo processVideo = videoRecognitionHandle.processVideo(recognitionArg, "observationId");
        //5807 5813
        VideoContent videoContent = videoRecognitionHandle.queryVideo("5817", "observationId");
//        System.out.println(videoContent);
        ResponseBody responseBody = JSONObject.parseObject(videoContent.getData().getResponse_body(),ResponseBody.class);
        System.out.println(JSONObject.toJSONString(responseBody));

    }

    public  ProcessVideo processVideo(RecognitionArg  recognitionArg, String observationId) {
        String url = analysisVideoConfig.getAnalyzeUrl()+"/video_flow";

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("observation-id", observationId);
        log.info("调用视频整体分析服务请求{}",recognitionArg);
        HttpEntity<RecognitionArg> requestEntity = new HttpEntity<>(recognitionArg, headers);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url,requestEntity,String.class);
        log.info("调用视频整体分析服务返回{}",responseEntity.getBody());
        ProcessVideo processVideo = JSONObject.parseObject(responseEntity.getBody(), ProcessVideo.class);
        return processVideo;
    }



    public  VideoContent queryVideo(String  db_unique_id, String observationId) {
        String url = analysisVideoConfig.getAnalyzeUrl()+"/unique_id/"+db_unique_id;

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("observation-id", observationId);
        log.info("调用视频分析结果查询请求{}",url);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        log.info("调用视频分析结果查询返回{}",responseEntity.getBody());
        VideoContent videoContent = JSONObject.parseObject(responseEntity.getBody(), VideoContent.class);
        return videoContent;

    }


    @Data
    public static class  RecognitionArg{

        //视频URL
        private String video_url;

        //标题
        private String title;

        //内容
        private String content;

        //行业
        private String industry;

        //可减少的分析维度
        private String industry_prompt;


    }


    @Data
    public static class ProcessVideo {
        // 消息内容
        private String message;

        // 响应代码
        private int code;

        private ProcessVideoData data;

    }

    @lombok.Data
    public static class ProcessVideoData {
        private String db_unique_id;
    }


    @Data
    public static class VideoContent {
        // 消息内容
        private String message;

        // 响应代码
        private int code;

        // 数据对象
        private VideoContentData data;

    }

    @Data
    public static class VideoContentData{
        //Pending, Processing, Finished, Error
        private String status;

        private String error_msg;

        private String response_body;

    }


    @lombok.Data
    public static class ResponseBody {
        // ASR识别结果
        private ASR asr;

        // 四有三好打分
        private String rating;

        // 行业解码信息
        private Map<String,String> industry_decoding;

        // 分镜识别信息
        private List<Map<String,Object>> scene_split;

    }

    @lombok.Data
    public static class ASR {
        // 视频台词完整拼接
        private String text;

        // 带时间戳的句子
        private List<Sentences> sentences;

    }

    @lombok.Data
    public static class Sentences {
        // 台词
        private String text;

        // 开始时间(毫秒)
        private String start;

        // 结束时间(毫秒)
        private String end;

    }


}
