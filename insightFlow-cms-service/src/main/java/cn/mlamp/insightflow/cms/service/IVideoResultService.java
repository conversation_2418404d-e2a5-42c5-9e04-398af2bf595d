package cn.mlamp.insightflow.cms.service;

import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.entity.CmsVideoResult;
import cn.mlamp.insightflow.cms.model.query.VideoResultRequest;
import cn.mlamp.insightflow.cms.model.vo.VideoResultVO;
import com.baomidou.mybatisplus.extension.service.IService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * @Author: husuper
 * @CreateTime: 2025-03-21
 */
public interface IVideoResultService extends IService<CmsVideoResult> {


    List<String> getVideoResult(List<Integer> resultIds);

    List<CmsVideoResult> getSceneSplitResultByVideoId(Integer videoId);

    /**
     * 查询视频的ASR 5秒结果
     *
     * @param videoIds 视频ID列表
     * @return 以视频esId为键，ASR 5秒结果为值的Map
     */
    Map<String, String> getVideoAsr5Results(List<Integer> videoIds);


    Boolean update(List<VideoResultRequest> request);

    Boolean back(Integer videoId);

    void exportReusltDetail(Integer videoId, HttpServletResponse response);



}
