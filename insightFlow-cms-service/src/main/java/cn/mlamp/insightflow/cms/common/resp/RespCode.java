package cn.mlamp.insightflow.cms.common.resp;

/**
 * 公共响应码
 */
public enum RespCode implements ResultCode {
    /**
     * Default OK
     */
    OK(200, "OK", "请求成功"),
    /**
     * Default Fail
     */
    FAIL(330, "FAIL", "请求失败"),
    /**
     * BAD_REQUEST
     */
    BAD_REQUEST(400, "Bad Request", "参数无效"),
    /**
     * UNAUTHORIZED
     */
    UNAUTHORIZED(401, "Unauthorized", "未经授权的访问,由于凭据无效被拒绝"),
    /**
     *
     */
    AUTHENTICATION_FAILED(401, "Authentication Failed", "用户信息认证失败"),
    /**
     * FORBIDDEN
     */
    FORBIDDEN(403, "Forbidden", "请求资源的访问被服务器拒绝"),

    /**
     * Visit Frequently
     */
    VISIT_FREQUENTLY(403, "Visit Frequently", "请求过于频繁，请稍后重试"),
    /**
     * NOT_FOUND
     */
    NOT_FOUND(404, "Not Found", "资源未找到"),
    /**
     * METHOD_NOT_ALLOWED
     */
    METHOD_NOT_ALLOWED(405, "Method Not Allowed", "请求的HTTP方法不允许"),
    /**
     * REQUEST_TIMEOUT
     */
    REQUEST_TIMEOUT(408, "Request Timeout", "请求超时"),
    /**
     * URI_TOO_LONG
     */
    URI_TOO_LONG(414, "URI Too Long", "请求的URL地址长度超限"),

    /**
     * UNSUPPORTED_MEDIA_TYPE
     */
    UNSUPPORTED_MEDIA_TYPE(415, "Unsupported Media Type", "不支持的媒体类型(Content-Type 或 Content-Encoding)"),
    /**
     * TOO_MANY_REQUESTS
     */
    TOO_MANY_REQUESTS(429, "Too Many Requests", "该客户端请求频率过高,请稍后重试"),
    /**
     * MISSING_REQ_HEADER
     */
    MISSING_REQ_HEADER(445, "Missing Request Header", "缺失必要的请求头(Headers)"),
    /**
     * INTERNAL_SERVER_ERROR
     */
    INTERNAL_SERVER_ERROR(500, "Internal Server Error", "服务器内部系统未知异常"),
    /**
     * BAD_GATEWAY
     */
    BAD_GATEWAY(502, "Bad Gateway", "网关异常"),
    /**
     * GATEWAY_TIMEOUT
     */
    GATEWAY_TIMEOUT(504, "Gateway Timeout", "网关超时"),
    /**
     * CONNECT_EXCEPTION
     */
    CONNECT_EXCEPTION(530, "Service Connect Exception", "服务连接异常"),
    /**
     * NULL_POINTER_EXCEPTION
     */
    NULL_POINTER_EXCEPTION(550, "Null Pointer Exception", "服务器内部空指针异常"),
    /**
     * DATABASE_EXCEPTION
     */
    DATABASE_EXCEPTION(551, "Database Exception", "服务器内部数据库发生异常"),
    /**
     * SQL_EXCEPTION
     */
    SQL_EXCEPTION(552, "Sql Exception", "服务器内部数据库SQL执行异常"),

    /**
     * SQL_EXCEPTION
     */
    TRANSFER_EXCEPTION(553, "ParamsType Exception", "类型转换异常"),

    /**
     * SQL_EXCEPTION
     */
    THREAD_BREAK_EXCEPTION(554, "ThreadBreak Exception", "线程阻塞中断异常"),

    /**
     * SQL_EXCEPTION
     */
    THREAD_DATA_EXCEPTION(555, "ThreadData Exception", "线程池获取数据异常"),

    /**
     * 未激活用户异常<br/>
     * 需输入内测码激活
     */
    INACTIVE_USER_EXCEPTION(601, "Inactive User Exception", "用户未激活"),
    /**
     * NOT_HAVE_EMPLOYEE_INFO_EXCEPTION
     */
    NOT_HAVE_EMPLOYEE(602, "Business Exception", "当前操作人没有管辖的员工"),
    /**
     * NOT_HAVE_EMPLOYEE_INFO_EXCEPTION
     */
    NO_PAGE_PARAM(603, "Business Exception", "没有分页参数"),




    CONTENT_AUDIT_FAILED(422, "Content Audit Failed",
            "内容涉及政治、色情、暴力等敏感内容"),

    BUSINESS_NO_POINTS(70100, "No points business", "没有可用的点数或者点数不足"),

    BOT_NOT_AUTH(43100, "No permission to access the robot！！", "用户无权限，请联系管理员"),

    /**
     * 弹窗提示错误信息
     */
    USER__OPERATION_EXCEPTION(600, "User operation exception", "用户操作异常"),

    ACCOUNT_OR_PASSWORD_ERROR(601, "Account or password error", "账号或密码错误"),


    LIMIT_MSG_EXCEPTION(43200,"限流异常","已达到限流");
    /**
     * 自定义 返回码
     */
    private final Integer code;
    /**
     * 返回码 描述
     */
    private final String desc;
    /**
     * 返回码提示说明
     */
    private final String message;

    RespCode(Integer code, String desc, String message) {
        this.code = code;
        this.desc = desc;
        this.message = message;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public String getMessage() {
        return message;
    }
}

