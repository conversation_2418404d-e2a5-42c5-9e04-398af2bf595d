package cn.mlamp.insightflow.cms.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class DeepanaDySkuResponseDTO {
    private String title;
    private List<SkyTag> tags;

    @JsonProperty("shop_name")
    private String shopName;

    @Data
    public static class SkyTag {
        private List<SkuTagMessage> message;
        private String name;
    }

    @Data
    public static class SkuTagMessage {
        String desc;
    }
}
