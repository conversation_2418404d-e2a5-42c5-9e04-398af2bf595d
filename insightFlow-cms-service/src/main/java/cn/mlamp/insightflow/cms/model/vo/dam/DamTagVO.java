package cn.mlamp.insightflow.cms.model.vo.dam;

import cn.mlamp.insightflow.cms.entity.dam.DamTag;
import cn.mlamp.insightflow.cms.enums.dam.DamTagTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * DAM标签VO
 */
@Data
@Schema(description = "DAM标签VO")
public class DamTagVO {
    
    @Schema(description = "标签ID")
    private Integer id;
    
    @Schema(description = "标签名称")
    private String name;
    
    @Schema(description = "标签类型：1-公共标签, 2-自定义标签")
    private DamTagTypeEnum type;
    
    @Schema(description = "标签描述")
    private String description;
    
    @Schema(description = "标签示例")
    private String example;
    
    @Schema(description = "标签应用频次")
    private Integer usedNum;
    
    @Schema(description = "推荐标签值列表")
    private List<String> values;
    
    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;

    public static DamTagVO from(DamTag tag) {
        DamTagVO tagVO = new DamTagVO();
        tagVO.setId(tag.getId());
        tagVO.setName(tag.getName());
        tagVO.setType(tag.getType());
        tagVO.setDescription(tag.getDescription());
        tagVO.setExample(tag.getExample());
        tagVO.setCreateTime(tag.getCreateTime());
        tagVO.setUpdateTime(tag.getUpdateTime());
        return tagVO;
    }
} 