package cn.mlamp.insightflow.cms.service.dam;

import cn.mlamp.insightflow.cms.entity.dam.DamAsset;
import cn.mlamp.insightflow.cms.entity.dam.DamAssetEmbedding;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

public interface IDamAssetEmbeddingService extends IService<DamAssetEmbedding> {

    void saveByAssetAndEmbeddingContent(DamAsset asset, String content, Map<String, Object> metadata);

    // 不允许修改分辨率aspectRatio，content和embedding
    // metadata未填就不修改
    void updateByAsset(DamAsset asset, Map<String, Object> metadata);

    void moveByAsserts(List<DamAsset> assets);

    void copyByAsserts(List<Integer> oldIds, List<DamAsset> assets);

    void saveOrUpdateByAssets(List<DamAsset> assets, Integer userId, Integer tenantId, boolean isUpdate);

    Page<DamAssetEmbedding> pageEmbedding(Integer current, Integer pageSize, String aspectRatio, String content,
                                          Integer tenantId, Integer userId, List<Integer> directoryIds);

    List<DamAssetEmbedding> topKEmbedding(String content, Integer limit, Float threshold, String aspectRatio,
                                          Integer tenantId, Integer userId, List<Integer> directoryIds);

    List<DamAssetEmbedding> topKEmbedding(Float[] embedding, Integer limit, Float threshold,
                                          String aspectRatio,
                                          Integer tenantId, Integer userId, List<Integer> directoryIds);

    int updateEmbedding(Integer id, String content);

    int deleteEmbedding(Integer id);

    void restoreEmbedding(List<Integer> ids);
}
