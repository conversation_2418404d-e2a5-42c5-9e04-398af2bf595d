package cn.mlamp.insightflow.cms.entity.dam;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * DAM素材表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
@Getter
@Setter
@TableName("cms_asset")
@Schema(name = "CmsAsset", description = "DAM素材表")
public class DamAsset implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @Schema(description = "租户ID（租户素材）")
    @TableField("tenant_id")
    private Integer tenantId;

    @Schema(description = "上传用户ID")
    @TableField("user_id")
    private Integer userId;

    @Schema(description = "所属目录ID")
    @TableField("directory_id")
    private Integer directoryId;

    @Schema(description = "素材名称")
    @TableField("name")
    private String name;

    @Schema(description = "视频时长（秒）")
    @TableField("duration")
    private Integer duration;

    @Schema(description = "缩略图URL")
    @TableField("thumbnail_oss_id")
    private String thumbnailOssId;

    @Schema(description = "拓展名")
    @TableField("ext")
    private String ext;

    @Schema(description = "画面比率 16:9、9:16、1:1")
    @TableField("aspect_ratio")
    private String aspectRatio;

    @Schema(description = "oss 存储 objId")
    @TableField("oss_id")
    private String ossId;

    @Schema(description = "入库时间")
    @TableField("storage_time")
    private Date storageTime;

    @Schema(description = "是否入库，0-未入库，1-入库")
    @TableField("is_stored")
    private Boolean isStored = false;

    @Schema(description = "应用频次")
    @TableField("used_num")
    private Integer usedNum = 0;

    @Schema(description = "创建时间")
    @TableField("create_time")
    private Date createTime;

    @Schema(description = "更新时间")
    @TableField("update_time")
    private Date updateTime;

    @Schema(description = "逻辑删除：0-未删除，1-已删除")
    @TableField("is_deleted")
    private Boolean isDeleted = false;

    @TableField(exist = false) // 不必映射的字段
    private String createBy;
}
