package cn.mlamp.insightflow.cms.controller;

import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.service.IGoldFiveSecondTaskService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 黄金五秒任务控制器
 * <AUTHOR> yang<PERSON>bo
 * @date : 2025-5-15
 */
@Slf4j
@RestController
@RequestMapping("/gold/five/second/task")
@RequiredArgsConstructor
@Tag(name = "黄金五秒任务接口")
public class GoldFiveSecondTaskController {

    private final IGoldFiveSecondTaskService goldFiveSecondTaskService;

    @GetMapping("/trigger")
    @Operation(summary = "手动触发黄金五秒任务")
    public RespBody<?> triggerTask(@RequestParam(value = "dateStr", required = false) String dateStr,
            @RequestParam(value = "industry", required = false) String industry,
            @RequestParam(value = "tag", required = false) String tag) {
        log.info("手动触发黄金五秒任务，日期：{}，行业：{}，标签：{}", dateStr, industry, tag);
        goldFiveSecondTaskService.manualTriggerTask(dateStr, industry, tag);
        return RespBody.ok();
    }
}
