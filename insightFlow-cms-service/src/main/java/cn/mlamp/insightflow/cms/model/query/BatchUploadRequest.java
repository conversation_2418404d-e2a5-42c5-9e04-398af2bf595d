package cn.mlamp.insightflow.cms.model.query;

import cn.mlamp.insightflow.cms.enums.UploadSourceTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class BatchUploadRequest implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "上传本地文件时必填", required = false)
    private List<UploadDocInfo> docInfos = new ArrayList<>();

    @Schema(description = "上传来源类型", required = true)
    private UploadSourceTypeEnum sourceType;

    private Integer userId;
    private Integer tenantId;

    @Data
    public static class UploadDocInfo {
        @Schema(description = "文件在oss中的id(本地上传时必填)", required = false)
        private String objOssId;
        @Schema(description = "视频链接地址(上传链接时必填)", required = false)
        private String url;
        @Schema(description = "文件名（本地上传需要带扩展名）", required = true)
        private String name;
        @Schema(description = "文件大小（本地上传时必传）", required = false)
        private Long size;
    }
}
