package cn.mlamp.insightflow.cms.auth.tcc.model;

public class Constants {

    public static final Integer INT_ZERO = 0;
    public static final Integer INT_ONE = 1;
    public static final Integer OK_CODE = 0;
    /**
     * ttc租户管理员的名称
     */
    public static final String TTC_TENANT_ADMIN_NAME = "租户管理员";
    /**
     * 统一多租户中心的租户下用户状态 1:激活
     */
    public static final Integer TTC_TENANT_USER_ACTIVE_STATUS = 1;

    /**
     * 当前用户的ticket的名称
     */
    public static final String REQUEST_ATTR_TICKET_NAME = "ticket";

    /**
     * 当前用户的名称
     */
    public static final String REQUEST_ATTR_USER_NAME = "user";

    /**
     * 用户在ttc配置行业资源的类型key
     */
    public static final String INDUSTRY_RESOURCE_TYPE_KEY = "cvb-Industry";

    /**
     * 用户在ttc配置广告主资源的类型key
     */
    public static final String ADVERTISER_RESOURCE_TYPE_KEY = "cvb-advertiser";

}
