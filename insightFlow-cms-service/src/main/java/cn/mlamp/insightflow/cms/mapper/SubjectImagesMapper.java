package cn.mlamp.insightflow.cms.mapper;

import cn.mlamp.insightflow.cms.entity.CmsSubjectImages;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface SubjectImagesMapper extends BaseMapper<CmsSubjectImages> {

    /**
     * 查询主体图片总数
     */
    @Select("SELECT COUNT(id) FROM cms_subject_images")
    Integer countAllSubjectImages();


}
