package cn.mlamp.insightflow.cms.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class LinkFileVO {
    @Schema(description = "视频url", required = true)
    private String url;
    @Schema(description = "错误信息", required = false)
    private String errorMsg;
    @Schema(description = "视频平台icon", required = false)
    private String iconKey;
    @Schema(description = "视频标题", required = false)
    private String title;
}
