package cn.mlamp.insightflow.cms.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.mlamp.insightflow.cms.config.UserContext;
import cn.mlamp.insightflow.cms.entity.CmsPullTaskDedupedData;
import cn.mlamp.insightflow.cms.entity.CmsPullTaskRawData;
import cn.mlamp.insightflow.cms.enums.AnalysisStatusEnum;
import cn.mlamp.insightflow.cms.enums.DownloadStatusEnum;
import cn.mlamp.insightflow.cms.mapper.CmsPullTaskDedupedDataMapper;
import cn.mlamp.insightflow.cms.model.query.RecommenListRequest;
import cn.mlamp.insightflow.cms.model.query.VideoHotspotQueryRequest;
import cn.mlamp.insightflow.cms.service.CmsPullTaskDedupedDataService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.StringUtils;
// import org.springframework.ai.document.Document;
// import org.springframework.ai.vectorstore.SearchRequest;
// import org.springframework.ai.vectorstore.filter.FilterExpressionBuilder;
// import org.springframework.ai.vectorstore.pgvector.PgVectorStore;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class CmsPullTaskDedupedDataServiceImpl extends ServiceImpl<CmsPullTaskDedupedDataMapper, CmsPullTaskDedupedData>
        implements CmsPullTaskDedupedDataService {

    // @Autowired
    // @Qualifier("userNicknameVectorStore")
    // private PgVectorStore userNicknameVectorStore;

    // @Autowired
    // @Qualifier("textContentVectorStore")
    // private PgVectorStore textContentVectorStore;

    // @Autowired
    // @Qualifier("videoContentVectorStore")
    // private PgVectorStore videoContentVectorStore;

    // @Autowired
    // @Qualifier("combinedDataVectorStore")
    // private PgVectorStore combinedDataVectorStore;

    public static final Map<String, String> TRIBE_MAP;

    static {
        TRIBE_MAP = new HashMap<>();
        TRIBE_MAP.put("Tribe-1.1", "美妆护肤圈");
        TRIBE_MAP.put("Tribe-1.2", "美发圈");
        TRIBE_MAP.put("Tribe-1.3", "时尚穿搭圈");
        TRIBE_MAP.put("Tribe-1.4", "个性风格圈");
        TRIBE_MAP.put("Tribe-1.5", "生活态度圈");
        TRIBE_MAP.put("Tribe-1.6", "日常分享圈");
        TRIBE_MAP.put("Tribe-1.7", "手工手作圈");
        TRIBE_MAP.put("Tribe-1.8", "萌宠圈");
        TRIBE_MAP.put("Tribe-1.9", "园艺植物圈");
        TRIBE_MAP.put("Tribe-1.10", "户外探索圈");
        TRIBE_MAP.put("Tribe-1.11", "家居圈");
        TRIBE_MAP.put("Tribe-1.12", "美食圈");
        TRIBE_MAP.put("Tribe-2.1", "动漫圈");
        TRIBE_MAP.put("Tribe-2.2", "衍生内容圈");
        TRIBE_MAP.put("Tribe-2.3", "网文小说圈");
        TRIBE_MAP.put("Tribe-2.4", "模型玩具圈");
        TRIBE_MAP.put("Tribe-3.1", "游戏设备圈");
        TRIBE_MAP.put("Tribe-3.2", "游戏类型圈");
        TRIBE_MAP.put("Tribe-4.1", "公益圈");
        TRIBE_MAP.put("Tribe-5.1", "数码科技圈");
        TRIBE_MAP.put("Tribe-6.1", "极限运动圈");
        TRIBE_MAP.put("Tribe-6.2", "街头运动圈");
        TRIBE_MAP.put("Tribe-6.3", "户外运动圈");
        TRIBE_MAP.put("Tribe-6.4", "健身圈");
        TRIBE_MAP.put("Tribe-6.5", "传统运动圈");
        TRIBE_MAP.put("Tribe-7.1", "小众音乐圈");
        TRIBE_MAP.put("Tribe-7.2", "嘻哈文化圈");
        TRIBE_MAP.put("Tribe-7.3", "民族音乐圈");
        TRIBE_MAP.put("Tribe-7.4", "乐器圈");
        TRIBE_MAP.put("Tribe-8.1", "戏剧艺术圈");
        TRIBE_MAP.put("Tribe-8.2", "当代艺术圈");
        TRIBE_MAP.put("Tribe-8.3", "书画舞蹈圈");
    }

    @Override
    public List<CmsPullTaskDedupedData> getRecentDedupedData(Date startDate, Date endDate, Integer type) {
        return this.lambdaQuery()
                .ge(CmsPullTaskDedupedData::getDatePublishedAt, startDate) // 开始时间
                .le(CmsPullTaskDedupedData::getDatePublishedAt, endDate) // 结束时间
                .eq(CmsPullTaskDedupedData::getType, type) // 类型
                .list();
    }

    @Override
    public void saveDedupedDatas(List<CmsPullTaskRawData> finalData) {
        if (CollectionUtil.isEmpty(finalData)) {
            return;
        }

        List<CmsPullTaskDedupedData> dedupedDataList = finalData.stream().map(data -> {
            CmsPullTaskDedupedData dedupedData = new CmsPullTaskDedupedData();
            dedupedData.setTaskId(data.getTaskId());
            dedupedData.setKwKbIndustry(data.getKwKbIndustry());
            dedupedData.setEsId(data.getEsId());
            dedupedData.setSourceType(1);
            dedupedData.setKwSource(data.getKwSource());
            dedupedData.setType(data.getType());
            dedupedData.setKwTwoLevelTribeTag(data.getKwTwoLevelTribeTag());
            dedupedData.setTextContent(data.getTextContent());
            dedupedData.setTextTitle(data.getTextTitle());
            dedupedData.setKwUrl(data.getKwUrl());
            dedupedData.setDatePublishedAt(data.getDatePublishedAt());
            dedupedData.setLongInteractCount(data.getLongInteractCount());
            dedupedData.setLongRepostCount(data.getLongRepostCount());
            dedupedData.setLongCommentCount(data.getLongCommentCount());
            dedupedData.setLongLikeCount(data.getLongLikeCount());
            dedupedData.setBoolIsDeleted(data.getBoolIsDeleted());
            dedupedData.setTextNickName(data.getTextNickName());
            dedupedData.setKwProfileImageUrl(data.getKwProfileImageUrl());
            dedupedData.setKwUserUrl(data.getKwUserUrl());
            dedupedData.setLongVideoDuration(data.getLongVideoDuration());
            dedupedData.setKwHeadImage(data.getKwHeadImage());
            dedupedData.setKwVideoUrl(data.getKwVideoUrl());
            dedupedData.setKwVideoContent(data.getKwVideoContent());
            dedupedData.setKwCommonSentimentPlus(data.getKwCommonSentimentPlus());
            dedupedData.setKwDataTagPlus(data.getKwDataTagPlus());
            dedupedData.setLongViewCount(data.getLongViewCount());
            dedupedData.setLongFollowerCount(data.getLongFollowerCount());
            dedupedData.setLongCollectCount(data.getLongCollectCount());
            return dedupedData;
        }).collect(Collectors.toList());

        // 批量插入
        this.saveBatch(dedupedDataList);
    }

    @Override
    public IPage<CmsPullTaskDedupedData> getVideoHotspotList(VideoHotspotQueryRequest request) {
        // 构造查询条件
        LambdaQueryWrapper<CmsPullTaskDedupedData> queryWrapper = new LambdaQueryWrapper<>();

        long startTime = 0;
        long endTime = 0;
        // 根据开始时间筛选
        if (request.getStartTime() != null) {
            queryWrapper.ge(CmsPullTaskDedupedData::getDatePublishedAt, request.getStartTime());
            startTime = request.getStartTime().getTime();
        }

        // 根据结束时间筛选
        if (request.getEndTime() != null) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(request.getEndTime());
            calendar.set(Calendar.HOUR_OF_DAY, 23);
            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.SECOND, 59);
            calendar.set(Calendar.MILLISECOND, 999);

            queryWrapper.le(CmsPullTaskDedupedData::getDatePublishedAt, calendar);
            endTime = calendar.getTime().getTime();
        }

        // 默认7天内的数据，如果开始时间和结束时间都为空
        if (request.getStartTime() == null && request.getEndTime() == null) {
            LocalDateTime sevenDaysAgo = LocalDateTime.now().minusDays(8);
            queryWrapper.ge(CmsPullTaskDedupedData::getDatePublishedAt, sevenDaysAgo);
            // 记录时间戳
            startTime = sevenDaysAgo.toInstant(ZoneOffset.UTC).toEpochMilli();
            endTime = System.currentTimeMillis(); // 结束时间戳（当前时间）
        }

        // 根据关键词筛选，text_content字段模糊匹配
        if (StringUtils.isNotBlank(request.getKeyword())) {
            // // 根据 type 进行筛选，类型 1, 2, 0 对应不同的向量库
            // PgVectorStore vectorStore = null;

            // // 根据 type 动态注入对应的向量库
            // if ("0".equals(request.getSelectType())) {
            // vectorStore = combinedDataVectorStore;
            // } else if ("1".equals(request.getSelectType())) {
            // vectorStore = userNicknameVectorStore;
            // } else if ("2".equals(request.getSelectType())) {
            // vectorStore = textContentVectorStore;
            // } else if ("3".equals(request.getSelectType())) {
            // vectorStore = videoContentVectorStore;
            // }

            // // 如果没有正确的向量库，抛出异常或返回空
            // if (vectorStore == null) {
            // throw new IllegalArgumentException("Invalid type: " +
            // request.getSelectType());
            // }
            // // 创建 FilterExpressionBuilder，用于构造 filterExpression
            // FilterExpressionBuilder b = new FilterExpressionBuilder();

            // // 默认 topK 设置为 10，如果是 videoContentVectorStore，则将 topK 设置为 100
            // int topK = ("3".equals(request.getSelectType()) ||
            // "0".equals(request.getSelectType())) ? 100 : 30;

            // // 使用 vectorStore 进行相似度搜索
            // SearchRequest searchRequest =
            // SearchRequest.builder().query(request.getKeyword()) // 使用传入的关键词
            // .topK(topK).similarityThreshold(0.8) // 设定相似度阈值
            // // .filterExpression(
            // // b.and(b.gte("datePublishedAt", startTime), b.lte("datePublishedAt",
            // // endTime)).build())
            // .build();
            // // 查询向量库
            // List<Document> documents = vectorStore.similaritySearch(searchRequest);
            // // 提取 documents 中的 es_id 列表
            // // 提取 documents 中 metadata 字段中的 "id" 值
            // List<String> esIds = documents.stream().map(document -> (String)
            // document.getMetadata().get("id"))
            // .collect(Collectors.toList());
            // // 创建一个 Map，将 esId 和其在 esIds 中的位置映射
            // Map<String, Integer> esIdIndexMap = new HashMap<>();
            // for (int i = 0; i < esIds.size(); i++) {
            // esIdIndexMap.put(esIds.get(i), i);
            // }
            // // 然后根据 es_id 列表进行查询
            // if (!esIds.isEmpty()) {
            // queryWrapper.in(CmsPullTaskDedupedData::getEsId, esIds);
            // } else {
            // Page<CmsPullTaskDedupedData> emptyPage = new Page<>(request.getPage(),
            // request.getSize());
            // emptyPage.setTotal(0); // 设置总数为 0
            // emptyPage.setRecords(Collections.emptyList()); // 设置空记录
            // return emptyPage;
            // }

            if ("0".equals(request.getSelectType())) {
                queryWrapper.and(wrapper -> wrapper.like(CmsPullTaskDedupedData::getTextNickName, request.getKeyword())
                        .or().like(CmsPullTaskDedupedData::getTextContent, request.getKeyword()).or()
                        .like(CmsPullTaskDedupedData::getKwVideoContent, request.getKeyword()));
            } else if ("1".equals(request.getSelectType())) {
                queryWrapper.like(CmsPullTaskDedupedData::getTextNickName, request.getKeyword());
            } else if ("2".equals(request.getSelectType())) {
                queryWrapper.like(CmsPullTaskDedupedData::getTextContent, request.getKeyword());
            } else if ("3".equals(request.getSelectType())) {
                queryWrapper.like(CmsPullTaskDedupedData::getKwVideoContent, request.getKeyword());
            }


        }

        // 根据类型筛选（1:视频热点，2:圈层热点，3:上传视频）
        queryWrapper.eq(CmsPullTaskDedupedData::getType, request.getType());

        if (request.getType() == 3) {
            queryWrapper.eq(CmsPullTaskDedupedData::getTenantId, UserContext.getTenantId());
        }

        // 根据品类筛选
        if (StringUtils.isNotBlank(request.getKwKbIndustry())) {
            queryWrapper.like(CmsPullTaskDedupedData::getKwKbIndustry, request.getKwKbIndustry());
        }

        // 根据圈层筛选
        if (StringUtils.isNotBlank(request.getKwTwoLevelTribeTag())) {
            queryWrapper.like(CmsPullTaskDedupedData::getKwTwoLevelTribeTag, request.getKwTwoLevelTribeTag());
        }

        // 根据视频name模糊筛选
        if (StringUtils.isNotBlank(request.getVideoName())) {
            queryWrapper.like(CmsPullTaskDedupedData::getTextContent, request.getVideoName());
        }

        // 排序条件：根据互动数、点赞数、评论数、创意分值进行排序
        if ("interactCount".equals(request.getSortBy())) {
            queryWrapper.orderBy(true, "asc".equals(request.getSortOrder()),
                    CmsPullTaskDedupedData::getLongInteractCount);
        } else if ("likeCount".equals(request.getSortBy())) {
            queryWrapper.orderBy(true, "asc".equals(request.getSortOrder()), CmsPullTaskDedupedData::getLongLikeCount);
        } else if ("commentCount".equals(request.getSortBy())) {
            queryWrapper.orderBy(true, "asc".equals(request.getSortOrder()),
                    CmsPullTaskDedupedData::getLongCommentCount);
        } else if ("getRating".equals(request.getSortBy())) {
            queryWrapper.orderBy(true, "asc".equals(request.getSortOrder()), CmsPullTaskDedupedData::getRating);
        } else {
            // 默认按照互动数倒序排序
            queryWrapper.orderByDesc(CmsPullTaskDedupedData::getLongInteractCount);
        }
        Integer dataSource = request.getDataSource();
        if (dataSource != null && (dataSource == 1 || dataSource == 2 || dataSource == 3)) {
            queryWrapper.eq(CmsPullTaskDedupedData::getSourceType, dataSource);
        }
        queryWrapper.eq(CmsPullTaskDedupedData::getAnalysisStatus, 2);
        // 分页查询
        Page<CmsPullTaskDedupedData> page = new Page<>(request.getPage(), request.getSize());

        // 返回符合条件的记录
        return this.baseMapper.selectPage(page, queryWrapper);
    }

    @Override
    public CmsPullTaskDedupedData getTopInteractiveDataOfToday() {
        // 构造查询条件
        LambdaQueryWrapper<CmsPullTaskDedupedData> queryWrapper = new LambdaQueryWrapper<>();

        LocalDateTime sevenDaysAgo = LocalDateTime.now().minusDays(5);
        queryWrapper.ge(CmsPullTaskDedupedData::getDatePublishedAt, sevenDaysAgo)
                .orderByDesc(CmsPullTaskDedupedData::getLongInteractCount); // 按互动数降序排序;
        // 根据类型筛选（1:视频热点，2:圈层热点，3:上传视频）
        queryWrapper.eq(CmsPullTaskDedupedData::getType, 1);
        queryWrapper.eq(CmsPullTaskDedupedData::getAnalysisStatus, 2);
        // 查询当天的数据
        List<CmsPullTaskDedupedData> dataList = this.list(queryWrapper);

        // 返回互动数最高的数据，如果没有数据则返回 null
        return dataList.isEmpty() ? null : dataList.get(0);
    }

    @Override
    public List<CmsPullTaskDedupedData> getRecommenListRequest(RecommenListRequest request) {
        // 构造查询条件
        LambdaQueryWrapper<CmsPullTaskDedupedData> queryWrapper = new LambdaQueryWrapper<>();

        // 根据开始时间筛选
        if (request.getStartTime() != null) {
            queryWrapper.ge(CmsPullTaskDedupedData::getDatePublishedAt, request.getStartTime());
        }

        // 根据结束时间筛选
        if (request.getEndTime() != null) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(request.getEndTime());
            calendar.set(Calendar.HOUR_OF_DAY, 23);
            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.SECOND, 59);
            calendar.set(Calendar.MILLISECOND, 999);
            queryWrapper.le(CmsPullTaskDedupedData::getDatePublishedAt, calendar.getTime());
        }

        // 如果开始时间和结束时间都为空，默认查询最近8天的数据
        if (request.getStartTime() == null && request.getEndTime() == null) {
            LocalDateTime sevenDaysAgo = LocalDateTime.now().minusDays(8);
            queryWrapper.ge(CmsPullTaskDedupedData::getDatePublishedAt, sevenDaysAgo);
        }
        // 格式化为字符串

        // // 根据 type 进行筛选，类型 1, 2, 0 对应不同的向量库
        // PgVectorStore vectorStore = null;

        // // 根据 type 动态注入对应的向量库
        // if ("0".equals(request.getType())) {
        // vectorStore = combinedDataVectorStore;
        // } else if ("1".equals(request.getType())) {
        // vectorStore = userNicknameVectorStore;
        // } else if ("2".equals(request.getType())) {
        // vectorStore = textContentVectorStore;
        // } else if ("3".equals(request.getType())) {
        // vectorStore = videoContentVectorStore;
        // }

        // // 如果没有正确的向量库，抛出异常或返回空
        // if (vectorStore == null) {
        // throw new IllegalArgumentException("Invalid type: " + request.getType());
        // }
        // // 创建 FilterExpressionBuilder，用于构造 filterExpression
        // FilterExpressionBuilder b = new FilterExpressionBuilder();

        // // 默认 topK 设置为 10，如果是 combinedDataVectorStore videoContentVectorStore，则将 topK
        // // 设置为 100
        // int topK = ("3".equals(request.getType()) || "0".equals(request.getType())) ?
        // 100 : 30;

        // // 使用 vectorStore 进行相似度搜索
        // SearchRequest searchRequest =
        // SearchRequest.builder().query(request.getKeyword()) // 使用传入的关键词
        // .topK(topK).similarityThreshold(0.8) // 设定相似度阈值
        // // .filterExpression(b.gte("datePublishedAt", timestamp) // 将8天前的日期转成字符串
        // // .build()) // 添加日期筛选条件
        // .build();

        // List<Document> documents = vectorStore.similaritySearch(searchRequest);

        // // 提取 documents 中 metadata 字段中的 "id" 值，同时构造 esIdIndexMap
        // Map<String, Integer> esIdIndexMap = new HashMap<>();
        // Map<String, String> esIdTextMap = new LinkedHashMap<>();
        // List<String> esIds = new ArrayList<>();

        // for (int i = 0; i < documents.size(); i++) {
        // Document document = documents.get(i);
        // String esId = (String) document.getMetadata().get("id");
        // String text = document.getText();

        // if (!esIdIndexMap.containsKey(esId)) {
        // esIdIndexMap.put(esId, i); // 记录索引位置，确保排序
        // esIds.add(esId); // 维护 esId 列表
        // esIdTextMap.put(esId, text); // 仅存储第一个 text
        // }
        // }

        // // 直接判断是否有数据
        // if (esIds.isEmpty()) {
        // return new ArrayList<>();
        // }

        // // 查询数据库
        // queryWrapper.in(CmsPullTaskDedupedData::getEsId,
        // esIds).eq(CmsPullTaskDedupedData::getType, 1)
        // .eq(CmsPullTaskDedupedData::getAnalysisStatus, 2);

        // List<CmsPullTaskDedupedData> result =
        // this.baseMapper.selectList(queryWrapper);

        // // 直接在排序时赋值 text
        // result.sort(Comparator.comparingInt(a ->
        // esIdIndexMap.getOrDefault(a.getEsId(), Integer.MAX_VALUE)));

        // for (CmsPullTaskDedupedData data : result) {
        // data.setTextContent(esIdTextMap.getOrDefault(data.getEsId(), ""));
        // }

        if ("0".equals(request.getType())) {
            queryWrapper.and(wrapper -> wrapper.like(CmsPullTaskDedupedData::getTextNickName, request.getKeyword()).or()
                    .like(CmsPullTaskDedupedData::getTextContent, request.getKeyword()).or()
                    .like(CmsPullTaskDedupedData::getKwVideoContent, request.getKeyword()));
        } else if ("1".equals(request.getType())) {
            queryWrapper.like(CmsPullTaskDedupedData::getTextNickName, request.getKeyword());
        } else if ("2".equals(request.getType())) {
            queryWrapper.like(CmsPullTaskDedupedData::getTextContent, request.getKeyword());
        } else if ("3".equals(request.getType())) {
            queryWrapper.like(CmsPullTaskDedupedData::getKwVideoContent, request.getKeyword());
        }

        queryWrapper.eq(CmsPullTaskDedupedData::getType, 1)
                .eq(CmsPullTaskDedupedData::getAnalysisStatus, 2);

        List<CmsPullTaskDedupedData> result = this.baseMapper.selectList(queryWrapper);

        return result;
    }

    @Override
    public List<Map<String, Object>> getTribeStatisticsList() {
        Map<String, Long[]> tribeStatsMap = new HashMap<>();
        LocalDate endDate = LocalDate.now();
        LocalDate startDate = endDate.minusDays(8);
        // 一次性查询所有相关数据
        List<CmsPullTaskDedupedData> allTribeDataList = getTribeDataForAll(Arrays.asList(2), startDate, endDate);

        // 遍历所有数据，拆分 `tribeTag` 并更新统计信息
        for (CmsPullTaskDedupedData data : allTribeDataList) {
            if (StringUtils.isNotBlank(data.getKwTwoLevelTribeTag())) {
                String[] tribeTags = data.getKwTwoLevelTribeTag().split(",");

                for (String tribeTag : tribeTags) {
                    tribeTag = tribeTag.trim(); // 去掉可能的空格

                    // 获取或初始化 count & longInteractCount
                    Long[] stats = tribeStatsMap.getOrDefault(tribeTag, new Long[]{0L, 0L});
                    stats[0] += 1; // 计数
                    stats[1] += data.getLongInteractCount(); // 累加互动数

                    tribeStatsMap.put(tribeTag, stats);
                }
            }
        }

        List<Map<String, Object>> resultList = new ArrayList<>();

        // 遍历 `TRIBE_MAP.keySet()`，只保留已定义的圈层
        for (String tribeTag : TRIBE_MAP.keySet()) {
            Long[] stats = tribeStatsMap.getOrDefault(tribeTag, new Long[]{0L, 0L});

            // 组装数据
            Map<String, Object> tribeInfo = new HashMap<>();
            tribeInfo.put("tribeTag", tribeTag);
            tribeInfo.put("count", stats[0]);
            tribeInfo.put("longInteractCount", stats[1]);

            resultList.add(tribeInfo);
        }

        // 按照 `longInteractCount` 降序排序
        return resultList.stream().sorted(Comparator.comparingLong(map -> -((long) map.get("longInteractCount"))))
                .collect(Collectors.toList());
    }

    @Override
    public List<CmsPullTaskDedupedData> getTribeDataForAll(List<Integer> types, LocalDate startDate,
                                                           LocalDate endDate) {
        LambdaQueryWrapper<CmsPullTaskDedupedData> queryWrapper = new LambdaQueryWrapper<>();

        // 使用 `IN` 查询，匹配多个 `type`
        queryWrapper.in(CmsPullTaskDedupedData::getType, types);
        queryWrapper.eq(CmsPullTaskDedupedData::getAnalysisStatus, 2);

        // 转换 LocalDate 为 LocalDateTime
        LocalDateTime startDateTime = startDate.atStartOfDay();
        LocalDateTime endDateTime = endDate.atTime(LocalTime.MAX);

        // 过滤指定时间范围内的数据
        queryWrapper.ge(CmsPullTaskDedupedData::getDatePublishedAt, startDateTime)
                .le(CmsPullTaskDedupedData::getDatePublishedAt, endDateTime);

        return this.list(queryWrapper);
    }


    @Override
    public boolean updateBatch(List<CmsPullTaskDedupedData> updateList) {
        if (updateList == null || updateList.isEmpty()) {
            return false;
        }

        int batchSize = 500; // 每批次处理的大小
        int totalSize = updateList.size();

        // 将数据分批处理，每次更新500条
        for (int i = 0; i < totalSize; i += batchSize) {
            int end = Math.min(i + batchSize, totalSize);
            List<CmsPullTaskDedupedData> batch = updateList.subList(i, end);

            // 批量保存或更新
            this.saveOrUpdateBatch(batch);
        }

        return true;
    }

    @Override
    public long countRecentDedupedData(Date date, String kwKbIndustry, Integer type) {
        Date end_data = getNextDay(date);

        return this.lambdaQuery().ge(CmsPullTaskDedupedData::getDatePublishedAt, date) // 传递的时间作为开始时间
                .lt(CmsPullTaskDedupedData::getDatePublishedAt, end_data) // 小于第二天，即当天数据
                .eq(CmsPullTaskDedupedData::getType, type) // 类型是 1
                .like(CmsPullTaskDedupedData::getKwKbIndustry, kwKbIndustry).count(); // 计算总数
    }

    /**
     * 获取指定日期的下一天
     */
    private Date getNextDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_MONTH, 1); // 增加 1 天
        return calendar.getTime();
    }

    @Override
    public CmsPullTaskDedupedData findFirstByEsId(String esId) {
        LambdaQueryWrapper<CmsPullTaskDedupedData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CmsPullTaskDedupedData::getEsId, esId)
                .orderByDesc(CmsPullTaskDedupedData::getCreateTime)
                .last("LIMIT 1");
        return this.getOne(queryWrapper);
    }

    @Override
    public void updateStatusByEsId(String esId, AnalysisStatusEnum analysisStatusEnum){
        this.update(new LambdaUpdateWrapper<CmsPullTaskDedupedData>().
                set(CmsPullTaskDedupedData::getAnalysisStatus,analysisStatusEnum.getCode())
                .eq(CmsPullTaskDedupedData::getEsId,esId));
    }

    @Override
    public void updateDownloadStatusByEsId(String esId, DownloadStatusEnum downloadStatusEnum){
        this.update(new LambdaUpdateWrapper<CmsPullTaskDedupedData>().
                set(CmsPullTaskDedupedData::getDownloadStatus,downloadStatusEnum.getCode())
                .eq(CmsPullTaskDedupedData::getEsId,esId));
    }

}
