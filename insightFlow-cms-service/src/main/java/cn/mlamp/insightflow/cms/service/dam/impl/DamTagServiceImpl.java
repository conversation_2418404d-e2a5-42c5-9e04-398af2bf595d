package cn.mlamp.insightflow.cms.service.dam.impl;

import cn.mlamp.insightflow.cms.config.UserContext;
import cn.mlamp.insightflow.cms.constant.PermissionConstant;
import cn.mlamp.insightflow.cms.entity.dam.DamPublicTag;
import cn.mlamp.insightflow.cms.entity.dam.DamTag;
import cn.mlamp.insightflow.cms.entity.dam.DamTagValue;
import cn.mlamp.insightflow.cms.enums.ErrorCode;
import cn.mlamp.insightflow.cms.enums.dam.DamTagTypeEnum;
import cn.mlamp.insightflow.cms.exception.BusinessException;
import cn.mlamp.insightflow.cms.mapper.dam.DamTagMapper;
import cn.mlamp.insightflow.cms.model.converter.dam.DamTagConverter;
import cn.mlamp.insightflow.cms.model.dto.dam.DamTagDTO;
import cn.mlamp.insightflow.cms.model.vo.dam.DamTagVO;
import cn.mlamp.insightflow.cms.service.dam.IDamPublicTagService;
import cn.mlamp.insightflow.cms.service.dam.IDamTagService;
import cn.mlamp.insightflow.cms.service.dam.IDamTagValueService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * DAM标签表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
@Slf4j
@Service
public class DamTagServiceImpl extends ServiceImpl<DamTagMapper, DamTag> implements IDamTagService {

    @Autowired
    private IDamPublicTagService publicTagService;

    @Autowired
    private IDamTagValueService tagValueService;

    @Autowired
    private DamTagConverter tagConverter;

    @Override
    public DamTagVO createCustomTag(DamTagDTO tagDTO, Integer userId, Integer tenantId) {
        // 判断是否存在同名的标签
        final List<DamTag> existingTags = list(new LambdaQueryWrapper<DamTag>()
                .eq(DamTag::getName, tagDTO.getName())
                .eq(DamTag::getTenantId, tenantId)
                .eq(DamTag::getIsDeleted, false));
        if (!existingTags.isEmpty()) {
            throw new BusinessException(ErrorCode.PARAM_ERROR, "存在同名标签");
        }
        final DamTag tag = tagConverter.toEntity(tagDTO);
        tag.setTenantId(tenantId);
        tag.setUserId(userId);
        tag.setType(DamTagTypeEnum.CUSTOM);
        save(tag);
        return tagConverter.toVO(tag);
    }

    @Override
    public List<DamTagVO> getTagList(@Nullable DamTagTypeEnum type, boolean suggestValues, Integer userId,
                                     Integer tenantId) {
        final List<DamTag> tags = list(
                new LambdaQueryWrapper<DamTag>()
                        .eq(type != null, DamTag::getType, type)
                        .eq(DamTag::getTenantId, tenantId)
                        .eq(DamTag::getIsDeleted, false)
                        .orderByDesc(DamTag::getId));
        final List<DamTagVO> result = tagConverter.toVOs(tags);
        if (suggestValues) {
            final List<DamTag> neededPublicTags = tags.stream()
                    .filter(tag -> DamTagTypeEnum.PUBLIC.equals(tag.getType()))
                    .toList();
            if (!neededPublicTags.isEmpty()) {
                // 如果有公共标签，则查询所有公共标签, 获取建议值
                final List<String> publicTagNames = neededPublicTags.stream()
                        .map(DamTag::getName)
                        .toList();
                final Map<String, List<String>> publicTag2Values = publicTagService.list(
                                new LambdaQueryWrapper<DamPublicTag>()
                                        .in(DamPublicTag::getName, publicTagNames)
                                        .eq(DamPublicTag::getIsDeleted, false))
                        .stream()
                        .collect(Collectors.toMap(DamPublicTag::getName, publicTag -> Objects.requireNonNullElse(publicTag.getSuggestValues(), Collections.emptyList())));

                for (DamTagVO tag : result) {
                    if (tag.getType() == null || !DamTagTypeEnum.PUBLIC.equals(tag.getType())) {
                        continue;
                    }
                    if (publicTag2Values.containsKey(tag.getName())) {
                        tag.setValues(publicTag2Values.getOrDefault(tag.getName(), Collections.emptyList()));
                    }
                }
            }
        }
        return result;
    }

    /**
     * 搜索标签
     */
    @Override
    public List<DamTagVO> searchTags(String keyword, Integer userId, Integer tenantId) {
        return baseMapper.selectJoinList(
                DamTagVO.class,
                new MPJLambdaWrapper<DamTag>()
                        .distinct()
                        .select(DamTag::getId)
                        .select(DamTag::getName)
                        .select(DamTag::getType)
                        .leftJoin(DamTagValue.class, DamTagValue::getTagId, DamTag::getId, on -> on.eq(DamTagValue::getIsDeleted, false).like(DamTagValue::getValue, "%" + keyword + "%"))
                        .eq(DamTag::getTenantId, tenantId)
                        .eq(DamTag::getIsDeleted, false)
        );
    }

    @Override
    public DamTagVO updateTag(Integer tagId, DamTagDTO tagDTO, Integer userId, Integer tenantId) {
        final DamTag existingTag = getOne(new LambdaQueryWrapper<DamTag>()
                .eq(DamTag::getId, tagId)
                .eq(DamTag::getTenantId, tenantId)
                .eq(DamTag::getIsDeleted, false));
        if (existingTag == null) {
            throw new IllegalArgumentException("标签未找到");
        }
        if (existingTag.getType().equals(DamTagTypeEnum.PUBLIC)) {
            throw new BusinessException(ErrorCode.AUTHORIZATION_FAIL, "公共标签不能修改");
        }
        if (!existingTag.getUserId().equals(userId) && !UserContext.hasPermission(PermissionConstant.DAM_ADMIN)) {
            throw new BusinessException(ErrorCode.AUTHORIZATION_FAIL, "只允许修改自己创建的标签");
        }
        existingTag.setName(tagDTO.getName());
        existingTag.setDescription(tagDTO.getDescription());
        existingTag.setExample(tagDTO.getExample());
        existingTag.setUpdateTime(new Date());
        updateById(existingTag);
        return tagConverter.toVO(existingTag);
    }

    @Override
    public boolean deleteTag(Integer tagId, Integer userId, Integer tenantId) {
        final DamTag tag = getOne(new LambdaQueryWrapper<DamTag>()
                .eq(DamTag::getId, tagId)
                .eq(DamTag::getTenantId, tenantId)
                .eq(DamTag::getIsDeleted, false));
        if (tag == null) {
            throw new BusinessException(ErrorCode.NOT_FOUND, "标签未找到");
        }
        if (tag.getType().equals(DamTagTypeEnum.PUBLIC)) {
            throw new BusinessException(ErrorCode.AUTHORIZATION_FAIL, "公共标签不允许删除");
        }
        if (!tag.getUserId().equals(userId) && !UserContext.hasPermission(PermissionConstant.DAM_ADMIN)) {
            throw new BusinessException(ErrorCode.AUTHORIZATION_FAIL, "只允许删除自己创建的标签");
        }
        tag.setIsDeleted(true);
        updateById(tag);

        tagValueService.update(
                new LambdaUpdateWrapper<>(DamTagValue.class)
                        .eq(DamTagValue::getTagId, tag.getId())
                        .eq(DamTagValue::getIsDeleted, false)
                        .set(DamTagValue::getIsDeleted, true)
                        .set(DamTagValue::getUpdateTime, new Date())
        );

        return true;
    }

}
