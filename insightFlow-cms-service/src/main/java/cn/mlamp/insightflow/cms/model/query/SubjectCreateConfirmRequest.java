package cn.mlamp.insightflow.cms.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


import java.io.Serializable;
import java.util.List;
/**
 * @Author: renguangzong
 * @CreateTime: 2025-05-12
 */
@Data
public class SubjectCreateConfirmRequest {
    /**
     * 主体ID， 当存在时，表示编辑主体
     */
    @Schema(description = "主体id", required = false)
    private Integer subjectId;

    @Schema(description = "主体图片对象集合", required = true)
    private List<SubjectImageRequest> subjectImages;

    @Schema(description = "主体姓名", required = true)
    private String subjectName;

    @Schema(description = "主体标签", required = false)
    private String subjectTag;

    @Schema(description = "主体可见性: 0 个人可见 1 租户可见", required = true)
    private Integer visibility;

    @Schema(description = "主体风格", required = true)
    private String subjectStyle;

    @Schema(description = "主体描述", required = true)
    private String subjectDescription;

    @Data
    public static class SubjectImageRequest implements Serializable {

        @Schema(description = "主体图片ossId", required = true)
        private String imageOssId;

        @Schema(description = "主体图片名", required = true)
        private String imageNames;

        @Schema(description = "主体图片类型", required = true)
        private String imageType;

        @Schema(description = "主体图片排序", required = true)
        private Integer imageOrder;

        @Schema(description = "主体图片大小", required = true)
        private Long imageSize;
    }

}
