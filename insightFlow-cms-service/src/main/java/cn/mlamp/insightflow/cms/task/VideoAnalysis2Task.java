//package cn.mlamp.insightflow.cms.task;
//
//import cn.mlamp.insightflow.cms.config.TaskConfig;
//import cn.mlamp.insightflow.cms.entity.BaseEntity;
//import cn.mlamp.insightflow.cms.entity.CmsPullTaskDedupedData;
//import cn.mlamp.insightflow.cms.enums.AnalysisStatusEnum;
//import cn.mlamp.insightflow.cms.enums.AnalysisVideoTypeEnum;
//import cn.mlamp.insightflow.cms.enums.DownloadStatusEnum;
//import cn.mlamp.insightflow.cms.enums.VideoInfoStatusEnum;
//import cn.mlamp.insightflow.cms.model.query.AnalysisVideoCreateRequest;
//import cn.mlamp.insightflow.cms.model.query.AnalysisVideoQueryRequest;
//import cn.mlamp.insightflow.cms.model.vo.AnalysisVideoResultVO;
//import cn.mlamp.insightflow.cms.service.CmsPullTaskDedupedDataService;
//import cn.mlamp.insightflow.cms.service.IVideoInfoService;
//import cn.mlamp.insightflow.cms.strategy.handle.VideoRecognition2Handle;
//import cn.mlamp.insightflow.cms.strategy.video.create.AnalysisVideoStrategyMap;
//import cn.mlamp.insightflow.cms.util.DateUtil;
//import cn.mlamp.insightflow.cms.util.FileDownloadUtil;
//import cn.mlamp.insightflow.cms.util.VideoUtil;
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.data.redis.core.StringRedisTemplate;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Component;
//
//import java.time.LocalDate;
//import java.time.LocalDateTime;
//import java.time.format.DateTimeFormatter;
//import java.util.Date;
//import java.util.List;
//import java.util.concurrent.TimeUnit;
//
///**
// * @Author: husuper
// * @CreateTime: 2025-03-26
// */
//@Component
//@Slf4j
//public class VideoAnalysis2Task {
//
//    @Autowired
//    private StringRedisTemplate stringRedisTemplate;
//
//    public static final String VIDEO_ANALYSIS_KEY = "videoAnalysisJob.analysis.";
//
//    @Autowired
//    private TaskConfig taskConfig;
//
//    @Autowired
//    private IVideoInfoService videoInfoService;
//
//    @Autowired
//    private CmsPullTaskDedupedDataService cmsPullTaskDedupedDataService;
//
//    @Autowired
//    private QianchuanVideoAnalysisTask qianchuanVideoAnalysisTask;
//
//    @Autowired
//    private VideoRecognition2Handle videoRecognition2Handle;
//
//    @Scheduled(cron = "0 0/2 * * * ?")
//    public void videoAnalysisJob()  {
//        if(taskConfig.isLocal()){
//            return;
//        }
//
//        if(isVideoAnalysisJobRunning()){
//            return;
//        }
//
//        // 分布式锁(防止同一时间多台服务器重复触发)，保证每次定时任务只有一台服务器在执行
//        boolean result = Boolean.TRUE.equals(stringRedisTemplate.opsForValue().setIfAbsent("videoAnalysisJob", "ImSyncJob-timer", 300, TimeUnit.SECONDS));
//        if (result) {
//            videoAnalysisJob(DateUtil.getYYYYMMDD(new Date()));
//            stringRedisTemplate.delete("videoAnalysisJob");
//        } else {
//            log.warn("多台服务器，防止同一个任务重复执行");
//        }
//    }
//
//    /**
//     * 检查 Redis 中是否存在视频分析任务锁
//     *
//     * @return 如果存在返回 true，否则返回 false
//     */
//    public boolean isVideoAnalysisJobRunning() {
//        String jobKey = "videoAnalysis2Task";
//        return Boolean.TRUE.equals(stringRedisTemplate.hasKey(jobKey));
//    }
//
//    public void videoAnalysisJob(String dateStr){
//        List<CmsPullTaskDedupedData> queryDatas =queryData(dateStr, AnalysisStatusEnum.PROCESSING);
//        for (CmsPullTaskDedupedData cmsPullTaskDedupedData : queryDatas){
//            boolean result = Boolean.TRUE.equals(stringRedisTemplate.opsForValue().setIfAbsent(VIDEO_ANALYSIS_KEY+cmsPullTaskDedupedData.getEsId(), "ImSyncJob-timer", 900, TimeUnit.SECONDS));
//            if(result){
//                videoResult(cmsPullTaskDedupedData);
//                stringRedisTemplate.delete(VIDEO_ANALYSIS_KEY+cmsPullTaskDedupedData.getEsId());
//            }
//        }
//
//
//        if(queryDatas.size()>4){
//            log.info("进行中的分析任务超过20条，暂停发送新的分析任务，进行中任务为{}条", queryDatas.size());
//            return;
//        }
//
//        int count = videoRecognition2Handle.countPending().getData().getPending_count();
//        if(count>4){
//            log.info("进行中的视频识别任务超过5条，暂停发送新的分析任务");
//            return;
//        }
//
//        for (CmsPullTaskDedupedData cmsPullTaskDedupedData : queryData(dateStr, AnalysisStatusEnum.WAITING)){
//            boolean result = Boolean.TRUE.equals(stringRedisTemplate.opsForValue().setIfAbsent(VIDEO_ANALYSIS_KEY+cmsPullTaskDedupedData.getEsId(), "ImSyncJob-timer", 900, TimeUnit.SECONDS));
//            if(result){
//                videoAnalysis(cmsPullTaskDedupedData);
//                stringRedisTemplate.delete(VIDEO_ANALYSIS_KEY+cmsPullTaskDedupedData.getEsId());
//            }
//        }
//    }
//
//
//    private List<CmsPullTaskDedupedData> queryData(String dateStr,AnalysisStatusEnum status){
//        //查询创建时间为 date的数据   date格式是yyyy-mm-dd
//        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
//        LocalDate date = LocalDate.parse(dateStr, formatter);
//
//        LocalDateTime startOfDay = date.atStartOfDay();
//        LocalDateTime endOfDay = date.plusDays(1).atStartOfDay();
//
//        LambdaQueryWrapper<CmsPullTaskDedupedData> queryWrapper = new LambdaQueryWrapper<CmsPullTaskDedupedData>()
//                .ge(CmsPullTaskDedupedData::getCreateTime, startOfDay)
//                .lt(CmsPullTaskDedupedData::getCreateTime, endOfDay);
//        queryWrapper.eq(CmsPullTaskDedupedData::getSourceType, 1);
//        queryWrapper.eq(CmsPullTaskDedupedData::getType, 2);
//        queryWrapper.eq(CmsPullTaskDedupedData::getDownloadStatus, DownloadStatusEnum.SUCCESS.getCode());
//        queryWrapper.eq(CmsPullTaskDedupedData::getAnalysisStatus, status.getCode());
//        queryWrapper.groupBy(CmsPullTaskDedupedData::getEsId);
//        queryWrapper.orderByDesc(CmsPullTaskDedupedData::getDatePublishedAt);
//        queryWrapper.orderByDesc(CmsPullTaskDedupedData::getLongInteractCount);
//        queryWrapper.orderByDesc(CmsPullTaskDedupedData::getLongLikeCount);
//        queryWrapper.orderByAsc(CmsPullTaskDedupedData::getCreateTime);
//        //限制在10条数据
//        queryWrapper.last("limit 5");
//
//        List<CmsPullTaskDedupedData> list = cmsPullTaskDedupedDataService.list(queryWrapper);
//        return list;
//    }
//
//
//    private void  videoAnalysis(CmsPullTaskDedupedData cmsPullTaskDedupedData){
//        try {
//            //分析视频
//            AnalysisVideoCreateRequest analysisVideoCreateRequest= new AnalysisVideoCreateRequest();
//            analysisVideoCreateRequest.setEsId(cmsPullTaskDedupedData.getEsId());
//            analysisVideoCreateRequest.setTypeName(AnalysisVideoTypeEnum.VIDEO_ANALYSIS.getVideoType());
//            AnalysisVideoStrategyMap.process(analysisVideoCreateRequest);
//            updateByEsId(cmsPullTaskDedupedData.getEsId(),AnalysisStatusEnum.PROCESSING);
//        }catch (Exception e){
//            log.error("视频分析失败",e);
//            updateByEsId(cmsPullTaskDedupedData.getEsId(),AnalysisStatusEnum.ERROR);
//
//        }
//
//    }
//
//
//    private void  videoResult(CmsPullTaskDedupedData cmsPullTaskDedupedData){
//        try {
//            //查询视频结果
//            AnalysisVideoQueryRequest analysisVideoQueryRequest= new AnalysisVideoQueryRequest();
//            analysisVideoQueryRequest.setEsId(cmsPullTaskDedupedData.getEsId());
//            analysisVideoQueryRequest.setTypeName(AnalysisVideoTypeEnum.VIDEO_ANALYSIS.getVideoType());
//            AnalysisVideoResultVO analysisVideoResultVO = AnalysisVideoStrategyMap.queryResult(analysisVideoQueryRequest);
//        }catch (Exception e){
//            log.error("视频分析失败",e);
//            updateByEsId(cmsPullTaskDedupedData.getEsId(),AnalysisStatusEnum.ERROR);
//        }
//    }
//
//    private void  updateByEsId(String esId,AnalysisStatusEnum analysisStatusEnum){
//        cmsPullTaskDedupedDataService.update(new LambdaUpdateWrapper<CmsPullTaskDedupedData>().
//                set(CmsPullTaskDedupedData::getAnalysisStatus,analysisStatusEnum.getCode())
//                .eq(CmsPullTaskDedupedData::getEsId,esId).eq(BaseEntity::getIsDeleted,0)
//                .eq(CmsPullTaskDedupedData::getSourceType,1));
//    }
//
//
//
//}
