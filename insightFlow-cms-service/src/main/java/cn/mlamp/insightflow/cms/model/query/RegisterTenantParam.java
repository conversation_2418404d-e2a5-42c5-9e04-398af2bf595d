package cn.mlamp.insightflow.cms.model.query;

import cn.mlamp.insightflow.cms.constant.CommonConstant;
import cn.mlamp.insightflow.cms.validation.ObjectPropertiesMustBeConsistentConstraint;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.ToString;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;

/**
 * 自助注册租户Param
 *
 * <AUTHOR>
 * @since 2022-09-08 12:00:39
 */
@Data
@ToString(onlyExplicitlyIncluded = true)
@ObjectPropertiesMustBeConsistentConstraint(properties = { "password",
        "passwordCheck" }, message = "override_message.password_not_equals_password_check")
public class RegisterTenantParam {

    /**
     * 租户名
     */
    @NotBlank(message = "override_message.tenant_name_not_blank")
    @Size(max = 20, message = "override_message.tenant_name_out_of_length")
    @ToString.Include
    private String nickName;
    /**
     * 密码
     */
    @NotBlank(message = "override_message.password_not_blank")
    @Pattern(regexp = CommonConstant.PASSWORD_REGX, message = "override_message.password_invalid")
    @ToString.Exclude
    private String password;
    /**
     * 密码二次确认
     */
    @NotBlank(message = "override_message.password_check_not_blank")
    @ToString.Exclude
    private String passwordCheck;
    /**
     * 邮箱
     */
    @NotBlank(message = "override_message.email_not_empty")
    @Pattern(regexp = CommonConstant.EMAIL_REGX, message = "override_message.email_invalid")
    @ToString.Include
    private String email;
    /**
     * 邮箱验证码
     */
    @Pattern(regexp = CommonConstant.VERIFICATION_CODE_REGX, message = "override_message.email_verification_code_invalid")
    @ToString.Include
    private String emailCode;
    /**
     * 手机号
     */
    @NotBlank(message = "override_message.mobile_not_empty")
    @Pattern(regexp = CommonConstant.MOBILE_REGX, message = "override_message.mobile_invalid")
    @ToString.Include
    private String mobile;
    /**
     * 手机号验证码
     */
    @Pattern(regexp = CommonConstant.VERIFICATION_CODE_REGX, message = "override_message.mobile_verification_code_invalid")
    @ToString.Include
    private String mobileCode;

    /**
     * 是否强制创建新租户 false-如果已有关联租户，则不再创建新租户 true-直接创建新租户
     */
    @ToString.Include
    private Boolean isForceCreateTenant;

}