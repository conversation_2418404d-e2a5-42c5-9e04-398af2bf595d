package cn.mlamp.insightflow.cms.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class FeedbackRequest {

    @Schema(description = "反馈来源类型", example = "1")
    private Integer sourceType;

    @Schema(description = "反馈来源Id", example = "1")
    private Integer sourceId;

    @Schema(description = "反馈选项", example = "1")

    private List<String> options = new ArrayList<>();

    @Schema(description = "反馈内容", example = "1")
    private String content;
}
