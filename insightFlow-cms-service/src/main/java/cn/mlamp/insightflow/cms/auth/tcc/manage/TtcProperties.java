package cn.mlamp.insightflow.cms.auth.tcc.manage;//package cn.mlamp.insightflow.cms.auth.tcc.manage;

import lombok.Getter;
import lombok.Setter;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

@Getter
@Setter
@Configuration
public class TtcProperties {

    /**
     * ttc的错误码与应用的业务错误码映射关系的map
     */
    public Map<String, Integer> ttcErrorCodeToBizErrorCodeMap = new HashMap<>();
    {
        ttcErrorCodeToBizErrorCodeMap.put("100",1000004);
        ttcErrorCodeToBizErrorCodeMap.put("103",2000009);
        ttcErrorCodeToBizErrorCodeMap.put("104",2000009);
        ttcErrorCodeToBizErrorCodeMap.put("102",2000001);
        ttcErrorCodeToBizErrorCodeMap.put("105",2000001);
        ttcErrorCodeToBizErrorCodeMap.put("106",2000001);
        ttcErrorCodeToBizErrorCodeMap.put("107",2000001);
        ttcErrorCodeToBizErrorCodeMap.put("700001",2000003);
        ttcErrorCodeToBizErrorCodeMap.put("700002",2000008);
        ttcErrorCodeToBizErrorCodeMap.put("700004",2000009);
        ttcErrorCodeToBizErrorCodeMap.put("700005",2000008);
        ttcErrorCodeToBizErrorCodeMap.put("700006",2000001);
        ttcErrorCodeToBizErrorCodeMap.put("700007",1000004);
        ttcErrorCodeToBizErrorCodeMap.put("700008",2000013);
        ttcErrorCodeToBizErrorCodeMap.put("700009",2000014);
        ttcErrorCodeToBizErrorCodeMap.put("700011",2000015);
        ttcErrorCodeToBizErrorCodeMap.put("700012",2000015);
        ttcErrorCodeToBizErrorCodeMap.put("700013",1000004);
    }
}
