package cn.mlamp.insightflow.cms.model.query;

import jakarta.validation.constraints.Size;
import lombok.Data;
import cn.mlamp.insightflow.cms.constant.CommonConstant;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;

/**
 * 自助激活租户Param
 *
 * <AUTHOR>
 * @since 2022-09-16 14:02:09
 */
@Data
public class ActivateTenantParam {

    /**
     * 租户名
     */
    @NotBlank(message = "override_message.tenant_name_not_blank")
    @Size(max = 20, message = "override_message.tenant_name_out_of_length")
    private String nickName;
    /**
     * 邮箱
     */
    @NotBlank(message = "override_message.email_not_empty")
    @Pattern(regexp = CommonConstant.EMAIL_REGX, message = "override_message.email_invalid")
    private String email;

}