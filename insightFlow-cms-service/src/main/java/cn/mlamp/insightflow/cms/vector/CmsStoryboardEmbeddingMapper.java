package cn.mlamp.insightflow.cms.vector;

import cn.mlamp.insightflow.cms.entity.CmsStoryboardEmbedding;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface CmsStoryboardEmbeddingMapper extends BaseMapper<CmsStoryboardEmbedding> {
    long totalEmbeddingCount(@Param("taskId") Integer taskId,
                             @Param("tenantId") Integer tenantId,
                             @Param("userId") Integer userId);

    /**
     * 分页查询embedding相似度排序结果
     *
     * @param embedding   查询向量
     * @param pageSize    每页数量
     * @param offset      偏移量
     * @param tenantId    租户ID
     * @param userId      用户ID
     * @param taskId      任务ID
     * @return 结果列表
     */
    List<CmsStoryboardEmbedding> pageEmbedding(@Param("embedding") float[] embedding,
                                              @Param("offset") int offset,
                                              @Param("pageSize") int pageSize,
                                              @Param("taskId") Integer taskId,
                                              @Param("tenantId") Integer tenantId,
                                              @Param("userId") Integer userId);

    List<CmsStoryboardEmbedding> topKEmbedding(@Param("embedding") float[] embedding,
                                              @Param("limit") int limit,
                                              @Param("threshold") Float threshold,
                                              @Param("taskId") Integer taskId,
                                              @Param("tenantId") Integer tenantId,
                                              @Param("userId") Integer userId);

    int updateEmbedding(@Param("id") Integer id,
                        @Param("content") String content,
                        @Param("embedding") Float[] embedding);

}
