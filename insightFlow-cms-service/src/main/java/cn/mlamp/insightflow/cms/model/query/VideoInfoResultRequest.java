package cn.mlamp.insightflow.cms.model.query;

import lombok.Data;

import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * @Author: husuper
 * @CreateTime: 2025-03-19
 */
@Data
public class VideoInfoResultRequest implements Serializable {

    @Schema(description = "视频分析Id", required = false)
    private Integer id;

    @Schema(description = "1:ES数据，精选和圈层热点；4：上传视频;5:千川视频", required = false)
    private Integer type;

    @Schema(description = "帖子ID", required = false)
    private String esId;

}
