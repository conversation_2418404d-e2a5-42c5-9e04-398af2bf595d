package cn.mlamp.insightflow.cms.auth.tcc.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/8/23 4:05 下午
 * @desc TODO
 */
@Data
public class TenantVo {

    @Schema(description = "租户Id", required = true)
    private Integer id;

    @Schema(description = "租户名称", required = true)
    private String name;

    @Schema(description = "有效时间", required = true)
    private Long expiredTime;

    @Schema(description = "是否是当前租户", required = true)
    private Boolean isCurrent;

}
