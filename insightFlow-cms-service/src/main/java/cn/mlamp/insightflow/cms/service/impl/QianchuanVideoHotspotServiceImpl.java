
package cn.mlamp.insightflow.cms.service.impl;

import cn.mlamp.insightflow.cms.entity.QianchuanMaterialVideo;
import cn.mlamp.insightflow.cms.mapper.QianchuanVideoHotspotMapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import cn.mlamp.insightflow.cms.model.dto.QianchuanVideoHotspotQueryDTO;
import cn.mlamp.insightflow.cms.model.dto.SortOption;
import cn.mlamp.insightflow.cms.model.query.RecommenListRequest;
import cn.mlamp.insightflow.cms.model.vo.BrandProductCountVO;
import cn.mlamp.insightflow.cms.model.vo.BrandProductRecommendVO;
import cn.mlamp.insightflow.cms.service.QianchuanVideoHotspotService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class QianchuanVideoHotspotServiceImpl extends ServiceImpl<QianchuanVideoHotspotMapper, QianchuanMaterialVideo>
        implements QianchuanVideoHotspotService {

    private final QianchuanVideoHotspotMapper videoHotspotMapper;

    /**
     * 从数据库获取品牌推荐
     */
    private List<BrandProductRecommendVO> getBrandRecommendationsFromDB(String keyword, Date startTime, Date endTime,
            int limit) {
        List<BrandProductCountVO> brandCounts = videoHotspotMapper.getBrandCountList(keyword, startTime, endTime,
                limit);
        return brandCounts.stream().map(item -> {
            BrandProductRecommendVO vo = new BrandProductRecommendVO();
            vo.setName(item.getName());
            vo.setType("brand");
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 从数据库获取产品推荐
     */
    private List<BrandProductRecommendVO> getProductRecommendationsFromDB(String keyword, Date startTime, Date endTime,
            int limit) {
        List<BrandProductCountVO> productCounts = videoHotspotMapper.getProductCountList(keyword, startTime, endTime,
                limit);
        return productCounts.stream().map(item -> {
            BrandProductRecommendVO vo = new BrandProductRecommendVO();
            vo.setName(item.getName());
            vo.setType("product");
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 从数据库获取综合推荐（品牌和产品）
     */
    private List<BrandProductRecommendVO> getCombinedRecommendationsFromDB(String keyword, Date startTime,
            Date endTime) {
        // 查询品牌和产品的出现频率
        List<BrandProductCountVO> brandCounts = videoHotspotMapper.getBrandCountList(keyword, startTime, endTime, 10);
        List<BrandProductCountVO> productCounts = videoHotspotMapper.getProductCountList(keyword, startTime, endTime,
                10);

        // 合并排序，先按数量降序，数量相同时品牌优先
        List<Object[]> combinedEntries = new ArrayList<>();

        // 添加品牌条目
        for (BrandProductCountVO item : brandCounts) {
            combinedEntries.add(new Object[] { item.getName(), item.getCount(), "brand" });
        }

        // 添加产品条目
        for (BrandProductCountVO item : productCounts) {
            combinedEntries.add(new Object[] { item.getName(), item.getCount(), "product" });
        }

        // 按数量降序排序，数量相同时品牌优先
        combinedEntries.sort((a, b) -> {
            Integer countA = (Integer) a[1];
            Integer countB = (Integer) b[1];
            int countCompare = countB.compareTo(countA); // 降序
            if (countCompare != 0) {
                return countCompare;
            }

            // 数量相同时，品牌优先
            String typeA = (String) a[2];
            String typeB = (String) b[2];
            if ("brand".equals(typeA) && "product".equals(typeB)) {
                return -1; // 品牌排前
            } else if ("product".equals(typeA) && "brand".equals(typeB)) {
                return 1; // 产品排后
            }
            return 0;
        });

        // 取前10个结果
        List<BrandProductRecommendVO> result = new ArrayList<>();
        int limit = Math.min(10, combinedEntries.size());
        for (int i = 0; i < limit; i++) {
            Object[] entry = combinedEntries.get(i);
            BrandProductRecommendVO vo = new BrandProductRecommendVO();
            vo.setName((String) entry[0]);
            vo.setType((String) entry[2]);
            result.add(vo);
        }

        return result;
    }

    @Override
    public IPage<QianchuanMaterialVideo> listVideos(QianchuanVideoHotspotQueryDTO queryDTO) {
        LambdaQueryWrapper<QianchuanMaterialVideo> wrapper = new LambdaQueryWrapper<>();

        // 默认查询条件：未删除且分析状态为成功(2)
        wrapper.eq(QianchuanMaterialVideo::getIsDeleted, 0);
        wrapper.eq(QianchuanMaterialVideo::getAnalysisStatus, 2);

        // 条件构建
        wrapper.eq(StringUtils.isNotBlank(queryDTO.getRankingType()), QianchuanMaterialVideo::getRankingType,
                queryDTO.getRankingType());
        wrapper.eq(StringUtils.isNotBlank(queryDTO.getIndustry()), QianchuanMaterialVideo::getIndustry,
                queryDTO.getIndustry());

        // 处理消费范围筛选
        if (StringUtils.isNotBlank(queryDTO.getConsumeRange())) {
            handleConsumeRangeFilter(wrapper, queryDTO.getConsumeRange());
        }

        // 视频时长范围
        if (queryDTO.getDurationMin() != null && queryDTO.getDurationMax() != null) {
            // 如果最小值和最大值都有，使用between
            wrapper.between(QianchuanMaterialVideo::getDuration, queryDTO.getDurationMin(), queryDTO.getDurationMax());
        } else if (queryDTO.getDurationMin() != null) {
            // 如果只有最小值，使用大于等于条件
            wrapper.ge(QianchuanMaterialVideo::getDuration, queryDTO.getDurationMin());
        } else if (queryDTO.getDurationMax() != null) {
            // 如果只有最大值，使用小于等于条件
            wrapper.le(QianchuanMaterialVideo::getDuration, queryDTO.getDurationMax());
        }

        // 发布时间范围
        if (queryDTO.getPublishTimeStart() != null && queryDTO.getPublishTimeEnd() != null) {
            wrapper.between(QianchuanMaterialVideo::getPublishTime, queryDTO.getPublishTimeStart(),
                    queryDTO.getPublishTimeEnd());
        }

        // 关键词模糊查询
        if (StringUtils.isNotBlank(queryDTO.getKeyword())) {

            if ("0".equals(queryDTO.getSelectType())) {
                // 综合查询：标题、口播文案、品牌、产品名称
                wrapper.and(w -> w.like(QianchuanMaterialVideo::getTitle, queryDTO.getKeyword()).or()
                        .like(QianchuanMaterialVideo::getKwVideoContent, queryDTO.getKeyword()).or()
                        .like(QianchuanMaterialVideo::getBrand, queryDTO.getKeyword()).or()
                        .like(QianchuanMaterialVideo::getProductName, queryDTO.getKeyword()));
            } else if ("1".equals(queryDTO.getSelectType())) {
                // 品牌查询
                wrapper.and(w -> w.like(QianchuanMaterialVideo::getBrand, queryDTO.getKeyword()));
            } else if ("2".equals(queryDTO.getSelectType())) {
                // 产品查询
                wrapper.and(w -> w.like(QianchuanMaterialVideo::getProductName, queryDTO.getKeyword()));
            } else if ("3".equals(queryDTO.getSelectType())) {
                // 标题查询
                wrapper.and(w -> w.like(QianchuanMaterialVideo::getTitle, queryDTO.getKeyword()));
            } else if ("4".equals(queryDTO.getSelectType())) {
                // 口播文案查询
                wrapper.and(w -> w.like(QianchuanMaterialVideo::getKwVideoContent, queryDTO.getKeyword()));
            }

        }

        // 排序
        // 处理多字段排序
        if (queryDTO.getSortOptions() != null && !queryDTO.getSortOptions().isEmpty()) {
            for (SortOption option : queryDTO.getSortOptions()) {
                String sortField = option.getSortField();
                String sortOrder = option.getSortOrder();

                if (StringUtils.isNotBlank(sortField) && StringUtils.isNotBlank(sortOrder)) {
                    boolean isAsc = "asc".equalsIgnoreCase(sortOrder);
                    wrapper.orderBy(true, isAsc, getSortField(sortField));
                }
            }
        }
        // 兼容旧版单字段排序
        else if (StringUtils.isNotBlank(queryDTO.getSortField())) {
            boolean isAsc = "asc".equalsIgnoreCase(queryDTO.getSortOrder());
            wrapper.orderBy(true, isAsc, getSortField(queryDTO.getSortField()));
        }
        // 如果没有指定任何排序，则使用默认排序
        else {
            wrapper.orderByDesc(QianchuanMaterialVideo::getConsumeRangeWeight);
        }

        // 分页查询
        IPage<QianchuanMaterialVideo> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        page = videoHotspotMapper.selectPage(page, wrapper);

        return page;
    }

    private SFunction<QianchuanMaterialVideo, ?> getSortField(String sortField) {
        switch (sortField) {
        case "exposure":
            return QianchuanMaterialVideo::getExposure;
        case "shares":
            return QianchuanMaterialVideo::getShares;
        case "comments":
            return QianchuanMaterialVideo::getComments;
        case "likes":
            return QianchuanMaterialVideo::getLikes;
        case "clicks":
            return QianchuanMaterialVideo::getClicks;
        case "rating":
            return QianchuanMaterialVideo::getRating;
        case "consume_range_weight":
            return QianchuanMaterialVideo::getConsumeRangeWeight;
        default:
            return QianchuanMaterialVideo::getExposure;
        }
    }

    @Override
    public QianchuanMaterialVideo getTopExposureVideo() {
        LambdaQueryWrapper<QianchuanMaterialVideo> wrapper = new LambdaQueryWrapper<>();

        // 默认查询条件：未删除且分析状态为成功(2)
        wrapper.eq(QianchuanMaterialVideo::getIsDeleted, 0);
        wrapper.eq(QianchuanMaterialVideo::getAnalysisStatus, 2);

        LocalDateTime fiveDaysAgo = LocalDateTime.now().minusDays(5);
        wrapper.ge(QianchuanMaterialVideo::getPublishTime, fiveDaysAgo).orderByDesc(QianchuanMaterialVideo::getExposure)
                .last("LIMIT 1");
        return getOne(wrapper);
    }

    /**
     * 处理消费范围筛选 将前端传入的标准范围（如 "0-10w"）映射到数据库中的实际范围值
     *
     * @param wrapper      查询条件包装器
     * @param consumeRange 前端传入的消费范围
     */
    private void handleConsumeRangeFilter(LambdaQueryWrapper<QianchuanMaterialVideo> wrapper, String consumeRange) {
        // 如果是直接匹配数据库中的值，直接使用等于条件
        // 判断是否是数据库中的原始格式，如 "5w-10w"
        if (consumeRange.matches("\\d+w-\\d+w")) {
            wrapper.eq(QianchuanMaterialVideo::getConsumeRange, consumeRange);
            return;
        }

        // 处理标准范围
        List<String> possibleRanges = new ArrayList<>();

        switch (consumeRange) {
        case "0-10w":
            // 匹配所有 0w 到 10w 之间的范围
            // 0w-5w 范围细分为 0w-1w, 1w-2w, 2w-3w, 3w-4w, 4w-5w
            possibleRanges.addAll(Arrays.asList("0w-1w", "1w-2w", "2w-3w", "3w-4w", "4w-5w", "5w-10w"));
            break;
        case "10-20w":
            possibleRanges.addAll(Arrays.asList("10w-15w", "15w-20w"));
            break;
        case "20-30w":
            possibleRanges.addAll(Arrays.asList("20w-25w", "25w-30w"));
            break;
        case "30-40w":
            possibleRanges.addAll(Arrays.asList("30w-35w", "35w-40w"));
            break;
        case "40-50w":
            possibleRanges.addAll(Arrays.asList("40w-45w", "45w-50w"));
            break;
        case "50-60w":
            possibleRanges.addAll(Arrays.asList("50w-55w", "55w-60w"));
            break;
        case "60-70w":
            possibleRanges.addAll(Arrays.asList("60w-65w", "65w-70w"));
            break;
        case "70-80w":
            possibleRanges.addAll(Arrays.asList("70w-75w", "75w-80w"));
            break;
        case "80-90w":
            possibleRanges.addAll(Arrays.asList("80w-85w", "85w-90w"));
            break;
        case "90-100w":
            possibleRanges.addAll(Arrays.asList("90w-95w", "95w-100w"));
            break;
        case "100w以上":
            // 匹配所有大于等于 100w 的范围，通过排除所有小于100w的范围
            List<String> lessThan100wRanges = Arrays.asList("0w-1w", "1w-2w", "2w-3w", "3w-4w", "4w-5w", "5w-10w",
                    "10w-15w", "15w-20w", "20w-25w", "25w-30w", "30w-35w", "35w-40w", "40w-45w", "45w-50w", "50w-55w",
                    "55w-60w", "60w-65w", "65w-70w", "70w-75w", "75w-80w", "80w-85w", "85w-90w", "90w-95w", "95w-100w");
            wrapper.notIn(QianchuanMaterialVideo::getConsumeRange, lessThan100wRanges);
            return;
        default:
            // 如果没有匹配的范围，直接使用等于条件
            wrapper.eq(QianchuanMaterialVideo::getConsumeRange, consumeRange);
            return;
        }

        // 使用 OR 条件组合多个可能的范围
        if (!possibleRanges.isEmpty()) {
            wrapper.in(QianchuanMaterialVideo::getConsumeRange, possibleRanges);
        }
    }

    @Override
    public List<QianchuanMaterialVideo> getRecommenListRequest(RecommenListRequest request) {
        LambdaQueryWrapper<QianchuanMaterialVideo> wrapper = new LambdaQueryWrapper<>();

        // 默认查询条件：未删除且分析状态为成功(2)
        wrapper.eq(QianchuanMaterialVideo::getIsDeleted, 0);
        wrapper.eq(QianchuanMaterialVideo::getAnalysisStatus, 2);

        // 时间范围筛选
        if (request.getStartTime() != null) {
            wrapper.ge(QianchuanMaterialVideo::getPublishTime, request.getStartTime());
        }

        if (request.getEndTime() != null) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(request.getEndTime());
            calendar.set(Calendar.HOUR_OF_DAY, 23);
            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.SECOND, 59);
            calendar.set(Calendar.MILLISECOND, 999);
            wrapper.le(QianchuanMaterialVideo::getPublishTime, calendar.getTime());
        }

        // 默认查询最近8天数据
        if (request.getStartTime() == null && request.getEndTime() == null) {
            LocalDateTime eightDaysAgo = LocalDateTime.now().minusDays(8);
            wrapper.ge(QianchuanMaterialVideo::getPublishTime, eightDaysAgo);
        }

        // 关键词模糊查询
        if (StringUtils.isNotBlank(request.getKeyword())) {
            if ("0".equals(request.getType())) {
                // 综合查询：标题、口播文案、品牌、产品名称
                wrapper.and(w -> w.like(QianchuanMaterialVideo::getTitle, request.getKeyword()).or()
                        .like(QianchuanMaterialVideo::getKwVideoContent, request.getKeyword()).or()
                        .like(QianchuanMaterialVideo::getBrand, request.getKeyword()).or()
                        .like(QianchuanMaterialVideo::getProductName, request.getKeyword()));
            } else if ("1".equals(request.getType())) {
                // 品牌查询
                wrapper.and(w -> w.like(QianchuanMaterialVideo::getBrand, request.getKeyword()));
            } else if ("2".equals(request.getType())) {
                // 产品查询
                wrapper.and(w -> w.like(QianchuanMaterialVideo::getProductName, request.getKeyword()));
            } else if ("3".equals(request.getType())) {
                // 标题查询
                wrapper.and(w -> w.like(QianchuanMaterialVideo::getTitle, request.getKeyword()));
            } else if ("4".equals(request.getType())) {
                // 口播文案查询
                wrapper.and(w -> w.like(QianchuanMaterialVideo::getKwVideoContent, request.getKeyword()));
            }
        }

        // 添加按 consume_range_weight 倒序排序
        wrapper.orderByDesc(QianchuanMaterialVideo::getConsumeRangeWeight);

        return list(wrapper);
    }

    @Override
    public List<BrandProductRecommendVO> getBrandProductRecommendList(RecommenListRequest request) {
        if (StringUtils.isBlank(request.getKeyword())) {
            return Collections.emptyList();
        }

        // 如果没有指定时间范围，默认查询最近1个月的数据
        if (request.getStartTime() == null) {
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.MONTH, -1); // 一个月前
            request.setStartTime(calendar.getTime());
        }
        if (request.getEndTime() == null) {
            request.setEndTime(new Date()); // 当前时间
        }

        // 根据查询类型处理
        String type = request.getType();
        if ("1".equals(type)) {
            // 只查询品牌，取前10个
            return getBrandRecommendationsFromDB(request.getKeyword(), request.getStartTime(), request.getEndTime(),
                    10);
        } else if ("2".equals(type)) {
            // 只查询产品，取前10个
            return getProductRecommendationsFromDB(request.getKeyword(), request.getStartTime(), request.getEndTime(),
                    10);
        } else {
            // 综合查询，品牌和产品同时查询，按数量排序
            return getCombinedRecommendationsFromDB(request.getKeyword(), request.getStartTime(), request.getEndTime());
        }
    }


}
