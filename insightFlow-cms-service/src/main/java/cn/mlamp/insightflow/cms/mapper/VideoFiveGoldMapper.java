package cn.mlamp.insightflow.cms.mapper;

import cn.mlamp.insightflow.cms.entity.CmsVideoFiveGold;
import cn.mlamp.insightflow.cms.entity.CmsVideoThreeGoldRelation;
import cn.mlamp.insightflow.cms.model.query.VideoFiveGoldDetailRequest;
import cn.mlamp.insightflow.cms.model.vo.VideoFiveGoldDetailVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: husuper
 * @CreateTime: 2025-03-21
 */
@Mapper
public interface VideoFiveGoldMapper extends BaseMapper<CmsVideoFiveGold> {


}
