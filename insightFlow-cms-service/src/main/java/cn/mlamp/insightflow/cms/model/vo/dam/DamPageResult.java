package cn.mlamp.insightflow.cms.model.vo.dam;

import java.util.Collections;
import java.util.List;
import java.util.function.Function;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * DAM分页结果类
 */
@Data
@Schema(description = "分页结果")
public class DamPageResult<T> {

    @Schema(description = "总记录数")
    private Long total;

    @Schema(description = "数据列表")
    private List<T> records;

    @Schema(description = "当前页码")
    private Integer current;

    @Schema(description = "每页大小")
    private Integer size;

    public static <T, R> DamPageResult<R> from(Page<T> page, Function<T, R> mapping) {
        return of(page.getTotal(), page.getRecords().stream().map(mapping).toList(), (int) page.getCurrent(),
                (int) page.getSize());
    }

    /**
     * 创建分页结果
     *
     * @param total   总记录数
     * @param records 数据列表
     * @param current 当前页码
     * @param size    每页大小
     * @param <T>     数据类型
     * @return 分页结果
     */
    public static <T> DamPageResult<T> of(long total, List<T> records, int current, int size) {
        DamPageResult<T> result = new DamPageResult<>();
        result.setTotal(total);
        result.setRecords(records);
        result.setCurrent(current);
        result.setSize(size);
        return result;
    }

    public static <T> DamPageResult<T> emptyResult() {
        return of(0L, Collections.emptyList(), 1, 0);
    }
}