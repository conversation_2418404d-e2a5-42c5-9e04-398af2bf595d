package cn.mlamp.insightflow.cms.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.mlamp.insightflow.cms.config.UserContext;
import cn.mlamp.insightflow.cms.entity.BaseEntity;
import cn.mlamp.insightflow.cms.entity.CmsVideoInfo;
import cn.mlamp.insightflow.cms.entity.CmsVideoResult;
import cn.mlamp.insightflow.cms.enums.VideoResultTypeEnum;
import cn.mlamp.insightflow.cms.exception.BusinessException;
import cn.mlamp.insightflow.cms.mapper.VideorResultMapper;
import cn.mlamp.insightflow.cms.model.query.VideoResultRequest;
import cn.mlamp.insightflow.cms.model.vo.VideoInfoSceneSplitExcelVO;
import cn.mlamp.insightflow.cms.model.vo.VideoResultVO;
import cn.mlamp.insightflow.cms.service.IVideoInfoService;
import cn.mlamp.insightflow.cms.service.IVideoResultService;
import cn.mlamp.insightflow.cms.strategy.excel.VideoResultExcelStrategy;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: husuper
 * @CreateTime: 2025-03-21
 */
@Service
public class VideoResultServiceImpl extends ServiceImpl<VideorResultMapper, CmsVideoResult> implements IVideoResultService {

    @Autowired
    @Lazy
    private IVideoInfoService videoInfoService;

    @Autowired
    private VideorResultMapper videorResultMapper;


    @Override
    public List<String> getVideoResult(List<Integer> resultIds) {
        return this.list(new LambdaUpdateWrapper<CmsVideoResult>()
                        .in(CmsVideoResult::getId, resultIds)).stream()
                .filter(result -> result != null && StrUtil.isNotBlank(result.getData()))
                .map(CmsVideoResult::getData)
                .toList();
    }

    @Override
    public List<CmsVideoResult> getSceneSplitResultByVideoId(Integer videoId) {
        List<CmsVideoResult> cmsVideoResults = videorResultMapper.selectList(new LambdaQueryWrapper<CmsVideoResult>()
                .eq(CmsVideoResult::getVideoId,videoId)
                .eq(CmsVideoResult::getType, VideoResultTypeEnum.SCENE_SPLIT2.getCode())
                .eq(BaseEntity::getIsDeleted,0));

        Integer userId= UserContext.getUserId();
        Integer tenantId=UserContext.getTenantId();


        //替换分镜的用户版本
        List<CmsVideoResult> userVideoResults = videorResultMapper.selectList(new LambdaQueryWrapper<CmsVideoResult>()
                .eq(CmsVideoResult::getVideoId,videoId)
                .eq(CmsVideoResult::getType,VideoResultTypeEnum.USER_SCENE_SPLIT2.getCode())
                .eq(CmsVideoResult::getUserId,userId)
                .eq(CmsVideoResult::getTenantId,tenantId));

        if(userVideoResults==null||userVideoResults.isEmpty()){
            return cmsVideoResults;
        }

        for (CmsVideoResult cmsVideoResult : cmsVideoResults){
            for (CmsVideoResult userVideoResult : userVideoResults){
                //替换用户的分镜数据
                if(cmsVideoResult.getIndex().equals(userVideoResult.getIndex())){
                    cmsVideoResult.setData(userVideoResult.getData());
                }
            }
        }
        return cmsVideoResults;

    }

    @Override
    public Map<String, String> getVideoAsr5Results(List<Integer> videoIds) {
        if (videoIds == null || videoIds.isEmpty()) {
            return new HashMap<>();
        }

        // 1. 查询视频信息，获取esId
        List<CmsVideoInfo> videoInfoList = videoInfoService.listByIds(videoIds);
        if (videoInfoList.isEmpty()) {
            return new HashMap<>();
        }

        // 创建videoId到esId的映射
        Map<Integer, String> videoIdToEsIdMap = videoInfoList.stream()
                .collect(Collectors.toMap(CmsVideoInfo::getId, CmsVideoInfo::getEsId));

        // 2. 查询视频的ASR 5秒结果
        LambdaQueryWrapper<CmsVideoResult> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CmsVideoResult::getVideoId, videoIds).eq(CmsVideoResult::getType,
                VideoResultTypeEnum.ASR5.getCode());

        List<CmsVideoResult> resultList = this.list(queryWrapper);
        if (resultList.isEmpty()) {
            return new HashMap<>();
        }

        // 3. 解析data字段中的JSON字符串，提取asr5值
        Map<String, String> esIdToAsr5Map = new HashMap<>();
        for (CmsVideoResult result : resultList) {
            Integer videoId = result.getVideoId();
            String esId = videoIdToEsIdMap.get(videoId);
            if (esId == null || StrUtil.isBlank(result.getData())) {
                continue;
            }

            try {
                JSONObject jsonObject = JSON.parseObject(result.getData());
                String asr5 = jsonObject.getString("asr5");
                if (StrUtil.isNotBlank(asr5)) {
                    esIdToAsr5Map.put(esId, asr5);
                }
            } catch (Exception e) {
                // 忽略解析异常
                continue;
            }
        }

        return esIdToAsr5Map;
    }

    @Override
    public Boolean update(List<VideoResultRequest> request) {
        boolean  flag=true;
        for (VideoResultRequest videoResultRequest : request){
            if(!update(videoResultRequest)){
                flag=false;
            }
        }
        return flag;
    }



    public Boolean update(VideoResultRequest request) {
        if(request.getVideoInfoReusltId()==null &&
                (request.getVideoInfoReusltIds()==null||request.getVideoInfoReusltIds().isEmpty())){
            throw new BusinessException("视频分析结果Id或视频分析结果Ids不能同时为空");
        }
        List<Integer> list=new ArrayList<>();
        if(request.getVideoInfoReusltIds()!=null && !request.getVideoInfoReusltIds().isEmpty()){
            list=request.getVideoInfoReusltIds();
        }else{
            list.add(request.getVideoInfoReusltId());
        }

        for(Integer id:list){
            CmsVideoResult cmsVideoResult =  videorResultMapper.selectById(id);
            if(cmsVideoResult==null){
                throw new BusinessException("没有查询到视频分镜信息");
            }
            if(cmsVideoResult.getType()==VideoResultTypeEnum.SCENE_SPLIT2.getCode()){
                //需要新增
                handleAdd(cmsVideoResult,request.getSceneDecoding());
            }else if(cmsVideoResult.getType()==VideoResultTypeEnum.USER_SCENE_SPLIT2.getCode()){
                //需要更新
                handleUpdate(cmsVideoResult,request.getSceneDecoding());
            }else{
                throw new BusinessException("只有分镜数据才可以更新");
            }
        }

        return true;
    }

    private void handleAdd(CmsVideoResult cmsVideoResult,Map<String,Object> sceneDecoding){
        Integer userId= UserContext.getUserId();
        Integer tenantId=UserContext.getTenantId();
        cmsVideoResult.setId(null);
        cmsVideoResult.setType(VideoResultTypeEnum.USER_SCENE_SPLIT2.getCode());
        cmsVideoResult.setCreateTime(null);
        cmsVideoResult.setUpdateTime(null);
        cmsVideoResult.setUserId(userId);
        cmsVideoResult.setTenantId(tenantId);

        Map<String,Object> map=JSONObject.parseObject(cmsVideoResult.getData(),Map.class);
        for (String key:sceneDecoding.keySet()){
            map.put(key,sceneDecoding.get(key));
        }
        cmsVideoResult.setData(JSONObject.toJSONString(map));
        this.save(cmsVideoResult);
    }

    private void handleUpdate(CmsVideoResult cmsVideoResult,Map<String,Object> sceneDecoding){
        Integer userId= UserContext.getUserId();
        Integer tenantId=UserContext.getTenantId();
        if(!userId.equals(cmsVideoResult.getUserId())){
            throw new BusinessException("只能更新自己的数据");
        }
        if(!tenantId.equals(cmsVideoResult.getTenantId())){
            throw new BusinessException("只能更新自己租户的数据");
        }

        Map<String,Object> map=JSONObject.parseObject(cmsVideoResult.getData(),Map.class);
        for (String key:sceneDecoding.keySet()){
            map.put(key,sceneDecoding.get(key));
        }

        cmsVideoResult.setData(JSONObject.toJSONString(map));
        this.updateById(cmsVideoResult);
    }



    @Override
    public Boolean back(Integer videoId) {
        CmsVideoInfo   cmsVideoInfo= videoInfoService.getOne(new LambdaUpdateWrapper<CmsVideoInfo>().eq(CmsVideoInfo::getId,videoId));
        if(cmsVideoInfo==null){
            throw new BusinessException("没查到分析信息");
        }
        Integer userId= UserContext.getUserId();
        Integer tenantId=UserContext.getTenantId();
        LambdaUpdateWrapper <CmsVideoResult> updateWrapper = new LambdaUpdateWrapper<CmsVideoResult>();
        updateWrapper.eq(CmsVideoResult::getVideoId,videoId)
                .eq(CmsVideoResult::getType,VideoResultTypeEnum.USER_SCENE_SPLIT2.getCode())
                .eq(CmsVideoResult::getUserId,userId)
                .eq(CmsVideoResult::getTenantId,tenantId)
                .set(CmsVideoResult::getIsDeleted,1);

       int count = videorResultMapper.update(updateWrapper);
        if(count<=0){
            return false;
        }
        return true;
    }

    @Override
    public void exportReusltDetail(Integer videoId, HttpServletResponse response) {
        List<CmsVideoResult> cmsVideoResults = videorResultMapper.selectList(new LambdaQueryWrapper<CmsVideoResult>()
                .eq(CmsVideoResult::getVideoId,videoId)
                .eq(CmsVideoResult::getType, VideoResultTypeEnum.SCENE_SPLIT2.getCode())
                .eq(BaseEntity::getIsDeleted,0));

        Integer userId= UserContext.getUserId();
        Integer tenantId=UserContext.getTenantId();

        List<Map<String,Object>> sceneDecodings=new ArrayList<>();
        for (CmsVideoResult cmsVideoResult : cmsVideoResults){
            Map<String,Object> sceneDecoding=JSONObject.parseObject(cmsVideoResult.getData(), Map.class);
            sceneDecodings.add(sceneDecoding);
        }

        //替换分镜的用户版本
        List<CmsVideoResult> userVideoResults = videorResultMapper.selectList(new LambdaQueryWrapper<CmsVideoResult>()
                .eq(CmsVideoResult::getVideoId,videoId)
                .eq(CmsVideoResult::getType,VideoResultTypeEnum.USER_SCENE_SPLIT2.getCode())
                .eq(CmsVideoResult::getUserId,userId)
                .eq(CmsVideoResult::getTenantId,tenantId));

        for (CmsVideoResult userVideoResult : userVideoResults){
            //替换用户的分镜数据
            for (Map<String,Object> sceneDecoding : sceneDecodings) {
                if(sceneDecoding.get("index").equals(userVideoResult.getIndex())){
                    Map<String,Object> userSceneDecoding=JSONObject.parseObject(userVideoResult.getData(), Map.class);
                    for (String key:userSceneDecoding.keySet()){
                        sceneDecoding.put(key,userSceneDecoding.get(key));
                    }
                    break;
                }
            }
        }
        List<VideoInfoSceneSplitExcelVO> dataListMap = new ArrayList<>();
        int segmentIndex = 0;
        String previousDialogue = null;
        for (int i = 0; i < sceneDecodings.size(); i++) {
            Map<String, Object> map = sceneDecodings.get(i);
            VideoInfoSceneSplitExcelVO vo = JSONObject.parseObject(JSONObject.toJSONString(map), VideoInfoSceneSplitExcelVO.class);
            String currentDialogue = vo.get台词();
            if(currentDialogue == null || currentDialogue.equals("")){
                segmentIndex++;
                vo.set片段("片段" + segmentIndex);
            }else if (!currentDialogue.equals(previousDialogue)) {
                segmentIndex++;
                vo.set片段("片段" + segmentIndex);
            } else {
                vo.set片段("片段" + segmentIndex); // 保持片段列为空，以便在合并单元格时只显示一次
            }
            previousDialogue = currentDialogue;
            if (vo.getStart() != null && vo.getEnd() != null) {
                try {
                    vo.setDuration(formatTimestamp(subtractPositiveIntegers(vo.getEnd(),vo.getStart())));
                } catch (NumberFormatException e) {
                    vo.setDuration("00:00"); // 异常时默认值
                }
            } else {
                vo.setDuration("00:00"); // 如果 start 或 end 为空，默认值
            }
            // 格式化时间戳
            if (vo.getStart() != null) {
                vo.setStart(formatTimestamp(vo.getStart()));
            }
            if (vo.getEnd() != null) {
                vo.setEnd(formatTimestamp(vo.getEnd()));
            }


            dataListMap.add(vo);
        }
        List<VideoInfoSceneSplitExcelVO> sortedList = dataListMap.stream()
                .sorted(Comparator.comparing(VideoInfoSceneSplitExcelVO::getStart))
                .collect(Collectors.toList());
        try {
            // 设置响应头信息
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Pragma", "No-cache");
            response.setHeader("Cache-Control", "No-cache");
            response.setDateHeader("Expires", 0);
            response.setContentType("application/vnd.ms-excel");
            response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode("分镜数据.xlsx", StandardCharsets.UTF_8));
            int startRowIndex = 1; // 假设数据从第2行开始（索引为1）
            int[] mergeColumns = {0, 1}; // 需要合并的列索引，例如：0表示“片段”列，1表示“台词”列

            VideoResultExcelStrategy mergeStrategy = new VideoResultExcelStrategy(startRowIndex, mergeColumns);
            ServletOutputStream outputStream = response.getOutputStream();
            EasyExcel.write(outputStream, VideoInfoSceneSplitExcelVO.class)
                    .registerWriteHandler(mergeStrategy)
                    .inMemory(true)
                    .autoCloseStream(true)
                    .sheet("分镜数据")
                    .doWrite(sortedList);
        }catch (Exception e){
            log.error("导出失败",e);
            throw new BusinessException("导出失败");
        }
    }

    // 将 "mm:ss" 解析为秒数
    private long parseTimeToSeconds(String time) {
        String[] parts = time.split(":");
        if (parts.length != 2) {
            throw new NumberFormatException("Invalid time format");
        }
        long minutes = Long.parseLong(parts[0]);
        long seconds = Long.parseLong(parts[1]);
        return minutes * 60 + seconds;
    }

    // 将秒数格式化为 "mm:ss"
    private String formatDuration(long seconds) {
        long minutes = seconds / 60;
        seconds = seconds % 60;
        return String.format("%02d:%02d", minutes, seconds);
    }

    // 时间戳格式化工具方法
    private String formatTimestamp(String timestampMillis) {
        try {
            long millis = Long.parseLong(timestampMillis);
            long seconds = millis / 1000;
            return String.format("00:%02d", seconds % 60);
        } catch (NumberFormatException e) {
            return "00:00"; // 默认值
        }
    }

    /**
     * 正整数字符串相减（返回字符串结果）
     * @param numStr1 被减数字符串（如"123"）
     * @param numStr2 减数字符串（如"45"）
     * @return 相减结果字符串（若输入无效则返回"0"）
     */
    public static String subtractPositiveIntegers(String numStr1, String numStr2) {
        try {
            // 转换为长整型（支持大数）
            long num1 = Long.parseLong(numStr1);
            long num2 = Long.parseLong(numStr2);

            // 执行减法
            long result = num1 - num2;

            // 处理负数结果（根据业务需求可调整）
            if (result < 0) {
                return "0";
            }

            return String.valueOf(result);
        } catch (NumberFormatException e) {
            return "0"; // 非数字输入时返回默认值
        }
    }


}
