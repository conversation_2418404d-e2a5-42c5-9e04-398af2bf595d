package cn.mlamp.insightflow.cms.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.mlamp.insightflow.cms.common.file.IS3FlowService;
import cn.mlamp.insightflow.cms.common.resp.RespCode;
import cn.mlamp.insightflow.cms.config.UserContext;
import cn.mlamp.insightflow.cms.config.properties.ObjectStorageFlowProperties;
import cn.mlamp.insightflow.cms.config.properties.UploadLinkProperties;
import cn.mlamp.insightflow.cms.constant.FileConstant;
import cn.mlamp.insightflow.cms.entity.CmsDocumentInfo;
import cn.mlamp.insightflow.cms.entity.CmsPullTaskDedupedData;
import cn.mlamp.insightflow.cms.entity.CmsVideoInfo;
import cn.mlamp.insightflow.cms.entity.CmsVideoResultDetail;
import cn.mlamp.insightflow.cms.enums.*;
import cn.mlamp.insightflow.cms.exception.BusinessException;
import cn.mlamp.insightflow.cms.model.dto.DeepanaTikTokVideoDTO;
import cn.mlamp.insightflow.cms.model.dto.DocumentExtraInfoDTO;
import cn.mlamp.insightflow.cms.model.query.*;
import cn.mlamp.insightflow.cms.model.vo.DocTaskUploadVO;
import cn.mlamp.insightflow.cms.model.vo.LinkFileVO;
import cn.mlamp.insightflow.cms.model.vo.TaskUploadVO;
import cn.mlamp.insightflow.cms.service.*;
import cn.mlamp.insightflow.cms.service.webflux.WebClientService;
import cn.mlamp.insightflow.cms.strategy.video.create.AnalysisVideoStrategyMap;
import cn.mlamp.insightflow.cms.util.FilePathBuilder;
import cn.mlamp.insightflow.cms.util.PageUtils;
import cn.mlamp.insightflow.cms.util.PathUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import jodd.io.FileNameUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
public class FileServiceImpl implements FileService {

    @Resource
    private TenantTokenService tenantTokenService;

    @Autowired
    private IS3FlowService ossService;

    @Resource(name = "cmsS3FlowService")
    private IS3FlowService cmsS3FlowService;

    @Resource
    private UploadLinkProperties uploadLinkProperties;

    @Resource
    private WebClientService webClientService;

    @Resource
    private ICmsDocumentInfoService documentInfoService;

    @Resource
    private ICmsTaskInfoService taskInfoService;

    @Resource
    private IVideoInfoService videoInfoService;

    @Resource
    private IVideoResultDetailService videoResultDetailService;

    @Resource
    private CmsPullTaskDedupedDataService pullTaskDedupedDataService;

    @Resource
    private ObjectStorageFlowProperties objectStorageFlowProperties;

    @Override
    public TaskUploadVO getUploadSignatureUrl(TaskUploadRequest request) {
        String fileName = request.getFilename();
        Integer type = request.getType();
        String bucket = request.getBucket();
        String fileUrl = request.getFileUrl();

        String ossPath;

        // 统一生成 ossPath，根据不同类型
        if (type != null) {
            switch (type) {
                case 1:
                    // 图片上传路径
                    ossPath = FilePathBuilder.generateImageOssPath(FileConstant.OSS_PATH_PIC_PREFIX,
                            PathUtil.pathToSuffix(fileName));
                    break;
                case 2:
                    // 校验文件格式
                    List<String> allowFileExt = Stream.of(VideoTypeEnum.AVI.getName(), VideoTypeEnum.MKV.getName(),
                            VideoTypeEnum.FLV.getName(), VideoTypeEnum.MP4.getName()).toList();
                    String extension = FileNameUtil.getExtension(fileName).toLowerCase();
                    if (!allowFileExt.contains(extension)) {
                        String errMessage = StrUtil.format("{} 暂不支持该类型的文件", fileName);
                        throw new BusinessException(RespCode.BAD_REQUEST, errMessage);
                    }
                    if ("video-decode".equals(bucket)) {
                        if ("/".equals(fileUrl)) {
                            fileUrl = "";
                        } else {
                            if (fileUrl.startsWith("/")) {
                                fileUrl = fileUrl.substring(1);
                            }
                            if (!fileUrl.endsWith("/")) {
                                fileUrl += "/";
                            }
                        }
                        Integer tenantId = UserContext.getTenantId();
                        ossPath = "videos/" + tenantId + "/" + fileUrl + fileName;
                    } else {
                        ossPath = FilePathBuilder.generateImageOssPath(FileConstant.OSS_PATH_PIC_PREFIX,
                                PathUtil.pathToSuffix(fileName));
                    }
                    break;
                default:
                    // 默认Excel上传路径
                    ossPath = FilePathBuilder.generateImageOssPath(FileConstant.OSS_PATH_EXCEL_PREFIX,
                            PathUtil.pathToSuffix(fileName));
            }
        } else {
            // 如果没有指定类型，默认使用Excel路径
            ossPath = FilePathBuilder.generateImageOssPath(FileConstant.OSS_PATH_EXCEL_PREFIX,
                    PathUtil.pathToSuffix(fileName));
        }

//        if ("video-decode".equals(bucket)) {
//            return new TaskUploadVO(ossPath,
//                    videoDecodeS3FlowService.uploadPresidedUrl(ossPath, FileConstant.FILE_EXPIRE_TIME).toString(),
//                    fileName);
//        }
        if ("ai-pc-cms".equals(bucket)) {
            return new TaskUploadVO(ossPath,
                    cmsS3FlowService.uploadPresidedUrl(ossPath, FileConstant.FILE_EXPIRE_TIME).toString(),
                    fileName);
        }
        // 获取上传签名的 URL
        URL url = ossService.uploadPresidedUrl(ossPath, FileConstant.FILE_EXPIRE_TIME);

        return new TaskUploadVO(ossPath, url.toString(), fileName);
    }

    @Override
    public List<String> getDownloadSignatureUrl(List<String> ossIds) {
        final List<String> downloadSignatureUrl = new ArrayList<>(ossIds.size());
        for (final String ossId : ossIds) {
            final URL url;
            try {
                url = ossService.downloadPresignedUrl(objectStorageFlowProperties.getCms().getBucketName(), ossId,
                        FileConstant.FILE_EXPIRE_TIME, 0);
            } catch (Exception e) {
                log.error("获取其他文件 其OSS路径为 {} 的访问链接失败", ossId, e);
                continue;
            }
            downloadSignatureUrl.add(url.toString());
        }

        return downloadSignatureUrl;
    }

    @Override
    public String getPicDownloadSignatureUrl(String picOssId) {
        final URL url;
        try {
//           return new StringBuilder("https://ai-pc-test.oss-cn-beijing.aliyuncs.com/").append(picOssId).toString();
            url = ossService.downloadPresignedUrl(objectStorageFlowProperties.getCms().getBucketName(), picOssId, 14400, 0);
        } catch (Exception e) {
            log.error("获取其他文件 其OSS路径为 {} 的访问链接失败", picOssId, e);
            throw new BusinessException("签名失败");
        }
        return url.toString();
    }


//    public OpenUrlDTO copeToOpenUrl(String picOssId) {
//        String bucketName = objectStorageFlowProperties.getPic().getBucketName();
//        String openOssId = new StringBuilder(s3FlowConfig.getProfile()).append("/temp/").append(picOssId).toString();
//        CopyObjectRequest copyObjRequest = new CopyObjectRequest(bucketName, picOssId, bucketName, openOssId);
//        ossService.getClient().copyObject(copyObjRequest);
//        OpenUrlDTO openUrlDTO = new OpenUrlDTO();
//        openUrlDTO.setOpenUrl(new StringBuilder("https://mos-ex.intra.mlamp.cn/ai-pc-image/").append(openOssId).toString());
//        openUrlDTO.setOpenOssId(openOssId);
//        return openUrlDTO;
//    }


    @Override
    public List<LinkFileVO> uploadLinkCheck(LinkUploadRequest request) {
        List<LinkFileVO> result = new ArrayList<>();
        Map<String, String> urlMap = new HashMap<>();
        uploadLinkProperties.getDomains().forEach(domain -> {
            domain.getUrls().forEach(url -> urlMap.put(url, domain.getIconKey()));
        });
        for (String link : request.getUrls()) {
            LinkFileVO vo = new LinkFileVO();
            vo.setUrl(link);

            // 格式校验与链接标准化
            link = processLink(link);
            if (link == null) {
                vo.setErrorMsg("链接格式有误");
                result.add(vo);
                continue;
            }
            String iconKey = getIconKey(urlMap, link);

            // 3.调用py获取标题信息
            try {
                var response = webClientService.getTikTokTitle(link).block();
                if (response != null && response.isSuccess()) {
                    vo.setTitle(response.getData());
                } else {
                    vo.setErrorMsg("获取不到该链接内容，请稍后重试");
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                vo.setErrorMsg("暂不支持获取该链接内容，请修改后重试");
            }
            // 4.如果调用成功，再根据map获取图标iconKey
            if (StrUtil.isEmpty(vo.getErrorMsg())) {
                vo.setIconKey(iconKey);
            }
            result.add(vo);
        }
        return result;
    }

    public String getIconKey(String link) {
        Map<String, String> urlMap = new HashMap<>();
        uploadLinkProperties.getDomains().forEach(domain -> {
            domain.getUrls().forEach(url -> urlMap.put(url, domain.getIconKey()));
        });
        return getIconKey(urlMap, link);
    }

    // 链接处理：格式校验 + 转换为标准格式
    private String processLink(String rawLink) {
        // 1. 从文本中提取第一个有效的抖音链接（如果输入是文本）
        String extractedLink = extractFirstDouyinLink(rawLink);
        if (extractedLink == null) {
            return null; // 未找到有效链接
        }

        // 2. 转换特定格式
        String processedLink = extractedLink;
        if (processedLink.contains("discover?modal_id=")) {
            processedLink = convertDiscoverLink(processedLink);
        } else if (processedLink.contains("v.douyin.com")) {
            processedLink = convertShortLink(processedLink);
        }

        // 3. 正则匹配最终格式
        String regex = "https?://(?:www\\.|v\\.)?douyin\\.com/(?:video/\\d+|\\w+/?|discover\\?.*modal_id=\\d+)";
        return Pattern.matches(regex, processedLink) ? processedLink : null;
    }

    //  从文本中提取第一个有效的抖音链接
    private String extractFirstDouyinLink(String text) {
        // 匹配抖音短链、长链、discover链接
        String regex = "(https?://(?:www\\.|v\\.)?douyin\\.com/(?:video/\\d+|discover\\?.*modal_id=\\d+|\\w+/?))";
        Matcher matcher = Pattern.compile(regex).matcher(text);
        if (matcher.find()) {
            // 清理链接末尾的无效字符（如标点、空格）
            String rawLink = matcher.group(1).replaceAll("[^\\w:/?#&=.]", "");
            return rawLink;
        }
        return null; // 未找到匹配链接
    }

    // 转换 discover 链接
    private String convertDiscoverLink(String link) {
        try {
            String modalId = link.split("modal_id=")[1].split("&")[0];
            return "https://www.douyin.com/video/" + modalId;
        } catch (Exception e) {
            return null;
        }
    }

    // 转换短链
    private String convertShortLink(String shortLink) {
        try {
            String longUrl = resolveShortUrl(shortLink);
            String videoId = extractVideoId(longUrl);
            return buildStandardUrl(videoId);
        } catch (Exception e) {
            log.error("短链解析失败: {}", shortLink);
            return null;
        }
    }

    //    private Boolean checkLinkFormat(String link) {
//        return link.startsWith("http://") || link.startsWith("https://");
//    }
    // 解析短链重定向
    public static String resolveShortUrl(String shortUrl) {
        try {
            URL url = new URL(shortUrl);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setInstanceFollowRedirects(false);
            conn.setConnectTimeout(5000);
            conn.setRequestMethod("HEAD");

            int status = conn.getResponseCode();
            if (status == HttpURLConnection.HTTP_MOVED_TEMP
                    || status == HttpURLConnection.HTTP_MOVED_PERM) {
                String location = conn.getHeaderField("Location");
                return resolveShortUrl(location);
            }
            return shortUrl;
        } catch (Exception e) {
            return null;
        }
    }

    // 提取视频ID
    public static String extractVideoId(String longUrl) {
        String regex = "/video/(\\d+)";
        Matcher matcher = Pattern.compile(regex).matcher(longUrl);
        return matcher.find() ? matcher.group(1) : null;
    }

    // 构造标准链接
    public static String buildStandardUrl(String videoId) {
        return "https://www.douyin.com/video/" + videoId;
    }

    private String getIconKey(Map<String, String> urlMap, String link) {
        String iconKey = "";
        for (Map.Entry<String, String> entry : urlMap.entrySet()) {
            if (link.contains(entry.getKey())) {
                iconKey = entry.getValue();
                break;
            }
        }
        return iconKey;
    }


    @Override
    public List<CmsVideoInfo> batchUploadVideo(BatchUploadRequest request) {
        // 上传之前判断token点数
        if (tenantTokenService.checkBalance(request.getTenantId()) < 10 * request.getDocInfos().size()) {
            throw new RuntimeException("租户点数不足");
        }
        // 存储到docInfo表
        var sourceType = request.getSourceType();
        List<CmsDocumentInfo> documentInfos = new ArrayList<>();
        for (var docInfo : request.getDocInfos()) {
            var documentInfo = new CmsDocumentInfo();
            documentInfo.setDocName(docInfo.getName());
            documentInfo.setSourceType(sourceType.name());
            documentInfo.setUserId(request.getUserId());
            documentInfo.setTenantId(request.getTenantId());
            switch (sourceType) {
                case from_local -> {
                    String extension = FileNameUtil.getExtension(documentInfo.getDocName());
                    documentInfo.setDocType(extension);
                    documentInfo.setObjId(docInfo.getObjOssId());
                    documentInfo.setSize(docInfo.getSize());
                    documentInfo.setStatus(DocumentStatusEnum.WAITING.name());
                    documentInfo.setBucketName(objectStorageFlowProperties.getCms().getBucketName());
                }
                case from_url -> {
                    documentInfo.setLink(docInfo.getUrl());
                    documentInfo.setStatus(DocumentStatusEnum.PROCESSING.name());
                    documentInfo.setBucketName(objectStorageFlowProperties.getCms().getBucketName());
                }
            }
            documentInfo.setEsId(UUID.randomUUID().toString().replace("-", ""));
            documentInfos.add(documentInfo);
        }
        documentInfoService.saveBatch(documentInfos);

        // 存储到cms_video_info表, 等待处理
        List<CmsVideoInfo> videoInfos = getCmsVideoInfos(request, documentInfos);
        videoInfoService.saveBatch(videoInfos);

        return videoInfos;
    }

    private List<CmsVideoInfo> getCmsVideoInfos(BatchUploadRequest request, List<CmsDocumentInfo> documentInfos) {
        List<CmsVideoInfo> videoInfos = new ArrayList<>();
        for (var documentInfo : documentInfos) {
            var videoInfo = new CmsVideoInfo();
            videoInfo.setType(4);
            videoInfo.setEsId(documentInfo.getEsId());
            videoInfo.setStatus(VideoInfoStatusEnum.WAITING.getCode());
            videoInfo.setSourceFileId(documentInfo.getId());
            videoInfo.setUserId(request.getUserId());
            videoInfo.setTenantId(request.getTenantId());
            videoInfos.add(videoInfo);
        }
        return videoInfos;
    }

    @Override
    @Async("commonTaskExecutor")
    public void decodeUploadVideo(List<CmsVideoInfo> videoInfos, UploadSourceTypeEnum sourceType) {
        for (var videoInfo : videoInfos) {
            // 链接上传首先要调用第三方供应商抓取互动信息
            var documentInfo = documentInfoService.getById(videoInfo.getSourceFileId());
            if (sourceType == UploadSourceTypeEnum.from_url) {
                try {
                    log.info("开始抓取视频互动信息：{}", documentInfo.getId());
                    var link = processLink(documentInfo.getLink()); // 需要转换url格式为供应商标规定的准格式
                    var response = webClientService.getTikTokVideoInfo(link, documentInfo.getEsId(), documentInfo.getCreateTime()).block();
                    if (response != null && response.isSuccess()) {
                        log.info("视频互动信息抓取成功：{}", documentInfo.getId());
                        // 保存视频互动信息
                        DeepanaTikTokVideoDTO videoInfoDto = response.getData();
                        DocumentExtraInfoDTO extraInfo = new DocumentExtraInfoDTO();
                        extraInfo.setDpVideoJsonResult(JSON.toJSONString(videoInfoDto));
                        // 存储互动信息到 cms_pull_task_deduped_datas表
                        var pullTaskDedupedData = DeepanaTikTokVideoDTO.buildBy(videoInfo, documentInfo, videoInfoDto);
                        pullTaskDedupedDataService.save(pullTaskDedupedData);
                    } else {
                        throw new RuntimeException("视频互动信息抓取失败");
                    }
                    documentInfoService.updateDocumentInfoStatus(DocumentStatusEnum.PROCESSING, documentInfo.getId());
                    // 注：1.发起解码请求使用url上传专有定时任务 2.查询解码结果和本地上传解码结果查询共用一个定时任务
                } catch (Exception e) {
                    log.error("视频互动信息抓取失败:{}，报错信息:{}", documentInfo.getId(), e.getMessage());
                    documentInfoService.updateDocumentInfoStatus(DocumentStatusEnum.FAIL, documentInfo.getId());
                    videoInfoService.updateStatusByDocId(videoInfo.getId(), VideoInfoStatusEnum.ERROR, "视频互动信息抓取失败");
                    continue;
                }
            }
            if (sourceType == UploadSourceTypeEnum.from_local) { // 本地上传视频
                // 存储信息到cms_pull_task_deduped_datas表
                var pullTaskDedupedData = DeepanaTikTokVideoDTO.buildBy(videoInfo, documentInfo, null);
                pullTaskDedupedDataService.save(pullTaskDedupedData);

                //  解析视频信息(decode)
                log.info("开始解析视频信息：{}", documentInfo.getId());
                try {
                    AnalysisVideoCreateRequest analysisVideoCreateRequest = new AnalysisVideoCreateRequest();
                    analysisVideoCreateRequest.setEsId(videoInfo.getEsId());
                    analysisVideoCreateRequest.setTypeName(AnalysisVideoTypeEnum.UPLOAD_VIDEO.getVideoType());
                    analysisVideoCreateRequest.setVideoDownloadUrl(getPicDownloadSignatureUrl(documentInfo.getObjId()));
                    analysisVideoCreateRequest.setVideoInfoId(videoInfo.getId());
                    analysisVideoCreateRequest.setUserId(videoInfo.getUserId());
                    analysisVideoCreateRequest.setTenantId(videoInfo.getTenantId());
                    AnalysisVideoStrategyMap.process(analysisVideoCreateRequest);
                } catch (Exception e) {
                    log.error("错误信息,", e);
                    log.error("发送视频解析失败：{}", documentInfo.getId());
                    documentInfoService.updateDocumentInfoStatus(DocumentStatusEnum.FAIL, documentInfo.getId());
                    videoInfoService.updateStatusByDocId(videoInfo.getId(), VideoInfoStatusEnum.ERROR, "发送视频解析失败");
                    continue;
                }
            }
            documentInfoService.updateDocumentInfoStatus(DocumentStatusEnum.ANALYZING, videoInfo.getSourceFileId());
            // 注：本地上传解码结果查询使用定时任务（2分钟一次）
        }
    }

    private void fileDecode(CmsDocumentInfo documentInfo, CmsVideoInfo videoInfo) {
        var sourceType = UploadSourceTypeEnum.valueOf(documentInfo.getSourceType());
        if (sourceType == UploadSourceTypeEnum.from_url) {
            try {
                log.info("开始抓取视频互动信息：{}", documentInfo.getId());
                var response = webClientService.getTikTokVideoInfo(documentInfo.getLink(),
                        documentInfo.getEsId(), documentInfo.getCreateTime()).block();
                if (response != null && response.isSuccess()) {
                    log.info("视频互动信息抓取成功：{}", documentInfo.getId());
                    // 保存视频互动信息
                    DeepanaTikTokVideoDTO videoInfoDto = response.getData();
                    DocumentExtraInfoDTO extraInfo = new DocumentExtraInfoDTO();
                    extraInfo.setDpVideoJsonResult(JSON.toJSONString(videoInfoDto));
                    // 存储互动信息到 cms_pull_task_deduped_datas表
                    var pullTaskDedupedData = DeepanaTikTokVideoDTO.buildBy(videoInfo, documentInfo, videoInfoDto);
                    pullTaskDedupedDataService.save(pullTaskDedupedData);
                } else {
                    throw new RuntimeException("视频互动信息抓取失败");
                }
                documentInfoService.updateDocumentInfoStatus(DocumentStatusEnum.PROCESSING, documentInfo.getId());
                // 注：1.发起解码请求使用url上传专有定时任务 2.查询解码结果和本地上传解码结果查询共用一个定时任务
            } catch (Exception e) {
                log.error("视频互动信息抓取失败:{}，报错信息:{}", documentInfo.getId(), e.getMessage());
                documentInfoService.updateDocumentInfoStatus(DocumentStatusEnum.FAIL, documentInfo.getId());
            }
        }
        if (sourceType == UploadSourceTypeEnum.from_local) { // 本地上传视频
            // 存储信息到cms_pull_task_deduped_datas表
            var pullTaskDedupedData = DeepanaTikTokVideoDTO.buildBy(videoInfo, documentInfo, null);
            pullTaskDedupedDataService.save(pullTaskDedupedData);

            //  解析视频信息(decode)
            log.info("开始解析视频信息：{}", documentInfo.getId());
            try {
                AnalysisVideoCreateRequest analysisVideoCreateRequest = new AnalysisVideoCreateRequest();
                analysisVideoCreateRequest.setEsId(videoInfo.getEsId());
                analysisVideoCreateRequest.setTypeName(AnalysisVideoTypeEnum.UPLOAD_VIDEO.getVideoType());
                analysisVideoCreateRequest.setVideoDownloadUrl(getPicDownloadSignatureUrl(documentInfo.getObjId()));
                analysisVideoCreateRequest.setVideoInfoId(videoInfo.getId());
                analysisVideoCreateRequest.setUserId(videoInfo.getUserId());
                analysisVideoCreateRequest.setTenantId(videoInfo.getTenantId());
                AnalysisVideoStrategyMap.process(analysisVideoCreateRequest);
            } catch (Exception e) {
                log.error("错误信息,", e);
                log.error("视频解析失败：{}", documentInfo.getId());
                documentInfoService.updateDocumentInfoStatus(DocumentStatusEnum.FAIL, documentInfo.getId());
            }
        }
        documentInfoService.updateDocumentInfoStatus(DocumentStatusEnum.ANALYZING, videoInfo.getSourceFileId());
    }

    @Override
    public Page<DocTaskUploadVO> uploadHistory(Integer current, Integer pageSize, Integer userId, Integer tenantId) {
        var page = documentInfoService.page(new Page<>(current, pageSize),
                new LambdaQueryWrapper<CmsDocumentInfo>()
                        .eq(CmsDocumentInfo::getUserId, userId)
                        .eq(CmsDocumentInfo::getTenantId, tenantId)
                        .eq(CmsDocumentInfo::getIsDeleted, 0)
                        .orderByDesc(CmsDocumentInfo::getCreateTime)
        );
        return PageUtils.convertVOPage(page, this::buildByCmsDocumentInfo);
    }

    private DocTaskUploadVO buildByCmsDocumentInfo(CmsDocumentInfo documentInfo) {
        return DocTaskUploadVO.mapperByDocumentInfo(documentInfo, this::getIconKey);
    }

    @Override
    public List<DocTaskUploadVO> uploadTaskStatus(UploadTaskQueryRequest request) {
        var type = request.getType(); // 上传任务0，脚本生成任务1
        switch (type) {
            case 0 -> {
                return documentInfoService.list(new LambdaQueryWrapper<CmsDocumentInfo>()
                                .eq(CmsDocumentInfo::getUserId, request.getUserId())
                                .eq(CmsDocumentInfo::getTenantId, request.getTenantId())
                                .eq(CmsDocumentInfo::getIsDeleted, 0)
                                .in(CmsDocumentInfo::getId, request.getTaskIds()))
                        .stream().map(doc -> DocTaskUploadVO.mapperByDocumentInfo(doc, this::getIconKey))
                        .collect(Collectors.toList());
            }
            case 1 -> {
                return taskInfoService.videoTaskStatus(
                        request.getUserId(), request.getTenantId(), request.getTaskIds());
            }
        }
        return new ArrayList<>();
    }

    @Override
    public void uploadCancel(Integer taskId, Integer userId, Integer tenantId) {
        documentInfoService.updateDocumentInfoStatus(DocumentStatusEnum.CANCEL, taskId, userId, tenantId);
        //  中断任务进度
    }

    @Override
    public void uploadRetry(Integer taskId, Integer userId, Integer tenantId) {
        int row = documentInfoService.updateDocumentInfoStatus(DocumentStatusEnum.PROCESSING, taskId, userId, tenantId);
        if (row == 0) { // 只能变更自己创建的任务状态
            throw new BusinessException(RespCode.BAD_REQUEST, "任务不存在");
        }
        // 初始化video_task状态为Waiting，删除cms_pull_task_deduped_datas表中数据, 重新来一遍流程
        var documentInfo = documentInfoService.getById(taskId);
        String esId = documentInfo.getEsId();
        // 删除cms_pull_task_deduped_datas表中数据(必须修改esId，否则侧入新的会被idx逻辑报错)
        String newEsId = UUID.randomUUID().toString().replace("-", "") + "-1";
        pullTaskDedupedDataService.update(new LambdaUpdateWrapper<CmsPullTaskDedupedData>()
                .set(CmsPullTaskDedupedData::getIsDeleted, 1)
                .set(CmsPullTaskDedupedData::getEsId, newEsId)
                .eq(CmsPullTaskDedupedData::getEsId, esId));
        // 删除video_info结果，变更video_info状态为Waiting
        var videoInfo = videoInfoService.getOne(new LambdaQueryWrapper<CmsVideoInfo>()
                .eq(CmsVideoInfo::getEsId, esId));
        if (videoInfo != null) {
            // 删除任务结果
            videoResultDetailService.remove(new LambdaQueryWrapper<CmsVideoResultDetail>()
                    .eq(CmsVideoResultDetail::getVideoId, videoInfo.getId()));
            videoInfo.setStatus(VideoInfoStatusEnum.WAITING.getCode());
            videoInfoService.updateById(videoInfo);
            // 执行解析视频信息(decode)流程
            fileDecode(documentInfo, videoInfo);
        }
    }

    @Override
    public void uploadTaskDelete(Integer taskId, Integer userId, Integer tenantId) {
        var docInfo = documentInfoService.getById(taskId);
        if (docInfo == null) { // 只能变更自己创建的任务状态
            throw new BusinessException(RespCode.BAD_REQUEST, "任务不存在");
        }
        // 删除文件
        documentInfoService.removeById(docInfo.getId());
        // 删除任务
        videoInfoService.remove(new LambdaQueryWrapper<CmsVideoInfo>()
                .eq(CmsVideoInfo::getEsId, docInfo.getEsId()));
    }

    public void deleteFile(String sourceOssId) {
        cmsS3FlowService.deleteFile(sourceOssId);
    }

}
