package cn.mlamp.insightflow.cms.auth.cms.bo;

import com.fasterxml.jackson.annotation.JsonFormat;

import cn.mlamp.insightflow.cms.auth.cms.dto.TtcRequestBaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 租户模板 DTO
 *
 * <AUTHOR>
 * @since 2022-09-08 15:20:16
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(onlyExplicitlyIncluded = true, callSuper = true)
public class TenantTemplateBO extends TtcRequestBaseDTO {

    /**
     * 是否强制创建新租户 false-如果已有关联租户，则不再创建新租户 true-直接创建新租户
     */
    @NotNull(message = "必须指定是否强制创建新租户")
    @ToString.Include
    private Boolean isForceCreateTenant;

    /**
     * 租户有效期
     */
    @NotNull(message = "请输入租户有效时间，单位：天")
    @Min(value = 1, message = "租户有效时间最小值为1")
    @ToString.Include
    private Integer expiredDay;
    /**
     * 租户有效期
     */
    @JsonFormat(pattern = "yyyy.MM.dd", timezone = "GMT+8")
    @ToString.Include
    private Date expiredDate;
    /**
     * 备注
     */
    @Size(max = 50, message = "最多50个字符上限")
    @ToString.Include
    private String remark;
    /**
     * 注意这里是资源集合，应该是资源对象Object
     */
    @ToString.Include
    private List<TenantTemplateResourceTypeBO> resources;
    /**
     * 权限
     */
    @NotEmpty(message = "权限集合不能为空!")
    @ToString.Include
    private Set<Integer> permissions;
    /**
     * 产品配额
     */
    @ToString.Include
    private List<TenantTmplateQuotaBO> quotas;
    /**
     * 租户下的最大用户数
     */
    @Min(value = 1, message = "用户上限最小值为1")
    @NotNull(message = "请输入用户上限")
    @ToString.Include
    private Integer userCountLimit;
    /**
     * 租户标签
     */
    @ToString.Include
    private List<String> tenantTagList;

}
