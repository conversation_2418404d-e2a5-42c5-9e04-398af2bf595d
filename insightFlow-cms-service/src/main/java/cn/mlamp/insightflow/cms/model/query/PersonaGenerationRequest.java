package cn.mlamp.insightflow.cms.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * 人物画像生成请求
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
@Schema(description = "人物画像生成请求")
public class PersonaGenerationRequest {

    @Schema(description = "品牌", required = true, example = "欧莱雅")
    @NotBlank(message = "品牌不能为空")
    private String brand;

    @Schema(description = "产品", required = true, example = "小白瓶")
    @NotBlank(message = "产品不能为空")
    private String product;

    @Schema(description = "卖点", required = true, example = "美白精华")
    @NotBlank(message = "卖点不能为空")
    private String sellingPoint;
}
