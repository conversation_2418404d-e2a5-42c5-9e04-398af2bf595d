package cn.mlamp.insightflow.cms.model.converter.dam;

import cn.mlamp.insightflow.cms.entity.dam.DamAsset;
import cn.mlamp.insightflow.cms.model.dto.dam.DamAssetDTO;
import cn.mlamp.insightflow.cms.model.vo.dam.DamAssetVO;

import java.util.List;

/**
 * Dam 转换类
 *
 * <AUTHOR> liuyuan
 **/
@org.mapstruct.Mapper(componentModel = "spring")
public interface DamAssetConverter {

    /**
     * DTO 转换为实体
     *
     * @param assetDTO DTO 对象
     * @return 实体
     */
    DamAsset toEntity(DamAssetDTO assetDTO);

    /**
     * VO 转换为实体
     * 部分场景下需要
     *
     * @param assetVO VO 对象
     * @return 实体
     */
    DamAsset toEntity(DamAssetVO assetVO);

    /**
     * 对象转换方法
     *
     * @param entity 实体
     * @return VO
     */
    DamAssetVO toVO(DamAsset entity);

    /**
     * 对象集合转换方法
     *
     * @param entities 实体列表
     * @return VO 列表
     */
    List<DamAssetVO> toVOs(List<DamAsset> entities);

}
