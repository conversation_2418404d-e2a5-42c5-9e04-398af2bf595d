package cn.mlamp.insightflow.cms.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


/**
 * @Author: husuper
 * @CreateTime: 2025-03-19
 */
@Data
public class VideoFiveGoldRequest extends PageRequest {

    /**
     * 行业分类
     */
    @Schema(description = "行业分类", required = false)
    private String industry;

    /**
     * 黄金标签
     */
    @Schema(description = "黄金标签", required = false)
    private String tag;

    /**
     * 排序字段
     */
    @Schema(description = "排序字段，可选值：videoNum，exposureCount，interactCount，likeCount，commentCount，originalityNum", required = false)
    private String sortField;

    /**
     * 排序方式
     */
    @Schema(description = "排序方式，可选值：asc（升序），desc（降序），默认为desc", required = false)
    private String sortOrder = "desc";

}
