package cn.mlamp.insightflow.cms.model.dto;

import lombok.Data;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Data
public class DifyScriptGenResponseDTO {
    private List<Scene> scenes;

    @Data
    public static class Scene {
        private Integer sceneNumber;          // 镜头编号
        private String sceneDescription;      // 镜头描述
        private String brandIntegration;      // 品牌植入
        private String cameraType;            // 镜头类型
        private String cameraView;            // 运镜方式
        private Integer sceneLength;          // 时长(秒)
        private String actors;                // 出现演员
        private String costumes;              // 服化道
        private String actorActions;          // 演员动作
        private String actorExpressions;      // 演员表情
        private String dialogue;              // 台词
        private String dialogueEmotion;       // 台词情绪
        private String costumeSuggestions;    // 服装造型建议
        private String props;                 // 道具清单
        private String setRequirements;       // 布景要求
        private String bgm;                   // 背景音乐/音效
        private String lightColor;            // 光影与色彩要求
        private String equipment;             // 摄影器材
        private String contentStrategy;       // 视频内容策略字段
        private String imagePrompt;           // 绘画提示词
        private Map<String, String> detailMap = new LinkedHashMap<>(); // 信息map字段
    }

    public static List<Scene> parseScriptByText(String scriptContent) {
        List<DifyScriptGenResponseDTO.Scene> scenes = new ArrayList<>();
        DifyScriptGenResponseDTO.Scene currentScene = null;
        int index = 1;
        boolean multiLine = false;
        boolean hasFirst = false;

        // 去除多余的字符
        scriptContent = scriptContent.replace("- **", "");
        scriptContent = scriptContent.replace("**", "");
        scriptContent = scriptContent.replace(" :", ":");

        String[] lines = scriptContent.split("\\r?\\n");
        for (String line : lines) {
            if(line.isEmpty()) continue;

            if (multiLine) {
                break;
            }
            line = line.trim();

            // 处理分隔线
            if (line.startsWith("####") || line.startsWith("---") || line.isEmpty()) {
                if (currentScene != null) {
                    scenes.add(currentScene);
                    currentScene = null;
                }
                continue;
            }

            // 处理多方案
            if (line.startsWith("## 方案")) {
                if (hasFirst) {
                    multiLine = true;
                }
                hasFirst = true;
                continue;
            }

            // 处理镜头编号
            if (line.startsWith("镜头编号") || line.startsWith("## 镜头")) {
                if (currentScene != null) {
                    scenes.add(currentScene);
                }
                currentScene = new DifyScriptGenResponseDTO.Scene();
                var indexStr = getLineValueText(line);
                if (!indexStr.isEmpty()) {
                    index = Integer.parseInt(indexStr);
                    currentScene.setSceneNumber(index);
                } else {
                    currentScene.setSceneNumber(index++);
                }
                continue;
            }

            // 处理其他字段
            if (currentScene != null) {
                if (line.startsWith("镜头描述")) {
                    var sceneDescription = getLineValueText(line);
                    currentScene.setSceneDescription(sceneDescription);
                    currentScene.getDetailMap().put("镜头描述", sceneDescription);
                } else if (line.startsWith("品牌植入")) {
                    var brandIntegration = getLineValueText(line);
                    currentScene.setBrandIntegration(brandIntegration);
                    currentScene.getDetailMap().put("品牌植入", brandIntegration);
                } else if (line.startsWith("镜头类型")) {
                    var cameraType = getLineValueText(line);
                    currentScene.setCameraType(cameraType);
                    currentScene.getDetailMap().put("镜头类型", cameraType);
                } else if (line.startsWith("运镜方式")) {
                    var cameraView = getLineValueText(line);
                    currentScene.setCameraView(cameraView);
                    currentScene.getDetailMap().put("运镜方式", cameraView);
                } else if (line.startsWith("时长")) {
                    String durationStr = getLineValueText(line);
                    int duration = 1;
                    if (durationStr.contains("ms") || durationStr.contains("毫秒")) {
                        durationStr = durationStr.replace("ms", "").replace("毫秒", "");
                        float durationF = Float.parseFloat(durationStr);
                        if (durationF > 1000) {
                            duration = (int) durationF / 1000;
                        }
                    }
                    if (durationStr.contains("s") || durationStr.contains("秒")) {
                        durationStr = durationStr.replace("s", "").replace("秒", "");
                        float durationF = Float.parseFloat(durationStr);
                        duration = (int) durationF;
                    }
                    currentScene.setSceneLength(duration);
//                    currentScene.getDetailMap().put("时长", duration);  // 不需要时长信息
                } else if (line.startsWith("出现演员")) {
                    var actors = getLineValueText(line);
                    currentScene.setActors(actors);
                    currentScene.getDetailMap().put("出现演员", actors);
                } else if (line.startsWith("服化道")) {
                    var costumes = getLineValueText(line);
                    currentScene.setCostumes(costumes);
                    currentScene.getDetailMap().put("服化道", costumes);
                } else if (line.startsWith("演员动作")) {
                    var actions = getLineValueText(line);
                    currentScene.setActorActions(actions);
                    currentScene.getDetailMap().put("演员动作", actions);
                } else if (line.startsWith("演员表情")) {
                    var expressions = getLineValueText(line);
                    currentScene.setActorExpressions(expressions);
                    currentScene.getDetailMap().put("演员表情", expressions);
                } else if (line.startsWith("台词:") || line.startsWith("台词：")) {
                    String dialogue = getLineValueText(line);
                    if (dialogue.startsWith("\"") && dialogue.endsWith("\"")) {
                        dialogue = dialogue.substring(1, dialogue.length() - 1);
                    }
                    currentScene.setDialogue(dialogue);
                    currentScene.getDetailMap().put("台词", dialogue);
                } else if (line.startsWith("台词情绪")) {
                    var emotion = getLineValueText(line);
                    currentScene.setDialogueEmotion(emotion);
                    currentScene.getDetailMap().put("台词情绪", emotion);
                } else if (line.startsWith("服装造型") || line.startsWith("服装造型建议")) {
                    var costume = getLineValueText(line);
                    currentScene.setCostumeSuggestions(costume);
                    currentScene.getDetailMap().put("服装造型建议", costume);
                } else if (line.startsWith("道具清单")) {
                    var props = getLineValueText(line);
                    currentScene.setProps(props);
                    currentScene.getDetailMap().put("道具清单", props);
                } else if (line.startsWith("布景要求")) {
                    var requirements = getLineValueText(line);
                    currentScene.setSetRequirements(requirements);
                    currentScene.getDetailMap().put("布景要求", requirements);
                } else if (line.startsWith("背景音乐/音效") || line.startsWith("背景音乐") || line.startsWith("背景音效")) {
                    var bgm = getLineValueText(line);
                    currentScene.setBgm(bgm);
                    currentScene.getDetailMap().put("背景音乐", bgm);
                } else if (line.startsWith("光影与色彩要求")) {
                    var lightColor = getLineValueText(line);
                    currentScene.setLightColor(lightColor);
                    currentScene.getDetailMap().put("光影与色彩要求", lightColor);
                } else if (line.startsWith("摄影器材")) {
                    var equipment = getLineValueText(line);
                    currentScene.setEquipment(equipment);
                    currentScene.getDetailMap().put("摄影器材", equipment);
                } else if (line.startsWith("视频内容策略字段") || line.startsWith("视频内容策略")) {
                    var strategy = getLineValueText(line);
                    currentScene.setContentStrategy(strategy);
                    currentScene.getDetailMap().put("视频内容策略", strategy);
                } else if (line.startsWith("绘图提示词") || line.contains("提示词")) {
                    var imagePrompt = getLineValueText(line);
                    currentScene.setImagePrompt(imagePrompt);
                }
            }
        }

        // 添加最后一个场景
        if (currentScene != null) {
            scenes.add(currentScene);
        }

        scenes = scenes.stream()
                .filter(scene -> scene.getSceneDescription() != null)
                .collect(Collectors.toList());

        return scenes;
    }

    public static String getLineValueText(String line) {
        if (line.contains(":")) {
            return line.substring(line.indexOf(":") + 1).trim();
        }
        if (line.contains("：")) {
            return line.substring(line.indexOf("：") + 1).trim();
        }
        return line;
    }
}
