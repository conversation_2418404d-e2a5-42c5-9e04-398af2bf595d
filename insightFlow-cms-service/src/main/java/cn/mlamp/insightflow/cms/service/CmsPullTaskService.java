package cn.mlamp.insightflow.cms.service;

import java.time.LocalDate;

import com.baomidou.mybatisplus.extension.service.IService;

import cn.mlamp.insightflow.cms.entity.CmsPullTask;

public interface CmsPullTaskService extends IService<CmsPullTask> {
    public void executeIndustryDataPull(LocalDate endDate);

    public void executeTribeDataPull(LocalDate endDate);

    public void syncDataFromEsToDb(LocalDate endDate);

}
