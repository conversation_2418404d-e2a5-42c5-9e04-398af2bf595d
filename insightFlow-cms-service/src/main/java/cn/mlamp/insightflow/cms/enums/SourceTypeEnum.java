package cn.mlamp.insightflow.cms.enums;

/**
 * @Author: husuper
 * @CreateTime: 2025-03-30
 */
public enum SourceTypeEnum {

    //1 es，2 上传，3 链接
    ES(1, "es"),
    UPLOAD(2, "上传"),
    LINK(3, "链接");



    private final int code;

    private final String msg;

    SourceTypeEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
