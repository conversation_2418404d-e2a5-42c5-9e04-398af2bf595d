package cn.mlamp.insightflow.cms.model.query;//package cn.mlamp.insightflow.cms.model.query;
//
//import io.swagger.annotations.ApiModelProperty;
//import lombok.Data;
//
//import java.io.Serializable;
//
///**
// * @Author: husuper
// * @CreateTime: 2024-10-07
// */
//@Data
//public class UserLoginRequest implements Serializable {
//
//    @ApiModelProperty(value = "账号", required = true)
//    private String account;
//
//    @ApiModelProperty(value = "密码（MD5加密后全大写）", required = true)
//    private String password;
//
//
//}
