package cn.mlamp.insightflow.cms.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 视频合成任务列表项VO
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
public class VideoSynthesisListItemVO {

    @Schema(description = "任务ID", required = true)
    private Integer taskId;

    @Schema(description = "任务名称", required = true)
    private String taskName;

    @Schema(description = "任务状态", required = true)
    private Integer taskStatus;

    @Schema(description = "任务状态描述", required = true)
    private String taskStatusDesc;

    @Schema(description = "合成视频OSS ID", required = false)
    private String resultOssId;

    @Schema(description = "合成视频签名地址", required = false)
    private String resultOssUrl;

    @Schema(description = "视频首帧图片签名地址", required = false)
    private String firstFrameOssUrl;

    @Schema(description = "创建时间", required = true)
    private Date createTime;

    @Schema(description = "更新时间", required = true)
    private Date updateTime;

    @Schema(description = "错误信息", required = false)
    private String errorMessage;

    @Schema(description = "任务信息", required = false)
    private String taskInfo;
}
