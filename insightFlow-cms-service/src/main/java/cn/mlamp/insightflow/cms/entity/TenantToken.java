package cn.mlamp.insightflow.cms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 租户Token统计表;
 * <AUTHOR> husper
 * @date : 2025-3-19
 */
@Data
@TableName("cms_tenant_token")
public class TenantToken extends BaseEntity {
    /** id */
    @TableId(type = IdType.AUTO)
    private Integer id ;
    /** 租户Id */
    private Integer tenantId ;
    /** token余额 */
    private Integer balance ;
    /** token累计充值 */
    private Integer accumulatedRecharge ;
    /** token累计消耗 */
    private Integer accumulatedExpenses ;
    /** 统计时间 */
    private Date statisticalTime ;


}