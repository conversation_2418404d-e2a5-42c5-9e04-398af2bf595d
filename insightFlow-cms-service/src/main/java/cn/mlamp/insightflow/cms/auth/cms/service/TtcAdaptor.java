package cn.mlamp.insightflow.cms.auth.cms.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.mlamp.insightflow.cms.config.TenantTemplateConfig;
import cn.mlamp.insightflow.cms.config.TtcConfig;
import cn.mlamp.insightflow.cms.constant.CommonConstant;
import cn.mlamp.insightflow.cms.enums.ErrorCode;
import cn.mlamp.insightflow.cms.enums.TtcBizCode;
import cn.mlamp.insightflow.cms.enums.VerificationTypeEnum;
import cn.mlamp.insightflow.cms.exception.BusinessException;
import cn.mlamp.insightflow.cms.model.dto.TtcRequestVerificationCodeDTO;
import cn.mlamp.insightflow.cms.util.HttpUtil;
import cn.mlamp.insightflow.cms.util.JsonUtil;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import cn.mlamp.insightflow.cms.auth.cms.dto.*;

/**
 * 自助注册Service
 *
 * <AUTHOR>
 * @since 2022-09-15 10:26:04
 */
@Service
@Slf4j
public class TtcAdaptor {

    /**
     * 租户模板配置
     */
    @Autowired
    private TenantTemplateConfig tenantTemplateConfig;

    @Autowired
    private TtcConfig ttcConfig;

    /**
     * 获取Header
     */
    private Map<String, String> getHeaderMap() {
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("client-id", ttcConfig.getServerOpenApiClientId());
        headerMap.put("sign", ttcConfig.getServerOpenApiSign());
        return headerMap;
    }

    /**
     * 发送验证码 ref https://yapi.mlamp.cn/project/136/interface/api/42457
     *
     * @param ttcVerificationCodeRequestDTO passport发送验证码DTO
     */
    public void sendVerificationCode(TtcRequestVerificationCodeDTO ttcVerificationCodeRequestDTO) {
        // 执行验证码请求
        TtcResponseBaseDTO<?> responseDTO = HttpUtil.postJsonParams(ttcConfig.getSendVerificationCodeUrl(),
                getHeaderMap(), ttcVerificationCodeRequestDTO, new TypeReference<TtcResponseBaseDTO<?>>() {
                });

        // 成功
        if (TtcBizCode.TTC_SUCCESS.getTtcCode().equals(responseDTO.getCode())) {
            log.info("PassportClient-sendVerificationCode success, request:{}, response:{}",
                    ttcVerificationCodeRequestDTO, responseDTO);
            return;
        }

        // 失败
        log.warn("TtcService-sendVerificationCode fail, request:{}, response:{}", ttcVerificationCodeRequestDTO,
                responseDTO);

        // 如果账号已绑定，则提示已绑定
        if (TtcBizCode.ACCOUNT_HAS_BEEN_EXIST_ERROR.getTtcCode().equals(responseDTO.getCode())) {
            // 邮箱提示已注册，请激活
            if (VerificationTypeEnum.TYPE_EMAIL.getCode().equals(ttcVerificationCodeRequestDTO.getVerificationType())) {
                throw new BusinessException(TtcBizCode.ACCOUNT_HAS_BEEN_EXIST_ERROR);
                // 手机号提示已注册
            } else if (VerificationTypeEnum.TYPE_MOBILE.getCode()
                    .equals(ttcVerificationCodeRequestDTO.getVerificationType())) {
                throw new BusinessException(TtcBizCode.MOBILE_HAS_BEEN_EXIST_ERROR);
            }
        }
        // 如果账号、租户均已绑定，则提示请登录
        if (TtcBizCode.ACCOUNT_AND_TTC_TENANT_HAVE_BEEN_EXIST_ERROR.getTtcCode().equals(responseDTO.getCode())) {
            // 邮箱提示已注册，请登陆
            if (VerificationTypeEnum.TYPE_EMAIL.getCode().equals(ttcVerificationCodeRequestDTO.getVerificationType())) {
                throw new BusinessException(TtcBizCode.ACCOUNT_AND_TTC_TENANT_HAVE_BEEN_EXIST_ERROR);
                // 手机号提示已注册
            } else if (VerificationTypeEnum.TYPE_MOBILE.getCode()
                    .equals(ttcVerificationCodeRequestDTO.getVerificationType())) {
                throw new BusinessException(TtcBizCode.MOBILE_HAS_BEEN_EXIST_ERROR);
            }
        }
        // 如果获取验证码太频繁，则提示需间隔60s
        if (TtcBizCode.GET_VERIFICATION_CODE_LIMIT_ERROR.getTtcCode().equals(responseDTO.getCode())) {
            throw new BusinessException(TtcBizCode.GET_VERIFICATION_CODE_LIMIT_ERROR);
        }
        // 如果账号不存在
        if (TtcBizCode.UNREGISTER_ACCOUNT_ERROR.getTtcCode().equals(responseDTO.getCode())) {
            throw new BusinessException(TtcBizCode.UNREGISTER_ACCOUNT_ERROR);
        }
        // 其他异常
        log.error("TtcService-sendVerificationCode fail, request:{}, response:{}", ttcVerificationCodeRequestDTO,
                responseDTO);
        throw new BusinessException(ErrorCode.UNKNOW_ERROR);
    }

    /**
     * 校验租户名是否存在 ref https://yapi.mlamp.cn/project/136/interface/api/42965
     *
     * @param ttcRequestCheckTenantNameExistDTO 校验租户名是否存在DTO
     */
    public void checkTenantNameExist(TtcRequestCheckTenantNameExistDTO ttcRequestCheckTenantNameExistDTO) {
        // 执行验证码请求
        TtcResponseBaseDTO<Boolean> responseDTO = HttpUtil.postJsonParams(ttcConfig.getCheckTenantNameExistUrl(),
                getHeaderMap(), ttcRequestCheckTenantNameExistDTO, new TypeReference<TtcResponseBaseDTO<Boolean>>() {
                });

        // 租户已存在
        if (TtcBizCode.TENANT_EXISTS_ERROR.getTtcCode().equals(responseDTO.getCode())) {
            throw new BusinessException(TtcBizCode.TENANT_EXISTS_ERROR);
        }
        // 未知异常
        if (!TtcBizCode.TTC_SUCCESS.getTtcCode().equals(responseDTO.getCode())) {
            log.error("TtcService-checkTenantNameExist fail, unknown error, request:{}, response:{}",
                    ttcRequestCheckTenantNameExistDTO, responseDTO);
            throw new BusinessException(ErrorCode.UNKNOW_ERROR);
        }

        log.info("PassportClient-checkTenantNameExist success, request:{}, response:{}",
                ttcRequestCheckTenantNameExistDTO, responseDTO);
    }

    /**
     * 注册用户 ref https://yapi.mlamp.cn/project/136/interface/api/42273
     *
     * @param ttcRequestRegisterUserDTO ttc注册用户DTO
     */
    public void registerUser(TtcRequestRegisterUserTemplateDTO ttcRequestRegisterUserDTO) {
        log.info("用户注册参数：{}", ttcRequestRegisterUserDTO);
        // 追加租户模板参数
        tenantTemplateConfig.addTenantTemplateArgs(ttcRequestRegisterUserDTO);

        // 执行注册请求
        TtcResponseBaseDTO<?> responseDTO = HttpUtil.postJsonParams(ttcConfig.getRegisterUrl(), getHeaderMap(),
                ttcRequestRegisterUserDTO, new TypeReference<TtcResponseBaseDTO<?>>() {
                });

        // 成功
        if (TtcBizCode.TTC_SUCCESS.getTtcCode().equals(responseDTO.getCode())) {
            log.info("PassportClient-registerUser register success, request:{}, response:{}", ttcRequestRegisterUserDTO,
                    responseDTO);
            return;
        }

        // 失败
        log.warn("PassportClient-registerUser register fail, request:{}, response:{}", ttcRequestRegisterUserDTO,
                responseDTO);

        // 租户已存在
        if (TtcBizCode.TENANT_EXISTS_ERROR.getTtcCode().equals(responseDTO.getCode())) {
            throw new BusinessException(TtcBizCode.TENANT_EXISTS_ERROR);
        }
        // 用户已拥有关联租户
        if (TtcBizCode.USER_HAS_OWN_TENANT_ERROR.getTtcCode().equals(responseDTO.getCode())) {
            throw new BusinessException(TtcBizCode.USER_HAS_OWN_TENANT_ERROR);
        }
        // 邮箱、手机号验证码错误
        if (TtcBizCode.EMAIL_AND_MOBILE_CODE_INVALID.getTtcCode().equals(responseDTO.getCode())) {
            throw new BusinessException(TtcBizCode.EMAIL_AND_MOBILE_CODE_INVALID);
        }
        // 邮箱验证码错误
        if (TtcBizCode.EMAIL_CODE_INVALID.getTtcCode().equals(responseDTO.getCode())) {
            throw new BusinessException(TtcBizCode.EMAIL_CODE_INVALID);
        }
        // 手机号验证码错误
        if (TtcBizCode.MOBILE_CODE_INVALID.getTtcCode().equals(responseDTO.getCode())) {
            throw new BusinessException(TtcBizCode.MOBILE_CODE_INVALID);
        }
        // 账号已注册
        if (TtcBizCode.ACCOUNT_AND_TTC_TENANT_HAVE_BEEN_EXIST_ERROR.getTtcCode().equals(responseDTO.getCode())) {
            throw new BusinessException(TtcBizCode.ACCOUNT_AND_TTC_TENANT_HAVE_BEEN_EXIST_ERROR);
        }
        // 参数错误
        if (TtcBizCode.PARAM_ERROR.getTtcCode().equals(responseDTO.getCode())) {
            if (StrUtil.isNotBlank(responseDTO.getMsg())) {
                List<TtcResponseErrorDTO> decodeList = JsonUtil.decode(responseDTO.getMsg(),
                        new TypeReference<List<TtcResponseErrorDTO>>() {
                        });
                if (CollUtil.isNotEmpty(decodeList)) {
                    throw new BusinessException(TtcBizCode.PARAM_ERROR, StrUtil.join(CommonConstant.COMMA,
                            decodeList.stream().map(TtcResponseErrorDTO::getMsg).collect(Collectors.toList())));
                }
            }
        }
        // 未知异常
        log.error("PassportClient-registerUser register fail, request:{}, response:{}", ttcRequestRegisterUserDTO,
                responseDTO);
        throw new BusinessException(ErrorCode.UNKNOW_ERROR);
    }

    /**
     * 激活用户 ref https://yapi.mlamp.cn/project/136/interface/api/42449
     *
     * @param ttcRequestActivateUserDTO ttc激活用户DTO
     */
    public void activateUser(TtcRequestActivateUserTemplateBO ttcRequestActivateUserDTO) {
        // 追加租户模板参数
        tenantTemplateConfig.addTenantTemplateArgs(ttcRequestActivateUserDTO);

        // 执行激活请求
        TtcResponseBaseDTO<?> responseDTO = HttpUtil.postJsonParams(ttcConfig.getActivateUrl(), getHeaderMap(),
                ttcRequestActivateUserDTO, new TypeReference<TtcResponseBaseDTO<?>>() {
                });

        // 成功
        if (TtcBizCode.TTC_SUCCESS.getTtcCode().equals(responseDTO.getCode())) {
            log.info("TtcService-registerUser register success, request:{}, response:{}", ttcRequestActivateUserDTO,
                    responseDTO);
            return;
        }

        // 租户名称已存在
        if (TtcBizCode.TENANT_EXISTS_ERROR.getTtcCode().equals(responseDTO.getCode())) {
            log.warn("TtcService-registerUser register user fail, request:{}, response:{}", ttcRequestActivateUserDTO,
                    responseDTO);
            throw new BusinessException(TtcBizCode.TENANT_EXISTS_ERROR);
        }
        // 用户已拥有关联租户
        if (TtcBizCode.USER_HAS_OWN_TENANT_ERROR.getTtcCode().equals(responseDTO.getCode())) {
            log.warn("TtcService-registerUser register user fail, request:{}, response:{}", ttcRequestActivateUserDTO,
                    responseDTO);
            throw new BusinessException(TtcBizCode.USER_HAS_OWN_TENANT_ERROR);
        }
        // 未知异常
        log.error("TtcService-registerUser register user fail, unknown error, request:{}, response:{}",
                ttcRequestActivateUserDTO, responseDTO);
        throw new BusinessException(ErrorCode.UNKNOW_ERROR);
    }

    /**
     * 重置密码 ref
     * https://code.mlamp.cn/web-project/pub-wiki/-/wikis/passport/user/password/retrieve
     *
     * @param ttcRequestResetPasswordDTO ttc重置密码DTO
     */
    public void resetPassword(TtcRequestResetPasswordDTO ttcRequestResetPasswordDTO) {
        log.info("重置密码参数：{}", ttcRequestResetPasswordDTO);
        // 执行重置密码请求
        TtcResponseBaseDTO<TtcResponseResetPasswordDTO> responseDTO = HttpUtil.postJsonParams(
                ttcConfig.getResetPasswordUrl(), getHeaderMap(), ttcRequestResetPasswordDTO,
                new TypeReference<TtcResponseBaseDTO<TtcResponseResetPasswordDTO>>() {
                });

        // 成功
        if (TtcBizCode.TTC_SUCCESS.getTtcCode().equals(responseDTO.getCode())) {
            log.info("TtcService-resetPassword success, request:{}, response:{}", ttcRequestResetPasswordDTO,
                    responseDTO);
            return;
        }
        // 内部用户不支持通过open api修改
        if (TtcBizCode.INTERNAL_USER_RESET_PASSWORD_ERROR.getTtcCode().equals(responseDTO.getCode())) {
            throw new BusinessException(TtcBizCode.INTERNAL_USER_RESET_PASSWORD_ERROR);
        }

        // 验证码错误
        if (TtcBizCode.PARAM_ERROR.getTtcCode().equals(responseDTO.getCode())) {
            log.warn("TtcService-resetPassword fail, request:{}, response:{}", ttcRequestResetPasswordDTO, responseDTO);
            if (StrUtil.isNotBlank(responseDTO.getMsg())) {
                List<TtcResponseErrorDTO> decodeList = JsonUtil.decode(responseDTO.getMsg(),
                        new TypeReference<List<TtcResponseErrorDTO>>() {
                        });
                if (CollUtil.isNotEmpty(decodeList)) {
                    throw new BusinessException(TtcBizCode.PARAM_ERROR, StrUtil.join(CommonConstant.COMMA,
                            decodeList.stream().map(TtcResponseErrorDTO::getMsg).collect(Collectors.toList())));
                }
            }
            throw new BusinessException(TtcBizCode.PARAM_ERROR);
        }
        // 未知异常
        log.error("TtcService-resetPassword fail, unknown error, request:{}, response:{}", ttcRequestResetPasswordDTO,
                responseDTO);
        throw new BusinessException(ErrorCode.UNKNOW_ERROR);
    }

    private static final String PRODUCT_KEY = "productKey";
    private static final String PAGE_NO_KEY = "pageNo";
    private static final String PAGE_SIZE_KEY = "pageSize";
    private static final Integer MAX_LOOP_COUNT = 10000;

    /**
     * 获取ttc中socialx所有租户
     * 
     * @return
     */
    public List<TtcTenantBaseInfoDTO> queryTenantList() {
        List<TtcTenantBaseInfoDTO> resultList = Lists.newArrayList();

        Map<String, String> paramMap = Maps.newHashMap();
        paramMap.put(PRODUCT_KEY, ttcConfig.getProductKey());
        int pageNo = 0;
        int pageSize = 100;
        while (true) {
            if (pageNo > MAX_LOOP_COUNT) {
                log.error("TtcAdaptor-queryTenantList reach max loop error, param={} ,pageNo:{}", paramMap, pageNo);
                break;
            }
            paramMap.put(PAGE_NO_KEY, String.valueOf(++pageNo));
            paramMap.put(PAGE_SIZE_KEY, String.valueOf(pageSize));
            TtcResponseBaseDTO<TtcTenantResponse> response = null;
            TtcTenantResponse data = null;
            List<TtcTenantBaseInfoDTO> list = Lists.newArrayList();

            try {
                response = HttpUtil.get(ttcConfig.getTenantListUrl(), getHeaderMap(), paramMap,
                        new TypeReference<TtcResponseBaseDTO<TtcTenantResponse>>() {
                        });
            } catch (BusinessException biz) {
                log.error("TtcAdaptor-queryTenantList biz error, param:{}, biz:", paramMap, biz);
                break;
            } catch (Exception ex) {
                log.error("TtcAdaptor-queryTenantList error, param:{}, biz:", paramMap, ex);
                break;
            }

            if (null == response) {
                log.error("TtcAdaptor-queryTenantList error, response is empty, param:{}, biz:", paramMap);
                break;
            }

            if (null == (data = response.getData())) {
                log.error("TtcAdaptor-queryTenantList error, response data is empty, param:{}", paramMap);
                break;
            }

            if (CollUtil.isEmpty(list = data.getList())) {
                log.info("TtcAdaptor-queryTenantList finish, response list is empty, last page, param:{}", paramMap);
                break;
            }

            // 最后一页了
            if (Objects.equals(data.getPageNo(), data.getPages())) {
                resultList.addAll(list);
                log.info("TtcAdaptor-queryTenantList finish last page, param:{}", paramMap);
                break;
            }

            resultList.addAll(list);
        }
        return resultList;
    }

}
