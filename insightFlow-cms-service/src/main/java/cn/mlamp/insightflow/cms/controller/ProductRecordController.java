package cn.mlamp.insightflow.cms.controller;

import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.config.UserContext;
import cn.mlamp.insightflow.cms.model.vo.TaskUploadVO;
import cn.mlamp.insightflow.cms.model.vo.UploadProductVO;
import cn.mlamp.insightflow.cms.service.ICmsProductRecordService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RequestMapping("/product/record")
@RestController
@RequiredArgsConstructor
@Tag(name = "产品记录相关接口")
public class ProductRecordController {

    private final ICmsProductRecordService productRecordService;

    @PostMapping("/upload/url")
    @Operation(summary = "上传商品链接获取信息")
    public RespBody<UploadProductVO> videoTaskProductUrl(@RequestBody TaskUploadVO uploadVO) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();
        return RespBody.ok(productRecordService.videoTaskProductUrl(uploadVO, userId, tenantId));
    }

    @GetMapping("/list")
    @Operation(summary = "历史商品列表")
    public RespBody<Page<UploadProductVO>> videoTaskProductList(@RequestParam(value = "current", defaultValue = "1") Integer current,
                                                                @RequestParam(value = "pageSize", defaultValue = "5") Integer pageSize) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();
        return RespBody.ok(productRecordService.videoTaskProductList(current, pageSize, userId, tenantId));
    }

    @DeleteMapping("/{recordId}")
    @Operation(summary = "删除历史商品")
    public RespBody<Void> deleteProductRecord(@PathVariable("recordId") Integer recordId) {
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();
        productRecordService.deleteProductRecord(recordId, userId, tenantId);
        return RespBody.ok();
    }

}
