package cn.mlamp.insightflow.cms.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@AllArgsConstructor
@Data
@NoArgsConstructor
public class SubjectImageAnalyseVO implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "主体图片分析结果", required = true)
    private String subjectImageResult;



}
