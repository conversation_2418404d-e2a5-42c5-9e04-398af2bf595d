package cn.mlamp.insightflow.cms.model.dto.dam;

import java.util.List;

import lombok.Data;

//@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class DamAssetUploadTaskArg {

    private Integer directoryId;

    /**
     * AI分镜视频任务ID（taskType=2时必填）
     */
    private Integer videoTaskId;

    private List<Asset> assets;

    /**
     * 标签列表
     */
    private List<Tag> tags;

    @Data
    public static class Asset {

        private Integer assetId;

        private String ossId;

        private Integer start;

        private Integer end;

    }

    @Data
    public static class Tag {
        
        private Integer id;

        private String name;

        private String description;

        private String example;

    }

}