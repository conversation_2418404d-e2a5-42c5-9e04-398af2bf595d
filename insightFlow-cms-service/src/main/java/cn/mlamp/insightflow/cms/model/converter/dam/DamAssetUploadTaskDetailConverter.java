package cn.mlamp.insightflow.cms.model.converter.dam;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import cn.mlamp.insightflow.cms.entity.CmsTaskInfo;
import cn.mlamp.insightflow.cms.entity.dam.DamAssetUploadTaskDetail;
import cn.mlamp.insightflow.cms.entity.dam.DamTag;
import cn.mlamp.insightflow.cms.model.dto.dam.DamAssetUploadTaskArg;
import cn.mlamp.insightflow.cms.model.dto.dam.DamAssetUploadTaskDTO;
import cn.mlamp.insightflow.cms.model.vo.dam.DamAssetUploadTaskVO;

@Mapper(componentModel = "spring")
public interface DamAssetUploadTaskDetailConverter {

//    List<DamAssetUploadTaskVO> toVOs(List<DamAssetUploadTaskDetail> entities);
    
    List<DamAssetUploadTaskArg.Tag> toArgTags(List<DamTag> tags);

}
