package cn.mlamp.insightflow.cms;

import cn.mlamp.insightflow.cms.config.properties.ThirdWebProperties;
import com.mz.ttc.annotation.EnableAuthCenter;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;

@EnableAsync
@EnableWebMvc
@EnableAuthCenter
@EnableTransactionManagement
@SpringBootApplication
@EnableScheduling
@EnableConfigurationProperties({ThirdWebProperties.class})
public class CmsApplication {

    public static void main(String[] args) {
        SpringApplication.run(CmsApplication.class, args);
    }
}
