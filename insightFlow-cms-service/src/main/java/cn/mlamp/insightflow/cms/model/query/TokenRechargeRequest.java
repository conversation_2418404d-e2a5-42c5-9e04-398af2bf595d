package cn.mlamp.insightflow.cms.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;

/**
 * Token充值请求参数
 *
 * <AUTHOR>
 * @since 2024-10-15
 */
@Data
public class TokenRechargeRequest {

    @NotNull(message = "租户ID不能为空")
    @Schema(description = "租户ID", required = true)
    private Integer tenantId;

    @NotNull(message = "租户名称不能为空")
    @Schema(description = "租户名称", required = true)
    private String tenantName;

    @NotNull(message = "充值token数不能为空")
    @Min(value = 1, message = "充值token数最小为1")
    @Schema(description = "充值token数", required = true)
    private Integer rechargeTokens;
}
