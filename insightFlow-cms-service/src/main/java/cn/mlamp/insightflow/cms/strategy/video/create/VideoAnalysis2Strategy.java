//package cn.mlamp.insightflow.cms.strategy.video.create;
//
//import cn.mlamp.insightflow.cms.common.file.IS3FlowService;
//import cn.mlamp.insightflow.cms.config.AnalysisVideoConfig;
//import cn.mlamp.insightflow.cms.entity.*;
//import cn.mlamp.insightflow.cms.enums.*;
//import cn.mlamp.insightflow.cms.exception.BusinessException;
//import cn.mlamp.insightflow.cms.model.query.AnalysisVideoCreateRequest;
//import cn.mlamp.insightflow.cms.model.query.AnalysisVideoQueryRequest;
//import cn.mlamp.insightflow.cms.model.vo.AnalysisVideoCreateVO;
//import cn.mlamp.insightflow.cms.model.vo.AnalysisVideoResultVO;
//import cn.mlamp.insightflow.cms.service.*;
//import cn.mlamp.insightflow.cms.strategy.handle.VideoRecognition2Handle;
//import cn.mlamp.insightflow.cms.util.FileDownloadUtil;
//import cn.mlamp.insightflow.cms.util.FilePathBuilder;
//import cn.mlamp.insightflow.cms.util.ObservationIdUtil;
//import cn.mlamp.insightflow.cms.util.VideoUtil;
//import com.alibaba.fastjson.JSONObject;
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import jakarta.annotation.PostConstruct;
//import jakarta.annotation.Resource;
//import lombok.AllArgsConstructor;
//import lombok.Data;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//import org.springframework.transaction.annotation.Transactional;
//
//import java.io.File;
//import java.util.*;
//import java.util.concurrent.*;
//
//
///**
// * @Author: husuper
// * @CreateTime: 2025-02-18
// */
//@Slf4j
//@Component
//@RequiredArgsConstructor
//public class VideoAnalysis2Strategy implements ProcessAnalysisVideoStrategyInterface {
//
//    private final CmsPullTaskDedupedDataService pullTaskDedupedDataService;
//
//    private final VideoRecognition2Handle videoRecognition2Handle;
//
//    private final IVideoInfoService videoInfoService;
//
//    private final IVideoResultService videoResultService;
//
//    private final IVideoAsrService videoAsrService;
//
//    private final FileService fileService;
//
//    private final QianchuanVideoStrategy qianchuanVideoStrategy;
//
//    @Resource(name = "cmsS3FlowService")
//    private IS3FlowService cmsS3FlowService;
//
//    private final AnalysisVideoConfig analysisVideoConfig;
//
//    @Autowired
//    private final TokenUseDetailService tokenUseDetailService;
//
//
////    public final static String OSS_ID_PREFIX = "qianchuan/videos/compress/";
//
////    public final static String OSS_ID_PROCESS_PREFIX = "qianchuan/videos/process/";
//    @Resource(name = "analysisThreadExecutor")
//    private  final ExecutorService analysisThreadExecutor;
//
//    @PostConstruct
//    public void init() {
//        AnalysisVideoStrategyMap.register(AnalysisVideoTypeEnum.VIDEO_ANALYSIS.getVideoType(), this);
//        AnalysisVideoStrategyMap.register(AnalysisVideoTypeEnum.UPLOAD_VIDEO.getVideoType(), this);
////        this.executorService = Executors.newFixedThreadPool(4);
//    }
//
//
//    @Override
//    @Transactional
//    public AnalysisVideoCreateVO process(AnalysisVideoCreateRequest request) {
//        Integer id = null;
//        try {
//            String esId = request.getEsId();
//            if (StringUtils.isBlank(esId)) {
//                throw new BusinessException("esId不能为空");
//            }
//            LambdaQueryWrapper<CmsPullTaskDedupedData> queryWrapper = new LambdaQueryWrapper<>();
//            queryWrapper.eq(CmsPullTaskDedupedData::getEsId, esId);
//            CmsPullTaskDedupedData dedupedData = pullTaskDedupedDataService.getOne(queryWrapper);
//            if (dedupedData == null) {
//                throw new BusinessException("esId不存在");
//            }
//            if (dedupedData.getDownloadStatus() != DownloadStatusEnum.SUCCESS.getCode()) {
//                throw new BusinessException("视频未下载完成");
//            }
//
//
//            //调用算法
//            VideoRecognition2Handle.RecognitionArg recognitionArg = new VideoRecognition2Handle.RecognitionArg();
//            if (StringUtils.isBlank(request.getVideoDownloadUrl())) {
//                recognitionArg.setVideo_url(VideoUtil.getVideoUrl(dedupedData.getDatePublishedAt(), dedupedData.getEsId()));
//                if (dedupedData.getSourceType() == 3) { //链接上传,取createTime
//                    recognitionArg.setVideo_url(VideoUtil.getVideoUrl(dedupedData.getCreateTime(), dedupedData.getEsId()));
//                }
//            } else {
//                recognitionArg.setVideo_url(request.getVideoDownloadUrl());
//            }
//
//            if (dedupedData.getSourceType() == 2) {
//                dedupedData.setVideoOssId(dedupedData.getKwVideoUrl());
//            } else if (dedupedData.getSourceType() == 3) {
//                dedupedData.setVideoOssId(VideoUtil.getESVideoOSSId(dedupedData.getCreateTime(), dedupedData.getEsId()));
//            } else if (dedupedData.getSourceType() == 1) {
//                dedupedData.setVideoOssId(VideoUtil.getESVideoOSSId(dedupedData.getDatePublishedAt(), dedupedData.getEsId()));
//            }
//
//            //是否是开放地址
//            boolean isOpen = false;
//            if (dedupedData.getSourceType() == 1) {
//                isOpen = true;
//            }
//
//            //下载视频
//            String videoUrl = recognitionArg.getVideo_url();
//            String fileName = dedupedData.getEsId() + ".mp4";
//            String localFilePath = FileDownloadUtil.getPath(fileName);
//            FileDownloadUtil.downloadFile2(videoUrl, localFilePath);
//            //ASR抽离
//            String audiofileName = dedupedData.getEsId() + ".wav";
//            String audiolocalFilePath = FileDownloadUtil.getPath(audiofileName);
//            VideoUtil.convertVideoToAudio(localFilePath, audiolocalFilePath);
//            VideoRecognition2Handle.ASR asr = videoRecognition2Handle.asrGpuService(audiolocalFilePath, request.getEsId());
//            FileDownloadUtil.deleteFile(audiolocalFilePath);
//
//            //压缩视频
//            String compressMp4Path = FileDownloadUtil.getPath("compress_" + esId + ".mp4");
//            VideoUtil.compressVideo(localFilePath, compressMp4Path);
//            //上传压缩视频
//            String compressMp4OSSId = FilePathBuilder.getVideoDecodeOssPath(esId, "compress_" + esId + ".mp4", isOpen);
//            uploadVideo(compressMp4OSSId, compressMp4Path);
//            //切出视频头图
//            String headPicFilePath = FileDownloadUtil.getPath("head_pic_" + esId + ".jpg");
//            VideoUtil.cutImage(localFilePath, headPicFilePath, 5);
//            String headPicOSSId = FilePathBuilder.getVideoDecodeOssPath(esId, "head_pic_" + esId + ".jpg", isOpen);
//            uploadVideo(headPicOSSId, headPicFilePath);
//            dedupedData.setVideoHeadPicOssId(headPicOSSId);
//            FileDownloadUtil.deleteFile(headPicFilePath);
//
//            recognitionArg.setTitle(dedupedData.getTextTitle());
//            recognitionArg.setContent(dedupedData.getTextContent());
//            recognitionArg.setIndustry(IndustryEnum.getByCode(dedupedData.getKwKbIndustry()).getDescription());
//            String observationId = null;
//            if (AnalysisVideoTypeEnum.UPLOAD_VIDEO.getVideoType().equals(request.getTypeName())) {
//                observationId = ObservationIdUtil.getObservationId(TokenTaskTypeEnum.UPLOAD_TASK);
//            } else {
//                observationId = ObservationIdUtil.getObservationId(AnalysisVideoTypeEnum.VIDEO_ANALYSIS);
//            }
//            if (dedupedData.getSourceType() == 2) {
//                recognitionArg.setContent(null);
//                recognitionArg.setTitle(null);
//            }
//
//            VideoRecognition2Handle.ProcessVideo processVideo = videoRecognition2Handle.processVideo(recognitionArg, observationId);
//
//            String db_unique_id = processVideo.getData().getDb_unique_id();
//            dedupedData.setDbUniqueId(db_unique_id);
//            dedupedData.setAnalysisStatus(AnalysisStatusEnum.PROCESSING.getCode());
//            dedupedData.setUpdateTime(new Date());
//            pullTaskDedupedDataService.updateById(dedupedData);
//
//            AnalysisVideoTypeEnum type = AnalysisVideoTypeEnum.getByVideoTypeStr(request.getTypeName());
//            log.info("视频整体分析开始任务");
//            CmsVideoInfo videoInfo = new CmsVideoInfo();
//            if (type == AnalysisVideoTypeEnum.VIDEO_ANALYSIS) {
//                videoInfo.setEsId(esId);
//                videoInfo.setType(type.getVideoCode());
//                videoInfo.setStatus(VideoInfoStatusEnum.PROCESSING.getCode());
//                Arg arg = new Arg();
//                arg.setDbUniqueId(db_unique_id);
//                arg.setCompressMp4OSSId(compressMp4OSSId);
//                videoInfo.setArg(JSONObject.toJSONString(arg));
//                videoInfoService.save(videoInfo);
//            }
//            if (type == AnalysisVideoTypeEnum.UPLOAD_VIDEO && request.getVideoInfoId() != null) { // 上传视频已经创建过videInfo,并且关联了document
//                videoInfo = videoInfoService.getById(request.getVideoInfoId());
//                Arg arg = new Arg();
//                arg.setDbUniqueId(db_unique_id);
//                arg.setObservationId(observationId);
//                arg.setCompressMp4OSSId(compressMp4OSSId);
//                videoInfo.setArg(JSONObject.toJSONString(arg));
//                videoInfo.setStatus(VideoInfoStatusEnum.PROCESSING.getCode());
//                videoInfoService.updateById(videoInfo);
//            }
//            id = videoInfo.getId();
//
//            //保存结果数据
//            CmsVideoResult videoResult = new CmsVideoResult();
//            videoResult.setVideoId(videoInfo.getId());
//            videoResult.setType(VideoResultTypeEnum.ALL_ASR.getCode());
//            videoResult.setData(JSONObject.toJSONString(asr));
//            videoResultService.save(videoResult);
//
//            //保存结果数据
//            CmsVideoResult videoResult2 = new CmsVideoResult();
//            videoResult2.setVideoId(videoInfo.getId());
//            videoResult2.setType(VideoResultTypeEnum.ASR5.getCode());
//            videoResult2.setData(JSONObject.toJSONString(getFirst5SecondsASR(asr)));
//            videoResultService.save(videoResult2);
//
//
//            AnalysisVideoCreateVO analysisVideoCreateVO = new AnalysisVideoCreateVO();
//            analysisVideoCreateVO.setId(videoInfo.getId());
//
//            return analysisVideoCreateVO;
//
//        } catch (Exception e) {
//            log.error("视频分析失败", e);
//            updateVideoInfoStatusFailed(id, e.getMessage());
//            throw new BusinessException(e.getMessage());
//        }
//    }
//
//    //根据id更新视频信息状态为失败的
//    public void updateVideoInfoStatusFailed(Integer id, String message) {
//        if (id == null) {
//            return;
//        }
//        CmsVideoInfo videoInfo = videoInfoService.getById(id);
//        if (videoInfo == null) {
//            return;
//        }
//        videoInfo.setStatus(VideoInfoStatusEnum.ERROR.getCode());
//        videoInfo.setErrorMessage(message);
//        videoInfo.setUpdateTime(new Date());
//        videoInfoService.updateById(videoInfo);
//    }
//
//    @Data
//    public static class Arg {
//        //视频分析任务Id
//        private String dbUniqueId;
//
//        private String observationId;
//
//        private String compressMp4OSSId;
//    }
//
//    /**
//     * 获取前5秒的ASR结果
//     *
//     * @param asr VideoRecognition2Handle.ASR对象
//     * @return 拼接后的前5秒ASR文本
//     */
//    public QianchuanVideoStrategy.ASR5 getFirst5SecondsASR(VideoRecognition2Handle.ASR asr) {
//        if (asr == null || asr.getSentences() == null || asr.getSentences().isEmpty()) {
//            return new QianchuanVideoStrategy.ASR5("");
//        }
//
//        StringBuilder result = new StringBuilder();
//        for (VideoRecognition2Handle.Sentences sentence : asr.getSentences()) {
//            if (sentence.getStart() < 3000) { // 保留start小于3000毫秒的句子
//                result.append(sentence.getText()).append(" ");
//            }
//        }
//
//        return new QianchuanVideoStrategy.ASR5(result.toString().trim()); // 去除末尾多余的空格
//    }
//
//    @Override
//    @Transactional
//    public AnalysisVideoResultVO queryResult(AnalysisVideoQueryRequest request) {
//        Integer id = null;
//        String localMp4FilePath = null;
//        String compressMp4Path = null;
//        try {
//            String esId = request.getEsId();
//            if (StringUtils.isBlank(esId)) {
//                throw new BusinessException("esId不能为空");
//            }
//            LambdaQueryWrapper<CmsPullTaskDedupedData> queryWrapper = new LambdaQueryWrapper<>();
//            queryWrapper.eq(CmsPullTaskDedupedData::getEsId, esId);
//            CmsPullTaskDedupedData dedupedData = pullTaskDedupedDataService.getOne(queryWrapper);
//            if (dedupedData == null) {
//                throw new BusinessException("esId不存在");
//            }
//
//            AnalysisVideoTypeEnum type = AnalysisVideoTypeEnum.getByVideoTypeStr(request.getTypeName());
//            if (type == null) {
//                throw new BusinessException("getTypeName不存在");
//            }
//            CmsVideoInfo cmsVideoInfo = videoInfoService.getOne(new LambdaQueryWrapper<CmsVideoInfo>().eq(CmsVideoInfo::getEsId, esId)
//                    .eq(CmsVideoInfo::getType, type.getVideoCode())
//                    .orderByDesc(CmsVideoInfo::getCreateTime)
//                    .last("limit 1"));
//            if (cmsVideoInfo == null) {
//                throw new BusinessException("没有查询到视频分析结果信息");
//            } else {
//                if (cmsVideoInfo.getStatus() == VideoInfoStatusEnum.SUCCESS.getCode()) {
//                    AnalysisVideoResultVO analysisVideoResultVO = new AnalysisVideoResultVO();
//                    analysisVideoResultVO.setId(cmsVideoInfo.getId());
//                    analysisVideoResultVO.setStatus(cmsVideoInfo.getStatus());
//                    return analysisVideoResultVO;
//                }
//            }
//            id = cmsVideoInfo.getId();
//
//            AnalysisVideoResultVO analysisVideoResultVO = new AnalysisVideoResultVO();
//            analysisVideoResultVO.setId(cmsVideoInfo.getId());
//
//            boolean isOpen = false;
//            if (dedupedData.getSourceType() == 1) {
//                isOpen = true;
//            }
//
//            if (cmsVideoInfo.getStatus() != VideoInfoStatusEnum.PROCESSING.getCode()) {
//                analysisVideoResultVO.setStatus(cmsVideoInfo.getStatus());
//                if (VideoInfoStatusEnum.ERROR.getCode() == cmsVideoInfo.getStatus()) {
//                    dedupedData.setUpdateTime(new Date());
//                    dedupedData.setAnalysisStatus(3);
//                    pullTaskDedupedDataService.updateById(dedupedData);
//                }
//                if (VideoInfoStatusEnum.SUCCESS.getCode() == cmsVideoInfo.getStatus()) {
//                    dedupedData.setUpdateTime(new Date());
//                    dedupedData.setAnalysisStatus(2);
//                    pullTaskDedupedDataService.updateById(dedupedData);
//                }
//
//                return analysisVideoResultVO;
//            }
//
//            Arg arg = JSONObject.parseObject(cmsVideoInfo.getArg(), Arg.class);
//            VideoRecognition2Handle.VideoContent content = videoRecognition2Handle.queryVideo(arg.getDbUniqueId(), arg.getObservationId());
//            VideoRecognition2Handle.VideoContentData data = content.getData();
//
//            //Pending, Processing, Finished, Error
//            if ("Processing".equals(data.getStatus()) || "Pending".equals(data.getStatus())) {
//                analysisVideoResultVO.setStatus(cmsVideoInfo.getStatus());
//                return analysisVideoResultVO;
//            }
//
//            if ("Error".equals(data.getStatus())) {
//                cmsVideoInfo.setStatus(VideoInfoStatusEnum.ERROR.getCode());
//                cmsVideoInfo.setErrorMessage(data.getError_msg());
//                videoInfoService.updateById(cmsVideoInfo);
//                analysisVideoResultVO.setStatus(VideoInfoStatusEnum.ERROR.getCode());
//                return analysisVideoResultVO;
//            }
//
//
//            String response_body = data.getResponse_body();
//            //保存整体分析结果数据
//            CmsVideoResult videoResult = new CmsVideoResult();
//            videoResult.setVideoId(cmsVideoInfo.getId());
//            videoResult.setType(VideoResultTypeEnum.INDUSTRY_DECODING2.getCode());
//            videoResult.setData(response_body);
//            videoResultService.save(videoResult);
//
//            //整体分析结果
//            Map<String, Object> industryDecoding = JSONObject.parseObject(response_body, Map.class);
//
//
//            //黄金3秒打标
//            compressMp4Path = FileDownloadUtil.getPath("compress_" + esId + ".mp4");
//            String fileName = esId + ".mp4";
//            localMp4FilePath = FileDownloadUtil.getPath(fileName);
//
//
//            VideoRecognition2Handle.ImageDecodingRequest imageDecodingRequest = new VideoRecognition2Handle.ImageDecodingRequest();
//            imageDecodingRequest.setImages(List.of(VideoUtil.cutImageOfBase64(compressMp4Path, 1000),
//                    VideoUtil.cutImageOfBase64(compressMp4Path, 2000),
//                    VideoUtil.cutImageOfBase64(compressMp4Path, 3000),
//                    VideoUtil.cutImageOfBase64(compressMp4Path, 4000),
//                    VideoUtil.cutImageOfBase64(compressMp4Path, 5000)
//            ));
//
//            CmsVideoResult videoResult2 = videoResultService.getOne(new LambdaQueryWrapper<CmsVideoResult>().eq(CmsVideoResult::getVideoId, cmsVideoInfo.getId()).eq(CmsVideoResult::getType, VideoResultTypeEnum.ASR5.getCode()));
//            QianchuanVideoStrategy.ASR5 asr5 = JSONObject.toJavaObject(JSONObject.parseObject(videoResult2.getData()), QianchuanVideoStrategy.ASR5.class);
//            imageDecodingRequest.setSentences(asr5.getAsr5());
//            imageDecodingRequest.setTitle(dedupedData.getTextTitle());
//            imageDecodingRequest.setContent(null);
//            VideoRecognition2Handle.ImageDecodingResponse imageDecodingResponse = videoRecognition2Handle.imageDecoding3s(imageDecodingRequest, arg.getObservationId());
//            dedupedData.setHighlight(imageDecodingResponse.getData().getIs_highlight());
//
//            CmsVideoResult videoResult3 = new CmsVideoResult();
//            videoResult3.setVideoId(cmsVideoInfo.getId());
//            videoResult3.setType(VideoResultTypeEnum.GOLD_FIVE2.getCode());
//            videoResult3.setData(JSONObject.toJSONString(imageDecodingResponse.getData()));
//            videoResultService.save(videoResult3);
//
//
//            // 6: 视频分割接口
//            CmsVideoResult videoResultAsr = videoResultService.getOne(new LambdaQueryWrapper<CmsVideoResult>().eq(CmsVideoResult::getVideoId, cmsVideoInfo.getId()).eq(CmsVideoResult::getType, VideoResultTypeEnum.ALL_ASR.getCode()));
//            VideoRecognition2Handle.ASR asr = JSONObject.parseObject(videoResultAsr.getData(), VideoRecognition2Handle.ASR.class);
//            VideoRecognition2Handle.VideoSplitRequest videoSplitRequest = new VideoRecognition2Handle.VideoSplitRequest();
//            videoSplitRequest.setVideo_url(fileService.getPicDownloadSignatureUrl(dedupedData.getVideoOssId()   ));
//            videoSplitRequest.setVideo_asr(asr);
//            VideoRecognition2Handle.VideoSplitResponse videoSplitResponse = videoRecognition2Handle.videoSplit(videoSplitRequest, arg.getObservationId());
//            VideoRecognition2Handle.VideoSplitData splitData = videoSplitResponse.getData();
//            handleVideoSplitData(splitData, compressMp4Path, asr, dedupedData, localMp4FilePath, cmsVideoInfo, isOpen,arg.getObservationId());
//            //视频分割数据存储
//            CmsVideoResult videoResult4 = new CmsVideoResult();
//            videoResult4.setVideoId(cmsVideoInfo.getId());
//            videoResult4.setType(VideoResultTypeEnum.VIDEO_SPLIT.getCode());
//            videoResult4.setData(JSONObject.toJSONString(splitData));
//            videoResultService.save(videoResult4);
//
//            //分析状态更新
//            cmsVideoInfo.setStatus(VideoInfoStatusEnum.SUCCESS.getCode());
//            //更新黄金3秒类型
//            cmsVideoInfo.setThreeGoldType(imageDecodingResponse.getData().getHighlight_type());
//            cmsVideoInfo.setRating(industryDecoding.get("创意得分") + "");
//            cmsVideoInfo.setUpdateTime(new Date());
//            videoInfoService.updateById(cmsVideoInfo);
//            analysisVideoResultVO.setStatus(VideoInfoStatusEnum.SUCCESS.getCode());
//            //更新千川表的状态
//            dedupedData.setAnalysisStatus(2);
//            dedupedData.setProductName(qianchuanVideoStrategy.joinWithSemicolon(industryDecoding.get("产品名称") + ""));
//            dedupedData.setCellingPoint(qianchuanVideoStrategy.joinWithSemicolon(industryDecoding.get("卖点") + ""));
//            dedupedData.setAimingTribe(qianchuanVideoStrategy.joinWithSemicolon(industryDecoding.get("受众人群") + ""));
//
//            dedupedData.setRating(Float.parseFloat(cmsVideoInfo.getRating()));
//            dedupedData.setUpdateTime(new Date());
//            pullTaskDedupedDataService.updateById(dedupedData);
//
//            try {
//                if (cmsVideoInfo.getType().equals(4)) {
//                    String filename = dedupedData.getTextContent() == null ? "esId: " + dedupedData.getEsId() : dedupedData.getTextContent();
//                    tokenUseDetailService.countTokenUse(arg.getObservationId(), cmsVideoInfo.getId(), 1, filename,
//                            cmsVideoInfo.getTenantId(), cmsVideoInfo.getUserId().toString(), null);
//                }
//            } catch (Exception e) {
//                log.error("记录token使用失败", e);
//            }
//
//            return analysisVideoResultVO;
//        } catch (Exception e) {
//            log.error("视频分析失败", e);
//            updateVideoInfoStatusFailed(id, e.getMessage());
//            throw new BusinessException(e.getMessage());
//        } finally {
//            FileDownloadUtil.deleteFile(localMp4FilePath);
//            FileDownloadUtil.deleteFile(compressMp4Path);
//        }
//    }
//
//    private void handleVideoSplitData(VideoRecognition2Handle.VideoSplitData splitData, String compressMp4Path, VideoRecognition2Handle.ASR asr, CmsPullTaskDedupedData dedupedData, String localMp4FilePath, CmsVideoInfo cmsVideoInfo, Boolean isOpen,String observationId) {
//        Map<String, VideoRecognition2Handle.ASRFragment> map = splitData.getASR();
//        List<QianchuanVideoStrategy.ASRFragment> asrFragmentList = qianchuanVideoStrategy.handleUserASRPre(map, dedupedData.getEsId(), isOpen);
//        List<List<Integer>> list = splitData.get画面分镜();
//        Long videoLength = VideoUtil.getVideoLength(compressMp4Path) * 1000;
////        int index = 0;
////        List<Future<?>> futures = new ArrayList<>();
////        for (List<Integer> time : list) {
////            Future<?> future = analysisThreadExecutor.submit(new SceneSegmentTask(
////                    time, compressMp4Path, asr, dedupedData,
////                    localMp4FilePath, cmsVideoInfo, isOpen,
////                    splitData, asrFragmentList, index++,videoLength,observationId
////            ));
////            futures.add(future);
////        }
////
////        // 等待所有任务完成
////        for (Future<?> future : futures) {
////            try {
////                future.get(60,  TimeUnit.SECONDS); // 可选：设置超时时间
////            } catch (Exception  e) {
////                log.error("并发处理分镜失败", e);
////                throw new BusinessException("并发处理分镜失败");
////            }
////        }
//
//        int index = 0;
//        for (List<Integer> time : list) {
//            Integer start = time.get(0);
//            Integer end = time.get(1);
//            if (end > videoLength) {
//                end = videoLength.intValue();
//            }
//            //5: 分镜图片打标接口
//            Map<String, String> sceneDecoding = sceneDecoding(compressMp4Path, asr, start, end, dedupedData,observationId);
//            sceneDecoding.put("start", start + "");
//            sceneDecoding.put("end", end + "");
//            //切视频片段
//            String fileName = "segment_" + dedupedData.getEsId() + "_" + start + "_" + end + ".mp4";
//            String segmentFilePath = FileDownloadUtil.getPath(fileName);
//            log.info("localMp4FilePath:{},segmentFilePath:{},start:{},end:{}", localMp4FilePath, segmentFilePath, start, end);
//            VideoUtil.cutVideoSegment(localMp4FilePath, segmentFilePath, start, end);
//            //上传视频片段到OSS
//            String ossId = FilePathBuilder.getVideoDecodeOssPath(dedupedData.getEsId(), fileName, isOpen);
//
//
//            uploadVideo(ossId, segmentFilePath);
//            sceneDecoding.put("sceneDecodingSegmentOssId", ossId);
//            //切出视频头图
//            String picFileName = "pic_" + dedupedData.getEsId() + "_" + start + "_" + end + ".jpg";
//            String picFilePath = FileDownloadUtil.getPath(picFileName);
//            VideoUtil.cutImage(segmentFilePath, picFilePath, 0);
//            String picOssId = FilePathBuilder.getVideoDecodeOssPath(dedupedData.getEsId(), picFileName, isOpen);
//
//
//            sceneDecoding.put("sceneDecodingSegmentPicOssId", picOssId);
//            uploadVideo(picOssId, picFilePath);
//
//            if (start < 5000) {
//                sceneDecoding.put("highlight", dedupedData.getHighlight() + "");
//            }
//
//            //把ASR片段加入到这个里面
//            if (splitData.isUse_ASR()) {
//                //加入台词，视频ASR分片信息
//                qianchuanVideoStrategy.handleUserASR(sceneDecoding, asrFragmentList);
//            } else {
//                sceneDecoding.put("sceneDecodingOssId", ossId);
//                sceneDecoding.put("sceneDecodingImageOssId", picOssId);
//            }
//
//            //保存数据
//            CmsVideoResult videoResult = new CmsVideoResult();
//            videoResult.setVideoId(cmsVideoInfo.getId());
//            videoResult.setIndex(index++);
//            videoResult.setType(VideoResultTypeEnum.SCENE_SPLIT2.getCode());
//            videoResult.setData(JSONObject.toJSONString(sceneDecoding));
//            videoResultService.save(videoResult);
//
//
//            //删除本地的素材
//            FileDownloadUtil.deleteFile(picFilePath);
//            FileDownloadUtil.deleteFile(segmentFilePath);
//        }
//
//
//    }
//
//
//    private class SceneSegmentTask implements Runnable {
//        private final List<Integer> time;
//        private final String compressMp4Path;
//        private final VideoRecognition2Handle.ASR asr;
//        private final CmsPullTaskDedupedData dedupedData;
//        private final String localMp4FilePath;
//        private final CmsVideoInfo cmsVideoInfo;
//        private final Boolean isOpen;
//        private final VideoRecognition2Handle.VideoSplitData splitData;
//        private final List<QianchuanVideoStrategy.ASRFragment> asrFragmentList;
//        private final int index;
//        private final Long videoLength;
//        private final String observationId;
//
//        public SceneSegmentTask(List<Integer> time, String compressMp4Path, VideoRecognition2Handle.ASR asr,
//                                CmsPullTaskDedupedData dedupedData, String localMp4FilePath,
//                                CmsVideoInfo cmsVideoInfo, Boolean isOpen,
//                                VideoRecognition2Handle.VideoSplitData splitData,
//                                List<QianchuanVideoStrategy.ASRFragment> asrFragmentList, int index, Long videoLength,String observationId) {
//            this.time = time;
//            this.compressMp4Path = compressMp4Path;
//            this.asr = asr;
//            this.dedupedData = dedupedData;
//            this.localMp4FilePath = localMp4FilePath;
//            this.cmsVideoInfo = cmsVideoInfo;
//            this.isOpen = isOpen;
//            this.splitData = splitData;
//            this.asrFragmentList = asrFragmentList;
//            this.index = index;
//            this.videoLength = videoLength;
//            this.observationId = observationId;
//        }
//
//        @Override
//        public void run() {
//            Integer start = time.get(0);
//            Integer end = time.get(1);
//            if (end > videoLength) {
//                end = videoLength.intValue();
//            }
//            Map<String, String> sceneDecoding = sceneDecoding(compressMp4Path, asr, start, end, dedupedData,observationId);
//            sceneDecoding.put("start", start + "");
//            sceneDecoding.put("end", end + "");
//
//            String fileName = "segment_" + dedupedData.getEsId() + "_" + start + "_" + end + ".mp4";
//            String segmentFilePath = FileDownloadUtil.getPath(fileName);
//            log.info("localMp4FilePath:{},segmentFilePath:{},start:{},end:{}", localMp4FilePath, segmentFilePath, start, end);
//            VideoUtil.cutVideoSegment(localMp4FilePath, segmentFilePath, start, end);
//
//            String ossId = FilePathBuilder.getVideoDecodeOssPath(dedupedData.getEsId(), fileName, isOpen);
//            uploadVideo(ossId, segmentFilePath);
//            sceneDecoding.put("sceneDecodingSegmentOssId", ossId);
//
//            String picFileName = "pic_" + dedupedData.getEsId() + "_" + start + "_" + end + ".jpg";
//            String picFilePath = FileDownloadUtil.getPath(picFileName);
//            VideoUtil.cutImage(segmentFilePath, picFilePath, 0);
//            String picOssId = FilePathBuilder.getVideoDecodeOssPath(dedupedData.getEsId(), picFileName, isOpen);
//            uploadVideo(picOssId, picFilePath);
//            sceneDecoding.put("sceneDecodingSegmentPicOssId", picOssId);
//
//            if (start < 5000) {
//                sceneDecoding.put("highlight", dedupedData.getHighlight() + "");
//            }
//
//            if (splitData.isUse_ASR()) {
//                qianchuanVideoStrategy.handleUserASR(sceneDecoding, asrFragmentList);
//            } else {
//                sceneDecoding.put("sceneDecodingOssId", ossId);
//                sceneDecoding.put("sceneDecodingImageOssId", picOssId);
//            }
//
//            CmsVideoResult videoResult = new CmsVideoResult();
//            videoResult.setVideoId(cmsVideoInfo.getId());
//            videoResult.setIndex(index);
//            videoResult.setType(VideoResultTypeEnum.SCENE_SPLIT2.getCode());
//            videoResult.setData(JSONObject.toJSONString(sceneDecoding));
//            videoResultService.save(videoResult);
//
//            FileDownloadUtil.deleteFile(picFilePath);
//            FileDownloadUtil.deleteFile(segmentFilePath);
//        }
//    }
//
//
//    private Map<String, String> sceneDecoding(String compressMp4Path, VideoRecognition2Handle.ASR asr, Integer start, Integer end, CmsPullTaskDedupedData dedupedData,String observationId) {
//        // 5: 分镜图片打标接口
//        VideoRecognition2Handle.SceneDecodingRequest sceneDecodingRequest = new VideoRecognition2Handle.SceneDecodingRequest();
//        sceneDecodingRequest.setImages(List.of(
//                VideoUtil.cutImageOfBase64(compressMp4Path, start),
//                VideoUtil.cutImageOfBase64(compressMp4Path, (start + end) / 2),
//                VideoUtil.cutImageOfBase64(compressMp4Path, end)
//        ));
//        sceneDecodingRequest.setSentences(getASR(asr, start, end));
//        sceneDecodingRequest.setTitle(dedupedData.getTextTitle());
//        sceneDecodingRequest.setContent(null);
//        VideoRecognition2Handle.SceneDecodingResponse sceneDecodingResponse = videoRecognition2Handle.sceneDecoding(sceneDecodingRequest, observationId);
//        Map<String, String> data = sceneDecodingResponse.getData();
//        return data;
//    }
//
//
//    private void uploadVideo(String ossId, String localFilePath) {
//        try {
//            cmsS3FlowService.upload(ossId, new File(localFilePath));
//        } catch (Exception e) {
//            log.error("上传视频图片失败", e);
//            throw new BusinessException("上传视频图片失败");
//        }
//    }
//
//    public String getASR(VideoRecognition2Handle.ASR asr, Integer start, Integer end) {
//        if (asr == null || asr.getSentences() == null || asr.getSentences().isEmpty()) {
//            return "";
//        }
//        StringBuilder result = new StringBuilder();
//        for (VideoRecognition2Handle.Sentences sentence : asr.getSentences()) {
//            if (sentence.getStart() >= start && sentence.getStart() < end) {
//                result.append(sentence.getText());
//            }
//
//        }
//        return result.toString();
//
//    }
//
//    @Data
//    @AllArgsConstructor
//    public static class ASR5 {
//        private String asr5;
//    }
//
//
//}
