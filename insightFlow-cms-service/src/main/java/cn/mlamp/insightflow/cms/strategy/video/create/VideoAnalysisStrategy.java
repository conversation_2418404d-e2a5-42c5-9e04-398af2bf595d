//package cn.mlamp.insightflow.cms.strategy.video.create;
//
//import cn.mlamp.insightflow.cms.common.file.IS3FlowService;
//import cn.mlamp.insightflow.cms.config.AnalysisVideoConfig;
//import cn.mlamp.insightflow.cms.entity.*;
//import cn.mlamp.insightflow.cms.enums.*;
//import cn.mlamp.insightflow.cms.exception.BusinessException;
//import cn.mlamp.insightflow.cms.model.query.AnalysisVideoCreateRequest;
//import cn.mlamp.insightflow.cms.model.query.AnalysisVideoQueryRequest;
//import cn.mlamp.insightflow.cms.model.vo.AnalysisVideoCreateVO;
//import cn.mlamp.insightflow.cms.model.vo.AnalysisVideoResultVO;
//import cn.mlamp.insightflow.cms.service.*;
//import cn.mlamp.insightflow.cms.strategy.handle.VideoRecognitionHandle;
//import cn.mlamp.insightflow.cms.util.FileDownloadUtil;
//import cn.mlamp.insightflow.cms.util.ObservationIdUtil;
//import cn.mlamp.insightflow.cms.util.VideoUtil;
//import com.alibaba.fastjson.JSONObject;
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
//import jakarta.annotation.PostConstruct;
//import jakarta.annotation.Resource;
//import jodd.io.FileNameUtil;
//import lombok.Data;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//// import org.springframework.ai.document.Document;
//// import org.springframework.ai.vectorstore.pgvector.PgVectorStore;
//import org.springframework.beans.factory.annotation.Autowired;
//// import org.springframework.beans.factory.annotation.Qualifier;
//import org.springframework.stereotype.Component;
//import org.springframework.transaction.annotation.Transactional;
//
//import java.io.File;
//import java.util.*;
//
//
///**
// * @Author: husuper
// * @CreateTime: 2025-02-18
// */
//@Slf4j
//@Component
//@RequiredArgsConstructor
//public class VideoAnalysisStrategy implements ProcessAnalysisVideoStrategyInterface {
//
//    private final CmsPullTaskDedupedDataService pullTaskDedupedDataService;
//
//    private final VideoRecognitionHandle videoRecognitionHandle;
//
//    private final IVideoInfoService videoInfoService;
//
//    private final IVideoResultService videoResultService;
//
//    private final IVideoAsrService videoAsrService;
//
//    private final FileService fileService;
//
//    @Resource(name = "cmsS3FlowService")
//    private IS3FlowService cmsS3FlowService;
//
//    private final AnalysisVideoConfig analysisVideoConfig;
//
//    @Autowired
//    private final TokenUseDetailService tokenUseDetailService;
//
//
//    @PostConstruct
//    public void init() {
//        AnalysisVideoStrategyMap.register(AnalysisVideoTypeEnum.VIDEO_ANALYSIS.getVideoType(), this);
//        AnalysisVideoStrategyMap.register(AnalysisVideoTypeEnum.UPLOAD_VIDEO.getVideoType(), this);
//    }
//
//
//    @Override
//    @Transactional
//    public AnalysisVideoCreateVO process(AnalysisVideoCreateRequest request) {
//        String esId=request.getEsId();
//        if(StringUtils.isBlank(esId)){
//            throw new BusinessException("esId不能为空");
//        }
//        LambdaQueryWrapper<CmsPullTaskDedupedData> queryWrapper =new LambdaQueryWrapper<>();
//        queryWrapper.eq(CmsPullTaskDedupedData::getEsId,esId);
//        CmsPullTaskDedupedData dedupedData = pullTaskDedupedDataService.getOne(queryWrapper);
//        if(dedupedData==null){
//            throw new BusinessException("esId不存在");
//        }
//        if(dedupedData.getDownloadStatus()!= DownloadStatusEnum.SUCCESS.getCode()){
//            throw new BusinessException("视频未下载完成");
//        }
//
//        //调用算法
//        VideoRecognitionHandle.RecognitionArg recognitionArg = new VideoRecognitionHandle.RecognitionArg();
//        if(StringUtils.isBlank(request.getVideoDownloadUrl())){
//            recognitionArg.setVideo_url(VideoUtil.getVideoUrl(dedupedData.getDatePublishedAt(),dedupedData.getEsId()));
//            if(dedupedData.getSourceType() == 3){ //链接上传,取createTime
//                recognitionArg.setVideo_url(VideoUtil.getVideoUrl(dedupedData.getCreateTime(),dedupedData.getEsId()));
//            }
//        }else{
//            recognitionArg.setVideo_url(request.getVideoDownloadUrl());
//        }
//        recognitionArg.setTitle(dedupedData.getTextTitle());
//        recognitionArg.setContent(dedupedData.getTextContent());
//        recognitionArg.setIndustry(IndustryEnum.getByCode(dedupedData.getKwKbIndustry()).getDescription());
//        String observationId = null;
//        if(AnalysisVideoTypeEnum.UPLOAD_VIDEO.getVideoType().equals(request.getTypeName())){
//             observationId = ObservationIdUtil.getObservationId(TokenTaskTypeEnum.UPLOAD_TASK);
//        }else{
//            observationId = ObservationIdUtil.getObservationId(AnalysisVideoTypeEnum.VIDEO_ANALYSIS);
//        }
//        if(dedupedData.getSourceType()==2){
//            recognitionArg.setContent(null);
//            recognitionArg.setTitle(null);
//        }
//
//        VideoRecognitionHandle.ProcessVideo processVideo = videoRecognitionHandle.processVideo(recognitionArg, observationId);
//
//        String db_unique_id = processVideo.getData().getDb_unique_id();
//        dedupedData.setDbUniqueId(db_unique_id);
//        dedupedData.setAnalysisStatus(AnalysisStatusEnum.PROCESSING.getCode());
//        pullTaskDedupedDataService.updateById(dedupedData);
//
//        AnalysisVideoTypeEnum type = AnalysisVideoTypeEnum.getByVideoTypeStr(request.getTypeName());
//        log.info("视频整体分析开始任务");
//        CmsVideoInfo videoInfo = new CmsVideoInfo();
//        if (type == AnalysisVideoTypeEnum.VIDEO_ANALYSIS) {
//            videoInfo.setEsId(esId);
//            videoInfo.setType(type.getVideoCode());
//            videoInfo.setStatus(VideoInfoStatusEnum.PROCESSING.getCode());
//            videoInfo.setUserId(request.getUserId());
//            videoInfo.setTenantId(request.getTenantId());
//            Arg arg = new Arg();
//            arg.setDbUniqueId(db_unique_id);
//            videoInfo.setArg(JSONObject.toJSONString(arg));
//            videoInfoService.save(videoInfo);
//        }
//        if (type == AnalysisVideoTypeEnum.UPLOAD_VIDEO && request.getVideoInfoId() != null) { // 上传视频已经创建过videInfo,并且关联了document
//            videoInfo = videoInfoService.getById(request.getVideoInfoId());
//            Arg arg = new Arg();
//            arg.setDbUniqueId(db_unique_id);
//            arg.setObservationId(observationId);
//            videoInfo.setArg(JSONObject.toJSONString(arg));
//            videoInfo.setStatus(VideoInfoStatusEnum.PROCESSING.getCode());
//            videoInfoService.updateById(videoInfo);
//        }
//
//        AnalysisVideoCreateVO analysisVideoCreateVO = new AnalysisVideoCreateVO();
//        analysisVideoCreateVO.setId(videoInfo.getId());
//
//        return analysisVideoCreateVO;
//    }
//
//    @Data
//    public static class Arg{
//        //视频分析任务Id
//        private String dbUniqueId;
//
//        private String observationId;
//    }
//
//    @Override
//    @Transactional
//    public AnalysisVideoResultVO queryResult(AnalysisVideoQueryRequest request) {
//        String esId=request.getEsId();
//        if(StringUtils.isBlank(esId)){
//            throw new BusinessException("esId不能为空");
//        }
//        LambdaQueryWrapper<CmsPullTaskDedupedData> queryWrapper =new LambdaQueryWrapper<>();
//        queryWrapper.eq(CmsPullTaskDedupedData::getEsId,esId);
//        CmsPullTaskDedupedData dedupedData = pullTaskDedupedDataService.getOne(queryWrapper);
//        if(dedupedData==null){
//            throw new BusinessException("esId不存在");
//        }
//
//        AnalysisVideoTypeEnum type = AnalysisVideoTypeEnum.getByVideoTypeStr(request.getTypeName());
//        if (type == null) {
//            throw new BusinessException("getTypeName不存在");
//        }
//        CmsVideoInfo cmsVideoInfo = videoInfoService.getOne(new LambdaQueryWrapper<CmsVideoInfo>().eq(CmsVideoInfo::getEsId, esId)
//                .eq(CmsVideoInfo::getType, type.getVideoCode())
//                .orderByDesc(CmsVideoInfo::getCreateTime)
//                .last("limit 1"));
//        if (cmsVideoInfo == null) {
//            throw new BusinessException("没有查询到视频分析结果信息");
//        }else{
//            if(cmsVideoInfo.getStatus()== VideoInfoStatusEnum.SUCCESS.getCode()){
//                AnalysisVideoResultVO analysisVideoResultVO=new AnalysisVideoResultVO();
//                analysisVideoResultVO.setId(cmsVideoInfo.getId());
//                analysisVideoResultVO.setStatus(cmsVideoInfo.getStatus());
//                return analysisVideoResultVO;
//            }
//        }
//        AnalysisVideoResultVO analysisVideoResultVO=new AnalysisVideoResultVO();
//        analysisVideoResultVO.setId(cmsVideoInfo.getId());
//
//        if(cmsVideoInfo.getStatus()!=VideoInfoStatusEnum.PROCESSING.getCode()){
//            analysisVideoResultVO.setStatus(cmsVideoInfo.getStatus());
//            return analysisVideoResultVO;
//        }
//
//        Arg arg=JSONObject.parseObject(cmsVideoInfo.getArg(),Arg.class);
//        VideoRecognitionHandle.VideoContent content= videoRecognitionHandle.queryVideo(arg.getDbUniqueId(),arg.getObservationId());
//        VideoRecognitionHandle.VideoContentData data=content.getData();
//
//        //Pending, Processing, Finished, Error
//        if("Processing".equals(data.getStatus()) ||"Pending".equals(data.getStatus())) {
//            analysisVideoResultVO.setStatus(cmsVideoInfo.getStatus());
//            return analysisVideoResultVO;
//        }
//
//        if("Error".equals(data.getStatus())){
//            cmsVideoInfo.setStatus(VideoInfoStatusEnum.ERROR.getCode());
//            cmsVideoInfo.setErrorMessage(data.getError_msg());
//            videoInfoService.updateById(cmsVideoInfo);
//            analysisVideoResultVO.setStatus(VideoInfoStatusEnum.ERROR.getCode());
//            return analysisVideoResultVO;
//        }
//        VideoRecognitionHandle.ResponseBody responseBody = JSONObject.parseObject(data.getResponse_body(), VideoRecognitionHandle.ResponseBody.class);
//
//        //行业解码信息
//        Map<String, String> industryDecoding = responseBody.getIndustry_decoding();
//        CmsVideoResult videoResult5=new CmsVideoResult();
//        videoResult5.setVideoId(cmsVideoInfo.getId());
//        videoResult5.setType(VideoResultTypeEnum.PRE_INDUSTRY_DECODING.getCode());
//        videoResult5.setData(JSONObject.toJSONString(industryDecoding));
//        videoResultService.save(videoResult5);
//
//        String rating=industryDecoding.get("创意得分");
//        String ratingStr=industryDecoding.get("评分理由");
//
//        CmsVideoResult videoResult1=new CmsVideoResult();
//        videoResult1.setVideoId(cmsVideoInfo.getId());
//        videoResult1.setType(VideoResultTypeEnum.INDUSTRY_DECODING.getCode());
//        videoResult1.setData(JSONObject.toJSONString(handel(industryDecoding,IndustryEnum.getByCode(dedupedData.getKwKbIndustry()))));
//        videoResultService.save(videoResult1);
//
//
//        //保存ASR数据
//        VideoRecognitionHandle.ASR asr=responseBody.getAsr();
//        CmsVideoResult videoResult3=new CmsVideoResult();
//        videoResult3.setVideoId(cmsVideoInfo.getId());
//        videoResult3.setType(3);
//        videoResult3.setData(JSONObject.toJSONString(new CmsVideoResult.Asr(asr.getText())));
//        videoResultService.save(videoResult3);
//
//        //单表保存
//        List<VideoRecognitionHandle.Sentences> sentences = asr.getSentences();
//        List<CmsVideoAsr> cmsVideoAsrList=new ArrayList<>();
//        for (VideoRecognitionHandle.Sentences sentence : sentences) {
//            CmsVideoAsr videoAsr=new CmsVideoAsr();
//            videoAsr.setVideoId(cmsVideoInfo.getId());
//            videoAsr.setStart(sentence.getStart());
//            videoAsr.setEnd(sentence.getEnd());
//            videoAsr.setText(sentence.getText());
//            videoAsr.setEsId(dedupedData.getEsId());
//            cmsVideoAsrList.add(videoAsr);
//        }
//        // saveToVectorStore(cmsVideoAsrList,dedupedData);
//        videoAsrService.saveBatch(cmsVideoAsrList);
//
//        boolean flag=false;
//        if(!FileDownloadUtil.fileExistenceChecker(request.getLocalFilePath())){
//            //下载视频
//            String videoUrl = VideoUtil.getVideoUrl(dedupedData.getDatePublishedAt(), dedupedData.getEsId());
//            if(dedupedData.getSourceType() == 3) { //链接上传,取createTime
//                videoUrl = VideoUtil.getVideoUrl(dedupedData.getCreateTime(), dedupedData.getEsId());
//            }
//            String localFilePath;
//            if(dedupedData.getSourceType() == 2){ //本地文件上传，文件名后缀取text_content(doc.name)，链接签名取kw_video_url（doc.objId）
//                videoUrl = fileService.getPicDownloadSignatureUrl(dedupedData.getKwVideoUrl());
//                String filename = dedupedData.getEsId() +"." + FileNameUtil.getExtension(dedupedData.getTextContent()).toLowerCase();
//                localFilePath = FileDownloadUtil.getPath(filename);
//            }else {
//                String fileName= dedupedData.getEsId()+".mp4";
//                localFilePath = FileDownloadUtil.getPath(fileName);
//            }
//            FileDownloadUtil.downloadFile2(videoUrl, localFilePath);
//            request.setLocalFilePath(localFilePath);
//            flag=true;
//        }
//
//
//        // 分镜识别信息
//        List<Map<String, Object>> sceneSplit = responseBody.getScene_split();
//        int i=1;
//        String kwHeadImage=null;
//        Long longVideoDuration= null;
//        for (Map<String, Object> map : sceneSplit) {
//            CmsVideoResult videoResult2=new CmsVideoResult();
//            videoResult2.setVideoId(cmsVideoInfo.getId());
//            videoResult2.setIndex(i++);
//            videoResult2.setType(2);
//            //切图片
//            long time=convertToMilliseconds(map.get("镜头参考").toString());
//            String fileName=time+".jpg";
//            String localFilePath = FileDownloadUtil.getPath(fileName);
//            VideoUtil.cutImage(request.getLocalFilePath(),localFilePath,time);
//            String ossId=analysisVideoConfig.getVideoPicPath()+"/"+esId+"/"+time+".jpg";
//            uploadVideoPic(ossId,localFilePath);
//            map.put("pic",analysisVideoConfig.getDomain()+"/"+ossId);
//            videoResult2.setData(JSONObject.toJSONString(map));
//            videoResultService.save(videoResult2);
//            if(videoResult2.getIndex().equals(1)){
//                kwHeadImage=map.get("pic").toString();
//                longVideoDuration=VideoUtil.getVideoLength(request.getLocalFilePath());
//            }
//            FileDownloadUtil.deleteFile(localFilePath);
//        }
//
//        cmsVideoInfo.setStatus(VideoInfoStatusEnum.SUCCESS.getCode());
//        cmsVideoInfo.setRating(responseBody.getRating());
//
//        //增加创意分值
//        updateRatingByEsId(dedupedData.getEsId(),convertStringToFloat(rating),ratingStr, kwHeadImage,asr.getText(),dedupedData.getSourceType(),longVideoDuration);
//
//
//        videoInfoService.updateById(cmsVideoInfo);
//        analysisVideoResultVO.setStatus(VideoInfoStatusEnum.SUCCESS.getCode());
//        if(flag){
//            FileDownloadUtil.deleteFile(request.getLocalFilePath());
//        }
//        try {
//            if(cmsVideoInfo.getType().equals(4)){
//                String filename = dedupedData.getTextContent() == null ? "esId: " + dedupedData.getEsId() : dedupedData.getTextContent();
//                tokenUseDetailService.countTokenUse(arg.getObservationId(),cmsVideoInfo.getId(),1, filename,
//                        cmsVideoInfo.getTenantId(),cmsVideoInfo.getUserId().toString(),null);
//            }
//        }catch (Exception e){
//            log.error("记录token使用失败",e);
//        }
//
//
//
//        return analysisVideoResultVO;
//    }
//
//    private void  updateRatingByEsId(String esId,Float rating,String ratingStr, String kwHeadImage,String asr,int sourceType,Long longVideoDuration){
//        if(esId==null||rating==null){
//            return;
//        }
//        LambdaUpdateWrapper<CmsPullTaskDedupedData>  lambdaUpdateWrapper=new LambdaUpdateWrapper<>();
//        lambdaUpdateWrapper.set(CmsPullTaskDedupedData::getRating,rating);
//        lambdaUpdateWrapper.set(CmsPullTaskDedupedData::getCreativeReasons,ratingStr);
//        if(StringUtils.isNoneBlank(kwHeadImage) && sourceType==2){
//            lambdaUpdateWrapper.set(CmsPullTaskDedupedData::getKwHeadImage,kwHeadImage);
//        }
//        if(longVideoDuration!=null && sourceType==2){
//            lambdaUpdateWrapper.set(CmsPullTaskDedupedData::getLongVideoDuration,longVideoDuration);
//        }
//        if(StringUtils.isNoneBlank(asr)){
//            lambdaUpdateWrapper.set(CmsPullTaskDedupedData::getKwVideoContent,asr);
//        }
//        lambdaUpdateWrapper.eq(CmsPullTaskDedupedData::getEsId,esId);
//        lambdaUpdateWrapper.eq(CmsPullTaskDedupedData::getIsDeleted,0);
//        pullTaskDedupedDataService.update(lambdaUpdateWrapper);
////        pullTaskDedupedDataService.update(new LambdaUpdateWrapper<CmsPullTaskDedupedData>().
////                set(CmsPullTaskDedupedData::getRating,rating)
////                .eq(CmsPullTaskDedupedData::getEsId,esId).eq(BaseEntity::getIsDeleted,0)
////                .eq(CmsPullTaskDedupedData::getSourceType,1));
//    }
//
//    public static Float convertStringToFloat(String input) {
//        try {
//            // 首先将字符串转换为浮点数
//            Float value = Float.parseFloat(input);
//            // 将结果乘以
//            value *= 1;
//            return value;
//        }catch (Exception e){
//            log.error("转换失败",e);
//            return null;
//        }
//
//    }
//
//
//    public static long convertToMilliseconds(String time) {
//        try {
//            if (time.contains(":")) {
//                // 处理格式为 "00:02"
//                String[] parts = time.split(":");
//                int minutes = Integer.parseInt(parts[0]);
//                int seconds = Integer.parseInt(parts[1]);
//                return (minutes * 60 + seconds) * 1000L;
//            } else if (time.endsWith("s")) {
//                // 处理格式为 "0.4s"
//                double seconds = Double.parseDouble(time.substring(0, time.length() - 1));
//                return (long) (seconds * 1000);
//            } else if (time.contains(".")) {
//                // 处理格式为 "0.0" 和 "0.3"
//                double seconds = Double.parseDouble(time);
//                return (long) (seconds * 1000);
//            } else {
//                // 处理格式为 "123344"
//                return Long.parseLong(time);
//            }
//        } catch (Exception e) {
//            log.error("转换失败",e);
//            throw new BusinessException("无效的时间格式: " + time);
//        }
//    }
//
//
//    // @Autowired
//    // @Qualifier("combinedDataVectorStore")
//    // private PgVectorStore combinedDataVectorStore;
//
//    // @Autowired
//    // @Qualifier("videoContentVectorStore")
//    // private PgVectorStore videoContentVectorStore;
//
//
//    // public void saveToVectorStore(List<CmsVideoAsr>
//    // cmsVideoAsrList,CmsPullTaskDedupedData dedupedData) {
//    // try {
//    // // 创建两个列表，分别存储 TextNickName 和 TextContent 的 Document
//    // List<Document> documents = new ArrayList<>();
//
//    // // 遍历 rawDataList，分别构造 Document 对象并存储
//    // for (CmsVideoAsr rawData : cmsVideoAsrList) {
//    // // 创建存储 TextNickName 的 Document
//    // documents.add(new Document(rawData.getText(),
//    // Map.of("id", rawData.getEsId(), "datePublishedAt",
//    // dedupedData.getDatePublishedAt()) // 存储 id
//    // ));
//    // }
//
//    // videoContentVectorStore.add(documents);
//    // combinedDataVectorStore.add(documents);
//    // log.info("数据已成功保存到向量库");
//    // }catch (Exception e){
//    // log.error("保存向量库失败",e);
//    // }
//    // }
//
//
//
//    private Map<String, Set<String>> handel(Map<String, String> industryDecoding, IndustryEnum industryEnum) {
//        Map<String, Set<String>> result = new TreeMap<>();
//        if (industryEnum == IndustryEnum.BEAUTY) {
//            result.put("视频场景", extractFields(industryDecoding,
//                    "情景切入", "提及场景", "产品展示方式", "产品出现情况", "素材人物"));
//            result.put("视频风格", extractFields(industryDecoding,
//                    "镜头切换手法", "视觉效果", "叙事方式", "视频类型", "字幕类型", "画面类型", "贴图相关", "视频风格"));
//            result.put("产品特性", extractFields(industryDecoding,
//                    "产品类型", "产品名称", "产品质地表述", "产品妆效、妆感表述", "上妆技巧及手法表述", "持妆环境", "持妆时间", "持妆效果",
//                    "遮瑕能力", "产品功效", "针对痛点", "产品成分"));
//            result.put("内容表达", extractFields(industryDecoding,
//                    "情感表述", "是否有反差式叙事镜头", "是否有反差化的表达"));
//        } else if (industryEnum == IndustryEnum.THREE_C) {
//            result.put("视频制作", extractFields(industryDecoding,
//                    "情景切入", "提及场景", "产品展示方式", "素材人物", "镜头切换手法", "叙事方式", "视觉效果",
//                    "视频类型", "字幕类型", "画面类型", "贴图相关", "视频风格"));
//            result.put("产品信息", extractFields(industryDecoding,
//                    "产品类型", "产品型号", "产品配置", "产品卖点", "产品使用场景", "产品配色",
//                    "产品出现方式", "AI能力", "价格"));
//            result.put("用户相关", extractFields(industryDecoding,
//                    "视频人数", "人物关系", "图片文字描述", "内容中提及的车主的性别、年龄、收入/消费水平、生活方式",
//                    "使用车辆的场景", "使用车辆的需求", "使用车辆的痛点"));
//            result.put("情感表达", extractFields(industryDecoding,
//                    "情感表述", "是否有反差式叙事镜头", "是否有反差化的表达", "对车辆的正面情绪情感表达",
//                    "对车辆的负面情绪情感表达", "传递的情感价值"));
//        } else if (industryEnum == IndustryEnum.FOOD) {
//            result.put("场景展示", extractFields(industryDecoding,
//                    "情景切入", "提及场景", "产品展示方式", "素材人物", "产品出现情况", "产品出现方式", "人物人设"));
//            result.put("技术风格", extractFields(industryDecoding,
//                    "镜头切换手法", "视觉效果", "叙事方式", "视频类型", "字幕类型", "画面类型", "贴图相关", "视频风格"));
//            result.put("产品信息", extractFields(industryDecoding,
//                    "产品类型", "产品名称", "产品风味", "产品外观", "产品口感", "产品概念", "产品包装", "产品联名",
//                    "产品原料", "产品售价", "产品特色"));
//            result.put("情感表达", extractFields(industryDecoding,
//                    "情感表述", "是否有反差式叙事镜头", "是否有反差化的表达"));
//            result.put("活动推广", extractFields(industryDecoding,
//                    "活动时间", "活动主题", "折扣力度"));
//        } else if (industryEnum == IndustryEnum.HEALTH) {
//            result.put("场景展示", extractFields(industryDecoding,
//                    "情景切入", "提及场景", "产品展示方式", "素材人物", "产品出现情况", "产品出现方式", "人物人设"));
//            result.put("技术风格", extractFields(industryDecoding,
//                    "镜头切换手法", "视觉效果", "叙事方式", "视频类型", "字幕类型", "画面类型", "贴图相关", "视频风格"));
//            result.put("产品特性", extractFields(industryDecoding,
//                    "产品类型", "产品名称", "产品外观", "产品形态", "产品口感", "产品概念", "产品包装", "产品联名",
//                    "产品功效", "适用人群", "产品原料", "产品售价", "产品特色"));
//            result.put("情感表达", extractFields(industryDecoding,
//                    "情感表述", "是否有反差式叙事镜头", "是否有反差化的表达"));
//            result.put("市场活动", extractFields(industryDecoding,
//                    "活动时间", "活动主题", "折扣力度"));
//        } else if (industryEnum == IndustryEnum.COMMON) {
//            result.put("场景展示", extractFields(industryDecoding,
//                    "情景切入", "提及场景", "产品展示方式", "素材人物", "产品出现情况", "产品出现方式"));
//            result.put("技术风格", extractFields(industryDecoding,
//                    "镜头切换手法", "视觉效果", "叙事方式", "视频类型", "字幕类型", "画面类型", "贴图相关", "视频风格"));
//            result.put("产品特性", extractFields(industryDecoding,
//                    "出现产品类型", "出现产品", "核心产品", "产品素材（配置）", "核心产品卖点", "产品功能", "产品价格"));
//            result.put("用户场景", extractFields(industryDecoding,
//                    "目标用户", "使用场景"));
//            result.put("情感表达", extractFields(industryDecoding,
//                    "情感表述", "是否有反差式叙事镜头", "是否有反差化的表达"));
//        }else if (industryEnum == IndustryEnum.AUTOMOBILE) {
//            result.put("视频制作", extractFields(industryDecoding,
//                    "情景切入", "提及场景", "车辆展示方式", "素材人物", "镜头切换手法", "叙事方式", "视觉效果",
//                    "视频类型", "字幕类型", "画面类型", "贴图相关", "视频风格"));
//            result.put("车辆信息", extractFields(industryDecoding,
//                    "车辆类型", "车辆型号", "车辆的核心卖点", "同时提及的其他车辆", "同时提及的其他车辆的卖点",
//                    "外观与内饰", "底盘与减震", "车身稳定性", "驾驶操作", "低噪声", "特色副驾驶"));
//            result.put("车辆功能", extractFields(industryDecoding,
//                    "充电速度", "续航里程", "油耗", "电耗", "辅助驾驶", "智能泊车", "智能座舱", "语音助手",
//                    "座椅记忆", "娱乐设施", "手机兼容", "智能系统"));
//            result.put("用户需求", extractFields(industryDecoding,
//                    "内容中提及的车主的性别、年龄、收入/消费水平、生活方式", "使用车辆的场景", "使用车辆的需求", "使用车辆的痛点"));
//            result.put("情感表达", extractFields(industryDecoding,
//                    "对车辆的正面情绪情感表达", "对车辆的负面情绪情感表达", "情感表述", "反差式叙事镜头", "反差化的表达"));
//        }
//        // 其他行业类型可以根据需求继续添加规则
//        result.entrySet().removeIf(entry -> entry.getValue() == null || entry.getValue().isEmpty());
//        return result;
//    }
//
//    private Set<String> extractFields(Map<String, String> industryDecoding, String... keys) {
//        Set<String> values = new HashSet<>();
//        for (String key : keys) {
//            if (industryDecoding.containsKey(key) ) {
//                String value = industryDecoding.get(key);
//                if(StringUtils.isNotBlank(value) && !"无".equals(value)
//                        && !"未提及".equals(value) && !"N/A".equals(value) && !"否".equals(value)){
//                    String str[]=value.split("、");
//                    for (String s : str){
//                        values.add(s);
//                    }
//                }
//            }
//        }
//        return values;
//    }
//
//
//    private void uploadVideoPic(String ossId,String localFilePath) {
//        try {
//            cmsS3FlowService.upload(ossId,  new File(localFilePath));
//        }catch (Exception e){
//            log.error("上传视频图片失败",e);
//            throw new BusinessException("上传视频图片失败");
//        }
//    }
//
//
//}
