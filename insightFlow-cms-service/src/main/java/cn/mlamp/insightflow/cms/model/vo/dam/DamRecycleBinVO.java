package cn.mlamp.insightflow.cms.model.vo.dam;

import java.util.Date;

import cn.mlamp.insightflow.cms.enums.dam.DamRecycleBinObjectTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * DAM回收站VO
 */
@Data
@Schema(description = "DAM回收站VO")
public class DamRecycleBinVO {
    
    @Schema(description = "回收站ID")
    private Integer id;
    
    @Schema(description = "租户ID")
    private Integer tenantId;
    
    @Schema(description = "操作用户ID")
    private Integer userId;
    
    @Schema(description = "对象类型：1-目录，2-素材")
    private DamRecycleBinObjectTypeEnum objectType;
    
    @Schema(description = "目录对象，当objectType=1时有值")
    private DamDirectoryVO directory;
    
    @Schema(description = "素材对象，当objectType=2时有值")
    private DamAssetVO asset;
    
    @Schema(description = "恢复时间")
    private Date recoverTime;
    
    @Schema(description = "创建时间")
    private Date createTime;

} 