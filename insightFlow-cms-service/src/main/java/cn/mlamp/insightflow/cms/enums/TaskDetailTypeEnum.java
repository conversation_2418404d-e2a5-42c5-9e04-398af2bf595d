package cn.mlamp.insightflow.cms.enums;


import lombok.Getter;

/**
 * 文件的状态信息
 */


@Getter
public enum TaskDetailTypeEnum {

    /**
     * 输入
     */
    INPUT(0, "输入"),

    /**
     * 输出
     */
    OUTPUT(1, "输出"),

    ;

    private final Integer code;
    private final String msg;

    TaskDetailTypeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static TaskDetailTypeEnum getByCode(Integer code) {
        for (TaskDetailTypeEnum type : TaskDetailTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
