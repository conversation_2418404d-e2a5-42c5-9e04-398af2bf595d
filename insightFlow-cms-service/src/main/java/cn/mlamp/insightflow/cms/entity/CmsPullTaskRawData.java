package cn.mlamp.insightflow.cms.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("cms_pull_task_raw_datas")
public class CmsPullTaskRawData extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer taskId; // 任务 ID，关联 cms_pull_tasks

    private String esId; // ES 唯一 ID（外部系统唯一标识）

    private String textContent; // 内容

    private String textTitle; // 标题

    private String kwUrl; // 帖子链接

    private String kwKbIndustry; // 圈成关键词行业

    private Date datePublishedAt; // 发布时间

    private Boolean boolIsDeleted; // 是否删除

    private Long longRepostCount; // 转发数

    private Long longCommentCount; // 评论数

    private Long longInteractCount; // 互动数

    private Long longLikeCount; // 点赞数

    private String textNickName; // 用户昵称

    private String kwProfileImageUrl; // 用户头像链接

    private String kwUserUrl; // 用户主页链接

    private Long longVideoDuration; // 视频时长

    private String kwHeadImage; // 视频头图

    private String kwVideoUrl; // 视频链接

    private String kwVideoContent; // 视频语音识别

    private String kwCommonSentimentPlus; // 通用情感 Plus 版

    private String kwDataTagPlus; // 数据标签 Plus 版

    private Long longViewCount; // 阅读数

    private Long longFollowerCount; // 发帖用户粉丝数

    private Long longCollectCount; // 收藏数

    @TableField(typeHandler = com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler.class)
    private String rawData; // 原始 JSON 数据

    private String kwSource; // 来源

    private Integer type; // 分类，圈成2， 行业1

    private String kwTwoLevelTribeTag; // 圈成二级标签

}
