package cn.mlamp.insightflow.cms.enums;


import lombok.Getter;

/**
 * 视频任务来源类型
 */

@Getter
public enum VideoTaskSourceTypeEnum {

    /**
     * 用户上传视频
     */
    UPLOAD(0, "用户上传"),

    /**
     * 千川拉取视频
     */
    QIAN_CHUAN(1, "千川拉取"),

    ;

    private final Integer code;
    private final String msg;

    VideoTaskSourceTypeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static VideoTaskSourceTypeEnum getByCode(Integer code) {
        for (VideoTaskSourceTypeEnum type : VideoTaskSourceTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
