package cn.mlamp.insightflow.cms.enums;


import lombok.Getter;

/**
 * 文件的状态信息
 */


@Getter
public enum VideoTaskStatusEnum {

    /**
     * 排队中
     */
    QUEUING(0, "排队中"),

    /**
     * 脚本生成中
     */
    SCRIPT_CREATING(1, "脚本生成中"),

    /**
     * 分镜生成中
     */
    IMAGE_CREATING(2, "分镜生成中"),

    /**
     * 已完成
     */
    COMPLETED(3, "已完成"),

    /**
     * 脚本生成失败
     */
    SCRIPT_CREATE_FAILED(4, "脚本生成失败"),

    /**
     * 分镜生成失败
     */
    IMAGE_CREATE_FAILED(5, "分镜生成失败"),

    /**
     * 已取消
     */
    CANCELED(6, "已取消"),

    /**
     * vidu视频生成中
     */
    VIDU_AI_VIDEO_GENERATING(7, "vidu视频生成中"),

    /**
     * vidu视频已生成(待入库)
     */
    VIDU_AI_VIDEO_ENTERSTOREHOUSE_WAIT(8, "vidu视频已生成"),

    /**
         * vidu视频失败()
     */
    VIDU_AI_VIDEO_GENERATE_FAILED(12, "vidu视频生成失败"),

    /**
     * vidu视频入库中
     */
    VIDU_AI_VIDEO_ENTERSTOREHOUSE_ENTERING(9, "vidu视频入库中"),

    /**
     * vidu视频已入库
     */
    VIDU_AI_VIDEO_ENTERSTOREHOUSE_SUCCESS(10, "vidu视频已入库"),

    /**
     * vidu视频入库失败
     */
    VIDU_AI_VIDEO_ENTERSTOREHOUSE_FAILED(11, "vidu视频入库失败")
    ;

    private final Integer code;
    private final String msg;

    VideoTaskStatusEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static VideoTaskStatusEnum getByCode(Integer code) {
        for (VideoTaskStatusEnum videoTaskStatusEnum : VideoTaskStatusEnum.values()) {
            if (videoTaskStatusEnum.getCode().equals(code)) {
                return videoTaskStatusEnum;
            }
        }
        return null;
    }
}
