package cn.mlamp.insightflow.cms.controller.dam;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.config.UserContext;
import cn.mlamp.insightflow.cms.enums.dam.DamDirectoryTypeEnum;
import cn.mlamp.insightflow.cms.model.dto.dam.DamRecycleBinDeleteDTO;
import cn.mlamp.insightflow.cms.model.dto.dam.DamRecycleBinRecoverDTO;
import cn.mlamp.insightflow.cms.model.vo.dam.DamRecycleBinRecoverItem;
import cn.mlamp.insightflow.cms.model.vo.dam.DamRecycleBinVO;
import cn.mlamp.insightflow.cms.service.dam.IDamRecycleBinService;

/**
 * DAM回收站Controller
 */
@RestController
@RequestMapping("/dam/recycle-bin")
public class DamRecycleBinController {

    @Autowired
    private IDamRecycleBinService recycleBinService;

    /**
     * 获取回收站列表
     */
    @GetMapping
    public RespBody<List<DamRecycleBinVO>> getRecycleBinList(
            @RequestParam(required = false) Integer type
    ) {
        // 从请求中获取用户ID和租户ID，实际情况应该从认证信息中获取
        final Integer userId = UserContext.getUserId();
        final Integer tenantId = UserContext.getTenantId();
        final List<DamRecycleBinVO> recycleBinList = recycleBinService.getRecycleBinList(
                DamDirectoryTypeEnum.getByCode(type),
                userId,
                tenantId
        );
        return RespBody.ok(recycleBinList);
    }

    /**
     * 恢复回收站对象
     */
    @PostMapping("/recover")
    public RespBody<List<DamRecycleBinRecoverItem>> recoverObjects(@RequestBody List<DamRecycleBinRecoverDTO> recycleBinDTOList) {
        // 从请求中获取用户ID和租户ID，实际情况应该从认证信息中获取
        final Integer userId = UserContext.getUserId();
        final Integer tenantId = UserContext.getTenantId();

        final List<DamRecycleBinRecoverItem> results = recycleBinService.recoverObjects(recycleBinDTOList, userId, tenantId);
        return RespBody.ok(results);
    }

    /**
     * 删除回收站对象
     */
    @PostMapping("/delete")
    public RespBody<Void> deleteObjects(@RequestBody DamRecycleBinDeleteDTO params) {
        // 从请求中获取用户ID和租户ID，实际情况应该从认证信息中获取
        final Integer userId = UserContext.getUserId();
        final Integer tenantId = UserContext.getTenantId();

        final List<Integer> recycleBinIds = params.getRecycleBinIds();

        recycleBinService.deleteObjects(recycleBinIds, userId, tenantId);
        return RespBody.ok();
    }

    /**
     * 清空回收站
     */
    @PostMapping("/empty")
    public RespBody<Void> emptyRecycleBin() {
        // 从请求中获取用户ID和租户ID，实际情况应该从认证信息中获取
        final Integer userId = UserContext.getUserId();
        final Integer tenantId = UserContext.getTenantId();

        recycleBinService.emptyRecycleBin(userId, tenantId);
        return RespBody.ok();
    }

} 