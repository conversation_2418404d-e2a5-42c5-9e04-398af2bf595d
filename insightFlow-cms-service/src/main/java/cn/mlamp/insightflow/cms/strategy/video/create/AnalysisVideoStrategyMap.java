package cn.mlamp.insightflow.cms.strategy.video.create;


import cn.mlamp.insightflow.cms.entity.CmsAsyncTask;
import cn.mlamp.insightflow.cms.model.query.AnalysisVideoCreateRequest;
import cn.mlamp.insightflow.cms.model.query.AnalysisVideoQueryRequest;
import cn.mlamp.insightflow.cms.model.query.AsyncResultRequest;
import cn.mlamp.insightflow.cms.model.vo.AnalysisVideoCreateVO;
import cn.mlamp.insightflow.cms.model.vo.AnalysisVideoResultVO;

import java.util.HashMap;
import java.util.Map;

public class AnalysisVideoStrategyMap {
    private static final Map<String, ProcessAnalysisVideoStrategyInterface> strategyMap = new HashMap<>();

    public static AnalysisVideoCreateVO process(AnalysisVideoCreateRequest request) {
        return strategyMap.get(request.getTypeName()).process(request);
    }

    public static AnalysisVideoResultVO queryResult(AnalysisVideoQueryRequest request) {
        return strategyMap.get(request.getTypeName()).queryResult(request);
    }

    public static void processAsyncTask(CmsAsyncTask request, AsyncResultRequest asyncResultRequest, String typeName) {
         strategyMap.get(typeName).processAsyncTask(request,asyncResultRequest);
    }

    public static void register(String typeName, ProcessAnalysisVideoStrategyInterface strategy) {
        strategyMap.put(typeName, strategy);
    }
}
