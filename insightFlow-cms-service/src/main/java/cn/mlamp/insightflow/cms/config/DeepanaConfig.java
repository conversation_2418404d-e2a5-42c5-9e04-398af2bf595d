package cn.mlamp.insightflow.cms.config;

import cn.mlamp.insightflow.cms.util.DeepanaSignUtil;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @Author: husuper
 * @CreateTime: 2025-03-27
 */
@Component
public class DeepanaConfig {

    @Value("${deepana.hsk.top.appId:insightflow-cms}")
    private  String appId;

    @Value("${deepana.hsk.top.appkey:d62c83b1bd204b23826e9c7b310bc332}")
    private  String appKey;


    @PostConstruct
    public void init(){
        DeepanaSignUtil.setAppId(appId);
        DeepanaSignUtil.setAppKey(appKey);
    }


}
