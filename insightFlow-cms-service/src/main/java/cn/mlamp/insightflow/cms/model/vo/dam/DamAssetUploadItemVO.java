package cn.mlamp.insightflow.cms.model.vo.dam;

import java.util.List;

import cn.mlamp.insightflow.cms.entity.CmsUser;
import cn.mlamp.insightflow.cms.entity.dam.DamAsset;
import cn.mlamp.insightflow.cms.entity.dam.DamAssetUploadTaskDetail;
import cn.mlamp.insightflow.cms.entity.dam.DamDirectory;
import cn.mlamp.insightflow.cms.enums.dam.DamTaskStatusEnum;
import cn.mlamp.insightflow.cms.model.dto.dam.DamTagValueDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.annotation.Nullable;

/**
 * 任务素材项VO
 */
@Data
@Schema(description = "任务素材项")
public class DamAssetUploadItemVO extends DamAssetVO {

    @Schema(description = "任务ID")
    private Integer taskId;

    @Schema(description = "任务详情ID")
    private Integer taskDetailId;

    @Schema(description = "任务状态：1-待处理，2-处理中，3-完成，4-失败")
    private DamTaskStatusEnum status;

    public static DamAssetUploadItemVO from(
            DamAsset asset,
            DamDirectory directory,
            DamAssetUploadTaskDetail taskDetail,
            String ossUrl,
            String thumbnailUrl,
            List<DamTagValueDTO> tagValues
    ) {
        return from(asset, directory, taskDetail, ossUrl, thumbnailUrl, tagValues, null);
    }

    public static DamAssetUploadItemVO from(
            DamAsset asset,
            DamDirectory directory,
            DamAssetUploadTaskDetail taskDetail,
            String ossUrl,
            String thumbnailUrl,
            List<DamTagValueDTO> tagValues,
            @Nullable CmsUser user
            ) {
        final DamAssetUploadItemVO vo = new DamAssetUploadItemVO();
        vo.setId(asset.getId());
        vo.setName(asset.getName());
        vo.setDirectoryId(asset.getDirectoryId());
        vo.setDirectoryName(directory.getName());
        vo.setDirectoryType(directory.getType());
        vo.setDuration(asset.getDuration());
        vo.setThumbnailUrl(thumbnailUrl);
        vo.setThumbnailOssId(asset.getThumbnailOssId());
        vo.setAspectRatio(asset.getAspectRatio());
        vo.setExt(asset.getExt());
        vo.setOssUrl(ossUrl);
        vo.setOssId(asset.getOssId());
        vo.setTags(tagValues);
        vo.setIsStored(asset.getIsStored());
        vo.setStatus(taskDetail.getStatus());
        vo.setStorageTime(asset.getStorageTime());
        vo.setCreateBy(user != null ? user.getUserName() : null);
        vo.setCreateTime(asset.getCreateTime());
        vo.setUpdateTime(asset.getUpdateTime());
        return vo;
    }

}
