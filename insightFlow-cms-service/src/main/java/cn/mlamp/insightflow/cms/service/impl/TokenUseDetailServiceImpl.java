package cn.mlamp.insightflow.cms.service.impl;

import cn.hutool.extra.spring.SpringUtil;
import cn.mlamp.insightflow.cms.common.resp.RespCode;
import cn.mlamp.insightflow.cms.config.AnalysisServerProperties;
import cn.mlamp.insightflow.cms.entity.CmsUser;
import cn.mlamp.insightflow.cms.entity.TokenUseDetail;
import cn.mlamp.insightflow.cms.enums.TokenTaskTypeEnum;
import cn.mlamp.insightflow.cms.exception.BusinessException;
import cn.mlamp.insightflow.cms.mapper.TokenUseDetailMapper;
import cn.mlamp.insightflow.cms.model.vo.ConsumptionVO;
import cn.mlamp.insightflow.cms.service.IUserService;
import cn.mlamp.insightflow.cms.service.TokenUseDetailService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import cn.mlamp.insightflow.cms.model.query.ConsumptionQueryDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【token_use_detail(Token使用明细表)】的数据库操作Service实现
 * @createDate 2025-01-13 15:57:05
 */
@Service
@Slf4j
public class TokenUseDetailServiceImpl extends ServiceImpl<TokenUseDetailMapper, TokenUseDetail>
        implements TokenUseDetailService {

    @Autowired
    private TokenUseDetailMapper tokenUseDetailMapper;

    @Autowired
    private IUserService userService;

    private final static String Authorization1 = "Bearer 1bb7422fca44460b81d91c683eb9a146";

    private final static String Authorization2 = "Bearer 777b9114cd6f432399545a636667847d";


    /**
     * @param observationId 请求头的id
     * @param taskId        任务的id（智能体可能为空）
     */
    public void countTokenUse(String observationId, Integer taskId, Integer taskType, String taskName, Integer tenantId,
                              String userId, String userName) {
        if (tenantId == null || StringUtils.isBlank(userId)) {
            return;
        }
        String Authorization=Authorization2;
        if (TokenTaskTypeEnum.AI_IMITATE_TASK.getTaskType().equals(taskType)){
            Authorization=Authorization1;
        }

        // 创建OkHttpClient实例
        OkHttpClient client = new OkHttpClient();
        // 请求访问网关地址
        String tokenUseAddress = SpringUtil.getBean(AnalysisServerProperties.class).getTokenUseAddress();
        // 拼接参数observationId，形成完善地址
        String requestUrl = tokenUseAddress + "?traceId=" + observationId;
        // 创建Request实例
        Request request = new Request.Builder().url(requestUrl).addHeader("Authorization", Authorization).build();

        // 发送请求并处理响应
        String responseBody = "";
        try (Response response = client.newCall(request).execute()) {
            if (response.isSuccessful()) {
                responseBody = response.body().string();
            } else {
                log.error("请求失败，响应码: " + response.code());
                throw new BusinessException(RespCode.FAIL, "token统计网关请求接口任务失败");
            }
        } catch (IOException e) {
            log.error("请求失败: {}", e.getMessage());
            throw new BusinessException(RespCode.FAIL, "token统计网关请求接口任务失败");
        }
        // 根据 responseBody 计算消耗的总token值
        JsonObject jsonObject = new Gson().fromJson(responseBody, JsonObject.class);
        // 提取data对象
        JsonObject data = jsonObject.getAsJsonObject("data");
        // 提取usages数组
        JsonArray usages = data.getAsJsonArray("usages");
        // 初始化总和
        int totalPromptTokens = 0;
        int totalCompletionTokens = 0;

        if (usages != null) {
            // 遍历usages数组
            for (int i = 0; i < usages.size(); i++) {
                JsonObject usage = usages.get(i).getAsJsonObject();
                totalPromptTokens += usage.get("promptTokens").getAsInt();
                totalCompletionTokens += usage.get("completionTokens").getAsInt();
            }
            // 计算总和
            int totalTokens = totalPromptTokens + totalCompletionTokens;

            if (totalTokens != 0) {
                // 插数据库表
                TokenUseDetail tokenUseDetail = new TokenUseDetail();

                tokenUseDetail.setTraceId(observationId);
                tokenUseDetail.setTaskId(taskId);
                tokenUseDetail.setTaskName(taskName);
                tokenUseDetail.setTenantId(tenantId);
                tokenUseDetail.setUserId(userId);
                tokenUseDetail.setUserName(userName);
                tokenUseDetail.setTokens(totalTokens);
                tokenUseDetail.setUsageTime(new Date());
                tokenUseDetail.setTaskType(taskType);
                tokenUseDetailMapper.insert(tokenUseDetail);
            }
        }
    }

    @Override
    public List<TokenUseDetail> getTokenDetailByTypeAndTaskIdIn(Integer taskType, List<Integer> taskIdList) {
        return baseMapper.selectList(new LambdaQueryWrapper<TokenUseDetail>()
                .eq(TokenUseDetail::getTaskType, taskType)
                .in(TokenUseDetail::getTaskId, taskIdList));
    }

    @Override
    public void saveTokenDetail(int tokens, String observationId, Integer taskId, Integer taskType,
                                String taskName, Integer tenantId, Integer userId) {
        // 插数据库表
        TokenUseDetail tokenUseDetail = new TokenUseDetail();
        tokenUseDetail.setTraceId(observationId);
        tokenUseDetail.setTaskId(taskId);
        tokenUseDetail.setTaskName(taskName);
        tokenUseDetail.setTenantId(tenantId);
        tokenUseDetail.setUserId(userId.toString());
        tokenUseDetail.setUserName(null);
        tokenUseDetail.setTokens(tokens);
        tokenUseDetail.setUsageTime(new Date());
        tokenUseDetail.setTaskType(taskType);
        tokenUseDetailMapper.insert(tokenUseDetail);
    }

    @Override
    public Page<ConsumptionVO> listConsumptions(Integer tenantId, ConsumptionQueryDTO queryDTO) {
        // 查询原始数据
        LambdaQueryWrapper<TokenUseDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TokenUseDetail::getTenantId, tenantId);
        if (StringUtils.isNotBlank(queryDTO.getKeyword())) {
            queryWrapper.like(TokenUseDetail::getTaskName, queryDTO.getKeyword());
        }

        // 处理排序
        if (StringUtils.isNotBlank(queryDTO.getUsageTime())) {
            if ("asc".equalsIgnoreCase(queryDTO.getUsageTime())) {
                queryWrapper.orderByAsc(TokenUseDetail::getUsageTime);
            } else {
                queryWrapper.orderByDesc(TokenUseDetail::getUsageTime);
            }
        }

        if (StringUtils.isNotBlank(queryDTO.getTokens())) {
            if ("asc".equalsIgnoreCase(queryDTO.getTokens())) {
                queryWrapper.orderByAsc(TokenUseDetail::getTokens);
            } else {
                queryWrapper.orderByDesc(TokenUseDetail::getTokens);
            }
        }

        // 默认按创建时间降序
        if (StringUtils.isBlank(queryDTO.getTokens()) && StringUtils.isBlank(queryDTO.getTokens())) {
            queryWrapper.orderByDesc(TokenUseDetail::getCreateTime);
        }

        // 添加分页支持
        Page<TokenUseDetail> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        Page<TokenUseDetail> resultPage = tokenUseDetailMapper.selectPage(page, queryWrapper);
        List<TokenUseDetail> details = resultPage.getRecords();

        // 批量查询用户信息
        Set<Integer> userIds = details.stream()
                .filter(detail -> detail.getUserId() != null && StringUtils.isNotBlank(detail.getUserId()))
                .map(detail -> Integer.parseInt(detail.getUserId()))
                .collect(Collectors.toSet());

        // 初始化用户名映射
        Map<Integer, String> userNameMap;

        // 只有当userIds非空时才查询数据库
        if (!userIds.isEmpty()) {
            userNameMap = userService.listByUserIds(new ArrayList<>(userIds)).stream()
                    .collect(Collectors.toMap(CmsUser::getUserId, CmsUser::getUserName));
        } else {
            userNameMap = new HashMap<>();
        }

        // 转换数据
        List<ConsumptionVO> consumptionVOList = details.stream().map(detail -> {
            // 获取用户名 - 使用Integer类型作为键
            String userName;
            try {
                Integer userIdInt = Integer.parseInt(detail.getUserId());
                userName = userNameMap.getOrDefault(userIdInt, "未知用户");
            } catch (NumberFormatException e) {
                userName = "未知用户";
            }

            // 创建转换后的对象
            ConsumptionVO newDetail = new ConsumptionVO();
            newDetail.setTraceId(detail.getTraceId());
            newDetail.setTaskId(detail.getTaskId());
            newDetail.setTaskName(detail.getTaskName());
            newDetail.setTenantId(detail.getTenantId());
            newDetail.setUserName(userName);
            // 将tokens除以1000，如果有小数点则四舍五入
            int tokensValue = detail.getTokens() != null ? detail.getTokens() : 0;
            int convertedTokens = Math.round(tokensValue / 1000.0f);
            newDetail.setTokens(convertedTokens);
            newDetail.setUsageTime(detail.getUsageTime());
            newDetail.setTaskType(convertTaskType(detail.getTaskType()));
            return newDetail;
        }).collect(Collectors.toList());

        // 创建新的Page对象并设置分页信息
        Page<ConsumptionVO> consumptionVOPage = new Page<>(resultPage.getCurrent(), resultPage.getSize(),
                resultPage.getTotal());
        consumptionVOPage.setRecords(consumptionVOList);

        return consumptionVOPage;
    }

    private String convertTaskType(Integer taskType) {
        if (taskType == null)
            return "未知";
        switch (taskType) {
        case 1:
            return "上传视频";
        case 2:
            return "AI仿写";
        case 4:
            return "素材上传任务";
        default:
            return "其他类型";
        }
    }
}
