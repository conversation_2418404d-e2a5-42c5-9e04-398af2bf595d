package cn.mlamp.insightflow.cms.service;

import cn.mlamp.insightflow.cms.entity.CmsVideoInfo;
import cn.mlamp.insightflow.cms.enums.UploadSourceTypeEnum;
import cn.mlamp.insightflow.cms.model.dto.OpenUrlDTO;
import cn.mlamp.insightflow.cms.model.query.BatchUploadRequest;
import cn.mlamp.insightflow.cms.model.query.LinkUploadRequest;
import cn.mlamp.insightflow.cms.model.query.TaskUploadRequest;
import cn.mlamp.insightflow.cms.model.query.UploadTaskQueryRequest;
import cn.mlamp.insightflow.cms.model.vo.DocTaskUploadVO;
import cn.mlamp.insightflow.cms.model.vo.LinkFileVO;
import cn.mlamp.insightflow.cms.model.vo.TaskUploadVO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

public interface FileService {

    /**
     * 获取文件上传预签名
     */
    TaskUploadVO getUploadSignatureUrl(TaskUploadRequest request);

    List<String> getDownloadSignatureUrl(List<String> ossIds);

    List<LinkFileVO> uploadLinkCheck(LinkUploadRequest request);

    List<CmsVideoInfo> batchUploadVideo(BatchUploadRequest request);

    void decodeUploadVideo(List<CmsVideoInfo> videoInfos, UploadSourceTypeEnum sourceType);

    Page<DocTaskUploadVO> uploadHistory(Integer current, Integer pageSize, Integer userId, Integer tenantId);

    List<DocTaskUploadVO> uploadTaskStatus(UploadTaskQueryRequest request);

    void uploadCancel(Integer taskId, Integer userId, Integer tenantId);

    void uploadRetry(Integer taskId, Integer userId, Integer tenantId);


    void uploadTaskDelete(Integer taskId, Integer userId, Integer tenantId);

    String getPicDownloadSignatureUrl(String picOssId);


    void deleteFile(String sourceOssId);


}
