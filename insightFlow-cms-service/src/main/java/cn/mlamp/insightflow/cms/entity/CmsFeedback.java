package cn.mlamp.insightflow.cms.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 反馈意见表
 *
 * @TableName cms_feedback
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value = "cms_feedback")
@Data
public class CmsFeedback extends BaseEntity implements Serializable {
    /**
     * 反馈内容
     */
    @TableField(value = "text")
    private String text;

    /**
     * 0:通用 1:视频分析2：AI仿写
     */
    @TableField(value = "type")
    private Integer type;

    /**
     * 用户Id
     */
    @TableField(value = "user_id")
    private Integer userId;

    /**
     * 租户Id
     */
    @TableField(value = "tenant_id")
    private Integer tenantId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}