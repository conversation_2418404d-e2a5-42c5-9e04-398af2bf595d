package cn.mlamp.insightflow.cms.service;

import cn.mlamp.insightflow.cms.enums.VideoSynthesisStatusEnum;
import cn.mlamp.insightflow.cms.model.query.VideoSynthesisDetailUpdateRequest;
import cn.mlamp.insightflow.cms.model.query.VideoSynthesisListRequest;
import cn.mlamp.insightflow.cms.model.query.VideoSynthesisRequest;
import cn.mlamp.insightflow.cms.model.vo.VideoSynthesisListItemVO;
import cn.mlamp.insightflow.cms.model.vo.VideoSynthesisVO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.scheduling.annotation.Async;

import java.io.IOException;
import java.util.List;

/**
 * 视频合成服务接口
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
public interface IVideoSynthesisService {

    /**
     * 创建视频合成任务
     *
     * @param request 视频合成请求
     * @return 任务ID
     */
    VideoSynthesisVO createSynthesisTask(VideoSynthesisRequest request);

    /**
     * 异步执行视频合成任务
     *
     * @param request 视频合成请求
     * @param taskId  任务ID
     */
    @Async("commonTaskExecutor")
    void synthesizeVideoAsync(VideoSynthesisRequest request, Integer taskId);

    /**
     * 更新视频合成任务状态
     *
     * @param taskId     任务ID
     * @param statusEnum 状态枚举
     * @param errorMsg   错误信息
     * @return 更新结果
     */
    int updateSynthesisTaskStatus(Integer taskId, VideoSynthesisStatusEnum statusEnum, String errorMsg);

    /**
     * 获取视频合成任务详情
     *
     * @param taskId 任务ID
     * @return 任务详情
     */
    VideoSynthesisVO getSynthesisTaskDetail(Integer taskId);

    /**
     * 获取视频合成结果的下载链接
     *
     * @param taskId 任务ID
     * @return 下载链接
     */
    String getDownloadUrl(Integer taskId);

    /**
     * 获取合成分段视频的下载链接列表
     *
     * @param taskId 任务ID
     * @return 下载链接列表
     */
    List<String> getSegmentVideoDownloadUrls(Integer taskId);

    /**
     * 获取视频合成任务列表
     *
     * @param request  查询请求
     * @param userId   用户ID
     * @param tenantId 租户ID
     * @return 分页结果
     *
     */
    Page<VideoSynthesisListItemVO> getSynthesisTaskList(VideoSynthesisListRequest request, Integer userId, Integer tenantId);

    /**
     * 导出视频合成脚本
     *
     * @param taskId   任务ID
     * @param response HTTP响应
     * @throws IOException IO异常
     */
    void exportSynthesisScript(Integer taskId, HttpServletResponse response) throws IOException;

    /**
     * 获取OSS文件的签名链接
     *
     * @param ossId OSS文件ID
     * @return 签名链接
     */
    String getSignedUrl(String ossId);

    /**
     * 更新视频合成任务详情数据
     *
     * @param request  更新请求
     * @param userId   用户ID
     * @param tenantId 租户ID
     * @return 是否更新成功
     */
    boolean updateSynthesisTaskDetail(VideoSynthesisDetailUpdateRequest request, Integer userId, Integer tenantId);

}
