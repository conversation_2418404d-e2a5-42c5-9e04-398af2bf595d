package cn.mlamp.insightflow.cms.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * HttpUtil
 *
 * <AUTHOR>
 * @since 2022-04-22 16:22:10
 **/
@Slf4j
public class HttpUtil {

    private static final String QUESTION_MARK = "?";
    private static final String AND_MARK = "&";
    private static final String EQUAL_MARK = "=";
    private static final String JSON_MEDIA_TYPE = "application/json; charset=utf-8";

    private static final String OCTET_STREAM = "application/octet-stream";

    /**
     * 连接池最大空闲5个，超过则会被清理掉
     */
    private static final int MAX_IDLE_CONNECTIONS = 200;

    /**
     * 保持存活时间设置为59秒
     * 上游服务端（包括词距等服务）springboot服务中容器的默认tcp超时时间是60秒，防止服务端超时执行断开第四次挥手FIN确认时，客户端刚好发送了新的请求而得不到回应
     */
    private static final int KEEP_ALIVE_DURATION = 59;

    /**
     * 连接超时时间，单位：秒
     */
    private static final int CONNECT_TIMEOUT = 10;

    /**
     * 读超时时间，单位：秒
     */
    private static final int READ_TIMEOUT = 300;

    /**
     * 写超时时间，单位：秒
     */
    private static final int WRITE_TIMEOUT = 30;

    private static final OkHttpClient okHttpClient = new OkHttpClient.Builder().retryOnConnectionFailure(true)
            .connectionPool(new ConnectionPool(MAX_IDLE_CONNECTIONS, KEEP_ALIVE_DURATION, TimeUnit.SECONDS))
            .connectTimeout(CONNECT_TIMEOUT, TimeUnit.SECONDS).readTimeout(READ_TIMEOUT, TimeUnit.SECONDS)
            .writeTimeout(WRITE_TIMEOUT, TimeUnit.SECONDS).hostnameVerifier((s, sslSession) -> true).build();

    /**
     * get
     *
     * @param url      请求的url
     * @param paramMap 请求的参数，在浏览器？后面的数据，没有可以传null
     */
    public static <T> T get(String url, Map<String, String> paramMap, Class<T> clazz) {
        url = concatParam(url, paramMap);

        Request request = new Request.Builder().url(url).get().build();

        String responseStr = invoke(url, paramMap, request);
        return JsonUtil.decode(responseStr, clazz);
    }

    /**
     * get
     *
     * @param url      请求的url
     * @param paramMap 请求的参数，在浏览器？后面的数据，没有可以传null
     */
    public static <T> T get(String url, Map<String, String> paramMap, TypeReference<T> t) {
        url = concatParam(url, paramMap);

        Request request = new Request.Builder().url(url).get().build();

        String responseStr = invoke(url, paramMap, request);
        return JsonUtil.decode(responseStr, t);
    }

    /**
     * get
     * 
     * @param url      请求的url
     * @param paramMap 请求的参数，在浏览器？后面的数据，没有可以传null
     */
    public static <T> T get(String url, Map<String, String> headerMap, Map<String, String> paramMap,
            TypeReference<T> t) {
        url = concatParam(url, paramMap);

        Request request = new Request.Builder().url(url)
                .headers(Headers.of(CollUtil.isNotEmpty(headerMap) ? headerMap : new HashMap<>(0))).get().build();

        String responseStr = invoke(url, paramMap, request);
        return JsonUtil.decode(responseStr, t);
    }

    /**
     * 拼接get请求参数
     */
    public static String concatParam(String url, Map<String, String> paramMap) {
        // 拼请求
        if (CollUtil.isNotEmpty(paramMap)) {
            List<String> list = Lists.newArrayList();
            paramMap.forEach((key, val) -> list.add(key + EQUAL_MARK + val));
            String paramStr = String.join(AND_MARK, list);

            url = url + QUESTION_MARK + paramStr;
        }
        return url;
    }

    /**
     * post json格式请求
     *
     * @param url        请求的url
     * @param jsonParams json body
     * @param clazz      泛型
     * @param headerMap  请求头
     * @return T
     */
    public static <T> T postJsonParams(String url, String jsonParams, Class<T> clazz, Map<String, String> headerMap) {
        String responseStr = doPostJsonParams(url, jsonParams, headerMap);
        return JsonUtil.decode(responseStr, clazz);
    }

    /**
     * post json格式请求
     *
     * @param url       请求的url
     * @param objBody   obj body
     * @param t         泛型
     * @param headerMap 请求头
     * @return T
     */
    public static <T> T postJsonParams(String url, Map<String, String> headerMap, Object objBody, TypeReference<T> t) {
        String responseStr = doPostJsonParams(url, JsonUtil.encode(objBody), headerMap);
        return JsonUtil.decode(responseStr, t);
    }

    /**
     * post json格式请求
     *
     * @param url        请求的url
     * @param jsonParams json body
     * @param headerMap  请求头
     * @return String
     */
    private static String doPostJsonParams(String url, String jsonParams, Map<String, String> headerMap) {
        RequestBody requestBody = RequestBody.create(MediaType.parse(JSON_MEDIA_TYPE), jsonParams);
        Request request = new Request.Builder().url(url)
                .headers(Headers.of(CollUtil.isNotEmpty(headerMap) ? headerMap : new HashMap<>(0))).post(requestBody)
                .build();

        return invoke(url, jsonParams, request);
    }

    /**
     * 通过Json为参数的方式请求，获取返回值字符串
     *
     * @param url        url
     * @param jsonParams jsonParams
     * @return 返回值字符串
     */
    public static String postJson(String url, String jsonParams) {
        RequestBody requestBody = RequestBody.create(MediaType.parse(JSON_MEDIA_TYPE), jsonParams);
        Request.Builder builder = new Request.Builder().url(url).post(requestBody);
        return invoke(url, jsonParams, builder.build());
    }

    /**
     * downloadByMethodGet
     * 
     * @param url      下载地址
     * @param paramMap 下载参数
     * @param path     下载到指定路径
     * @return Boolean
     */
//    public static Boolean downloadByMethodGet(String url, Map<String, String> paramMap, String path, String fileName) {
//        url = concatParam(url, paramMap);
//
//        Request request = new Request.Builder().url(url).get().build();
//
//        invokeDownload(url, paramMap, request, path, fileName);
//        return Boolean.TRUE;
//    }

    /**
     * 执行实际的http请求
     *
     * @param url     url
     * @param param   param
     * @param request request
     * @return responseStr
     */
    private static String invoke(String url, Object param, Request request) {
        Response response = null;
        // 截取url，去掉?之后的参数
        String url4Log = url.contains(QUESTION_MARK) ? StringUtils.substring(url, 0, url.indexOf(QUESTION_MARK)) : url;
        try {
            response = okHttpClient.newCall(request).execute();
            if (response.isSuccessful()) {
                return Objects.isNull(response.body()) ? StrUtil.EMPTY_JSON : response.body().string();
            } else {
                throw new RuntimeException("OkHttpUtil-invoke not successful.");
            }
        } catch (Exception ex) {
            String msg = null;
            try {
                msg = (null != response && null != response.body()) ? response.body().string() : StrUtil.EMPTY_JSON;
            } catch (IOException e) {
                log.error("get response.body().string() error", e);
            }
            log.error("OkHttpUtil-invoke execute fail, url:{}, param:{}, response:{}, response.body:{}", url4Log,
                    JsonUtil.encodeDesensitize(param), response, msg, ex);
            throw new RuntimeException("OkHttpUtil-invoke error");
        } finally {
            if (response != null) {
                response.close();
            }
        }
    }

    /**
     * 执行实际的http下载请求
     *
     * @param url      url
     * @param param    param
     * @param request  request
     * @param path     路径
     * @param fileName 文件名称
     * @return responseStr
     */
//    private static Boolean invokeDownload(String url, Object param, Request request, String path, String fileName) {
//        // 获取文件path
//        Path filePath = Paths.get(path + fileName);
//        // 截取url，去掉?之后的参数
//        String url4Log = url.contains(QUESTION_MARK) ? StringUtils.substring(url, 0, url.indexOf(QUESTION_MARK)) : url;
//
//        Response response = null;
//        Timer.Sample sample = Metrics.timerStart();
//        try {
//            response = okHttpClient.newCall(request).execute();
//            if (response.isSuccessful()) {
//                Metrics.name("okhttp_invoke_success").tag("url", url4Log).count();
//            } else {
//                throw new RuntimeException("OkHttpUtil-invokeDownload not successful.");
//            }
//            assert response.body() != null;
//            // 覆盖拷贝
//            Files.copy(response.body().byteStream(), filePath, StandardCopyOption.REPLACE_EXISTING);
//            return Boolean.TRUE;
//        } catch (Exception ex) {
//            Metrics.name("okhttp_invoke_fail").tag("url", url4Log).count();
//            String msg = null;
//            try {
//                msg = (null != response && null != response.body()) ? response.body().string() : StrUtil.EMPTY_JSON;
//            } catch (IOException e) {
//                log.error("get response.body().string() error", e);
//            }
//            log.error("OkHttpUtil-invokeDownload execute fail, url:{}, param:{}, response:{}, response.body:{}",
//                    url4Log, param, response, msg, ex);
//            throw new RuntimeException("OkHttpUtil-invoke error");
//        } finally {
//            if (response != null) {
//                response.close();
//            }
//            Metrics.name("okhttp_invoke_time").tag("url", url4Log).sample(sample).timerStop(Percentile.TP99,
//                    Percentile.TP995);
//        }
//    }

    /**
     * 上传文件
     *
     * @param url      url
     * @param name     name
     * @param filename 文件全名
     * @param file     file
     * @return 返回值字符串
     */
    public static String postFileUpload(String url, String name, String filename, File file) {

        RequestBody body = new MultipartBody.Builder().setType(MultipartBody.FORM)
                .addFormDataPart(name, filename, RequestBody.create(MediaType.parse(OCTET_STREAM), file)).build();
        Request request = new Request.Builder().url(url).post(body).build();

        return invoke(url, null, request);
    }

    /**
     * 根据HttpServletRequest获取DomainName
     *
     * @param request HttpServletRequest
     * @return DomainName
     */
    public static String getDomainName(HttpServletRequest request) {
        StringBuilder url = new StringBuilder();
        int port = request.getServerPort();
        String scheme = request.getScheme();

        url.append(scheme).append("://").append(request.getServerName());

        if ((scheme.equals("http") && port != 80) || (scheme.equals("https") && port != 443)) {
            url.append(":").append(port);
        }

        return url.toString();
    }

    /**
     * 根据HttpServletRequest获取DomainName
     *
     * @return DomainName
     */
    public static String getDomainName() {
        // 解析request
        HttpServletRequest request = null;
        try {
            request = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes()))
                    .getRequest();
        } catch (Exception e) {
            log.error("cannot parse request from RequestContextHolder", e);
        }
        if (Objects.isNull(request)) {
            return null;
        }

        StringBuilder url = new StringBuilder();
        int port = request.getServerPort();
        String scheme = request.getScheme();
        url.append(scheme).append("://").append(request.getServerName());
        if ((scheme.equals("http") && port != 80) || (scheme.equals("https") && port != 443)) {
            url.append(":").append(port);
        }
        return url.toString();
    }

    public static void asyncPostJson(String url, String jsonParams, Map<String, String> headerMap, Callback callback) {
        Headers headers = Headers.of(headerMap);
        RequestBody requestBody = RequestBody.create(MediaType.parse(JSON_MEDIA_TYPE), jsonParams);
        Request request = new Request.Builder().url(url).headers(headers).post(requestBody).build();
        try {
            Call call = okHttpClient.newCall(request);
            call.enqueue(callback);
        } catch (Exception ex) {
            log.error("log error", ex);
            throw new RuntimeException("OkHttpUtil-invoke error");
        }
    }

}
