package cn.mlamp.insightflow.cms.util.mybatis.pg;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.TypeException;

import java.sql.*;

@MappedJdbcTypes(JdbcType.OTHER)
public class FloatArrayTypeHandler extends BaseTypeHandler<Float[]> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, Float[] parameter, JdbcType jdbcType) throws SQLException {
        // 拼接为 pgvector 字符串
        StringBuilder sb = new StringBuilder("[");
        for (int j = 0; j < parameter.length; j++) {
            sb.append(parameter[j]);
            if (j < parameter.length - 1) sb.append(", ");
        }
        sb.append("]");
        ps.setObject(i, sb.toString(), Types.OTHER);
    }

    @Override
    public Float[] getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return parseVector(rs.getString(columnName));
    }

    @Override
    public Float[] getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return parseVector(rs.getString(columnIndex));
    }

    @Override
    public Float[] getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return parseVector(cs.getString(columnIndex));
    }

    private Float[] parseVector(String vectorString) {
        if (vectorString == null) return null;
        vectorString = vectorString.replaceAll("[\\[\\]]", "");
        if (vectorString.isBlank()) return new Float[0];
        String[] parts = vectorString.split(",");
        Float[] result = new Float[parts.length];
        for (int i = 0; i < parts.length; i++) {
            result[i] = Float.parseFloat(parts[i].trim());
        }
        return result;
    }
}