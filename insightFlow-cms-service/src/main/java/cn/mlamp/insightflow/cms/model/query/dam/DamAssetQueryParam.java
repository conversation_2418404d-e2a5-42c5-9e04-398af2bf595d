package cn.mlamp.insightflow.cms.model.query.dam;

import cn.mlamp.insightflow.cms.model.query.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * DAM素材查询参数
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "DAM素材查询参数")
public class DamAssetQueryParam extends PageParam {

    @Schema(description = "文件夹ID")
    private Integer directoryId;

    @Schema(description = "关键字搜索")
    private String keyword;

    @Schema(description = "标签搜索")
    private List<TagSearch> tags;

    /**
     * 标签搜索条件
     */
    @Data
    @Schema(description = "标签搜索条件")
    public static class TagSearch {

        @Schema(description = "标签")
        private String tag;

        @Schema(description = "标签值列表")
        private List<String> tagValues;
    }
} 