package cn.mlamp.insightflow.cms.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 任务分享响应
 * <AUTHOR>
 * @date 2025-05-20
 */
@Data
public class TaskShareVO {
    
    @Schema(description = "分享ID")
    private Integer id;
    
    @Schema(description = "任务ID")
    private Integer taskId;
    
    @Schema(description = "任务类型：1-视频合成任务")
    private Integer type;
    
    @Schema(description = "授权码")
    private String authorizeCode;
    
    @Schema(description = "过期时间")
    private Date expireTime;
    
    @Schema(description = "分享链接")
    private String shareUrl;
}
