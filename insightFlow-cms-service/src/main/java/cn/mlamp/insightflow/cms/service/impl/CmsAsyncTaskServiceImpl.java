package cn.mlamp.insightflow.cms.service.impl;

import cn.mlamp.insightflow.cms.enums.AnalysisVideoTypeEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import cn.mlamp.insightflow.cms.entity.CmsAsyncTask;
import cn.mlamp.insightflow.cms.mapper.CmsAsyncTaskMapper;
import cn.mlamp.insightflow.cms.service.CmsAsyncTaskService;

import java.time.LocalDateTime;
import java.util.List;


@Service
public class CmsAsyncTaskServiceImpl extends ServiceImpl<CmsAsyncTaskMapper, CmsAsyncTask> implements CmsAsyncTaskService {

    @Override
    public CmsAsyncTask save(String db_unique_id,Integer videoInfoType , Integer taskStatus, String esId,String taskType){
        CmsAsyncTask cmsAsyncTask = new CmsAsyncTask();
        cmsAsyncTask.setDbUniqueId(Long.parseLong(db_unique_id));
        cmsAsyncTask.setVideoInfoType(videoInfoType);
        cmsAsyncTask.setTaskStatus(taskStatus);
        cmsAsyncTask.setEsId(esId);
        cmsAsyncTask.setTaskType(taskType);
        cmsAsyncTask.setCreateTime(LocalDateTime.now());
        cmsAsyncTask.setUpdateTime(LocalDateTime.now());
        this.save(cmsAsyncTask);
        return cmsAsyncTask;
    }

    @Override
    public CmsAsyncTask save(String db_unique_id, Integer videoInfoType, Integer taskStatus, String esId, String taskType, String data) {
        CmsAsyncTask cmsAsyncTask = new CmsAsyncTask();
        cmsAsyncTask.setDbUniqueId(Long.parseLong(db_unique_id));
        cmsAsyncTask.setVideoInfoType(videoInfoType);
        cmsAsyncTask.setTaskStatus(taskStatus);
        cmsAsyncTask.setEsId(esId);
        cmsAsyncTask.setTaskType(taskType);
        cmsAsyncTask.setData(data);
        cmsAsyncTask.setCreateTime(LocalDateTime.now());
        cmsAsyncTask.setUpdateTime(LocalDateTime.now());
        this.save(cmsAsyncTask);
        return cmsAsyncTask;
    }

    @Override
    public Boolean AllStatusSuccess(String esId,Integer videoInfoType) {
        List<CmsAsyncTask> cmsAsyncTasks = this.getBaseMapper().selectList(new LambdaQueryWrapper<CmsAsyncTask>().eq(CmsAsyncTask::getEsId, esId).eq(CmsAsyncTask::getVideoInfoType, videoInfoType));
        if(cmsAsyncTasks.size() == 0){
            return false;
        }
        for (CmsAsyncTask cmsAsyncTask : cmsAsyncTasks){
            if(cmsAsyncTask.getTaskStatus() != 2){
                return false;
            }
        }
        return true;
    }

}