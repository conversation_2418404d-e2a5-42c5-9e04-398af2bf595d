package cn.mlamp.insightflow.cms.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class UploadProductVO {

    @Schema(description = "商品Id", required = true)
    private Integer id;

    @Schema(description = "导入链接", required = true)
    private String url;

    @Schema(description = "标题", required = true)
    private String title;

    @Schema(description = "品牌", required = true)
    private String brand;

    @Schema(description = "产品名称", required = true)
    private String productName;

    @Schema(description = "卖点", required = true)
    private String sellingPoint;

    @Schema(description = "时长(秒)", required = true)
    private Integer duration;

    @Schema(description = "镜头数", required = true)
    private Integer lensNum;

    @Schema(description = "场景")
    private String scene;

    @Schema(description = "人数")
    private Integer peopleNum;

    @Schema(description = "节日")
    private String festival;

}
