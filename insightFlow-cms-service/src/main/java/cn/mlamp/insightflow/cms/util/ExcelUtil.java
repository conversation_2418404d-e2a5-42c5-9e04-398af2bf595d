package cn.mlamp.insightflow.cms.util;//package cn.mlamp.insightflow.cms.util;
//
//
//import cn.hutool.core.io.IoUtil;
//import cn.hutool.core.util.NumberUtil;
//import cn.hutool.extra.spring.SpringUtil;
//import cn.mlamp.insightflow.cms.common.file.IS3FlowService;
//import cn.mlamp.insightflow.cms.config.properties.ObjectStorageFlowProperties;
//import cn.mlamp.insightflow.cms.exception.BusinessException;
//import cn.mlamp.insightflow.cms.model.vo.ExcelDataVO;
//import com.alibaba.excel.EasyExcel;
//import com.alibaba.excel.context.AnalysisContext;
//import com.alibaba.excel.event.AnalysisEventListener;
//import com.alibaba.excel.metadata.data.ReadCellData;
//import com.alibaba.excel.support.ExcelTypeEnum;
//import com.google.gson.Gson;
//import com.google.gson.JsonObject;
//import lombok.Getter;
//import lombok.extern.slf4j.Slf4j;
//
//import java.io.*;
//import java.util.*;
//
///**
// * excel工具类 所有流默认是ByteArraryInputStream用于一次操作后reset
// */
//@Slf4j
//public class ExcelUtil {
//
//    // 用于本地测试
//    public static void main(String[] args) throws IOException {
//        InputStream in = null;
//        try {
//            // in = new FileInputStream(System.getProperty("user.dir") + "/聚类后数据.xlsx");
//            in = new FileInputStream(new File("C:\\Users\\<USER>\\Desktop\\数字列3.xlsx"));
//        } catch (FileNotFoundException e) {
//            throw new RuntimeException(e);
//        }
//        ByteArrayInputStream fileData = null;
//        // 读取文件并保存到数据库
//        fileData = new ByteArrayInputStream(IoUtil.readBytes(in));
//        // System.out.println(getExcelSheetRowCounts(fileData, "xlsx"));
//        System.out.println(getExcelNumColumn(fileData, "xlsx"));
//    }
//
//    public static ByteArrayInputStream getExcelInputStream(String filePath) {
//        ObjectStorageFlowProperties objectStorageFlowProperties = SpringUtil.getBean(ObjectStorageFlowProperties.class);
//        IS3FlowService s3FlowService = SpringUtil.getBean(IS3FlowService.class);
//        try (final InputStream download = s3FlowService.download(objectStorageFlowProperties.getPic().getBucketName(),
//                filePath)) {
//            final ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(IoUtil.readBytes(download));
//            return byteArrayInputStream;
//        } catch (Exception e) {
//            return null;
//        }
//
//    }
//
//    /**
//     * 获取表格中的数字列号
//     *
//     * @param in        输入流
//     * @param excelType 文件类型
//     * @return 数字列号
//     */
//    public static List<String> getExcelNumColumn(InputStream in, String excelType) throws IOException {
//        class ReadNumColumn extends AnalysisEventListener<Map<Integer, String>> {
//
//            private final List<Integer> numColumn = new ArrayList<>();
//            private final List<String> numName = new ArrayList<>();
//            // 表头
//            private final Map<Integer, String> numToName = new HashMap<>();
//            // value 为 true 时说明是数字列
//            private final Map<Integer, Boolean> isNumColumn = new HashMap<>();
//
//            @Override
//            public void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context) {
//
//                for (Map.Entry<Integer, ReadCellData<?>> entry : headMap.entrySet()) {
//                    if (entry.getValue().getStringValue() != null && !entry.getValue().getStringValue().isEmpty()) {
//                        isNumColumn.put(entry.getKey(), true);
//                        numToName.put(entry.getKey(), entry.getValue().getStringValue());
//                    }
//                }
//            }
//
//            @Override
//            public void invoke(Map<Integer, String> integerStringMap, AnalysisContext analysisContext) {
//
//                for (Map.Entry<Integer, Boolean> entry : isNumColumn.entrySet()) {
//                    String column = integerStringMap.get(entry.getKey());
//                    if (column == null || column.isEmpty() || !NumberUtil.isNumber(column)) {
//                        isNumColumn.put(entry.getKey(), false);
//                    }
//                }
//
//                // for (Map.Entry<Integer, String> entry : integerStringMap.entrySet()) {
//                // //当已经发现它在 isNumColumn 中检测过不是数字列，直接跳过
//                // if(!isNumColumn.get(entry.getKey())) continue;
//                // if(entry.getValue() == null || entry.getValue().isEmpty() ||
//                // !NumberUtil.isNumber(entry.getValue())){
//                // isNumColumn.put(entry.getKey(), false);
//                // }
//                // }
//            }
//
//            @Override
//            public void doAfterAllAnalysed(AnalysisContext analysisContext) {
//                for (Map.Entry<Integer, Boolean> entry : isNumColumn.entrySet()) {
//                    if (entry.getValue())
//                        numColumn.add(entry.getKey());
//                }
//                Collections.sort(numColumn);
//                for (Integer num : numColumn) {
//                    if (!Objects.equals(numToName.get(num), "唯一ID")) {
//                        numName.add(numToName.get(num));
//                    }
//                }
//
//            }
//        }
//        try {
//            ReadNumColumn listener = new ReadNumColumn();
//            // 读取Excel文件并注册行计数监听器
//            EasyExcel.read(in).excelType(ExcelTypeEnum.valueOf(excelType.toUpperCase())).registerReadListener(listener)
//                    .doReadAll();
//            return listener.numName;
//        } catch (Exception e) {
//            log.error("从流中读取Excel失败: {}", e.getMessage(), e);
//        } finally {
//            // 重置输入流
//            in.reset();
//        }
//        return Collections.emptyList();
//
//    }
//
//    /**
//     * 获取Excel文件中每个sheet的行数
//     *
//     * @param in 输入流
//     * @return 每个sheet的行数
//     */
//    public static List<Integer> getExcelSheetRowCounts(InputStream in, String excelType) {
//        class MultiSheetRowCountListener extends AnalysisEventListener<Map<Integer, String>> {
//            @Getter
//            private List<Integer> sheetRowCounts = new ArrayList<>();
//            private int currentSheetRowCount = 0;
//
//            @Override
//            public void invoke(Map<Integer, String> data, AnalysisContext context) {
//                currentSheetRowCount++;
//            }
//
//            @Override
//            public void doAfterAllAnalysed(AnalysisContext context) {
//                sheetRowCounts.add(currentSheetRowCount);
//                currentSheetRowCount = 0;
//
//                try {
//                    context.getInputStream().reset();
//                } catch (IOException ignore) {
//
//                }
//            }
//
//            @Override
//            public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
//                // 如果要包括标题行，请取消下面这行的注释
//                // currentSheetRowCount++;
//            }
//        }
//        try {
//            MultiSheetRowCountListener listener = new MultiSheetRowCountListener();
//            // 读取Excel文件并注册行计数监听器
//            EasyExcel.read(in).excelType(ExcelTypeEnum.valueOf(excelType.toUpperCase())).registerReadListener(listener)
//                    .doReadAll();
//            return listener.getSheetRowCounts();
//        } catch (Exception e) {
//            log.error("从流中读取Excel失败: {}", e.getMessage(), e);
//        }
//        return Collections.emptyList();
//    }
//
//    /**
//     * 读取输入Excel文件并保存到数据库
//     *
//     * @param in                     输入流
//     * @param taskResultMapper       TaskResultMapper
//     * @param taskResultDetailMapper TaskResultDetailMapper
//     * @param taskType               任务类型
//     * @param taskId                 任务ID
//     * @param excelType              csv xlsx xls 严格要求
//     */
//    public static void readInFileAndSaveToDB(InputStream in, TaskResultMapper taskResultMapper,
//            TaskResultDetailMapper taskResultDetailMapper, String taskType, Integer taskId, String excelType) {
//        final TaskResult[] taskResult = { null };
//
//        // 定义监听器
//        class ExcelListener extends AnalysisEventListener<Map<Integer, String>> {
//            private final List<String> sheetTitles = new ArrayList<>();
//            private boolean isFirstSheet = true;
//            private final int readRowCount;
//            private int currentRow = 0;
//
//            ExcelListener(int readRowCount) {
//                this.readRowCount = readRowCount;
//            }
//
//            // 读取Excel每行数据并插入taskResultDetail表
//            @Override
//            public void invoke(Map<Integer, String> integerStringMap, AnalysisContext analysisContext) {
//                if (currentRow >= readRowCount) {
//                    return;
//                }
//                Integer taskResultId = taskResult[0].getId();
//                TaskResultDetail taskResultDetail = new TaskResultDetail();
//                taskResultDetail.setTaskResultId(taskResultId);
//                JsonObject jsonObject = new JsonObject();
//                List<String> valList = new ArrayList<>();
//                integerStringMap.forEach((k, v) -> {
//                    valList.add(v);
//                });
//                jsonObject.addProperty("type", "sheet");
//                jsonObject.add("values", new Gson().toJsonTree(valList));
//
//                taskResultDetail.setResultDetail(jsonObject.toString());
//
//                taskResultDetailMapper.insert(taskResultDetail);
//
//                currentRow++;
//            }
//
//            // 读完一个sheet后，更新TaskResult表的dataStructure字段
//            @Override
//            public void doAfterAllAnalysed(AnalysisContext analysisContext) {
//                JsonObject jsonObject = new JsonObject();
//                jsonObject.addProperty("type", "sheet");
//                jsonObject.addProperty("sheetName", analysisContext.readSheetHolder().getSheetName());
//                jsonObject.add("columns", new Gson().toJsonTree(sheetTitles));
//
//                TaskResult newTaskResult = taskResultMapper.selectById(taskResult[0].getId());
//                newTaskResult.setTaskId(taskId);
//                newTaskResult.setDataStructure(jsonObject.toString());
//                taskResultMapper.updateById(newTaskResult);
//            }
//
//            // 读每个sheet的标题行
//            @Override
//            public void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context) {
//
//                if (isFirstSheet) {
//                    int currentColumnIndex = 0;
//                    for (Map.Entry<Integer, ReadCellData<?>> entry : headMap.entrySet()) {
//                        if (currentColumnIndex != entry.getKey()) {
//                            while (currentColumnIndex < entry.getKey()) {
//                                sheetTitles.add(new String(" "));
//                                currentColumnIndex++;
//                            }
//                        }
//                        if (entry.getValue().getStringValue() != null)
//                            sheetTitles.add(entry.getValue().getStringValue());
//                        else {
//                            sheetTitles.add(new String(" "));
//                        }
//
//                        currentColumnIndex++;
//                    }
//                    isFirstSheet = false;
//                }
//                taskResult[0] = new TaskResult();
//                taskResult[0].setType(0);
//                taskResult[0].setTaskId(taskId);
//                taskResultMapper.insert(taskResult[0]);
//
//                currentRow = 0;
//            }
//        }
//        // 读取Excel文件并注册监听器
//        EasyExcel.read(in).registerReadListener(new ExcelListener(15))
//                .excelType(ExcelTypeEnum.valueOf(excelType.toUpperCase())).doReadAll();
//    }
//
//    public static ExcelDataVO getExcelDataVO(InputStream in, String excelType) {
//        ExcelDataVO excelDataVO = new ExcelDataVO();
//        ExcelDataVOListener excelDataVOListener = new ExcelDataVOListener();
//
//        try {
//            EasyExcel.read(in).registerReadListener(excelDataVOListener).doReadAll();
//        } catch (Exception e) {
//            log.error("读取Excel数据失败", e);
//            throw new BusinessException("读取Excel数据失败");
//        }
//
//        excelDataVO.setSheetData(excelDataVOListener.getSheetDataVOs());
//        return excelDataVO;
//    }
//
//    static class ExcelDataVOListener extends AnalysisEventListener<Map<Integer, String>> {
//        private final List<String> headerList = new ArrayList<>();
//        private final List<List<String>> dataList = new ArrayList<>();
//        private final List<ExcelDataVO.SheetDataVO> sheetDataVOs = new ArrayList<>();
//
//        @Override
//        public void invoke(Map<Integer, String> integerStringMap, AnalysisContext analysisContext) {
//            List<String> rowData = new ArrayList<>();
//            integerStringMap.forEach((k, v) -> rowData.add(v));
//            dataList.add(rowData);
//        }
//
//        @Override
//        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
//            ExcelDataVO.SheetDataVO sheetDataVO = new ExcelDataVO.SheetDataVO();
//            sheetDataVO.setHeader(new ArrayList<>(headerList));
//            sheetDataVO.setData(new ArrayList<>(dataList));
//            sheetDataVO.setSheetName(analysisContext.readSheetHolder().getSheetName());
//            // log.info("Processed sheet: {}", sheetDataVO);
//            sheetDataVOs.add(sheetDataVO);
//
//            // 清理数据，为下一个sheet做准备
//            headerList.clear();
//            dataList.clear();
//        }
//
//        @Override
//        public void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context) {
//            headMap.forEach((k, v) -> headerList.add(v.getStringValue()));
//
//        }
//
//        public List<ExcelDataVO.SheetDataVO> getSheetDataVOs() {
//            return sheetDataVOs;
//        }
//    }
//
//    public static List<String> getExcelSheetHeadList(InputStream in, String excelType, int sheetIndex) {
//        class ExcelHeadListener extends AnalysisEventListener<Map<Integer, String>> {
//            private final List<String> headList = new ArrayList<>();
//
//            @Override
//            public void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context) {
//                headMap.forEach((k, v) -> headList.add(v.getStringValue()));
//            }
//
//            @Override
//            public void invoke(Map<Integer, String> data, AnalysisContext context) {
//            }
//
//            @Override
//            public void doAfterAllAnalysed(AnalysisContext context) {
//                try {
//                    context.getInputStream().reset();
//                } catch (IOException ignore) {
//                }
//            }
//        }
//        ExcelHeadListener excelHeadListener = new ExcelHeadListener();
//        EasyExcel.read(in).registerReadListener(excelHeadListener).sheet(sheetIndex).doRead();
//        return excelHeadListener.headList;
//    }
//
//}
