package cn.mlamp.insightflow.cms.service.dam.impl;

import cn.hutool.core.util.StrUtil;
import cn.mlamp.insightflow.cms.common.file.IS3FlowService;
import cn.mlamp.insightflow.cms.common.resp.RespCode;
import cn.mlamp.insightflow.cms.config.properties.ObjectStorageFlowProperties;
import cn.mlamp.insightflow.cms.constant.FileConstant;
import cn.mlamp.insightflow.cms.entity.CmsUser;
import cn.mlamp.insightflow.cms.entity.dam.*;
import cn.mlamp.insightflow.cms.enums.dam.DamDirectoryTypeEnum;
import cn.mlamp.insightflow.cms.enums.dam.DamRecycleBinObjectTypeEnum;
import cn.mlamp.insightflow.cms.exception.BusinessException;
import cn.mlamp.insightflow.cms.mapper.dam.DamAssetMapper;
import cn.mlamp.insightflow.cms.mapper.dam.DamTagValueMapper;
import cn.mlamp.insightflow.cms.model.converter.dam.DamAssetConverter;
import cn.mlamp.insightflow.cms.model.dto.dam.DamAssertUpdateDTO;
import cn.mlamp.insightflow.cms.model.dto.dam.DamTagValueDTO;
import cn.mlamp.insightflow.cms.model.query.dam.DamAssetQueryParam;
import cn.mlamp.insightflow.cms.model.query.dam.DamAssetSemanticSearchParam;
import cn.mlamp.insightflow.cms.model.vo.dam.DamAssetVO;
import cn.mlamp.insightflow.cms.model.vo.dam.DamDirectoryVO;
import cn.mlamp.insightflow.cms.model.vo.dam.DamPageResult;
import cn.mlamp.insightflow.cms.service.IUserService;
import cn.mlamp.insightflow.cms.service.dam.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import jakarta.annotation.Resource;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.net.URL;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.mlamp.insightflow.cms.enums.dam.DamDirectoryTypeEnum.TENANT;
import static cn.mlamp.insightflow.cms.service.dam.impl.DamAssetEmbeddingServiceImpl.SHOT_DESCRIPTION_TAG_NAME;

/**
 * DAM素材表 服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class DamAssetServiceImpl extends ServiceImpl<DamAssetMapper, DamAsset> implements IDamAssetService {
    @Autowired
    private IS3FlowService ossService;

    @Resource
    @Lazy
    private IDamDirectoryService directoryService;

    @Resource
    private DamAssetConverter assetConverter;

    @Resource
    private IDamTagValueService tagValueService;

    // TODO: 是否需要复用 Service 服务
    @Resource
    private DamTagValueMapper tagValueMapper;

    @Resource
    @Lazy
    private IDamRecycleBinService damRecycleBinService;

    @Resource
    private ObjectStorageFlowProperties objectStorageFlowProperties;

    @Resource
    private IDamAssetEmbeddingService assetEmbeddingService;

    @Resource
    private IS3FlowService s3Service;

    /**
     * 用于 admin 判断
     * TODO: 需要更好的位置存放
     */
    @Resource
    private IUserService userService;

    /**
     * tag 查询需要使用到 assetId
     * TODO: 可考虑是否直接加入到 DamTagValueDTO 中
     */
    @Data
    @EqualsAndHashCode(callSuper = true)
    private static class DamTagValueDTOWithAssetId extends DamTagValueDTO {
        private Integer assetId;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    private static class DamAssetWithDirectoryType extends DamAsset {
        private DamDirectoryTypeEnum directoryType;
    }


    /**
     * 1. 关于标签过滤的使用，约定如下:
     * 1.1 keyword 存在的情况下，为搜索操作，如果存在标签，仅使用标签名作为结果过滤操作
     * 1.2 keyword 不存在的情况下，为标签搜索操作，可以使用标签 & 标签值对素材进行过滤
     *
     * @param queryParam 查询参数
     * @param userId     用户ID
     * @param tenantId   租户ID
     * @return asset vo list
     */
    @Override
    public DamPageResult<DamAssetVO> getAssetList(DamAssetQueryParam queryParam, Integer userId, Integer tenantId) {
        MPJLambdaWrapper<DamAsset> queryWrapper = baseWrapper(userId, tenantId)
                .eq(queryParam.getDirectoryId() != null, DamAsset::getDirectoryId, queryParam.getDirectoryId())
                // TODO: 用户被删除后，是否还能看到租户下的内容
                .leftJoin(CmsUser.class, on -> on
                        .eq(DamAsset::getUserId, CmsUser::getUserId)
                )
                .selectAs(CmsUser::getUserName, "createBy")
                .distinct()
                .leftJoin(DamTagValue.class, on -> on
                        .eq(DamTagValue::getAssetId, DamAsset::getId)
                )
                .innerJoin(DamTag.class, on -> on
                        .eq(DamTag::getId, DamTagValue::getTagId)
                );

        final boolean containsTagFilter = !CollectionUtils.isEmpty(queryParam.getTags());

        if (StringUtils.isNotBlank(queryParam.getKeyword())) {
            // 关键词搜索（素材名称或标签)
            // 如果需要 tag 过滤，匹配 tag 值即可
            String keyword = queryParam.getKeyword();
            List<String> tags = containsTagFilter ?
                    queryParam.getTags().stream().map(DamAssetQueryParam.TagSearch::getTag).toList() : List.of();

            queryWrapper.and(wrapper -> wrapper
                    .like(DamAsset::getName, keyword)
                    .or(tagWrapper -> tagWrapper
                            .like(DamTagValue::getValue, keyword)
                            .in(containsTagFilter, DamTag::getName, tags)
                    )
            );
        } else if (containsTagFilter) {
            for (DamAssetQueryParam.TagSearch tagSearch : queryParam.getTags()) {
                queryWrapper.and(orWrapper -> {
                            // 加入每个标签的过滤项
                            orWrapper.or(!CollectionUtils.isEmpty(tagSearch.getTagValues()), tagCondition ->
                                    tagCondition
                                            .eq(DamTag::getName, tagSearch.getTag())
                                            .and(tagValueCondition -> {
                                                for (String tagValue : tagSearch.getTagValues()) {
                                                    tagValueCondition
                                                            .or()
                                                            .like(DamTagValue::getValue, tagValue);
                                                }
                                            })
                            );
                        }
                );
            }
        }
        Page<DamAssetVO> page = queryParam.toPage(DamAssetVO.class);
        this.baseMapper.selectJoinPage(page, DamAssetVO.class, queryWrapper);
        List<DamAssetVO> records = page.getRecords();

        // 缩略图 oss id 换 url
        // TODO: 缓存？
        records.forEach(this::setThumbnailUrl);

        // 查询最终的 Tag 信息，进行信息合并
        List<Integer> assetIds = records.stream().map(DamAssetVO::getId).toList();
        if (assetIds.isEmpty()) {
            return DamPageResult.of(
                    0,
                    List.of(),
                    (int) page.getCurrent(),
                    (int) page.getSize()
            );
        }

        // 获取素材标签
        this.queryAndSetAssetTags(records);

        // TODO: 是否将 DamPageResult 定义修改为 long
        return DamPageResult.of(
                page.getTotal(),
                records,
                (int) page.getCurrent(),
                (int) page.getSize()
        );
    }

    // 批量查询并设置素材标签
    private void queryAndSetAssetTags(List<DamAssetVO> assets) {
        List<Integer> assetIds = assets.stream().map(DamAssetVO::getId).toList();
        Map<Integer, List<DamTagValueDTOWithAssetId>> tagValueMap = this.queryTags(assetIds);
        for (DamAssetVO asset : assets) {
            List<DamTagValueDTOWithAssetId> tagValueList = tagValueMap.get(asset.getId());
            if (tagValueList != null) {
                List<DamTagValueDTO> tags = tagValueList.stream()
                        .map(tagValue -> {
                            DamTagValueDTO tagVO = new DamTagValueDTO();
                            BeanUtils.copyProperties(tagValue, tagVO);
                            return tagVO;
                        })
                        .collect(Collectors.toList());
                asset.setTags(tags);
            }
        }
    }

    private Map<Integer, List<DamTagValueDTOWithAssetId>> queryTags(Collection<Integer> assetIds) {
        if (assetIds.isEmpty()) {
            return new HashMap<>();
        }
        MPJLambdaWrapper<DamTagValue> tagValueQueryWrapper = new MPJLambdaWrapper<DamTagValue>()
                .selectAll(DamTagValue.class)
                .selectAs(DamTag::getType, "tagType")
                .selectAs(DamTag::getName, "name")
                .in(DamTagValue::getAssetId, assetIds)
                .leftJoin(DamTag.class, on -> on
                        .eq(DamTag::getId, DamTagValue::getTagId));

        List<DamTagValueDTOWithAssetId> tagValues =
                tagValueMapper.selectJoinList(DamTagValueDTOWithAssetId.class, tagValueQueryWrapper);

        return tagValues.stream()
                .collect(Collectors.groupingBy(DamTagValueDTOWithAssetId::getAssetId));
    }

    @Override
    public DamAssetVO getAssetDetail(Integer assetId, Integer userId, Integer tenantId) {
        MPJLambdaWrapper<DamAsset> assetQueryWrapper = baseWrapper(userId, tenantId)
                .eq(DamAsset::getId, assetId)
                .leftJoin(CmsUser.class, on -> on
                        .eq(DamAsset::getUserId, CmsUser::getUserId)
                )
                .selectAs(CmsUser::getUserName, "createBy");
        DamAssetVO assetVO = this.baseMapper.selectJoinOne(DamAssetVO.class, assetQueryWrapper);
        if (assetVO == null) {
            throw new BusinessException(RespCode.NOT_FOUND.getCode(), "asset was not found");
        }
        setThumbnailUrl(assetVO);

        // 标签查询，无需考虑标签历史删除
        MPJLambdaWrapper<DamTagValue> tagValueQueryWrapper = new MPJLambdaWrapper<DamTagValue>()
                .selectAll(DamTagValue.class)
                .selectAs(DamTag::getType, "tagType")
                .selectAs(DamTag::getName, "name")
                .eq(DamTagValue::getAssetId, assetId)
                .leftJoin(DamTag.class, on -> on
                        .eq(DamTag::getId, DamTagValue::getTagId));
        List<DamTagValueDTO> tagValues = tagValueMapper.selectJoinList(DamTagValueDTO.class, tagValueQueryWrapper);
        assetVO.setTags(tagValues);
        return assetVO;
    }

    /**
     * 素材更新
     *
     * @param assetId         素材ID
     * @param assertUpdateDTO 素材更新 DTO
     * @param userId          用户ID
     * @param tenantId        租户ID
     * @return success or not
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateAsset(Integer assetId, DamAssertUpdateDTO assertUpdateDTO, Integer userId, Integer tenantId) {
        MPJLambdaWrapper<DamAsset> assetQueryWrapper = baseWrapper(userId, tenantId)
                .eq(DamAsset::getId, assetId);
        DamAssetWithDirectoryType asset = this.baseMapper.selectJoinOne(DamAssetWithDirectoryType.class, assetQueryWrapper);

        if (asset == null) {
            throw new BusinessException(RespCode.NOT_FOUND.getCode(), "asset was not found");
        }

        /*
          1. 可以修改个人文件夹中的素材
          2. 可以修改租户文件夹中自己的素材
          3. 租户管理员可以修改租户文件夹中的素材
          总结: 非租户管理员不能更新租户文件夹中非自己的素材
         */
        if (TENANT.equals(asset.getDirectoryType())
                && !asset.getUserId().equals(userId)
                && !isTenantAdmin()) {
            throw new BusinessException(RespCode.FORBIDDEN.getCode(), "cannot update tenant assets that you don't own");
        }

        if (StringUtils.isNotBlank(assertUpdateDTO.getName()) &&
                !StringUtils.equals(asset.getName(), assertUpdateDTO.getName())) {
            asset.setName(assertUpdateDTO.getName());
            updateById(asset);
        }

        if (CollectionUtils.isNotEmpty(assertUpdateDTO.getTags())) {
            Map<Integer, DamTagValueDTO> newTagMap = assertUpdateDTO.getTags().stream()
                    .collect(Collectors.toMap(DamTagValueDTO::getId, Function.identity()));

            List<DamTagValue> tagValues = tagValueService.listByIds(newTagMap.keySet());
            if (tagValues.stream().anyMatch(tagValue -> !assetId.equals(tagValue.getAssetId()))) {
                throw new BusinessException(RespCode.FORBIDDEN.getCode(), "some tags don't belong to asset");
            }
            // 新标签值覆盖旧值
            tagValues.forEach(oldTag -> {
                        var newTag = newTagMap.get(oldTag.getId());
                        if (SHOT_DESCRIPTION_TAG_NAME.equals(newTag.getName())) {
                            // 判断【镜头描述】字段是否发生变更，如果发生变更，需要更新content和向量值
                            if (!StringUtils.equals(oldTag.getValue(), newTag.getValue())) {
                                String content = newTag.getValue();
                                assetEmbeddingService.updateEmbedding(assetId, content);
                            }
                        }
                        oldTag.setValue(newTag.getValue());
                    }
            );
            tagValueService.updateBatchById(tagValues);
        }

        // embedding 更新
        var assetEmbedding = this.getById(assetId);
        assetEmbeddingService.saveOrUpdateByAssets(List.of(assetEmbedding), userId, tenantId, true);

        return true;
    }

    /**
     * 删除素材
     *
     * @param assetIds 素材ID列表
     * @param userId   用户ID
     * @param tenantId 租户ID
     * @return success or not
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteAssets(List<Integer> assetIds, Integer userId, Integer tenantId) {
        MPJLambdaWrapper<DamAsset> assetQueryWrapper = baseWrapper(userId, tenantId)
                .in(DamAsset::getId, assetIds);
        List<DamAssetWithDirectoryType> assets =
                this.baseMapper.selectJoinList(DamAssetWithDirectoryType.class, assetQueryWrapper);

        authCheck(assetIds, assets, userId, false);

        List<DamAsset> updateAssets = assets.stream().map(asset -> {
            asset.setIsDeleted(true);
            return (DamAsset) asset;
        }).toList();
        updateBatchById(updateAssets);

        // embedding 删除
        assets.forEach(asset ->
                assetEmbeddingService.deleteEmbedding(asset.getId())
        );

        // 添加到回收站
        List<DamRecycleBin> recycleBins = assets.stream().map(asset -> {
                    DamRecycleBin recycleBin = new DamRecycleBin();
                    recycleBin.setUserId(userId);
                    recycleBin.setTenantId(tenantId);
                    recycleBin.setDirectoryType(asset.getDirectoryType());
                    recycleBin.setObjectId(asset.getId());
                    recycleBin.setObjectType(DamRecycleBinObjectTypeEnum.ASSET);
                    return recycleBin;
                }
        ).toList();
        return damRecycleBinService.saveBatch(recycleBins);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean copyAssets(List<Integer> assetIds, Integer userId, Integer tenantId) {
        MPJLambdaWrapper<DamAsset> assetQueryWrapper = baseWrapper(userId, tenantId)
                .in(DamAsset::getId, assetIds);
        List<DamAssetWithDirectoryType> assets =
                this.baseMapper.selectJoinList(DamAssetWithDirectoryType.class, assetQueryWrapper);

        authCheck(assetIds, assets, userId, true);

        // 插入素材 & 标签值
        Map<Integer, DamAsset> newAssets = assets.stream().map(asset -> {
                    // TODO: 常量
                    asset.setName(asset.getName() + "_副本");
                    asset.setTenantId(tenantId);
                    asset.setUserId(userId);
                    asset.setUsedNum(0);
                    asset.setCreateTime(null);
                    asset.setUpdateTime(null);
                    return (DamAsset) asset;
                })
                .collect(Collectors.toMap(DamAsset::getId, Function.identity()));
        newAssets.values().forEach(asset -> asset.setId(null));
        saveBatch(newAssets.values());

        // embedding 复制
        assetEmbeddingService.copyByAsserts(assetIds, newAssets.values().stream().toList());

        List<DamTagValue> tagValues = tagValueService.list(new LambdaQueryWrapper<DamTagValue>().in(DamTagValue::getAssetId, assetIds));
        tagValues.forEach(tagValue -> {
            tagValue.setId(null);
            tagValue.setAssetId(newAssets.get(tagValue.getAssetId()).getId());
            tagValue.setTenantId(tenantId);
            tagValue.setCreateTime(null);
            tagValue.setUpdateTime(null);
        });
        return tagValueService.saveBatch(tagValues);
    }

    @Override
    public boolean moveAssets(List<Integer> assetIds, Integer targetDirectoryId, Integer userId, Integer tenantId) {
        DamDirectoryVO directory = directoryService.getDirectoryDetail(targetDirectoryId, userId, tenantId);
        if (directory == null) {
            throw new BusinessException(RespCode.NOT_FOUND.getCode(), "target directory was not found");
        }
        MPJLambdaWrapper<DamAsset> assetQueryWrapper = baseWrapper(userId, tenantId)
                .in(DamAsset::getId, assetIds);
        List<DamAssetWithDirectoryType> assets =
                this.baseMapper.selectJoinList(DamAssetWithDirectoryType.class, assetQueryWrapper);

        authCheck(assetIds, assets, userId, false);

        List<DamAsset> newAssets = assets.stream().map(asset -> {
            // 如果是租户往个人用户移动需要修改 userId
            if (DamDirectoryTypeEnum.PERSONAL.equals(directory.getType()) &&
                    TENANT.equals(asset.getDirectoryType())) {
                asset.setUserId(userId);
            }
            asset.setDirectoryId(targetDirectoryId);
            return (DamAsset) asset;
        }).toList();

        // embedding 移动
        assetEmbeddingService.moveByAsserts(newAssets);

        return updateBatchById(newAssets);
    }

    @Override
    public String getAssetDownloadUrl(Integer assetId, Integer userId, Integer tenantId) {
        throw new UnsupportedOperationException("download is not supported");
    }

    @Override
    public DamPageResult<DamAssetVO> semanticSearchAssets(DamAssetSemanticSearchParam searchParam,
                                                          Integer userId, Integer tenantId) {
        if (StrUtil.isBlank(searchParam.getQuery())) {
            return DamPageResult.emptyResult();
        }
        // 1.获取用户可见的目录
        var directories = directoryService.getViewableDirectory(userId, tenantId);
        if (directories.isEmpty()) {
            return DamPageResult.emptyResult();
        }
        Map<Integer, DamDirectory> userVisibleDirectories = directories.stream()
                .collect(Collectors.toMap(DamDirectory::getId, Function.identity()));
        // 2.embedding分页查询
        Page<DamAssetEmbedding> embeddingPage = assetEmbeddingService.pageEmbedding(searchParam.getPageNum(), searchParam.getPageSize(),
                searchParam.getAspectRatio(), searchParam.getQuery(), tenantId, null, new ArrayList<>(userVisibleDirectories.keySet()));
        // 3.封装结果
        List<DamAssetVO> resultList = new ArrayList<>();
        if (!embeddingPage.getRecords().isEmpty()) {
            // 根据id查询素材（需要连接查询createBy）
            List<Integer> assetIds = embeddingPage.getRecords().stream().map(DamAssetEmbedding::getId).toList();
            List<DamAsset> assets = this.baseMapper.selectJoinList(new MPJLambdaWrapper<DamAsset>()
                    .selectAll(DamAsset.class)
                    .selectAs(CmsUser::getUserName, DamAsset::getCreateBy) // 连接查询user
                    .leftJoin(CmsUser.class, CmsUser::getUserId, DamAsset::getUserId)
                    .in(DamAsset::getId, assetIds)
            );

            // in操作过后顺序发生了变更，应该把最相似的放前面，因此需要还原顺序
            var orderedAssets = sortByIds(assets, assetIds);

            resultList = this.streamBy(orderedAssets, userVisibleDirectories).toList();
            queryAndSetAssetTags(resultList);
        }
        return DamPageResult.of(embeddingPage.getTotal(), resultList, searchParam.getPageNum(), searchParam.getPageSize());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUsedNum(List<Integer> assetIds, Integer userId, Integer tenantId) {
            if (CollectionUtils.isEmpty(assetIds)) {
                    return true;
            }

            // 查询素材列表，只过滤租户ID，不过滤用户ID
            MPJLambdaWrapper<DamAsset> assetQueryWrapper = new MPJLambdaWrapper<DamAsset>().selectAll(DamAsset.class)
                            .eq(DamAsset::getIsDeleted, false).eq(DamAsset::getIsStored, true)
                            .eq(DamAsset::getTenantId, tenantId).in(DamAsset::getId, assetIds);
            List<DamAsset> assets = this.baseMapper.selectList(assetQueryWrapper);

            if (assets.isEmpty()) {
                    log.warn("更新素材使用次数失败，未找到素材，assetIds={}", assetIds);
                    return false;
            }

            // 更新素材使用次数
            for (DamAsset asset : assets) {
                    asset.setUsedNum(asset.getUsedNum() + 1);
                    asset.setUpdateTime(new Date());
            }

            return updateBatchById(assets);
    }

    @Override
    public Stream<DamAssetVO> streamBy(List<DamAsset> assets, Map<Integer, DamDirectory> userVisibleDirectories) {
        return assets.stream().map(asset -> DamAssetVO.from(
                asset,
                userVisibleDirectories.get(asset.getDirectoryId()),
                asset.getOssId() != null ? s3Service.downloadPresignedUrl(
                        objectStorageFlowProperties.getCms().getBucketName(),
                        asset.getOssId(),
                        FileConstant.OSS_PRESIGNED_URL_EXPIRE_TIME,
                        0
                ).toString() : null,
                asset.getThumbnailOssId() != null ? s3Service.downloadPresignedUrl(
                        objectStorageFlowProperties.getCms().getBucketName(),
                        asset.getThumbnailOssId(),
                        FileConstant.OSS_PRESIGNED_URL_EXPIRE_TIME,
                        0
                ).toString() : null
        ));
    }

    @Override
    public List<DamAssetVO> topKAssets(String aspectRatio, String content, Float[] embedding,
                                       Integer topK, Float threshold, Integer userId, Integer tenantId) {
        List<DamAssetVO> result = new ArrayList<>();
        if (StrUtil.isBlank(content)) return result;
        // 1.获取用户可见的目录
        var directories = directoryService.getViewableDirectory(userId, tenantId);
        if (directories.isEmpty()) {
            return result;
        }
        Map<Integer, DamDirectory> userVisibleDirectories = directories.stream()
                .collect(Collectors.toMap(DamDirectory::getId, Function.identity()));
        List<Integer> directoryIds = new ArrayList<>(userVisibleDirectories.keySet());
        List<DamAssetEmbedding> topResult;
        if (embedding != null) { // 如果传入embedding值，直接进行搜索
            topResult = assetEmbeddingService.topKEmbedding(embedding, topK, threshold, aspectRatio,
                    tenantId, null, directoryIds);
        } else {
            topResult = assetEmbeddingService.topKEmbedding(content, topK, threshold, aspectRatio,
                    tenantId, null, directoryIds);
        }
        if (!topResult.isEmpty()) {
            // 根据id查询素材（需要连接查询createBy）
            var assetIds = topResult.stream().map(DamAssetEmbedding::getId).toList();
            var assets = this.listByIds(assetIds);
            // in操作过后顺序发生了变更，应该把最相似的放前面，因此需要还原顺序
            var orderedAssets = sortByIds(assets, assetIds);
            return streamBy(orderedAssets, userVisibleDirectories).toList();
        }
        return result;
    }

    private List<DamAsset> sortByIds(List<DamAsset> assets, List<Integer> assetIds) {
        // 创建一个Map来存储id和asset的映射
        Map<Integer, DamAsset> assetMap = assets.stream()
                .collect(Collectors.toMap(DamAsset::getId, asset -> asset));
        // 按照assetIds的顺序创建有序列表
        return assetIds.stream()
                .map(assetMap::get)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Override
    public Map<Integer, List<DamTagValueDTO>> getTagsByAssertIds(Set<Integer> assetIds) {
        Map<Integer, List<DamTagValueDTOWithAssetId>> tagValueMap = this.queryTags(assetIds);
        return tagValueMap.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().stream()
                        .map(tagValue -> {
                            DamTagValueDTO tagVO = new DamTagValueDTO();
                            BeanUtils.copyProperties(tagValue, tagVO);
                            return tagVO;
                        })
                        .collect(Collectors.toList())));
    }

    /**
     * 构造 Asset & Directory 连表 QueryWrapper，方便复用
     *
     * @param userId   userId
     * @param tenantId tenantId
     * @return MPJLambdaWrapper<DamAsset>
     */
    private MPJLambdaWrapper<DamAsset> baseWrapper(Integer userId, Integer tenantId) {
        return new MPJLambdaWrapper<DamAsset>()
                .selectAll(DamAsset.class)
                .selectAs(DamDirectory::getName, "directoryName")
                .selectAs(DamDirectory::getType, "directoryType")
                .eq(DamAsset::getIsDeleted, false)
                .eq(DamAsset::getIsStored, true)
                .innerJoin(DamDirectory.class, on -> on
                        .eq(DamDirectory::getId, DamAsset::getDirectoryId)
                        .eq(DamDirectory::getIsDeleted, false)
                        .eq(DamDirectory::getTenantId, tenantId)
                        .eq(DamAsset::getTenantId, tenantId)
                        // 文件夹可见性筛选
                        .and(wrapper -> wrapper
                                .and(w -> w
                                        .eq(DamDirectory::getType, DamDirectoryTypeEnum.PERSONAL)
                                        .eq(DamDirectory::getUserId, userId)
                                        .eq(DamAsset::getUserId, userId)
                                )
                                .or(w -> w
                                        .eq(DamDirectory::getType, DamDirectoryTypeEnum.TENANT))
                        )
                );
    }

    /**
     * 删除、副本、移动素材前的权限检查
     *
     * @param assetIds assetIds
     * @param assets   assets
     * @param userId   userId
     * @param copyOp   copy 操作
     */
    private void authCheck(List<Integer> assetIds, List<DamAssetWithDirectoryType> assets,
                           Integer userId, boolean copyOp) {
        // asset 是否都可见
        if (assets.size() != assetIds.size()) {
            String message = assetIds.size() == 1 ? "asset was not found" :
                    String.format("Some assets were not found. (%s/%s are found)", assets.size(), assetIds.size());
            throw new BusinessException(RespCode.NOT_FOUND.getCode(), message);
        }

        if (!isTenantAdmin()) {
            // 普通用户不能操作租户内非自有素材
            if (assets.stream().anyMatch(asset ->
                    TENANT.equals(asset.getDirectoryType()) && !asset.getUserId().equals(userId))) {
                throw new BusinessException(RespCode.FORBIDDEN.getCode(),
                        "non-tenant-admin can't operate tenant asset not owned by you");
            }

            // 普通用户不支持租户内批量操作
            if (assetIds.size() > 1 && assets.stream().anyMatch(asset ->
                    TENANT.equals(asset.getDirectoryType()))) {
                throw new BusinessException(RespCode.FORBIDDEN.getCode(),
                        "non-tenant-admin cannot perform batch operations on tenant folder contents.");
            }
        }

        // 用于兜底 search 操作
        // 1. 普通用户不支持不同文件夹素材的批量操作
        // 2. 租户管理员不支持不同文件夹素材的拷贝操作
        if (!isTenantAdmin() || copyOp) {
            if (assets.stream().map(DamAsset::getDirectoryId).distinct().count() > 1) {
                throw new BusinessException(RespCode.FORBIDDEN.getCode(),
                        "non-tenant-admin cannot perform batch operations on different folder.");
            }
        }
    }

    /**
     * 缩略图 oss id 换 oss url
     *
     * @param asset asset
     */
    private void setThumbnailUrl(DamAssetVO asset) {
        if (asset.getThumbnailOssId() != null) {
            try {
                URL url = s3Service.downloadPresignedUrl(
                        objectStorageFlowProperties.getCms().getBucketName(),
                        asset.getThumbnailOssId(),
                        FileConstant.OSS_PRESIGNED_URL_EXPIRE_TIME,
                        0);
                asset.setThumbnailUrl(url.toString());
            } catch (Exception e) {
                // 忽略解析异常
                // TODO: 细化异常类型
                log.error("failed to get thumbnail url for asset", e);
            }
        }
    }

    /**
     * 用于调用 ttc 检查是否租户管理员
     * TODO: 作为工具提供
     */
    private boolean isTenantAdmin() {
        try {
            return userService.isTenantAdmin();
        } catch (org.apache.shiro.authz.AuthorizationException e) {
            return false;
        }
    }
}
