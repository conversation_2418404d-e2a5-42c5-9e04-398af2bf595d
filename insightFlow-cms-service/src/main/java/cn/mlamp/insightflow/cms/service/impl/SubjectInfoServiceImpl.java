package cn.mlamp.insightflow.cms.service.impl;

import cn.mlamp.insightflow.cms.common.resp.RespCode;
import cn.mlamp.insightflow.cms.config.UserContext;
import cn.mlamp.insightflow.cms.config.properties.ObjectStorageFlowProperties;
import cn.mlamp.insightflow.cms.entity.CmsDocumentInfo;
import cn.mlamp.insightflow.cms.entity.CmsSubjectImages;
import cn.mlamp.insightflow.cms.entity.CmsSubjectInfo;
import cn.mlamp.insightflow.cms.enums.DocumentStatusEnum;
import cn.mlamp.insightflow.cms.enums.UploadSourceTypeEnum;
import cn.mlamp.insightflow.cms.exception.BusinessException;
import cn.mlamp.insightflow.cms.mapper.CmsDocumentInfoMapper;
import cn.mlamp.insightflow.cms.mapper.SubjectImagesMapper;
import cn.mlamp.insightflow.cms.mapper.SubjectInfoMapper;
import cn.mlamp.insightflow.cms.model.query.SubjectCreateConfirmRequest;
import cn.mlamp.insightflow.cms.model.query.SubjectListRequest;
import cn.mlamp.insightflow.cms.model.vo.SubjectInfoVO;
import cn.mlamp.insightflow.cms.model.vo.SubjectListVO;
import cn.mlamp.insightflow.cms.service.FileService;
import cn.mlamp.insightflow.cms.service.ISubjectImagesService;
import cn.mlamp.insightflow.cms.service.ISubjectInfoService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.swing.*;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class SubjectInfoServiceImpl extends ServiceImpl<SubjectInfoMapper, CmsSubjectInfo> implements ISubjectInfoService {

    @Autowired
    private SubjectInfoMapper subjectInfoMapper;

    @Autowired
    private SubjectImagesMapper subjectImagesMapper;

    @Autowired
    private ISubjectImagesService subjectImagesService;

    @Autowired
    private CmsDocumentInfoMapper cmsDocumentInfoMapper;

    @Autowired
    private FileService fileService;

    @Resource
    private ObjectStorageFlowProperties objectStorageFlowProperties;
    @Override
    public Page<SubjectListVO> getList(SubjectListRequest subjectListRequest) {
        QueryWrapper<CmsSubjectInfo> queryWrapper = new QueryWrapper<>();

        queryWrapper.eq("is_deleted", 0);
        queryWrapper.orderByDesc("create_time");
        Page<CmsSubjectInfo> page = this.baseMapper.selectPage(new Page<>(subjectListRequest.getCurrent(), subjectListRequest.getPageSize()), queryWrapper);
        List<CmsSubjectInfo> cmsSubjectInfos = page.getRecords();
        if (cmsSubjectInfos.isEmpty() || cmsSubjectInfos.size() == 0) {
            return null;
        }
       /* List<Integer> subjectIds = new ArrayList<>();
        for (CmsSubjectInfo cmsSubjectInfo : cmsSubjectInfos) {
            subjectIds.add(cmsSubjectInfo.getSubjectId());
        }
        LambdaQueryWrapper<CmsSubjectImages> lambdaQueryWrapper = new LambdaQueryWrapper();

        lambdaQueryWrapper.eq(CmsSubjectImages::getUserId, UserContext.getUserId())
                .eq(CmsSubjectImages::getTenantId, UserContext.getTenantId())
                .in(CmsSubjectImages::getSubjectId, subjectIds)
                .eq(CmsSubjectImages::getIsDeleted, 0);

        List<CmsSubjectImages> cmsSubjectImages = subjectImagesMapper.selectList(lambdaQueryWrapper);

        List<SubjectListVO> subjectListVOList = new ArrayList<>();

        for (CmsSubjectInfo cmsSubjectInfo : cmsSubjectInfos) {
            SubjectListVO subjectListVO = new SubjectListVO();
            subjectListVO.setSubjectName(cmsSubjectInfo.getSubjectName());
            List<CmsSubjectImages> filteredImages = cmsSubjectImages.stream()
                    .filter(image -> image.getSubjectId().equals(cmsSubjectInfo.getSubjectId())) // 过滤条件
                    .collect(Collectors.toList());
            List<SubjectListVO.SubjectImage> list = new ArrayList<>();
            for (CmsSubjectImages filteredImage : filteredImages) {
                CmsDocumentInfo cmsDocumentInfo = cmsDocumentInfoMapper.selectById(filteredImage.getDocId());
                SubjectListVO.SubjectImage subjectImage = new SubjectListVO.SubjectImage();
                subjectImage.setImageNames(filteredImage.getImageName());
                subjectImage.setImageType(cmsDocumentInfo.getDocType());
                subjectImage.setImageUrl(fileService.getPicDownloadSignatureUrl(cmsDocumentInfo.getObjId()));
                subjectImage.setImageOrder(filteredImage.getSortOrder());
                list.add(subjectImage);
            }
            subjectListVO.setVisibility(cmsSubjectInfo.getVisibility());
            subjectListVO.setSubjectImages(list);
            subjectListVOList.add(subjectListVO);
        }*/
        List<SubjectListVO> subjectListVOList = new ArrayList<>();

        for (CmsSubjectInfo cmsSubjectInfo : cmsSubjectInfos) {
            List<SubjectListVO.SubjectImage> subjectImageList = searchSubjectImageListBySubjectId(cmsSubjectInfo.getSubjectId());
            SubjectListVO subjectListVO = new SubjectListVO();
            subjectListVO.setSubjectId(cmsSubjectInfo.getSubjectId());
            subjectListVO.setSubjectName(cmsSubjectInfo.getSubjectName());
            subjectListVO.setVisibility(cmsSubjectInfo.getVisibility());
            subjectListVO.setSubjectImages(subjectImageList);
            subjectListVOList.add(subjectListVO);
        }


        Page<SubjectListVO> page2 = new Page<>();
        page2.setRecords(subjectListVOList);
        page2.setTotal(page.getTotal());
        page2.setSize(page.getSize());
        page2.setCurrent(page.getCurrent());
        page2.setPages(page.getPages());
        return page2;
    }

    @Override
    public SubjectInfoVO getSubjectDetail(Integer subjectId) {

        //查找主体信息
        LambdaQueryWrapper<CmsSubjectInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CmsSubjectInfo::getSubjectId, subjectId);
        CmsSubjectInfo cmsSubjectInfo = this.baseMapper.selectOne(queryWrapper);
        if (cmsSubjectInfo == null) {
            throw new BusinessException(RespCode.BAD_REQUEST.getCode(), "主体信息不存在");
        }
        if (cmsSubjectInfo.getIsDeleted() == 1) {
            throw new BusinessException(RespCode.BAD_REQUEST.getCode(), "主体已被删除");
        }

        /*LambdaQueryWrapper<CmsSubjectImages> lambdaQueryWrapper = new LambdaQueryWrapper();

        lambdaQueryWrapper.eq(CmsSubjectImages::getUserId, UserContext.getUserId())
                .eq(CmsSubjectImages::getTenantId, UserContext.getTenantId())
                .in(CmsSubjectImages::getSubjectId, subjectId)
                .eq(CmsSubjectImages::getIsDeleted, 0);
        // 2. 根据主体id查询主体图片表
        List<CmsSubjectImages> cmsSubjectImages = subjectImagesMapper.selectList(lambdaQueryWrapper);
        SubjectInfoVO subjectInfoVO = new SubjectInfoVO();
        List<SubjectInfoVO.SubjectImage> subjectImageList = new ArrayList<>();
        for (CmsSubjectImages cmsSubjectImage : cmsSubjectImages) {
            SubjectInfoVO.SubjectImage subjectImage = new SubjectInfoVO.SubjectImage();
            subjectImage.setImageOrder(cmsSubjectImage.getSortOrder());
            subjectImage.setImageNames(cmsSubjectImage.getImageName());
            Integer docId = cmsSubjectImage.getDocId();
            CmsDocumentInfo cmsDocumentInfo = cmsDocumentInfoMapper.selectById(docId);
            subjectImage.setImageUrl(cmsDocumentInfo.getObjId());
            subjectImage.setImageType(fileService.getPicDownloadSignatureUrl(cmsDocumentInfo.getObjId()));
            subjectImageList.add(subjectImage);
        }*/
        SubjectInfoVO subjectInfoVO = new SubjectInfoVO();
        List<SubjectInfoVO.SubjectImageInfo> subjectImageList = searchSubjectInfoImageBySubjectId(subjectId);
        subjectInfoVO.setSubjectId(subjectId);
        subjectInfoVO.setSubjectName(cmsSubjectInfo.getSubjectName());
        subjectInfoVO.setSubjectLabel(cmsSubjectInfo.getLabel());
        subjectInfoVO.setSubjectStyle(cmsSubjectInfo.getStyle());
        subjectInfoVO.setSubjectDescription(cmsSubjectInfo.getDescription());
        subjectInfoVO.setVisibility(cmsSubjectInfo.getVisibility());
        subjectInfoVO.setSubjectImages(subjectImageList);

        return subjectInfoVO;
    }

    @Override
    public void deleteBatchByIds(List<Integer> ids) {
        if(ids.size() == 0 || ids.isEmpty()){
            return;
        }
        for (Integer id : ids) {
            subjectInfoMapper.update(null, new LambdaUpdateWrapper<CmsSubjectInfo>()
                    .eq(CmsSubjectInfo::getSubjectId, id)
                    .set(CmsSubjectInfo::getIsDeleted, 1));
         // 查询这些图片关联的文档 ID
            List<CmsSubjectImages> images = subjectImagesMapper.selectList(new LambdaQueryWrapper<CmsSubjectImages>()
                    .eq(CmsSubjectImages::getSubjectId, id)
                    .eq(CmsSubjectImages::getIsDeleted, 0));

            // 批量更新 cms_subject_images 表
            subjectImagesMapper.update(null, new LambdaUpdateWrapper<CmsSubjectImages>()
                    .eq(CmsSubjectImages::getSubjectId, id)
                    .eq(CmsSubjectImages::getIsDeleted, 0)
                    .set(CmsSubjectImages::getIsDeleted, 1));

            if (images != null && !images.isEmpty()) {
                List<Integer> docIds = images.stream()
                        .map(CmsSubjectImages::getDocId)
                        .distinct()
                        .collect(Collectors.toList());

                // 批量更新 cms_document_info 表
                if (!docIds.isEmpty()) {
                    cmsDocumentInfoMapper.update(null, new LambdaUpdateWrapper<CmsDocumentInfo>()
                            .in(CmsDocumentInfo::getId, docIds)
                            .set(CmsDocumentInfo::getIsDeleted, 1));
                }
            }
        }

    }

    @Override
    public void addOrUpdateSubjectConfirmDescription(SubjectCreateConfirmRequest subjectCreateConfirmRequest) {
        if(subjectCreateConfirmRequest.getSubjectId() == null){
            addSubjectConfirmDescription(subjectCreateConfirmRequest);
        }else {
            updateSubjectConfirmDescription(subjectCreateConfirmRequest);
        }
    }




    public void addSubjectConfirmDescription(SubjectCreateConfirmRequest subjectCreateConfirmRequest) {
        CmsSubjectInfo cmsSubjectInfo = new CmsSubjectInfo();
        Integer count = subjectInfoMapper.countAllSubjects();
        cmsSubjectInfo.setSubjectId(count + 1);
        cmsSubjectInfo.setSubjectName(subjectCreateConfirmRequest.getSubjectName());
        if (subjectCreateConfirmRequest.getSubjectTag() != null){
            cmsSubjectInfo.setLabel(subjectCreateConfirmRequest.getSubjectTag());
        }
        cmsSubjectInfo.setStyle(subjectCreateConfirmRequest.getSubjectStyle());
        cmsSubjectInfo.setVisibility(subjectCreateConfirmRequest.getVisibility());
        cmsSubjectInfo.setDescription(subjectCreateConfirmRequest.getSubjectDescription());
        cmsSubjectInfo.setIsDeleted(0);
        // 1.主体信息表插入数据
        this.baseMapper.insert(cmsSubjectInfo);

        //2.cms_document_info插入数据
        List<SubjectCreateConfirmRequest.SubjectImageRequest> subjectImages = subjectCreateConfirmRequest.getSubjectImages();
        for (SubjectCreateConfirmRequest.SubjectImageRequest subjectImage : subjectImages) {
            CmsDocumentInfo cmsDocumentInfo = new CmsDocumentInfo();
            cmsDocumentInfo.setDocName(subjectImage.getImageNames());
            cmsDocumentInfo.setDocType(subjectImage.getImageType());
            cmsDocumentInfo.setSourceType(UploadSourceTypeEnum.from_local.name());
            //TODO 桶名称
            cmsDocumentInfo.setBucketName(objectStorageFlowProperties.getCms().getBucketName());
            cmsDocumentInfo.setObjId(subjectImage.getImageOssId());
            cmsDocumentInfo.setSize(subjectImage.getImageSize());
            cmsDocumentInfo.setStatus(DocumentStatusEnum.COMPLETED.name());
            cmsDocumentInfo.setUserId(UserContext.getUserId());
            cmsDocumentInfo.setTenantId(UserContext.getTenantId());
            cmsDocumentInfoMapper.insert(cmsDocumentInfo);
            //3.主体图片信息表

            CmsSubjectImages cmsSubjectImages = new CmsSubjectImages();
            cmsSubjectImages.setSubjectId(cmsSubjectInfo.getSubjectId());
            cmsSubjectImages.setSortOrder(subjectImage.getImageOrder());
            cmsSubjectImages.setIsDeleted(0);
            cmsSubjectImages.setObjId(subjectImage.getImageOssId());
            cmsSubjectImages.setDocId(cmsDocumentInfo.getId());
            Integer cnt = subjectImagesMapper.countAllSubjectImages();
            cmsSubjectImages.setImageId(cnt + 1);
            subjectImagesMapper.insert(cmsSubjectImages);
        }



    }


    public void updateSubjectConfirmDescription(SubjectCreateConfirmRequest subjectCreateConfirmRequest) {
        //查找主体信息
        LambdaQueryWrapper<CmsSubjectInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CmsSubjectInfo::getSubjectId, subjectCreateConfirmRequest.getSubjectId());
        CmsSubjectInfo oldCmsSubjectInfo = subjectInfoMapper.selectOne(queryWrapper);
        oldCmsSubjectInfo.setSubjectName(subjectCreateConfirmRequest.getSubjectName());
        if (subjectCreateConfirmRequest.getSubjectTag() != null){
            oldCmsSubjectInfo.setLabel(subjectCreateConfirmRequest.getSubjectTag());
        }
        oldCmsSubjectInfo.setStyle(subjectCreateConfirmRequest.getSubjectStyle());
        oldCmsSubjectInfo.setVisibility(subjectCreateConfirmRequest.getVisibility());
        oldCmsSubjectInfo.setDescription(subjectCreateConfirmRequest.getSubjectDescription());
        oldCmsSubjectInfo.setUpdateTime(new Date());
        //主体信息表更新数据
        this.baseMapper.updateById(oldCmsSubjectInfo);

        // 查询这些图片关联的文档 ID
        List<CmsSubjectImages> images = subjectImagesMapper.selectList(new LambdaQueryWrapper<CmsSubjectImages>()
                .eq(CmsSubjectImages::getSubjectId, subjectCreateConfirmRequest.getSubjectId())
                .eq(CmsSubjectImages::getIsDeleted, 0));

        // 批量更新 cms_subject_images 表
        subjectImagesMapper.update(null, new LambdaUpdateWrapper<CmsSubjectImages>()
                .eq(CmsSubjectImages::getSubjectId, subjectCreateConfirmRequest.getSubjectId())
                .eq(CmsSubjectImages::getIsDeleted, 0)
                .set(CmsSubjectImages::getIsDeleted, 1));

        if (images != null && !images.isEmpty()) {
            List<Integer> docIds = images.stream()
                    .map(CmsSubjectImages::getDocId)
                    .distinct()
                    .collect(Collectors.toList());

            // 批量更新 cms_document_info 表
            if (!docIds.isEmpty()) {
                cmsDocumentInfoMapper.update(null, new LambdaUpdateWrapper<CmsDocumentInfo>()
                        .in(CmsDocumentInfo::getId, docIds)
                        .set(CmsDocumentInfo::getIsDeleted, 1));
            }
        }


        //2.cms_document_info插入数据
        List<SubjectCreateConfirmRequest.SubjectImageRequest> subjectImages = subjectCreateConfirmRequest.getSubjectImages();
        for (SubjectCreateConfirmRequest.SubjectImageRequest subjectImage : subjectImages) {
            CmsDocumentInfo cmsDocumentInfo = new CmsDocumentInfo();
            cmsDocumentInfo.setDocName(subjectImage.getImageNames());
            cmsDocumentInfo.setDocType(subjectImage.getImageType());
            cmsDocumentInfo.setSourceType(UploadSourceTypeEnum.from_local.name());
            //TODO 桶名称
            cmsDocumentInfo.setBucketName(objectStorageFlowProperties.getCms().getBucketName());
            cmsDocumentInfo.setObjId(subjectImage.getImageOssId());
            cmsDocumentInfo.setSize(subjectImage.getImageSize());
            cmsDocumentInfo.setStatus(DocumentStatusEnum.COMPLETED.name());
            cmsDocumentInfo.setUserId(UserContext.getUserId());
            cmsDocumentInfo.setTenantId(UserContext.getTenantId());
            cmsDocumentInfoMapper.insert(cmsDocumentInfo);
            //3.主体图片信息表

            CmsSubjectImages cmsSubjectImages = new CmsSubjectImages();
            cmsSubjectImages.setSubjectId(subjectCreateConfirmRequest.getSubjectId());
            cmsSubjectImages.setSortOrder(subjectImage.getImageOrder());
            cmsSubjectImages.setIsDeleted(0);
            cmsSubjectImages.setObjId(subjectImage.getImageOssId());
            cmsSubjectImages.setDocId(cmsDocumentInfo.getId());
            Integer cnt = subjectImagesMapper.countAllSubjectImages();
            cmsSubjectImages.setImageId(cnt + 1);
            subjectImagesMapper.insert(cmsSubjectImages);
        }



    }


    /**
     * 根据主体id查询主体图片信息
     * @param subjectId 主体id
     * @return 主体图片信息
     */
    public List<SubjectListVO.SubjectImage> searchSubjectImageListBySubjectId(Integer subjectId){
        LambdaQueryWrapper<CmsSubjectImages> lambdaQueryWrapper = new LambdaQueryWrapper();

        lambdaQueryWrapper.eq(CmsSubjectImages::getSubjectId, subjectId)
                .eq(CmsSubjectImages::getIsDeleted, 0);

        List<CmsSubjectImages> cmsSubjectImages = subjectImagesMapper.selectList(lambdaQueryWrapper);

        List<SubjectListVO.SubjectImage> subjectImageList = new ArrayList<>();

        for (CmsSubjectImages cmsSubjectImage : cmsSubjectImages) {
            SubjectListVO.SubjectImage subjectImage = new SubjectListVO.SubjectImage();
            subjectImage.setImageUrl(fileService.getPicDownloadSignatureUrl(cmsSubjectImage.getObjId()));
            subjectImage.setImageOrder(cmsSubjectImage.getSortOrder());
            subjectImage.setImageOssId(cmsSubjectImage.getObjId());
            subjectImageList.add(subjectImage);
        }
        return subjectImageList;
    }



    /**
     * 根据主体id查询主体图片信息
     * @param subjectId 主体id
     * @return 主体图片信息
     */
    public List<SubjectInfoVO.SubjectImageInfo> searchSubjectInfoImageBySubjectId(Integer subjectId){
        LambdaQueryWrapper<CmsSubjectImages> lambdaQueryWrapper = new LambdaQueryWrapper();

        lambdaQueryWrapper.eq(CmsSubjectImages::getSubjectId, subjectId)
                .eq(CmsSubjectImages::getIsDeleted, 0);

        List<CmsSubjectImages> cmsSubjectImages = subjectImagesMapper.selectList(lambdaQueryWrapper);

        List<SubjectInfoVO.SubjectImageInfo> subjectImageList = new ArrayList<>();

        for (CmsSubjectImages cmsSubjectImage : cmsSubjectImages) {
            SubjectInfoVO.SubjectImageInfo subjectImage = new SubjectInfoVO.SubjectImageInfo();
            subjectImage.setImageUrl(fileService.getPicDownloadSignatureUrl(cmsSubjectImage.getObjId()));
            subjectImage.setImageOrder(cmsSubjectImage.getSortOrder());
            subjectImage.setImageOssId(cmsSubjectImage.getObjId());
            subjectImageList.add(subjectImage);
        }
        return subjectImageList;
    }


}
