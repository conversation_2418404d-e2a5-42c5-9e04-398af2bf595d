package cn.mlamp.insightflow.cms.service;

import cn.mlamp.insightflow.cms.entity.CmsTaskShareReport;
import cn.mlamp.insightflow.cms.model.query.TaskShareRequest;
import cn.mlamp.insightflow.cms.model.vo.TaskShareDetailVO;
import cn.mlamp.insightflow.cms.model.vo.TaskShareVO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 任务分享报告服务接口
 * <AUTHOR>
 * @date 2025-05-20
 */
public interface ITaskShareReportService extends IService<CmsTaskShareReport> {

    /**
     * 创建任务分享
     * @param request 分享请求
     * @param userId 用户ID
     * @param tenantId 租户ID
     * @return 分享信息
     */
    TaskShareVO createTaskShare(TaskShareRequest request, Integer userId, Integer tenantId);

    /**
     * 根据授权码获取分享详情
     * @param authorizeCode 授权码
     * @return 分享详情
     */
    TaskShareDetailVO getShareDetailByCode(String authorizeCode);
}
