package cn.mlamp.insightflow.cms.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 任务分享请求
 * <AUTHOR>
 * @date 2025-05-20
 */
@Data
public class TaskShareRequest {
    
    @Schema(description = "任务ID", required = true)
    private Integer taskId;
    
    @Schema(description = "任务类型：1-视频合成任务", required = true)
    private Integer taskType;
    
    @Schema(description = "授权时长（单位分钟），默认为7天(10080分钟)", required = false)
    private Integer authorizeTime = 10080; // 默认7天
}
