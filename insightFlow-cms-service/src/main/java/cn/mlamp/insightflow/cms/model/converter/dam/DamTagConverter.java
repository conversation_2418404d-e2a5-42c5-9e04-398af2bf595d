package cn.mlamp.insightflow.cms.model.converter.dam;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import cn.mlamp.insightflow.cms.entity.dam.DamTag;
import cn.mlamp.insightflow.cms.model.dto.dam.DamTagDTO;
import cn.mlamp.insightflow.cms.model.vo.dam.DamTagVO;

@Mapper(componentModel = "spring")
public interface DamTagConverter {

    DamTag toEntity(DamTagDTO damTagDTO);

    DamTagVO toVO(DamTag damTag);

    List<DamTagVO> toVOs(List<DamTag> damTags);

}
