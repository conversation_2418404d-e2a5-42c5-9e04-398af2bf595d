package cn.mlamp.insightflow.cms.model.dto;

import cn.hutool.core.util.StrUtil;
import cn.mlamp.insightflow.cms.model.query.VideoScriptGenRequest;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import lombok.SneakyThrows;

import java.util.Map;

/**
 * dify分镜信息修改请求（润色）
 */
@Data
public class DifyStoryboardModifyRequestDTO {
    private String brand;
    private String product;
    private String scene;

    private String actions; //当前分镜json（必填）
    private String key; //字段名
    private String prompt; //调整方向

    @SneakyThrows
    public static Map<String, Object> buildDifyParams(VideoScriptGenRequest videoScriptGenRequest,
                                                      Map<String, String> storyBoard, String key, String prompt) {
        var dto = new DifyStoryboardModifyRequestDTO();
        var content = videoScriptGenRequest.getContent();
        // 必填
        dto.setBrand(content.getBrand());
        dto.setProduct(content.getProduct());
        dto.setKey(key);
        dto.setPrompt(prompt);
        // 选填
        dto.setScene(content.getScene());

        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);

        // actions分镜json
        if (storyBoard.isEmpty()) {
            throw new RuntimeException("参考分镜不能为空");
        }
        dto.setActions(objectMapper.writeValueAsString(storyBoard));

        return objectMapper.convertValue(dto, Map.class);
    }
}
