package cn.mlamp.insightflow.cms.model.dto;

import java.time.LocalDateTime;
import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class VideoIdsRequestDTO {

    @Schema(description = "视频ID列表")
    private List<String> videoIds;

    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    private LocalDateTime endTime;
}
