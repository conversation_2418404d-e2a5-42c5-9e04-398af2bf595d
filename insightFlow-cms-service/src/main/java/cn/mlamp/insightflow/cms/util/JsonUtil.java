package cn.mlamp.insightflow.cms.util;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.Version;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.List;
import java.util.Map;

@Slf4j
public class JsonUtil {

    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static List<String> desensitizeKeyList = Lists.newArrayList("password", "pswd", "secret");

    static {
        SimpleModule simpleModule = new SimpleModule("SimpleJodaModule", new Version(1, 0, 0, null, null, null));
        simpleModule.addSerializer(java.sql.Date.class, ToStringSerializer.instance);
        objectMapper.registerModule(simpleModule);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.configure(JsonParser.Feature.ALLOW_COMMENTS, true);
        objectMapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES, true);
        objectMapper.configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);

        // 解决jackson2无法反序列化LocalDateTime的问题
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        objectMapper.registerModule(new JavaTimeModule());
    }

    public static String encode(Object obj) {
        try {
            return objectMapper.writeValueAsString(obj);
        } catch (Exception e) {
            log.error("jackson encode error:", e); //$NON-NLS-1$
        }
        return StringUtils.EMPTY;
    }

    public static <T> T decode(String json, Class<T> valueType) {
        try {
            return objectMapper.readValue(json, valueType);
        } catch (Exception e) {
            log.error("jackson decode(String, Class<T>) error: ", e);
        }
        return null;
    }

    public static <T> T decode(String json, TypeReference<T> t) {
        try {
            return objectMapper.readValue(json, t);
        } catch (Exception e) {
            log.error("jackson decode(String, TypeReference<T>) error: ", e);
        }
        return null;
    }

    public static <T> List<T> decodeList(String json, Class<T> c) {
        List<T> t = null;
        try {
            if (json != null) {
                t = objectMapper.readValue(json, objectMapper.getTypeFactory().constructCollectionType(List.class, c));
            }
        } catch (Exception e) {
            log.error("decodeList error", e);
        }
        return t;
    }

    public static <K, V> List<Map<K, V>> decodeList(String json, TypeReference<List<Map<K, V>>> ref) {
        try {
            return objectMapper.readValue(json, ref);
        } catch (Exception e) {
            log.error("jackson decodeList(String, TypeReference<T>) error: ", e);
        }
        return null;
    }

    public static <K, V> Map<K, V> decodeMap(String json, Class<K> key, Class<V> value) {
        Map<K, V> map = null;
        try {
            if (json != null) {
                map = objectMapper.readValue(json,
                        objectMapper.getTypeFactory().constructMapType(Map.class, key, value));
            }
        } catch (Exception e) {
            log.error("decodeList error", e);
        }
        return map;
    }

    public static <K, V> Map<K, V> decodeMap(String json, TypeReference<Map<K, V>> ref) {
        try {
            return objectMapper.readValue(json, ref);
        } catch (Exception e) {
            log.error("jackson decodeMap(String, TypeReference<T>) error: ", e);
        }
        return null;
    }

    public static <K, V> List<Map<K, V>> decodeListOrMap(String json, K k, V v) {
        // try list
        List<Map<K, V>> list = decodeList(json, new TypeReference<List<Map<K, V>>>() {
        });
        if (list == null) {
            // try map
            Map<K, V> map = decodeMap(json, new TypeReference<Map<K, V>>() {
            });
            if (map == null) {
                log.error("jackson decodeListOrMap final fail:{}", json);
            } else {
                list = Lists.newArrayList(map);
                return list;
            }
        } else {
            return list;
        }
        return null;
    }

    public static JsonNode readTree(String json) {
        try {
            return objectMapper.readTree(json);
        } catch (IOException e) {
            log.error("jackson read tree error:{}", json);
            return null;
        }
    }

    /**
     * 序列化的同时，进行数据脱敏，常用于日志打印
     */
    public static String encodeDesensitize(Object obj) {
        // 执行序列化并全部转小写匹配敏感词
        String valueAsString = encode(obj).toLowerCase();
        for (String desensitizeKey : desensitizeKeyList) {
            // 包含任一个敏感词，则返回空串
            if (valueAsString.contains(desensitizeKey)) {
                return StringUtils.EMPTY;
            }
        }
        return valueAsString;
    }

}
