package cn.mlamp.insightflow.cms.enums.dam;

import javax.annotation.Nullable;

import cn.mlamp.insightflow.cms.enums.VideoTaskStatusEnum;
import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * DAM任务状态枚举
 */
@Getter
@AllArgsConstructor
public enum DamTaskStatusEnum implements IEnum<Integer> {
    
    PENDING(1, "待处理"),
    RUNNING(2, "处理中"),
    COMPLETED(3, "完成"),
    FAILED(4, "失败");

    @JsonValue
    private final Integer code;
    private final String desc;

    @Override
    public Integer getValue() {
        return code;
    }

    @JsonCreator
    @Nullable
    public static DamTaskStatusEnum getByCode(@Nullable Integer code) {
        if (code == null) {
            return null;
        }
        for (DamTaskStatusEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new IllegalArgumentException("Invalid value '" + code + "'");
    }
} 