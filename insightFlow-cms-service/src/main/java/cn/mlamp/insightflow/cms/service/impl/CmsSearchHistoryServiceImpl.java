package cn.mlamp.insightflow.cms.service.impl;

import cn.mlamp.insightflow.cms.entity.CmsSearchHistory;
import cn.mlamp.insightflow.cms.mapper.CmsSearchHistoryMapper;
import cn.mlamp.insightflow.cms.service.CmsSearchHistoryService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 用户查询历史记录Service实现类
 * 
 * <AUTHOR>
 * @date 2025-05-28
 */
@Service
@RequiredArgsConstructor
public class CmsSearchHistoryServiceImpl extends ServiceImpl<CmsSearchHistoryMapper, CmsSearchHistory>
        implements CmsSearchHistoryService {

    @Override
    public void saveSearchHistory(Integer userId, Integer tenantId, String keyword, String searchType,
            String searchModule) {
        // 如果关键词为空，不记录
        if (StringUtils.isBlank(keyword)) {
            return;
        }

        CmsSearchHistory searchHistory = new CmsSearchHistory();
        searchHistory.setUserId(userId);
        searchHistory.setTenantId(tenantId);
        searchHistory.setKeyword(keyword);
        searchHistory.setSearchType(searchType);
        searchHistory.setSearchModule(searchModule);
        searchHistory.setIsDeleted(0);

        save(searchHistory);
    }
}
