package cn.mlamp.insightflow.cms.model.query;

import cn.mlamp.insightflow.cms.entity.CmsPullTaskDedupedData;
import lombok.Data;

import java.util.List;

/**
 * @Author: husuper
 * @CreateTime: 2025-03-24
 */
@Data
public class AnalysisVideoCreateRequest {

   private String typeName;

   private String  esId;

   //非必须，视频下载的URL，预签名
   private String videoDownloadUrl;

   //非必须，视频信息id
   private Integer videoInfoId;

   /** 用户id */
   private Integer userId ;
   /** 租户id */
   private Integer tenantId ;

}
