package cn.mlamp.insightflow.cms.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 上传网页链接请求
 */
@Data
public class UploadTaskQueryRequest {
    @Schema(description = "任务id数组")
    private List<Integer> taskIds;
    @Schema(description = "任务类型（上传任务0，脚本生成任务1）", required = true)
    private Integer type;

    private Integer userId;
    private Integer tenantId;

}
