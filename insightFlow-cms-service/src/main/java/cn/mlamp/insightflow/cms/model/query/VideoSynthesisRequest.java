package cn.mlamp.insightflow.cms.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 视频合成请求
 * <AUTHOR>
 * @date 2025-05-20
 */
@Data
public class VideoSynthesisRequest {

    @Schema(description = "任务信息", required = true)
    private String taskInfo;

    @Schema(description = "视频OSS ID列表", required = true)
    private List<String> videoOssIds;

    @Schema(description = "素材视频ID列表", required = false)
    private List<Integer> assetIds;

    @Schema(description = "任务名称", required = false)
    private String taskName;

    @Schema(description = "用户ID", required = false)
    private Integer userId;

    @Schema(description = "租户ID", required = false)
    private Integer tenantId;
}
