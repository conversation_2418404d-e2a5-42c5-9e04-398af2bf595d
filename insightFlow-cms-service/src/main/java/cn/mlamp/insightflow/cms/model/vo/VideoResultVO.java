package cn.mlamp.insightflow.cms.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: husuper
 * @CreateTime: 2025-04-21
 */
@Data
public class VideoResultVO implements Serializable {

    @Schema(description = "视频分析结果Id", required = true)
    private Integer id ;

    /** 1：AI解码；2：分镜；3：ASR */
    @Schema(description = "视频分析结果类型", required = true)
    private Integer type ;

    /** 分析任务ID */
    @Schema(description = "视频分析id", required = true)
    private Integer videoId ;

    /** 排序序号 */
    @Schema(description = "排序序号", required = true)
    private Integer index ;

    /** json格式 */
    @Schema(description = "json格式", required = true)
    private String data ;



}
