package cn.mlamp.insightflow.cms.service;

import cn.mlamp.insightflow.cms.entity.CmsStoryboardEmbedding;
import cn.mlamp.insightflow.cms.model.dto.DifyScriptGenResponseDTO;
import cn.mlamp.insightflow.cms.model.vo.VideoStoryBoardVO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

public interface ICmsStoryboardEmbeddingService extends IService<CmsStoryboardEmbedding> {

    /**
     * 保存分镜嵌入向量数据
     *
     * @param storyboardEmbedding 分镜嵌入向量实体
     * @return 保存的实体
     */
    CmsStoryboardEmbedding saveStoryboardEmbedding(CmsStoryboardEmbedding storyboardEmbedding);

    /**
     * 更新分镜嵌入向量内容和嵌入向量
     *
     * @param id ID
     * @param content 嵌入内容
     * @return 更新结果
     */
    int updateEmbedding(Integer id, String content);

    /**
     * 删除分镜嵌入向量数据
     *
     * @param id ID
     * @return 删除结果
     */
    int deleteEmbedding(Integer id);

    /**
     * 分页查询分镜嵌入向量相似度排序结果
     *
     * @param current 当前页
     * @param pageSize 每页大小
     * @param taskId 任务ID
     * @param content 查询内容
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @return 分页结果
     */
    Page<CmsStoryboardEmbedding> pageEmbedding(Integer current, Integer pageSize, Integer taskId, String content,
                                              Integer tenantId, Integer userId);

    /**
     * 查询Top K相似度分镜嵌入向量
     *
     * @param content 查询内容
     * @param limit 限制数量
     * @param threshold 相似度阈值
     * @param taskId 任务ID
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @return 结果列表
     */
    List<CmsStoryboardEmbedding> topKEmbedding(String content, Integer limit, Float threshold, Integer taskId,
                                              Integer tenantId, Integer userId);

    void asyncEmbedding(Integer taskId, List<DifyScriptGenResponseDTO.Scene> scenes, Integer[] status,
                        Integer tenantId, Integer userId);

    void asyncEmbedding(Integer taskId, List<VideoStoryBoardVO> needSaveEmbedding, Integer tenantId, Integer userId);

    void asyncUpdate(List<VideoStoryBoardVO> oldStoryboards, List<VideoStoryBoardVO> newStoryboards);

    List<CmsStoryboardEmbedding> getByTaskId(Integer taskId);
}
