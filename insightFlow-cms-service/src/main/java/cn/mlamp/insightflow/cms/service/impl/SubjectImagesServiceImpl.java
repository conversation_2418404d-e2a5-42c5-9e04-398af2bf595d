package cn.mlamp.insightflow.cms.service.impl;

import cn.mlamp.insightflow.cms.config.properties.DifyRequestProperties;
import cn.mlamp.insightflow.cms.entity.CmsSubjectImages;
import cn.mlamp.insightflow.cms.mapper.SubjectImagesMapper;
import cn.mlamp.insightflow.cms.model.query.SubjectCreateUploadImageRequest;
import cn.mlamp.insightflow.cms.model.vo.SubjectImageAnalyseVO;
import cn.mlamp.insightflow.cms.service.FileService;
import cn.mlamp.insightflow.cms.service.ISubjectImagesService;
import cn.mlamp.insightflow.cms.service.webflux.DifyRequestService;
import cn.mlamp.insightflow.cms.util.dify.DifyUtil;
import cn.mlamp.insightflow.cms.util.dify.model.DifyRequest;
import cn.mlamp.insightflow.cms.util.dify.model.ViduImageFile;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeUnit;



@Service
@Slf4j
public class SubjectImagesServiceImpl extends ServiceImpl<SubjectImagesMapper, CmsSubjectImages> implements ISubjectImagesService {

    @Autowired
    private DifyRequestService difyRequestService;

    private final DifyUtil difyUtil;

    @Autowired
    private DifyRequestProperties difyRequestProperties;

    public SubjectImagesServiceImpl() {
        this.difyUtil = new DifyUtil();
    }

    @Autowired
    private FileService fileService;


    public SubjectImageAnalyseVO analyseImage(SubjectCreateUploadImageRequest subjectCreateUploadImageRequest) {
        List<String> ossIds = subjectCreateUploadImageRequest.getOssIds();
        ViduImageFile[] files = new ViduImageFile[ossIds.size()];
        for (int i = 0; i < ossIds.size(); i++) {
            files[i] = ViduImageFile.builder()
                    .type("image")
                    .transfer_method("remote_url")
                    .url(fileService.getPicDownloadSignatureUrl(ossIds.get(i)))
                    .build();
        }
        var difyRequest = DifyRequest.builder()
                .appKey(difyRequestProperties.getImageStyleAndDescKey())
                .baseUrl(difyRequestProperties.getBaseUrl())
                .query("给出图片的风格和描述")
                .files(files)
                .build();
        var difyResult = difyUtil.blockingImageChatRequest(difyRequest, UUID.randomUUID().toString());
        if (difyResult == null) {
            throw new RuntimeException("图片分析失败");
        }
        SubjectImageAnalyseVO subjectImageAnalyseVO = new SubjectImageAnalyseVO();
        subjectImageAnalyseVO.setSubjectImageResult(difyResult.toString());
        return subjectImageAnalyseVO;

    }




}
