package cn.mlamp.insightflow.cms.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Author: husuper
 * @CreateTime: 2025-03-19
 */
@Data
public class VideoFiveGoldDetailRequest extends PageRequest {

    @Schema(description = "黄金5秒id", required = true)
    private Integer fiveGoledId;

    /**
     * 排序字段
     */
    @Schema(description = "排序字段，可选值：exposure（曝光数），shares（分享数），comments（评论数），likes（点赞数），clicks（点击数），rating（创意分值），consume_range_weight（消耗区间权重）", required = false)
    private String sortField;

    /**
     * 排序方式
     */
    @Schema(description = "排序方式，可选值：asc（升序），desc（降序），默认为desc", required = false)
    private String sortOrder = "desc";
}
