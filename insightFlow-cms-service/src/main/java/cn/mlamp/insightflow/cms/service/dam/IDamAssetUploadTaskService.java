package cn.mlamp.insightflow.cms.service.dam;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;

import cn.mlamp.insightflow.cms.entity.dam.DamAssetUploadTask;
import cn.mlamp.insightflow.cms.enums.dam.DamTaskTypeEnum;
import cn.mlamp.insightflow.cms.model.dto.dam.DamAssetUploadTaskDTO;
import cn.mlamp.insightflow.cms.model.dto.dam.DamAssetUploadTaskStorageDTO;
import cn.mlamp.insightflow.cms.model.query.PageParam;
import cn.mlamp.insightflow.cms.model.vo.dam.DamAssetUploadTaskDetailVO;
import cn.mlamp.insightflow.cms.model.vo.dam.DamPageResult;

/**
 * <p>
 * DAM素材上传任务表-记录细节 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
public interface IDamAssetUploadTaskService extends IService<DamAssetUploadTask> {

    /**
     * 创建素材上传任务
     *
     * @param taskDTO 任务DTO
     * @param userId 用户ID
     * @param tenantId 租户ID
     * @return 任务ID
     */
    Integer createAssetUploadTask(DamAssetUploadTaskDTO taskDTO, Integer userId, Integer tenantId);

    /**
     * 获取素材上传任务列表
     *
     * @param type 任务类型 1: 素材上传任务, 2: AI分镜镜头入库
     * @param pageParam 分页参数
     * @param userId 用户ID
     * @param tenantId 租户ID
     * @return 任务分页结果
     */
    @Deprecated
    DamPageResult<DamAssetUploadTaskDetailVO> getAssetUploadTaskList(
            DamTaskTypeEnum type,
            PageParam pageParam,
            Integer userId,
            Integer tenantId);

    /**
     * 获取素材上传任务列表V2
     *
     * @param type 任务类型 1: 素材上传任务, 2: AI分镜镜头入库
     * @param pageParam 分页参数
     * @param userId 用户ID
     * @param tenantId 租户ID
     * @return 任务分页结果
     */
    DamPageResult<DamAssetUploadTaskDetailVO> getAssetUploadTaskListV2(
            DamTaskTypeEnum type,
            PageParam pageParam,
            Integer userId,
            Integer tenantId);

    DamAssetUploadTaskDetailVO getAssetUploadTaskDetailV2(Integer taskId, Integer userId, Integer tenantId);

    /**
     * 获取素材上传任务详情
     *
     * @param taskId 任务ID
     * @param userId 用户ID
     * @param tenantId 租户ID
     * @return 任务详情
     */
    @Deprecated
    DamAssetUploadTaskDetailVO getAssetUploadTaskDetail(Integer taskId, Integer userId, Integer tenantId);

    /**
     * 素材入库
     *
     * @param taskId 任务ID
     * @param request 入库请求
     * @param userId 用户ID
     * @param tenantId 租户ID
     * @return 是否成功
     * @throws JsonProcessingException
     * @throws JsonMappingException
     */
    boolean storageAssets(Integer taskId, DamAssetUploadTaskStorageDTO request,
            Integer userId, Integer tenantId) throws JsonMappingException, JsonProcessingException;

}
