package cn.mlamp.insightflow.cms.service;

import cn.mlamp.insightflow.cms.entity.TenantToken;
import cn.mlamp.insightflow.cms.model.query.TokenDetailsPageQueryRequest;
import cn.mlamp.insightflow.cms.model.vo.ConsumptionSummaryVO;
import cn.mlamp.insightflow.cms.model.vo.RechargeAllVO;
import cn.mlamp.insightflow.cms.model.vo.TenantTokenSummaryVO;
import cn.mlamp.insightflow.cms.model.vo.TokenDetailsVO;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Author: husuper
 * @CreateTime: 2025-03-21
 */
public interface TenantTokenService extends IService<TenantToken> {

    TenantTokenSummaryVO getSummary(Integer tenantId);

    ConsumptionSummaryVO consumptionSummary(Integer tenantId);

    Page<TokenDetailsVO> detailPageQuery(TokenDetailsPageQueryRequest request);

    Page<RechargeAllVO> allPageQuery(TokenDetailsPageQueryRequest request);

    /**
     * 租户Token充值
     *
     * @param tenantId       租户ID
     * @param tenantName     租户名称
     * @param rechargeTokens 充值Token数量
     * @param userId         充值用户ID
     */
    void rechargeToken(Integer tenantId, String tenantName, Integer rechargeTokens, Integer userId);

    /**
     * 检查租户Token余额
     *
     * @param tenantId 租户ID
     * @return 租户Token余额 余额和点数的关系是 余额/1000 = 点数
     */
    Integer checkBalance(Integer tenantId);
}
