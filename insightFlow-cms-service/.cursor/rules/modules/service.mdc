---
description:
globs: src/main/java/cn/mlamp/insightflow/cms/service/**/*.java
alwaysApply: false
---
# 服务层

服务层是系统的核心业务逻辑实现，遵循接口与实现分离的原则。服务接口定义在 [service](mdc:src/main/java/cn/mlamp/insightflow/cms/service) 目录，实现在 [service/impl](mdc:src/main/java/cn/mlamp/insightflow/cms/service/impl) 目录。

代码规范:

- 服务类必须是接口类型。
- 所有服务类方法的实现必须位于实现该服务类的ServiceImpl类中。
- 所有ServiceImpl类必须使用@Service注解。
- 除非另有规定，ServiceImpl类中的所有依赖项必须使用@Autowired注解，且不使用构造函数。
- 除非绝对必要，ServiceImpl方法的返回对象应该是数据传输对象（DTO），而不是实体类。
- 对于任何需要检查记录是否存在的逻辑，应使用相应的仓储方法，并搭配适当的.orElseThrow lambda方法。
- 对于任何多个连续的数据库执行操作，必须根据情况使用@Transactional注解或transactionTemplate。 

## 视频相关服务

- [IVideoInfoService.java](mdc:src/main/java/cn/mlamp/insightflow/cms/service/IVideoInfoService.java) - 视频信息服务
- [IVideoResultService.java](mdc:src/main/java/cn/mlamp/insightflow/cms/service/IVideoResultService.java) - 视频结果服务
- [IVideoResultDetailService.java](mdc:src/main/java/cn/mlamp/insightflow/cms/service/IVideoResultDetailService.java) - 视频结果详情服务
- [IVideoAsrService.java](mdc:src/main/java/cn/mlamp/insightflow/cms/service/IVideoAsrService.java) - 视频语音识别服务
- [IVideoFiveGoldService.java](mdc:src/main/java/cn/mlamp/insightflow/cms/service/IVideoFiveGoldService.java) - 视频五秒金句服务

## 任务相关服务

- [ICmsTaskInfoService.java](mdc:src/main/java/cn/mlamp/insightflow/cms/service/ICmsTaskInfoService.java) - 任务信息服务
- [ICmsTaskDetailService.java](mdc:src/main/java/cn/mlamp/insightflow/cms/service/ICmsTaskDetailService.java) - 任务详情服务
- [CmsPullTaskService.java](mdc:src/main/java/cn/mlamp/insightflow/cms/service/CmsPullTaskService.java) - 拉取任务服务
- [CmsPullTaskRawDataService.java](mdc:src/main/java/cn/mlamp/insightflow/cms/service/CmsPullTaskRawDataService.java) - 拉取任务原始数据服务
- [CmsPullTaskDedupedDataService.java](mdc:src/main/java/cn/mlamp/insightflow/cms/service/CmsPullTaskDedupedDataService.java) - 拉取任务去重数据服务

## 文档相关服务

- [ICmsDocumentInfoService.java](mdc:src/main/java/cn/mlamp/insightflow/cms/service/ICmsDocumentInfoService.java) - 文档信息服务

## 账户与权限相关服务

- [IUserService.java](mdc:src/main/java/cn/mlamp/insightflow/cms/service/IUserService.java) - 用户服务
- [TenantTokenService.java](mdc:src/main/java/cn/mlamp/insightflow/cms/service/TenantTokenService.java) - 租户令牌服务
- [TenantTokenInitService.java](mdc:src/main/java/cn/mlamp/insightflow/cms/service/TenantTokenInitService.java) - 租户令牌初始化服务
- [TokenRechargeDetailService.java](mdc:src/main/java/cn/mlamp/insightflow/cms/service/TokenRechargeDetailService.java) - 令牌充值明细服务
- [TokenUseDetailService.java](mdc:src/main/java/cn/mlamp/insightflow/cms/service/TokenUseDetailService.java) - 令牌使用明细服务

## 验证相关服务

- [CaptchaService.java](mdc:src/main/java/cn/mlamp/insightflow/cms/service/CaptchaService.java) - 验证码服务
- [VerificationCodeService.java](mdc:src/main/java/cn/mlamp/insightflow/cms/service/VerificationCodeService.java) - 验证码服务
- [RegisterService.java](mdc:src/main/java/cn/mlamp/insightflow/cms/service/RegisterService.java) - 注册服务

## 文件相关服务

- [FileService.java](mdc:src/main/java/cn/mlamp/insightflow/cms/service/FileService.java) - 文件服务

## WebFlux服务

[service/webflux](mdc:src/main/java/cn/mlamp/insightflow/cms/service/webflux) 目录包含WebFlux相关服务实现。
