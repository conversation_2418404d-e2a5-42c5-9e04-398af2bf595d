---
description:
globs: src/main/java/cn/mlamp/insightflow/cms/mapper/**/*.java
alwaysApply: false
---
# 数据访问层

数据访问层负责与数据库交互，主要是MyBatis的Mapper接口，位于 [mapper](mdc:src/main/java/cn/mlamp/insightflow/cms/mapper) 目录。

## 视频相关Mapper

- [VideoInfoMapper.java](mdc:src/main/java/cn/mlamp/insightflow/cms/mapper/VideoInfoMapper.java) - 视频信息Mapper
- [VideorResultMapper.java](mdc:src/main/java/cn/mlamp/insightflow/cms/mapper/VideorResultMapper.java) - 视频结果Mapper
- [VideorResultDetailMapper.java](mdc:src/main/java/cn/mlamp/insightflow/cms/mapper/VideorResultDetailMapper.java) - 视频结果详情Mapper
- [VideoAsrMapper.java](mdc:src/main/java/cn/mlamp/insightflow/cms/mapper/VideoAsrMapper.java) - 视频语音识别Mapper
- [VideoFiveGoldMapper.java](mdc:src/main/java/cn/mlamp/insightflow/cms/mapper/VideoFiveGoldMapper.java) - 视频五秒金句Mapper
- [VideoThreeGoldRelationMapper.java](mdc:src/main/java/cn/mlamp/insightflow/cms/mapper/VideoThreeGoldRelationMapper.java) - 视频三金句关系Mapper

## 任务相关Mapper

- [CmsTaskInfoMapper.java](mdc:src/main/java/cn/mlamp/insightflow/cms/mapper/CmsTaskInfoMapper.java) - 任务信息Mapper
- [CmsTaskDetailMapper.java](mdc:src/main/java/cn/mlamp/insightflow/cms/mapper/CmsTaskDetailMapper.java) - 任务详情Mapper
- [CmsPullTaskMapper.java](mdc:src/main/java/cn/mlamp/insightflow/cms/mapper/CmsPullTaskMapper.java) - 拉取任务Mapper
- [CmsPullTaskRawDataMapper.java](mdc:src/main/java/cn/mlamp/insightflow/cms/mapper/CmsPullTaskRawDataMapper.java) - 拉取任务原始数据Mapper
- [CmsPullTaskDedupedDataMapper.java](mdc:src/main/java/cn/mlamp/insightflow/cms/mapper/CmsPullTaskDedupedDataMapper.java) - 拉取任务去重数据Mapper

## 文档相关Mapper

- [CmsDocumentInfoMapper.java](mdc:src/main/java/cn/mlamp/insightflow/cms/mapper/CmsDocumentInfoMapper.java) - 文档信息Mapper

## 千川相关Mapper

- [QianchuanMaterialVideoMapper.java](mdc:src/main/java/cn/mlamp/insightflow/cms/mapper/QianchuanMaterialVideoMapper.java) - 千川素材视频Mapper
- [QianchuanVideoHotspotMapper.java](mdc:src/main/java/cn/mlamp/insightflow/cms/mapper/QianchuanVideoHotspotMapper.java) - 千川视频热点Mapper

## 账户相关Mapper

- [UserMapper.java](mdc:src/main/java/cn/mlamp/insightflow/cms/mapper/UserMapper.java) - 用户Mapper
- [TenantTokenMapper.java](mdc:src/main/java/cn/mlamp/insightflow/cms/mapper/TenantTokenMapper.java) - 租户令牌Mapper
- [TokenRechargeDetailMapper.java](mdc:src/main/java/cn/mlamp/insightflow/cms/mapper/TokenRechargeDetailMapper.java) - 令牌充值明细Mapper
- [TokenUseDetailMapper.java](mdc:src/main/java/cn/mlamp/insightflow/cms/mapper/TokenUseDetailMapper.java) - 令牌使用明细Mapper

## 其他Mapper

- [CmsFeedbackMapper.java](mdc:src/main/java/cn/mlamp/insightflow/cms/mapper/CmsFeedbackMapper.java) - 反馈Mapper
- [CmsProductRecordMapper.java](mdc:src/main/java/cn/mlamp/insightflow/cms/mapper/CmsProductRecordMapper.java) - 产品记录Mapper

## MyBatis XML配置

MyBatis XML配置文件位于 [resources/mapper](mdc:src/main/resources/mapper) 目录。
