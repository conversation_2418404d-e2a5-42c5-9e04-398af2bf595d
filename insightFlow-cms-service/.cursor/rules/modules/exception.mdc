---
description:
globs: src/main/java/cn/mlamp/insightflow/cms/exception/**/*.java
alwaysApply: false
---
# 异常处理

异常处理相关类位于 [exception](mdc:src/main/java/cn/mlamp/insightflow/cms/exception) 目录，定义了系统中的异常处理机制。

## 主要异常类

- [BusinessException.java](mdc:src/main/java/cn/mlamp/insightflow/cms/exception/BusinessException.java) - 业务异常，用于处理业务逻辑相关的异常情况
- [StateCodeException.java](mdc:src/main/java/cn/mlamp/insightflow/cms/exception/StateCodeException.java) - 状态码异常，用于处理与状态码相关的异常情况

## 错误码

系统中的错误码定义在 [enums/ErrorCode.java](mdc:src/main/java/cn/mlamp/insightflow/cms/enums/ErrorCode.java) 文件中。
