---
description:
globs: src/main/java/cn/mlamp/insightflow/cms/config/**/*.java
alwaysApply: false
---
# 配置类

配置类负责系统的各种配置，位于 [config](mdc:src/main/java/cn/mlamp/insightflow/cms/config) 目录。

## 代码规范

Spring Boot 配置类代码规范：

1. 配置类必须使用 @ConfigurationProperties 注解绑定外部配置，并明确指定配置前缀，确保与配置文件中的属性对应。
示例：
    @Configuration
    @ConfigurationProperties(prefix = "app.config")

2. 配置属性类应为纯 POJO（Plain Old Java Object），所有属性应声明为 private，并提供 public 的 getter 和 setter 方法，
以便 Spring Boot 能够自动注入和绑定属性值。

3. 配置类应仅负责读取和绑定配置参数，不应包含业务逻辑，保持配置数据与业务处理的分离。

4. 如需对配置属性进行合法性校验，可结合 @Validated 注解与 JSR-303 校验注解（例如 @NotNull、@Min、@Max 等），
确保绑定的配置值满足预期条件。
示例：
    @ConfigurationProperties(prefix = "app.config")
    @Validated
    public class AppConfigProperties {
        @NotNull
        private String name;
        // getter 和 setter 方法
    }

5. 建议将所有配置属性类统一放置在专门的包中（如 cn.mlamp.insightflow.cms.config.properties），
便于管理和后续维护。

7. 编写配置类时请遵循 KISS、DRY、SOLID 等设计原则，保持代码的简洁性和可扩展性。

示例代码：

@ConfigurationProperties(prefix = "app.config")
@Validated
public class AppConfigProperties {

    @NotNull
    private String name;

    private int timeout = 30;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getTimeout() {
        return timeout;
    }

    public void setTimeout(int timeout) {
        this.timeout = timeout;
    }
}

请严格遵循以上配置类代码规范，确保项目中配置类均符合 Spring Boot 的最佳实践。


## 主要配置类

- [AsyncThreadConfig.java](mdc:src/main/java/cn/mlamp/insightflow/cms/config/AsyncThreadConfig.java) - 异步线程配置
- [WebConfig.java](mdc:src/main/java/cn/mlamp/insightflow/cms/config/WebConfig.java) - Web配置
- [DatabaseConfig.java](mdc:src/main/java/cn/mlamp/insightflow/cms/config/DatabaseConfig.java) - 数据库配置
- [MybatisPlusConfig.java](mdc:src/main/java/cn/mlamp/insightflow/cms/config/MybatisPlusConfig.java) - MybatisPlus配置
- [SwaggerConfig.java](mdc:src/main/java/cn/mlamp/insightflow/cms/config/SwaggerConfig.java) - Swagger配置
- [Es7Config.java](mdc:src/main/java/cn/mlamp/insightflow/cms/config/Es7Config.java) - Elasticsearch 7配置
- [S3FlowConfig.java](mdc:src/main/java/cn/mlamp/insightflow/cms/config/S3FlowConfig.java) - S3流配置
- [PgVectorStoreConfig.java](mdc:src/main/java/cn/mlamp/insightflow/cms/config/PgVectorStoreConfig.java) - PgVector存储配置

## 视频分析配置

- [AnalysisVideoConfig.java](mdc:src/main/java/cn/mlamp/insightflow/cms/config/AnalysisVideoConfig.java) - 视频分析配置
- [AnalysisServerProperties.java](mdc:src/main/java/cn/mlamp/insightflow/cms/config/AnalysisServerProperties.java) - 分析服务器属性

## 用户与认证配置

- [CaptchaConfiguration.java](mdc:src/main/java/cn/mlamp/insightflow/cms/config/CaptchaConfiguration.java) - 验证码配置
- [CaptchaProperties.java](mdc:src/main/java/cn/mlamp/insightflow/cms/config/CaptchaProperties.java) - 验证码属性
- [AdminConfig.java](mdc:src/main/java/cn/mlamp/insightflow/cms/config/AdminConfig.java) - 管理员配置
- [UserContext.java](mdc:src/main/java/cn/mlamp/insightflow/cms/config/UserContext.java) - 用户上下文
- [SignCheckProperties.java](mdc:src/main/java/cn/mlamp/insightflow/cms/config/SignCheckProperties.java) - 签名检查属性

## 租户配置

- [TenantTemplateConfig.java](mdc:src/main/java/cn/mlamp/insightflow/cms/config/TenantTemplateConfig.java) - 租户模板配置
- [TenantTemplateConfigProperties.java](mdc:src/main/java/cn/mlamp/insightflow/cms/config/TenantTemplateConfigProperties.java) - 租户模板配置属性

## 其他配置

- [GlobalFacadeAdvice.java](mdc:src/main/java/cn/mlamp/insightflow/cms/config/GlobalFacadeAdvice.java) - 全局门面通知
- [TaskConfig.java](mdc:src/main/java/cn/mlamp/insightflow/cms/config/TaskConfig.java) - 任务配置
- [TtcConfig.java](mdc:src/main/java/cn/mlamp/insightflow/cms/config/TtcConfig.java) - TTC配置
- [DeepanaConfig.java](mdc:src/main/java/cn/mlamp/insightflow/cms/config/DeepanaConfig.java) - Deepana配置

## 配置属性

配置属性类位于 [config/properties](mdc:src/main/java/cn/mlamp/insightflow/cms/config/properties) 目录。
