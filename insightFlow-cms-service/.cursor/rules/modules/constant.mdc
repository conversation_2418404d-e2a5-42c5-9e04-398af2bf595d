---
description:
globs: src/main/java/cn/mlamp/insightflow/cms/constant/**/*.java
alwaysApply: false
---
# 常量定义

常量定义相关类位于 [constant](mdc:src/main/java/cn/mlamp/insightflow/cms/constant) 目录，定义了系统中使用的各种常量。

## 主要常量类

- [CommonConstant.java](mdc:src/main/java/cn/mlamp/insightflow/cms/constant/CommonConstant.java) - 通用常量，定义了系统中通用的常量值
- [FileConstant.java](mdc:src/main/java/cn/mlamp/insightflow/cms/constant/FileConstant.java) - 文件相关常量，定义了与文件操作相关的常量值
- [RedisConstant.java](mdc:src/main/java/cn/mlamp/insightflow/cms/constant/RedisConstant.java) - Redis相关常量，定义了与Redis操作相关的常量值
- [Constants.java](mdc:src/main/java/cn/mlamp/insightflow/cms/constant/Constants.java) - 其他常量值

## 枚举类型

系统中的枚举类型定义在 [enums](mdc:src/main/java/cn/mlamp/insightflow/cms/enums) 目录下，包括各种状态、类型等的枚举定义。
