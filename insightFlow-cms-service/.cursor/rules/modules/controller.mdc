---
description:
globs: src/main/java/cn/mlamp/insightflow/cms/controller/**/*.java
alwaysApply: false
---
# 控制器层

控制器层负责处理HTTP请求，实现RESTful API接口。主要位于 [controller](mdc:src/main/java/cn/mlamp/insightflow/cms/controller) 目录。

## 代码规范

这部分内容是关于Java Spring Boot项目中RestController类的规范说明：
1. **控制器类注解**：控制器类必须使用@RestController注解，这表明该类是一个RESTful风格的控制器，用于处理HTTP请求并返回响应。
2. **类级API路由指定**：必须使用@RequestMapping注解来指定类级别的API路由，示例为("/api/user") ，即该控制器下的所有接口都会以此为基础路径。
3. **类方法HTTP方法注解**：类中的方法必须使用最佳实践的HTTP方法注解，例如创建操作可以使用@PostMapping("/create") ，根据不同的业务操作选择合适的HTTP方法注解。
4. **依赖注入方式**：类方法中的所有依赖必须使用@Autowired进行注入，并且不通过构造函数注入，除非有特别说明。
5. **方法返回对象类型**：方法返回的对象必须是[RespBody.java](mdc:src/main/java/cn/mlamp/insightflow/cms/common/resp/RespBody.java)类型的Response Entity，即返回的响应数据需要符合特定的ApiResponse格式。

## 视频相关控制器

- [VideoInfoController.java](mdc:src/main/java/cn/mlamp/insightflow/cms/controller/VideoInfoController.java) - 视频信息控制器
- [VideoResultController.java](mdc:src/main/java/cn/mlamp/insightflow/cms/controller/VideoResultController.java) - 视频结果控制器
- [VideoFiveGoldController.java](mdc:src/main/java/cn/mlamp/insightflow/cms/controller/VideoFiveGoldController.java) - 视频五秒金句控制器
- [VideoTaskController.java](mdc:src/main/java/cn/mlamp/insightflow/cms/controller/VideoTaskController.java) - 视频任务控制器
- [VideoHotspotController.java](mdc:src/main/java/cn/mlamp/insightflow/cms/controller/VideoHotspotController.java) - 视频热点控制器

## 用户相关控制器

- [User2Controller.java](mdc:src/main/java/cn/mlamp/insightflow/cms/controller/User2Controller.java) - 用户控制器
- [AdminController.java](mdc:src/main/java/cn/mlamp/insightflow/cms/controller/AdminController.java) - 管理员控制器
- [VerificationCodeController.java](mdc:src/main/java/cn/mlamp/insightflow/cms/controller/VerificationCodeController.java) - 验证码控制器
- [TenantTokenController.java](mdc:src/main/java/cn/mlamp/insightflow/cms/controller/TenantTokenController.java) - 租户令牌控制器

## 任务相关控制器

- [CmsPullTaskController.java](mdc:src/main/java/cn/mlamp/insightflow/cms/controller/CmsPullTaskController.java) - 拉取任务控制器
- [GoldFiveSecondTaskController.java](mdc:src/main/java/cn/mlamp/insightflow/cms/controller/GoldFiveSecondTaskController.java) - 五秒金句任务控制器
- [ProductRecordController.java](mdc:src/main/java/cn/mlamp/insightflow/cms/controller/ProductRecordController.java) - 产品记录控制器

## 千川相关控制器

- [QianchuanMaterialVideoController.java](mdc:src/main/java/cn/mlamp/insightflow/cms/controller/QianchuanMaterialVideoController.java) - 千川素材视频控制器
- [QianchuanVideoHotspotController.java](mdc:src/main/java/cn/mlamp/insightflow/cms/controller/QianchuanVideoHotspotController.java) - 千川视频热点控制器

## 文件相关控制器

- [FileController.java](mdc:src/main/java/cn/mlamp/insightflow/cms/controller/FileController.java) - 文件控制器

## 其他控制器

- [EmbeddingController.java](mdc:src/main/java/cn/mlamp/insightflow/cms/controller/EmbeddingController.java) - 嵌入控制器
- [FeedbackController.java](mdc:src/main/java/cn/mlamp/insightflow/cms/controller/FeedbackController.java) - 反馈控制器
- [RootController.java](mdc:src/main/java/cn/mlamp/insightflow/cms/controller/RootController.java) - 根控制器
