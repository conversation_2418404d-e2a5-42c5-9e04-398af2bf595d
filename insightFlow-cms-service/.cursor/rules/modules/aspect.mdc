---
description:
globs: src/main/java/cn/mlamp/insightflow/cms/aspect/**/*.java
alwaysApply: false
---
# 切面编程

切面编程相关类位于 [aspect](mdc:src/main/java/cn/mlamp/insightflow/cms/aspect) 目录，实现了系统中的横切关注点。

## 主要切面

- [SignCheckAspect.java](mdc:src/main/java/cn/mlamp/insightflow/cms/aspect/SignCheckAspect.java) - 签名校验切面，用于对接口进行签名验证

## 切面注解

切面注解位于 [annotation](mdc:src/main/java/cn/mlamp/insightflow/cms/annotation) 目录，包括：

- [SignCheck.java](mdc:src/main/java/cn/mlamp/insightflow/cms/annotation/SignCheck.java) - 签名校验注解，标记需要进行签名校验的方法
