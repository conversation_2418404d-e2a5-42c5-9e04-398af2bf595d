---
description:
globs: src/test/java/cn/mlamp/insightflow/cms/**/*.java
alwaysApply: false
---
SpringBoot相关测试用例# 测试规范与最佳实践

## 测试架构

InsightFlow-CMS-Service项目的测试架构基于SpringBoot测试框架，主要包含以下几个关键组件：

- [BaseSpringBootTest.java](mdc:src/test/java/cn/mlamp/insightflow/cms/BaseSpringBootTest.java) - 所有SpringBoot测试的基类，提供了用户上下文模拟等通用功能
- [UserContextTest.java](mdc:src/test/java/cn/mlamp/insightflow/cms/UserContextTest.java) - 用户上下文模拟的示例测试类

## 测试代码规范

### 通用规范

1. 测试类命名必须以`Test`结尾
2. 测试方法命名必须以`test`开头
3. 每个测试方法应只测试一个功能点
4. 使用断言验证测试结果，避免空测试
5. 为每个测试用例添加清晰的注释，说明测试目的
6. 避免测试之间的依赖，每个测试应该能够独立运行

### 类继承结构

- 所有SpringBoot相关测试类必须继承 `BaseSpringBootTest.java`
- 控制器测试需添加 `@AutoConfigureMockMvc` 注解以支持模拟MVC请求

```java
@SpringBootTest
@AutoConfigureMockMvc
public class YourControllerTest extends BaseSpringBootTest {
    // 测试代码
}
```

## Mock标准

### 用户上下文模拟

所有需要用户上下文的测试必须使用 `BaseSpringBootTest` 提供的 `mockCurrentUser` 方法：

```java
// 模拟用户
mockCurrentUser(userId, userName, tenantId);

// 之后可以正常使用UserContext
Integer currentUserId = UserContext.getUserId();
```

详细使用流程请参考 [UserContextTest.java](mdc:src/test/java/cn/mlamp/insightflow/cms/UserContextTest.java)。

### 外部服务模拟

使用 `@MockBean` 注解模拟外部服务依赖：

```java
@MockBean
private ExternalService externalService;

@BeforeEach
public void setup() {
    // 设置模拟行为
    when(externalService.someMethod(any())).thenReturn(expectedResult);
}
```

### 静态方法模拟

使用 Mockito 的 `mockStatic` 方法模拟静态方法：

```java
private MockedStatic<UtilityClass> mockedUtility;

@BeforeEach
public void setup() {
    mockedUtility = Mockito.mockStatic(UtilityClass.class);
    mockedUtility.when(UtilityClass::staticMethod).thenReturn(expectedValue);
}

@AfterEach
public void tearDown() {
    if (mockedUtility != null) {
        mockedUtility.close();
    }
}
```

## 接口测试标准

所有涉及接口调用的测试必须使用 `MockMvc` 进行测试：

```java
@Autowired
private MockMvc mockMvc;

@Test
public void testEndpoint() throws Exception {
    // 执行GET请求
    mockMvc.perform(MockMvcRequestBuilders.get("/api/resource")
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(MockMvcResultMatchers.status().isOk())
            .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0));
            
    // 执行POST请求
    YourRequestDto request = new YourRequestDto();
    // 设置请求参数...
    
    mockMvc.perform(MockMvcRequestBuilders.post("/api/resource")
            .contentType(MediaType.APPLICATION_JSON)
            .content(objectMapper.writeValueAsString(request)))
            .andExpect(MockMvcResultMatchers.status().isOk())
            .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0));
}
```

参考案例: [DamTagControllerTest.java](mdc:src/test/java/cn/mlamp/insightflow/cms/controller/dam/DamTagControllerTest.java)

## 最佳实践

### 数据准备

1. 使用 `@BeforeEach` 为每个测试准备数据
2. 使用工厂方法创建测试数据，避免重复代码
3. 避免在测试中使用硬编码的数据值，而应使用常量或配置

```java
@TestConfiguration
public class TestConfig {
    @Bean
    public TestDataFactory testDataFactory() {
        return new TestDataFactory();
    }
}

// 在测试类中
@Autowired
private TestDataFactory testDataFactory;

@Test
public void testSomething() {
    // 使用工厂创建测试数据
    Entity entity = testDataFactory.createEntity();
    // 测试...
}
```

### 异常测试

测试异常场景时，使用 `assertThrows` 方法：

```java
@Test
public void testExceptionCase() {
    Exception exception = assertThrows(ExpectedException.class, () -> {
        // 调用应该抛出异常的方法
        service.methodThatShouldThrowException();
    });
    
    // 验证异常消息
    assertTrue(exception.getMessage().contains("expected error message"));
}
```

### 参数化测试

对于需要测试多种输入情况的方法，使用参数化测试：

```java
@ParameterizedTest
@ValueSource(strings = {"value1", "value2", "value3"})
public void testWithMultipleValues(String input) {
    // 使用不同的输入值进行测试
    assertNotNull(service.processInput(input));
}

@ParameterizedTest
@CsvSource({
    "input1, expected1",
    "input2, expected2"
})
public void testWithInputAndExpectedOutput(String input, String expected) {
    // 使用输入值测试并验证预期输出
    assertEquals(expected, service.processInput(input));
}
```

### 数据库测试

对于需要测试数据库操作的场景，推荐使用以下方式：

1. 使用H2内存数据库进行测试
2. 使用 `@Sql` 注解加载测试数据
3. 使用事务回滚确保测试数据不污染环境

```java
@SpringBootTest
@Transactional
public class DatabaseTest extends BaseSpringBootTest {

    @Test
    @Sql("/sql/test-data.sql")
    public void testDatabaseOperation() {
        // 测试代码...
    }
}
```

### 覆盖率要求

- 单元测试覆盖率目标：80%以上
- 接口测试覆盖率目标：90%以上
- 核心业务逻辑测试覆盖率目标：95%以上
