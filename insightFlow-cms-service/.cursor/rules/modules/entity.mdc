---
description:
globs: src/main/java/cn/mlamp/insightflow/cms/entity/**/*.java
alwaysApply: false
---
# 实体模型

实体类定义了系统的核心数据模型，主要位于 [entity](mdc:src/main/java/cn/mlamp/insightflow/cms/entity) 目录下。

## 核心实体

- [BaseEntity.java](mdc:src/main/java/cn/mlamp/insightflow/cms/entity/BaseEntity.java) - 所有实体的基类
- [User.java](mdc:src/main/java/cn/mlamp/insightflow/cms/entity/User.java) - 用户信息
- [CmsUser.java](mdc:src/main/java/cn/mlamp/insightflow/cms/entity/CmsUser.java) - CMS系统用户

## 视频相关实体

- [CmsVideoInfo.java](mdc:src/main/java/cn/mlamp/insightflow/cms/entity/CmsVideoInfo.java) - 视频基础信息
- [CmsVideoResult.java](mdc:src/main/java/cn/mlamp/insightflow/cms/entity/CmsVideoResult.java) - 视频分析结果
- [CmsVideoResultDetail.java](mdc:src/main/java/cn/mlamp/insightflow/cms/entity/CmsVideoResultDetail.java) - 视频分析结果详情
- [CmsVideoAsr.java](mdc:src/main/java/cn/mlamp/insightflow/cms/entity/CmsVideoAsr.java) - 视频语音转文字结果
- [CmsVideoFiveGold.java](mdc:src/main/java/cn/mlamp/insightflow/cms/entity/CmsVideoFiveGold.java) - 视频五秒金句

## 任务相关实体

- [CmsTaskInfo.java](mdc:src/main/java/cn/mlamp/insightflow/cms/entity/CmsTaskInfo.java) - 任务信息
- [CmsTaskDetail.java](mdc:src/main/java/cn/mlamp/insightflow/cms/entity/CmsTaskDetail.java) - 任务详情
- [CmsPullTask.java](mdc:src/main/java/cn/mlamp/insightflow/cms/entity/CmsPullTask.java) - 数据拉取任务
- [CmsPullTaskRawData.java](mdc:src/main/java/cn/mlamp/insightflow/cms/entity/CmsPullTaskRawData.java) - 拉取任务原始数据
- [CmsPullTaskDedupedData.java](mdc:src/main/java/cn/mlamp/insightflow/cms/entity/CmsPullTaskDedupedData.java) - 拉取任务去重数据

## 文档相关实体

- [CmsDocumentInfo.java](mdc:src/main/java/cn/mlamp/insightflow/cms/entity/CmsDocumentInfo.java) - 文档信息

## 账户与权限相关实体

- [TenantToken.java](mdc:src/main/java/cn/mlamp/insightflow/cms/entity/TenantToken.java) - 租户令牌
- [TokenRechargeDetail.java](mdc:src/main/java/cn/mlamp/insightflow/cms/entity/TokenRechargeDetail.java) - 令牌充值明细
- [TokenUseDetail.java](mdc:src/main/java/cn/mlamp/insightflow/cms/entity/TokenUseDetail.java) - 令牌使用明细
