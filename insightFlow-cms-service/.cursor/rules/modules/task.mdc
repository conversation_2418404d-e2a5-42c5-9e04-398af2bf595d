---
description:
globs: src/main/java/cn/mlamp/insightflow/cms/task/**/*.java
alwaysApply: false
---
# 任务处理

任务处理类负责系统中的异步任务执行，位于 [task](mdc:src/main/java/cn/mlamp/insightflow/cms/task) 目录。

## 视频分析任务

- [VideoAnalysisTask.java](mdc:src/main/java/cn/mlamp/insightflow/cms/task/VideoAnalysisTask.java) - 视频分析任务
- [VideoAnalysis2Task.java](mdc:src/main/java/cn/mlamp/insightflow/cms/task/VideoAnalysis2Task.java) - 视频分析任务（版本2）
- [QianchuanVideoAnalysisTask.java](mdc:src/main/java/cn/mlamp/insightflow/cms/task/QianchuanVideoAnalysisTask.java) - 千川视频分析任务
- [UserVideoAnalysisQueryTask.java](mdc:src/main/java/cn/mlamp/insightflow/cms/task/UserVideoAnalysisQueryTask.java) - 用户视频分析查询任务

## 视频下载任务

- [VideoDownloadTask.java](mdc:src/main/java/cn/mlamp/insightflow/cms/task/VideoDownloadTask.java) - 视频下载任务
- [UrlVideoDownloadCheckTask.java](mdc:src/main/java/cn/mlamp/insightflow/cms/task/UrlVideoDownloadCheckTask.java) - URL视频下载检查任务

## 金句任务

- [GoldFiveSecondTask.java](mdc:src/main/java/cn/mlamp/insightflow/cms/task/GoldFiveSecondTask.java) - 五秒金句任务
