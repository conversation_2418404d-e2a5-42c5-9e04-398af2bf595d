---
description:
globs: src/main/java/cn/mlamp/insightflow/cms/util/**/*.java
alwaysApply: false
---
# 工具类

工具类提供了系统中通用的功能，位于 [util](mdc:src/main/java/cn/mlamp/insightflow/cms/util) 目录。

## 文件相关工具

- [FilePathBuilder.java](mdc:src/main/java/cn/mlamp/insightflow/cms/util/FilePathBuilder.java) - 文件路径构建器
- [FileDownloadUtil.java](mdc:src/main/java/cn/mlamp/insightflow/cms/util/FileDownloadUtil.java) - 文件下载工具
- [PathUtil.java](mdc:src/main/java/cn/mlamp/insightflow/cms/util/PathUtil.java) - 路径工具

## 视频处理工具

- [VideoUtil.java](mdc:src/main/java/cn/mlamp/insightflow/cms/util/VideoUtil.java) - 视频处理工具

## HTTP请求工具

- [HttpUtil.java](mdc:src/main/java/cn/mlamp/insightflow/cms/util/HttpUtil.java) - HTTP请求工具
- [IpUtil.java](mdc:src/main/java/cn/mlamp/insightflow/cms/util/IpUtil.java) - IP地址工具

## 数据处理工具

- [JsonUtil.java](mdc:src/main/java/cn/mlamp/insightflow/cms/util/JsonUtil.java) - JSON处理工具
- [ExcelUtil.java](mdc:src/main/java/cn/mlamp/insightflow/cms/util/ExcelUtil.java) - Excel处理工具
- [ObjReflectUtil.java](mdc:src/main/java/cn/mlamp/insightflow/cms/util/ObjReflectUtil.java) - 对象反射工具
- [PageUtils.java](mdc:src/main/java/cn/mlamp/insightflow/cms/util/PageUtils.java) - 分页工具

## 日期时间工具

- [DateUtil.java](mdc:src/main/java/cn/mlamp/insightflow/cms/util/DateUtil.java) - 日期时间工具

## 验证与安全工具

- [SignUtils.java](mdc:src/main/java/cn/mlamp/insightflow/cms/util/SignUtils.java) - 签名工具
- [DeepanaSignUtil.java](mdc:src/main/java/cn/mlamp/insightflow/cms/util/DeepanaSignUtil.java) - Deepana签名工具
- [ValidatorUtil.java](mdc:src/main/java/cn/mlamp/insightflow/cms/util/ValidatorUtil.java) - 验证工具

## 性能监控工具

- [StopWatchUtil.java](mdc:src/main/java/cn/mlamp/insightflow/cms/util/StopWatchUtil.java) - 性能计时工具
- [ObservationIdUtil.java](mdc:src/main/java/cn/mlamp/insightflow/cms/util/ObservationIdUtil.java) - 观察ID工具

## Dify工具

Dify相关工具位于 [util/dify](mdc:src/main/java/cn/mlamp/insightflow/cms/util/dify) 目录。

## Excel处理工具

Excel处理相关工具位于 [util/excel](mdc:src/main/java/cn/mlamp/insightflow/cms/util/excel) 目录。
