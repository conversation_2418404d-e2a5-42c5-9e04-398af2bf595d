---
description:
globs: src/main/java/cn/mlamp/insightflow/cms/validation/**/*.java
alwaysApply: false
---
# 数据验证

数据验证相关类位于 [validation](mdc:src/main/java/cn/mlamp/insightflow/cms/validation) 目录，实现了系统中的数据校验机制。

## 验证约束

- [ObjectPropertiesMustBeConsistentConstraint.java](mdc:src/main/java/cn/mlamp/insightflow/cms/validation/ObjectPropertiesMustBeConsistentConstraint.java) - 对象属性一致性约束注解
- [VerificationConstraint.java](mdc:src/main/java/cn/mlamp/insightflow/cms/validation/VerificationConstraint.java) - 验证约束注解

## 验证器

- [ObjectPropertiesMustBeConsistentValidator.java](mdc:src/main/java/cn/mlamp/insightflow/cms/validation/ObjectPropertiesMustBeConsistentValidator.java) - 对象属性一致性校验器
- [VerificationValidator.java](mdc:src/main/java/cn/mlamp/insightflow/cms/validation/VerificationValidator.java) - 验证校验器
