---
description:
globs: src/main/java/cn/mlamp/insightflow/cms/auth/**/*.java
alwaysApply: false
---
# 认证与授权

认证与授权相关类位于 [auth](mdc:src/main/java/cn/mlamp/insightflow/cms/auth) 目录，负责系统的用户认证与权限管理。

## CMS认证

CMS认证相关类位于 [auth/cms](mdc:src/main/java/cn/mlamp/insightflow/cms/auth/cms) 目录，包括：

- [auth/cms/bo](mdc:src/main/java/cn/mlamp/insightflow/cms/auth/cms/bo) - CMS认证业务对象
- [auth/cms/dto](mdc:src/main/java/cn/mlamp/insightflow/cms/auth/cms/dto) - CMS认证数据传输对象
- [auth/cms/service](mdc:src/main/java/cn/mlamp/insightflow/cms/auth/cms/service) - CMS认证服务

## TCC认证

TCC认证相关类位于 [auth/tcc](mdc:src/main/java/cn/mlamp/insightflow/cms/auth/tcc) 目录，包括：

- [auth/tcc/manage](mdc:src/main/java/cn/mlamp/insightflow/cms/auth/tcc/manage) - TCC认证管理
- [auth/tcc/model](mdc:src/main/java/cn/mlamp/insightflow/cms/auth/tcc/model) - TCC认证模型

请求中如果需要获取当前登录用户、租户信息:

```java

import cn.mlamp.insightflow.cms.config.UserContext;

public void xxxxx() {
    var userId = UserContext.getUserId();
}

```