---
description:
globs: src/main/java/cn/mlamp/insightflow/cms/enums/**/*.java
alwaysApply: true
---
# 枚举类型

枚举类型定义了系统中使用的各种状态、类型等常量值，位于 [enums](mdc:src/main/java/cn/mlamp/insightflow/cms/enums) 目录。

## 错误与状态码

- [ErrorCode.java](mdc:src/main/java/cn/mlamp/insightflow/cms/enums/ErrorCode.java) - 错误码，定义了系统中的错误类型和错误码
- [ResultCode.java](mdc:src/main/java/cn/mlamp/insightflow/cms/enums/ResultCode.java) - 结果码，定义了系统中的结果状态码

## 视频相关枚举

- [AnalysisStatusEnum.java](mdc:src/main/java/cn/mlamp/insightflow/cms/enums/AnalysisStatusEnum.java) - 分析状态枚举
- [AnalysisVideoTypeEnum.java](mdc:src/main/java/cn/mlamp/insightflow/cms/enums/AnalysisVideoTypeEnum.java) - 分析视频类型枚举
- [VideoResultTypeEnum.java](mdc:src/main/java/cn/mlamp/insightflow/cms/enums/VideoResultTypeEnum.java) - 视频结果类型枚举
- [VideoInfoStatusEnum.java](mdc:src/main/java/cn/mlamp/insightflow/cms/enums/VideoInfoStatusEnum.java) - 视频信息状态枚举
- [VideoTypeEnum.java](mdc:src/main/java/cn/mlamp/insightflow/cms/enums/VideoTypeEnum.java) - 视频类型枚举

## 任务相关枚举

- [TaskTypeEnum.java](mdc:src/main/java/cn/mlamp/insightflow/cms/enums/TaskTypeEnum.java) - 任务类型枚举
- [TaskDetailTypeEnum.java](mdc:src/main/java/cn/mlamp/insightflow/cms/enums/TaskDetailTypeEnum.java) - 任务详情类型枚举
- [TaskDetailDataTypeEnum.java](mdc:src/main/java/cn/mlamp/insightflow/cms/enums/TaskDetailDataTypeEnum.java) - 任务详情数据类型枚举
- [VideoTaskStatusEnum.java](mdc:src/main/java/cn/mlamp/insightflow/cms/enums/VideoTaskStatusEnum.java) - 视频任务状态枚举
- [VideoTaskSourceTypeEnum.java](mdc:src/main/java/cn/mlamp/insightflow/cms/enums/VideoTaskSourceTypeEnum.java) - 视频任务来源类型枚举

## 文档相关枚举

- [DocumentStatusEnum.java](mdc:src/main/java/cn/mlamp/insightflow/cms/enums/DocumentStatusEnum.java) - 文档状态枚举
- [DownloadStatusEnum.java](mdc:src/main/java/cn/mlamp/insightflow/cms/enums/DownloadStatusEnum.java) - 下载状态枚举

## 其他枚举

- [IndustryEnum.java](mdc:src/main/java/cn/mlamp/insightflow/cms/enums/IndustryEnum.java) - 行业枚举
- [TtcBizCode.java](mdc:src/main/java/cn/mlamp/insightflow/cms/enums/TtcBizCode.java) - TTC业务码
- [VerificationTypeEnum.java](mdc:src/main/java/cn/mlamp/insightflow/cms/enums/VerificationTypeEnum.java) - 验证类型枚举
- [FeedbackSourceTypeEnum.java](mdc:src/main/java/cn/mlamp/insightflow/cms/enums/FeedbackSourceTypeEnum.java) - 反馈来源类型枚举
- [RequestDeviceSourceEnum.java](mdc:src/main/java/cn/mlamp/insightflow/cms/enums/RequestDeviceSourceEnum.java) - 请求设备来源枚举
- [TokenTaskTypeEnum.java](mdc:src/main/java/cn/mlamp/insightflow/cms/enums/TokenTaskTypeEnum.java) - 令牌任务类型枚举
- [UploadSourceTypeEnum.java](mdc:src/main/java/cn/mlamp/insightflow/cms/enums/UploadSourceTypeEnum.java) - 上传来源类型枚举
- [SourceTypeEnum.java](mdc:src/main/java/cn/mlamp/insightflow/cms/enums/SourceTypeEnum.java) - 来源类型枚举
