---
description:
globs: src/main/java/cn/mlamp/insightflow/cms/model/**/*.java
alwaysApply: false
---
# 模型层

模型层定义了系统的数据传输对象（DTO）、视图对象（VO）和查询对象（Query）。主要位于 [model](mdc:src/main/java/cn/mlamp/insightflow/cms/model) 目录。

DTO、VO、Entity之间的转换使用mapstruct库进行, 存放于src/main/java/cn/mlamp/insightflow/cms/model/converter中，具体识业务情况存放在指定的子目录

实现示例见[DamRecycleBinConverter.java](mdc:src/main/java/cn/mlamp/insightflow/cms/model/converter/dam/DamRecycleBinConverter.java)文件

```java
// 使用示例
@Autowired
private DamRecycleBinConverter recycleBinConverter;

private void xxxx(RecycleBin recycleBin) {
    final RecycleBinVO vo = recycleBinConverter.toVO(recycleBin);
}
```

## 数据传输对象（DTO）

数据传输对象位于 [model/dto](mdc:src/main/java/cn/mlamp/insightflow/cms/model/dto) 目录，主要用于接口间数据传输。

### Dify相关DTO

- [DifyAiImitateRequestDTO.java](mdc:src/main/java/cn/mlamp/insightflow/cms/model/dto/DifyAiImitateRequestDTO.java) - Dify AI模仿请求
- [DifyGoldFiveSecondRequestDTO.java](mdc:src/main/java/cn/mlamp/insightflow/cms/model/dto/DifyGoldFiveSecondRequestDTO.java) - Dify五秒金句请求
- [DifyScriptGenRequestDTO.java](mdc:src/main/java/cn/mlamp/insightflow/cms/model/dto/DifyScriptGenRequestDTO.java) - Dify脚本生成请求
- [DifyScriptGenResponseDTO.java](mdc:src/main/java/cn/mlamp/insightflow/cms/model/dto/DifyScriptGenResponseDTO.java) - Dify脚本生成响应
- [DifyStoryboardModifyRequestDTO.java](mdc:src/main/java/cn/mlamp/insightflow/cms/model/dto/DifyStoryboardModifyRequestDTO.java) - Dify分镜修改请求
- [DifyStoryboardRecRequestDTO.java](mdc:src/main/java/cn/mlamp/insightflow/cms/model/dto/DifyStoryboardRecRequestDTO.java) - Dify分镜推荐请求
- [DifyProductSummaryResponseDTO.java](mdc:src/main/java/cn/mlamp/insightflow/cms/model/dto/DifyProductSummaryResponseDTO.java) - Dify产品摘要响应

### 视频相关DTO

- [QianchuanMaterialVideoDTO.java](mdc:src/main/java/cn/mlamp/insightflow/cms/model/dto/QianchuanMaterialVideoDTO.java) - 千川素材视频
- [QianchuanVideoHotspotQueryDTO.java](mdc:src/main/java/cn/mlamp/insightflow/cms/model/dto/QianchuanVideoHotspotQueryDTO.java) - 千川视频热点查询
- [DeepanaTikTokVideoDTO.java](mdc:src/main/java/cn/mlamp/insightflow/cms/model/dto/DeepanaTikTokVideoDTO.java) - Deepana TikTok视频
- [DeepanaTiktokRequest.java](mdc:src/main/java/cn/mlamp/insightflow/cms/model/dto/DeepanaTiktokRequest.java) - Deepana TikTok请求
- [DeepanaDySkuResponseDTO.java](mdc:src/main/java/cn/mlamp/insightflow/cms/model/dto/DeepanaDySkuResponseDTO.java) - Deepana抖音商品响应

### 其他DTO

- [DocumentExtraInfoDTO.java](mdc:src/main/java/cn/mlamp/insightflow/cms/model/dto/DocumentExtraInfoDTO.java) - 文档额外信息
- [OpenUrlDTO.java](mdc:src/main/java/cn/mlamp/insightflow/cms/model/dto/OpenUrlDTO.java) - 打开URL
- [UserInfoDTO.java](mdc:src/main/java/cn/mlamp/insightflow/cms/model/dto/UserInfoDTO.java) - 用户信息
- [ConsumptionExportDTO.java](mdc:src/main/java/cn/mlamp/insightflow/cms/model/dto/ConsumptionExportDTO.java) - 消费导出

## 视图对象（VO）

视图对象位于 [model/vo](mdc:src/main/java/cn/mlamp/insightflow/cms/model/vo) 目录，主要用于前端展示。

## 查询对象（Query）

查询对象位于 [model/query](mdc:src/main/java/cn/mlamp/insightflow/cms/model/query) 目录，主要用于构建查询条件。
