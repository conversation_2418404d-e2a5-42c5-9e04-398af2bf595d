---
description: v0.1 SQL DDL
globs:
alwaysApply: false
---
CREATE TABLE cms_pull_task_raw_datas(
    `id` INT AUTO_INCREMENT COMMENT '' ,
    `task_id` INT NOT NULL  COMMENT '' ,
    `es_id` VARCHAR(255) NOT NULL  COMMENT '唯一 ID（外部系统唯一标识）' ,
    `kw_kb_industry` VARCHAR(500)   COMMENT '行业分类' ,
    `text_content` TEXT   COMMENT '内容' ,
    `text_title` VARCHAR(500)   COMMENT '标题' ,
    `kw_url` VARCHAR(2000)   COMMENT '帖子链接' ,
    `date_published_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '发布时间' ,
    `bool_is_deleted` BIT(1)  DEFAULT 0 COMMENT '是否删除' ,
    `long_repost_count` BIGINT  DEFAULT 0 COMMENT '转发数' ,
    `long_comment_count` BIGINT  DEFAULT 0 COMMENT '评论数' ,
    `long_interact_count` BIGINT  DEFAULT 0 COMMENT '互动数' ,
    `long_like_count` BIGINT  DEFAULT 0 COMMENT '点赞数' ,
    `text_nick_name` VARCHAR(255)   COMMENT '用户昵称' ,
    `kw_profile_image_url` VARCHAR(2000)   COMMENT '用户头像链接' ,
    `kw_user_url` VARCHAR(2000)   COMMENT '用户主页链接' ,
    `long_video_duration` BIGINT  DEFAULT 0 COMMENT '视频时长' ,
    `kw_head_image` VARCHAR(2000)   COMMENT '视频头图' ,
    `kw_video_url` VARCHAR(2000)   COMMENT '视频链接' ,
    `kw_video_content` TEXT   COMMENT '视频语音识别' ,
    `kw_common_sentiment_plus` TEXT   COMMENT '通用情感 Plus 版' ,
    `kw_data_tag_plus` TEXT   COMMENT '数据标签 Plus 版' ,
    `long_view_count` BIGINT  DEFAULT 0 COMMENT '阅读数' ,
    `long_follower_count` BIGINT  DEFAULT 0 COMMENT '发帖用户粉丝数' ,
    `long_collect_count` BIGINT  DEFAULT 0 COMMENT '收藏数' ,
    `raw_data` JSON   COMMENT '原始数据（存 JSON 格式）' ,
    `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
    `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
    `is_deleted` INT  DEFAULT 0 COMMENT '逻辑删除标志（0-未删除，1-已删除）' ,
    PRIMARY KEY (id)
)  COMMENT = '拉取原始数据表';



CREATE TABLE cms_pull_tasks(
    `id` INT AUTO_INCREMENT COMMENT '主键 ID，自增' ,
    `start_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '任务开始时间' ,
    `end_time` TIMESTAMP NOT NULL DEFAULT 0000-00-00 00:00:00 COMMENT '任务结束时间，可为空' ,
    `status` VARCHAR(200) NOT NULL DEFAULT 'pending' COMMENT '状态（pending, running, completed, failed）' ,
    `fail_message` TEXT   COMMENT '失败信息' ,
    `pull_params` TEXT NOT NULL  COMMENT '拉取参数，存储 JSON 或其他格式的参数' ,
    `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
    `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
    `is_deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '逻辑删除标志（0: 未删除, 1: 已删除）' ,
    PRIMARY KEY (id)
)  COMMENT = '拉取任务表';


CREATE TABLE cms_pull_task_deduped_datas(
    `id` INT AUTO_INCREMENT COMMENT '' ,
    `task_id` INT NOT NULL  COMMENT '' ,
    `type` VARCHAR(255)   COMMENT '1：精选，2：圈层，3：自定义' ,
    `kw_source` VARCHAR(255)   COMMENT '平台类型 1：抖音，2：小红书' ,
    `es_id` VARCHAR(255) NOT NULL  COMMENT '唯一ID' ,
    `kw_kb_industry` VARCHAR(500)   COMMENT '行业分类' ,
    `text_content` TEXT   COMMENT '内容' ,
    `text_title` VARCHAR(500)   COMMENT '标题' ,
    `kw_url` VARCHAR(2000)   COMMENT '帖子链接' ,
    `date_published_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '发布时间' ,
    `bool_is_deleted` BIT(1)  DEFAULT 0 COMMENT '是否删除' ,
    `long_repost_count` BIGINT  DEFAULT 0 COMMENT '转发数' ,
    `long_comment_count` BIGINT  DEFAULT 0 COMMENT '评论数' ,
    `long_interact_count` BIGINT  DEFAULT 0 COMMENT '互动数' ,
    `long_like_count` BIGINT  DEFAULT 0 COMMENT '点赞数' ,
    `text_nick_name` VARCHAR(255)   COMMENT '用户昵称' ,
    `kw_profile_image_url` VARCHAR(2000)   COMMENT '用户头像链接' ,
    `kw_user_url` VARCHAR(2000)   COMMENT '用户主页链接' ,
    `long_video_duration` BIGINT  DEFAULT 0 COMMENT '视频时长' ,
    `kw_head_image` VARCHAR(2000)   COMMENT '视频头图' ,
    `kw_video_url` VARCHAR(2000)   COMMENT '视频链接' ,
    `kw_video_content` TEXT   COMMENT '视频语音识别' ,
    `kw_common_sentiment_plus` TEXT   COMMENT '通用情感 Plus 版' ,
    `kw_data_tag_plus` TEXT   COMMENT '数据标签 Plus 版' ,
    `long_view_count` BIGINT  DEFAULT 0 COMMENT '阅读数' ,
    `long_follower_count` BIGINT  DEFAULT 0 COMMENT '发帖用户粉丝数' ,
    `long_collect_count` BIGINT  DEFAULT 0 COMMENT '收藏数' ,
    `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
    `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
    `is_deleted` INT  DEFAULT 0 COMMENT '逻辑删除标志（0-未删除，1-已删除）' ,
    `source_type` INT NOT NULL  COMMENT '1:es;2:上传;3:链接' ,
    `user_id` INT   COMMENT '用户id' ,
    `tenant_id` INT   COMMENT '租户id' ,
    PRIMARY KEY (id)
)  COMMENT = 'ES拉取处理后数据表';

CREATE TABLE cms_video_asr(
    `id` INT AUTO_INCREMENT COMMENT '主键 自增id' ,
    `es_id` INT   COMMENT '帖子Id' ,
    `video_id` VARCHAR(255)   COMMENT '视频id' ,
    `start` INT(255)   COMMENT '开始时间(毫秒)' ,
    `end` INT(255)   COMMENT '结束时间(毫秒)' ,
    `text` VARCHAR(255)   COMMENT '台词' ,
    `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
    `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
    `is_deleted` BIT(1) NOT NULL DEFAULT 0 COMMENT '数据逻辑删除标记' ,
    PRIMARY KEY (id)
)  COMMENT = '视频ASR表';

CREATE TABLE cms_video_info(
    `id` INT AUTO_INCREMENT COMMENT '主键 自增id' ,
    `type` INT   COMMENT '1:视频分析;2：黄金5秒;3:圈层热点；4：上传视频' ,
    `es_id` VARCHAR(255)   COMMENT '帖子ID' ,
    `five_gold_id` VARCHAR(255)   COMMENT '黄金5秒表id' ,
    `source_file_id` INT NOT NULL  COMMENT '视频源文件Id' ,
    `status` VARCHAR(255) NOT NULL  COMMENT '1：待处理；2：处理中，3：完成，4：失败' ,
    `rating` INT   COMMENT '四有三好打分' ,
    `user_id` INT   COMMENT '用户id' ,
    `tenant_id` INT   COMMENT '租户id' ,
    `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
    `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
    `is_deleted` BIT(1) NOT NULL DEFAULT 0 COMMENT '数据逻辑删除标记' ,
    PRIMARY KEY (id)
)  COMMENT = '视频分析信息表';

CREATE TABLE cms_video_result(
    `id` INT AUTO_INCREMENT COMMENT '主键 自增id' ,
    `type` INT   COMMENT '1：AI解码；2：分镜；3：ASR' ,
    `video_id` INT   COMMENT '分析任务ID' ,
    `index` INT   COMMENT '排序序号' ,
    `data` VARCHAR(255)   COMMENT 'json格式' ,
    `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
    `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
    `is_deleted` BIT(1) NOT NULL DEFAULT 0 COMMENT '数据逻辑删除标记' ,
    PRIMARY KEY (id)
)  COMMENT = '视频分析结果';

CREATE TABLE cms_video_result_detail(
    `id` INT AUTO_INCREMENT COMMENT '主键 自增id' ,
    `type` INT   COMMENT '1：分镜详情' ,
    `video_id` INT   COMMENT '视频信息id' ,
    `video_result_id` INT   COMMENT '视频分析结果id' ,
    `data` VARCHAR(255)   COMMENT 'json格式' ,
    `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
    `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
    `is_deleted` BIT(1) NOT NULL DEFAULT 0 COMMENT '数据逻辑删除标记' ,
    PRIMARY KEY (id)
)  COMMENT = '视频分析结果详情表';


CREATE TABLE cms_document_info(
    `id` INT AUTO_INCREMENT COMMENT 'id' ,
    `doc_name` VARCHAR(255) NOT NULL  COMMENT '文档名称（含后缀）' ,
    `doc_type` INT NOT NULL  COMMENT '1 .mp4 ；2 .avi' ,
    `obj_id` VARCHAR(255)   COMMENT '文档在minio的id' ,
    `bucket_name` VARCHAR(255)   COMMENT '文档存储桶名称' ,
    `md5` INT  DEFAULT 0 COMMENT 'md5' ,
    `extra` VARCHAR(255)   COMMENT '文档额外信息（json）' ,
    `user_id` INT   COMMENT '创建人id' ,
    `tenant_id` INT   COMMENT '租户Id' ,
    `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
    `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
    `is_deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '逻辑删除标记' ,
    PRIMARY KEY (id)
)  COMMENT = '文件表';


CREATE TABLE cms_video_five_gold(
    `id` INT AUTO_INCREMENT COMMENT '主键 自增id' ,
    `kw_kb_industry` VARCHAR(255)   COMMENT '行业分类' ,
    `time_slot` INT   COMMENT '时间段 3天,7天,30天' ,
    `cover_video_id` INT NOT NULL  COMMENT '封面id' ,
    `video_num` INT   COMMENT '关联素材（视频）' ,
    `interact_count` INT   COMMENT '平均互动数' ,
    `like_count` INT   COMMENT '平均点赞数' ,
    `comment_count` INT   COMMENT '平均评论数' ,
    `originality_num` VARCHAR(255)   COMMENT '创意分值' ,
    `visual_routine` VARCHAR(255)   COMMENT '画面套路' ,
    `dialogue_routine` VARCHAR(255)   COMMENT '台词讨论' ,
    `status` INT NOT NULL  COMMENT '1：待处理；2：处理中，3：完成，4：失败' ,
    `result_file_id` INT   COMMENT '结果文件id' ,
    `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
    `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
    `is_deleted` BIT(1) NOT NULL DEFAULT 0 COMMENT '数据逻辑删除标记' ,
    PRIMARY KEY (id)
)  COMMENT = '视频黄金5秒方法论表';

CREATE TABLE cms_task_info(
    `id` INT AUTO_INCREMENT COMMENT 'id' ,
    `video_id` VARCHAR(255)   COMMENT '视频id' ,
    `name` VARCHAR(255) NOT NULL  COMMENT '任务名称' ,
    `task_status` INT NOT NULL DEFAULT 1 COMMENT '任务状态' ,
    `task_type` INT NOT NULL DEFAULT 0 COMMENT '任务类型' ,
    `error_message` VARCHAR(255)   COMMENT '错误原因' ,
    `task_arg` VARCHAR   COMMENT 'JSON' ,
    `source_video_ids` VARCHAR(1024)   COMMENT '原视频分析id列表' ,
    `result_file_ids` VARCHAR(1024)   COMMENT '结果文档id列表' ,
    `extra` VARCHAR(1024)   COMMENT '任务额外信息，json存储' ,
    `user_id` INT   COMMENT '创建人id' ,
    `tenant_id` INT   COMMENT '租户Id' ,
    `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
    `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
    `is_deleted` BIT(1) NOT NULL DEFAULT 0 COMMENT '数据逻辑删除标记' ,
    PRIMARY KEY (id)
)  COMMENT = '任务表';


CREATE TABLE cms_task_detail(
    `id` INT AUTO_INCREMENT COMMENT 'id' ,
    `task_id` INT NOT NULL  COMMENT '任务id' ,
    `type` TINYINT NOT NULL  COMMENT '数据类型 0输入 1输出' ,
    `data_type` VARCHAR(255)   COMMENT '数据类型' ,
    `data` VARCHAR(1024)   COMMENT 'json' ,
    `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
    `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
    `is_deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '删除（0：没删 1：删除）' ,
    PRIMARY KEY (id)
)  COMMENT = '任务详情表';


CREATE TABLE cms_user(
    `id` INT AUTO_INCREMENT COMMENT '主键 自增id' ,
    `pic` VARCHAR(255)   COMMENT '用户头像url' ,
    `user_id` INT NOT NULL  COMMENT 'TCC的用户Id' ,
    `user_name` VARCHAR(255) NOT NULL  COMMENT 'TCC的用户名称' ,
    `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
    `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
    `is_deleted` BIT(1) NOT NULL DEFAULT 0 COMMENT '数据逻辑删除标记' ,
    PRIMARY KEY (id)
)  COMMENT = '用户表';


CREATE TABLE cms_token_use_detail(
    `id` INT AUTO_INCREMENT COMMENT 'id' ,
    `task_id` INT NOT NULL  COMMENT '任务Id' ,
    `tokens` INT   COMMENT '消耗token数' ,
    `usage_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'token使用时间' ,
    `trace_id` VARCHAR(255)   COMMENT '任务请求头唯一标识id' ,
    `user_id` VARCHAR(255)   COMMENT '用户Id' ,
    `tenant_id` INT NOT NULL  COMMENT '租户Id' ,
    `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
    `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
    `is_deleted` INT NOT NULL DEFAULT 0 COMMENT '删除（0：没删 1：删除）' ,
    PRIMARY KEY (id)
)  COMMENT = 'Token使用明细表';


CREATE TABLE cms_tenant_token(
    `id` INT AUTO_INCREMENT COMMENT 'id' ,
    `tenant_id` INT NOT NULL  COMMENT '租户Id' ,
    `balance` INT   COMMENT 'token余额' ,
    `accumulated_recharge` INT   COMMENT 'token累计充值' ,
    `accumulated_expenses` INT   COMMENT 'token累计消耗' ,
    `statistical_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '统计时间' ,
    `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
    `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
    `is_deleted` INT NOT NULL DEFAULT 0 COMMENT '删除（0：没删 1：删除）' ,
    PRIMARY KEY (id)
)  COMMENT = '租户Token统计表';



CREATE TABLE cms_token_recharge_detail(
    `id` INT AUTO_INCREMENT COMMENT 'id' ,
    `tenant_name` VARCHAR(255)   COMMENT '租户名称' ,
    `tenant_id` INT NOT NULL  COMMENT '租户Id' ,
    `user_id` INT NOT NULL  COMMENT '充值用户Id' ,
    `recharge_tokens` INT   COMMENT '充值token数' ,
    `balance_tokens` INT   COMMENT '充值后token余额' ,
    `recharge_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '充值时间' ,
    `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
    `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
    `is_deleted` INT NOT NULL DEFAULT 0 COMMENT '删除（0：没删 1：删除）' ,
    PRIMARY KEY (id)
)  COMMENT = 'Token充值明细表';


