---
description: v0.2 SQL DDL
globs:
alwaysApply: false
---
CREATE TABLE cms_qianchuan_material_video(
    `id` INT AUTO_INCREMENT COMMENT '主键ID' ,
    `ossid` VARCHAR(128) NOT NULL  COMMENT 'OSS ID' ,
    `video_id` VARCHAR(64) NOT NULL  COMMENT '视频ID' ,
    `consume_range` VARCHAR(32)   COMMENT '消耗区间' ,
    `title` VARCHAR(255)   COMMENT '标题' ,
    `brand` VARCHAR(100)   COMMENT '品牌' ,
    `exposure` INT  DEFAULT 0 COMMENT '曝光量' ,
    `shares` INT  DEFAULT 0 COMMENT '分享数' ,
    `comments` INT  DEFAULT 0 COMMENT '评论数' ,
    `likes` INT  DEFAULT 0 COMMENT '点赞数' ,
    `clicks` INT  DEFAULT 0 COMMENT '点击数' ,
    `industry` VARCHAR(64)   COMMENT '行业' ,
    `ranking_type` VARCHAR(32)   COMMENT '榜单类型（总榜/新兴榜）' ,
    `publish_time` TIMESTAMP   COMMENT '发布时间' ,
    `author_name` VARCHAR(100)   COMMENT '发布作者' ,
    `author_avatar` VARCHAR(255)   COMMENT '发布作者头像' ,
    `cover_image` VARCHAR(255)   COMMENT '视频首页头图' ,
    `duration` INT   COMMENT '视频时长（单位：秒）' ,
    `highlight` VARCHAR(255)   COMMENT '高光时刻0：没有 ；1：有' ,
    `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
    `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
    `is_deleted` INT  DEFAULT 0 COMMENT '逻辑删除标志（0-未删除，1-已删除）' ,
    PRIMARY KEY (id)
)  COMMENT = '千川素材视频数据表';

CREATE TABLE cms_video_three_gold(
    `id` INT AUTO_INCREMENT COMMENT '主键 自增id' ,
    `industry` VARCHAR(255) NOT NULL  COMMENT '行业分类' ,
    `gold_type` VARCHAR(255) NOT NULL  COMMENT '黄金3S类型' ,
    `time_slot` INT NOT NULL  COMMENT '时间段 3天,7天,30天' ,
    `start_time` TIMESTAMP   COMMENT '时间段的开始时间' ,
    `end_time` TIMESTAMP   COMMENT '时间段的结束时间' ,
    `cover_video` VARCHAR(1024) NOT NULL  COMMENT '封面视频json' ,
    `video_num` INT   COMMENT '关联素材（视频）' ,
    `interact_count` INT   COMMENT '平均互动数' ,
    `like_count` INT   COMMENT '平均点赞数' ,
    `comment_count` INT   COMMENT '平均评论数' ,
    `originality_num` VARCHAR(255)   COMMENT '创意分值' ,
    `dialogue_routine` VARCHAR(255)   COMMENT '台词套路' ,
    `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
    `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
    `is_deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '数据逻辑删除标记' ,
    PRIMARY KEY (id)
)  COMMENT = '黄金3秒方法论表';

CREATE TABLE cms_video_three_gold_relation(
    `id` INT AUTO_INCREMENT COMMENT '' ,
    `video_three_gold_id` INT NOT NULL  COMMENT '黄金3秒Id' ,
    `video_id` INT NOT NULL  COMMENT '视频分析Id' ,
    `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
    `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
    `is_deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '数据逻辑删除标记' ,
    PRIMARY KEY (id)
)  COMMENT = '黄金3秒视频分析关系表';

CREATE TABLE cms_feedback(
    `id` INT AUTO_INCREMENT COMMENT '主键 自增id' ,
    `text` TEXT   COMMENT '反馈内容' ,
    `user_id` INT NOT NULL  COMMENT '用户Id' ,
    `tenant_id` INT NOT NULL  COMMENT '租户Id' ,
    `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
    `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
    `is_deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '数据逻辑删除标记' ,
    PRIMARY KEY (id)
)  COMMENT = '反馈意见表';


CREATE TABLE cms_token_recharge_detail(
    `id` INT AUTO_INCREMENT COMMENT 'id' ,
    `tenant_name` VARCHAR(255)   COMMENT '租户名称' ,
    `tenant_id` INT NOT NULL  COMMENT '租户Id' ,
    `user_id` INT NOT NULL  COMMENT '充值用户Id' ,
    `recharge_tokens` INT   COMMENT '充值token数' ,
    `balance_tokens` INT   COMMENT '充值后token余额' ,
    `recharge_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '充值时间' ,
    `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
    `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
    `is_deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '删除（0：没删 1：删除）' ,
    PRIMARY KEY (id)
)  COMMENT = 'Token充值明细表';

CREATE TABLE cms_product_record(
    `id` INT AUTO_INCREMENT COMMENT '主键 自增id' ,
    `url` VARCHAR(2048) NOT NULL  COMMENT '导入的URL' ,
    `title` VARCHAR(255)   COMMENT '标题' ,
    `brand` VARCHAR(255)   COMMENT '品牌' ,
    `product_name` VARCHAR(255)   COMMENT '产品名称' ,
    `selling_point` VARCHAR(255)   COMMENT '卖点' ,
    `user_id` INT NOT NULL  COMMENT '用户Id' ,
    `tenant_id` INT NOT NULL  COMMENT '租户Id' ,
    `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
    `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
    `is_deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '数据逻辑删除标记' ,
    PRIMARY KEY (id)
)  COMMENT = '商品记录表';