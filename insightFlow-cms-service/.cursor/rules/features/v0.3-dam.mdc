---
description: v0.3 DAM素材管理方案设计
globs:
alwaysApply: false
---
# InsightFlow-CMS DAM素材库后端功能详细实现

## 1. 素材管理系统概述

InsightFlow-CMS DAM（数字资产管理）素材库旨在提供一套完整的视频素材管理解决方案，与现有CMS系统紧密集成，帮助用户高效管理、组织和利用视频素材资源。系统将基于现有的`CmsDocumentInfo`实体进行扩展，充分利用已有的文件存储和处理功能，同时增强素材管理、分类、标签和搜索能力。

### 1.1 素材上传功能
- **上传验证机制**：
  - 视频格式验证: MP4/MOV/AVI/FLV
  - 画面比例校验: 9:16、16:9、1:1
  - 文件大小校验: 50MB
  - 时长校验: 最长为1分钟

- **上传后处理**：
  - 生成视频封面图（提取关键帧）
  - 计算视频时长，并存储在`extra`字段
  - 自动AI标签生成（通过调用大模型服务）
  - 元数据提取（视频编码、分辨率、画面比例、时长等）

### 1.2 素材编辑与裁剪
- **视频截取功能**：
  - 精确时间点裁剪（精确到秒/毫秒）
  - 仅保留裁剪后的版本

- **素材重命名**：
  - 名称唯一性校验
  - 特殊字符过滤（不允许\、/、:、：、*、?、？、"、“、”、<、>、|等符号）
  - 名称长度限制（不超过20字符）

### 1.3 素材分类系统
- **分类层级管理**：
  - 支持分类结构
  - 默认分类（公共通用素材、个人素材、租户素材）
  - 分类类别: 公共通用素材、个人素材、租户素材

- **分类操作**：
  - 创建新分类（指定名称、分类类型）
  - 删除分类（包含回收站功能）
  - 重命名分类（同样有字符限制）
  - 分类移动（调整层级关系）

- **分类权限**：
  - 公共素材：所有用户可见
  - 个人素材：仅个人可见
  - 租户素材：租户内可见（需管理员权限管理）

### 1.4 标签管理系统
- **标签类型**：
  - AI标签（系统自动生成）
  - 个人标签（用户自定义）
  - 租户标签（租户内共享）

- **标签属性限制**：
  - 标签属性字段不超过40字
  - 标签值字段不超过250字

- **标签操作**：
  - 添加标签（自动添加到标签库）
  - 删除标签（检查关联素材数）
  - 修改标签（更新所有关联素材）
  - 批量操作标签（批量删除、批量更新）

- **标签值管理**：
  - 添加/修改标签值
  - 删除标签值
  - 标签值搜索和筛选

- **标签自动补全**：
  - 输入时自动匹配已有标签
  - 支持新标签创建

- **AI标签修改**：
  - 允许用户修改AI自动生成的标签
  - 修改记录保存

### 1.5 素材操作功能
- **素材删除**：
  - 单个素材删除
  - 批量素材删除
  - 软删除机制（移入回收站）
  - 权限控制（租户素材只有管理员可删除）

- **素材移动/分享**：
  - 个人素材分享到租户
  - 批量分享机制
  - 移动时权限变更处理

- **素材下载**：
  - 单个素材下载
  - 批量素材下载
  - 下载链接生成（带过期时间）
  - 下载权限控制

- **素材播放**：
  - 视频流式播放支持
  - 自适应比特率播放
  - 播放权限控制

## 3. 搜索与排序系统

### 3.1 多维搜索功能
- **搜索范围**：
  - 素材名称搜索
  - 素材标签搜索
  - 素材分类搜索

- **搜索算法**：
  - 模糊搜索支持

- **搜索结果优化**：
  - 搜索建议功能

### 3.2 排序功能
- **排序规则**：
  - 入库时间排序（降序）, 默认排序规则
  - 应用频次排序（降序）
  - 视频时长排序（降序）
  - 入库时间排序（降序）

## 4. 权限控制系统

### 4.1 基础权限模型
- **权限级别**：
  - 租户管理员权限
  - 普通用户权限

- **资源权限定义**：
  - 素材访问权限（读/写/删除）
  - 分类管理权限
  - 标签管理权限
  - 租户资源管理权限

### 4.2 素材权限控制
- **可见性控制**：
  - 个人素材：仅上传者可见
  - 租户素材：租户内所有用户可见
  - 公共素材：所有用户可见

- **操作权限控制**：
  - 个人素材：上传者拥有全部权限
  - 租户素材：租户管理员拥有管理权限，普通成员拥有使用权限
  - 公共素材：一般为只读权限

### 4.3 租户管理功能
- **租户素材管理**：
  - 租户管理员删除权限
  - 租户标签管理
  - 租户分类管理
  - 租户用户权限管理

- **个人素材分享**：
  - 分享个人素材到租户
  - 分享后权限变更机制

## 5. 特殊功能实现

### 5.2 AI标签生成
- **AI标签流程**：
  - 视频上传完成触发
  - AI服务调用（视频内容分析）

- **标签优化**：
  - 用户反馈机制
  - 标签精确度提升策略
  - 行业特定标签库

## 6. 数据库设计详细方案

### 6.1 核心表结构
```sql

```

### 6.2 与现有系统集成
- 与 `cms_document_info` 表集成，存储实际文件信息
- 使用TTC认证获取角色，通过角色限制操作权限，详见: @auth

## 7. API详细设计

### 7.1 素材管理API
```
# 上传相关
POST /api/dam/material/upload             # 上传素材（支持批量）
POST /api/dam/material/upload/check       # 预检查上传文件是否合法
GET  /api/dam/material/upload/progress    # 获取上传进度

# 素材CRUD
GET  /api/dam/material/list               # 获取素材列表（支持分页和筛选）
GET  /api/dam/material/{id}               # 获取单个素材详情
PUT  /api/dam/material/{id}               # 更新素材信息（重命名等）
DELETE /api/dam/material/{id}             # 删除素材（移入回收站）

# 批量操作
POST /api/dam/material/batch/delete       # 批量删除素材
POST /api/dam/material/batch/move         # 批量移动素材
POST /api/dam/material/batch/share        # 批量分享素材
POST /api/dam/material/batch/download     # 批量下载素材

# 播放相关
GET  /api/dam/material/{id}/stream        # 获取素材流媒体
GET  /api/dam/material/{id}/cover         # 获取素材封面图
```

### 7.2 分类管理API
```
# 基础CRUD
POST /api/dam/category                    # 创建分类
GET  /api/dam/category/list               # 获取分类列表（支持树形结构）
GET  /api/dam/category/{id}               # 获取分类详情
PUT  /api/dam/category/{id}               # 更新分类（重命名等）
DELETE /api/dam/category/{id}             # 删除分类

# 分类树操作
GET  /api/dam/category/tree               # 获取分类树结构
POST /api/dam/category/move               # 移动分类位置
GET  /api/dam/category/{id}/materials     # 获取分类下的素材
```

### 7.3 标签管理API
```
# 标签CRUD
POST /api/dam/tag                         # 创建标签
GET  /api/dam/tag/list                    # 获取标签列表
GET  /api/dam/tag/{id}                    # 获取标签详情
PUT  /api/dam/tag/{id}                    # 更新标签
DELETE /api/dam/tag/{id}                  # 删除标签

# 标签值CRUD
POST /api/dam/tag/value                   # 创建标签值
GET  /api/dam/tag/{id}/values             # 获取标签下的所有值
PUT  /api/dam/tag/value/{id}              # 更新标签值
DELETE /api/dam/tag/value/{id}            # 删除标签值

# 素材标签操作
POST /api/dam/material/{id}/tag           # 为素材添加标签
DELETE /api/dam/material/{id}/tag/{tagId} # 移除素材标签
```

### 7.4 搜索相关API
```
GET  /api/dam/search                      # 全局搜索
POST /api/dam/search/advanced             # 高级搜索（多条件组合）
GET  /api/dam/search/suggestions          # 获取搜索建议
```

### 7.5 回收站API
```
GET  /api/dam/recycle/list                # 获取回收站内容
POST /api/dam/recycle/restore/{id}        # 恢复回收站内容
DELETE /api/dam/recycle/{id}              # 永久删除回收站内容
POST /api/dam/recycle/empty               # 清空回收站
```

## 9. 特别注意事项

### 9.1 命名规则
- **素材命名规则**：
  - 名称长度不超过20字符
  - 不允许特殊字符：\、/、:、*、?、"、<、>、|
  - 命名冲突处理策略

- **分类命名规则**：
  - 同样的字符限制
  - 同级分类名称唯一性验证

### 9.2 集成注意点
- **与现有系统集成**：
  - 用户系统集成（从现有cms_user表获取用户信息）
  - 租户系统集成（租户权限控制）
  - OSS/Minio存储系统集成

- **与AI服务集成**：
  - 视频内容分析服务
  - 标签自动生成服务
  - ASR服务（如需提取视频音频内容）

### 9.3 错误处理
- **统一错误码**：
  - 10000 - 素材相关错误
  - 10100 - 分类相关错误
  - 10200 - 标签相关错误
  - 10300 - 权限相关错误
  - 10400 - 文件存储相关错误

- **错误处理机制**：
  - 错误日志记录
  - 用户友好错误提示
  - 关键操作错误回退机制

## 10. 代码组织建议

### 10.1 包结构设计
```
com.insightflow.cms.dam
├── config          // 配置类
├── controller      // API控制器
├── service         // 业务服务
│   └── impl        // 服务实现
├── mapper          // MyBatis映射
├── entity          // 实体类
├── dto             // 数据传输对象
├── vo              // 视图对象
├── enums           // 枚举定义
├── util            // 工具类
├── exception       // 异常定义
└── task            // 定时任务
```