FROM hub.intra.mlamp.cn/ai-pc/samepage-base-ffmpeg-jdk17-amd64

#FROM hub.intra.mlamp.cn/ai-pc/ffmpeg-nvidia-jdk17


WORKDIR /app

COPY ./target/*.jar /app/app.jar


# 设置环境变量TZ
ENV TZ=Asia/Shanghai

# 配置时区
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone


#RUN echo "http://mirrors.aliyun.com/alpine/edge/main" > /etc/apk/repositories && apk add --no-cache python3.12 py3-pip

# 设置环境变量
#ENV JAVA_OPTS="-XX:+PrintGCDetails -XX:+PrintGCDateStamps -Xloggc:/app/logs/gc.log -XX:+UseGCLogFileRotation -XX:NumberOfGCLogFiles=10 -XX:GCLogFileSize=100M -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/app/logs/dump_file.hprof"
# 设置JVM内存及GC参数（适用于JDK17）
ENV JAVA_OPTS="-Xms8g -Xmx24g"
#  默认启动命令
ENTRYPOINT java ${JAVA_OPTS} -jar app.jar 2>/app/logs/error.log

