apiVersion: apps/v1
kind: Deployment
metadata:
  name: pgvector-deployment
  namespace: ai-pc
spec:
  replicas: 1
  selector:
    matchLabels:
      app: pgvector
  template:
    metadata:
      labels:
        app: pgvector
    spec:
      imagePullSecrets:
        - name: ai-pc-ai-pc-robot
      containers:
        - name: pgvector
          image: hub.intra.mlamp.cn/ai-pc/pgvector:pg16
          ports:
            - containerPort: 5432
          env:
            - name: POSTGRES_PASSWORD
              value: "cms"
            - name: POSTGRES_USER
              value: "cms"
            - name: POSTGRES_DB
              value: "pgvector"
          volumeMounts:
            - name: pgvector-storage
              mountPath: /var/lib/postgresql/data
      volumes:
        - name: pgvector-storage
          persistentVolumeClaim:
            claimName: pgvector-pvc