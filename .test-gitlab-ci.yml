variables:
  DOCKER_HUB_NAMESPACE: ai-pc
  DOCKER_HOST: tcp://docker:2376
  DOCKER_TLS_CERTDIR: "/certs"
  DOCKER_TLS_VERIFY: 1
  DOCKER_CERT_PATH: "$DOCKER_TLS_CERTDIR/client"
  REPOSITORY: "hub.intra.mlamp.cn"
  HUB_JDK8: "$REPOSITORY/public/java/jdk8u201:omi-latest"
  IMAGE_VERSION: $CI_PIPELINE_ID
  NOTIFY_TITLE: "$CI_PROJECT_PATH"
  MSG_LINK: "${CI_PROJECT_URL}/-/pipelines/${CI_PIPELINE_ID}"
  EVAL_FUNC: ${GITLAB_EVAL_FUNC}

  # docker镜像名称
  DOCKER_HUB_IMAGE_PATH_API: $REPOSITORY/ai-pc/insightFlow-cms/insightflow-cms-service
  # k8s拉取docker镜像用的机器人账号secret, 需要在k8s平台创建, https://console.intra.mlamp.cn/kubeplay/cluster/secrets
  K8S_ROBOT: ai-pc-ai-pc-robot
  # 企业微信通知机器人key, 需要在gitlab变量中配置
  PROD_KEY: "$notify_robot_key"
  PREVIEW_KEY: "$notify_robot_key"
  TEST_KEY: "$notify_robot_key"
  SAMEPAGE_API_JAR: insightFlow-cms-service
  ENV: test
  NAMESPACE: ai-pc
  K8S_NAMESPACE: ai-pc
  REPLICAS: 1
  K8S_SERVICE_ENABLE: 1
  API_P_NAME: insight-flow-cms-api


mvn:package:test:
  #  <<: *check_changes
  rules:
    - if: $BUILD_API == "true"
    - if: '$CI_COMMIT_REF_NAME == "test"'
  image:
    name: hub.intra.mlamp.cn/public/docker-jdk-mvn-jdk17-amd64:latest
    entrypoint:
      - /usr/local/bin/entrypoint.sh
  stage: package
  tags:
    - ai-pc
  #  dependencies:
  #    - git:clone
  variables:
    #    GIT_STRATEGY: none
    MAVEN_OPTS: >-
      -Dhttps.protocols=TLSv1.2
      -Dorg.slf4j.simpleLogger.showDateTime=true
      -Djava.awt.headless=true
    MAVEN_CLI_OPTS: >-
      --batch-mode
      --errors
      --fail-at-end
      --show-version
      --no-transfer-progress
      -DinstallAtEnd=true
      -DdeployAtEnd=true
  script:
    - echo "---- mvn ----"
    - 'cd insightFlow-cms-service'
    - 'pwd'
    - 'mvn clean -U -e -DskipTests package'
  cache:
    - key: cache-build-${CI_PIPELINE_ID}
      policy: push
      paths:
        - insightFlow-cms-service/target/*.jar
        - insightFlow-cms-service/Dockerfile

docker:build:test:
  #  <<: $check_changes
  rules:
    - if: $BUILD_API == "true"
    - if: '$CI_COMMIT_REF_NAME == "test"'
  image:
    name: hub.intra.mlamp.cn/public/docker-jdk-mvn-jdk17-amd64:latest
    entrypoint:
      - /usr/local/bin/entrypoint.sh
  stage: build
  variables:
    GIT_STRATEGY: none
    DOCKER_FILE: ./Dockerfile
  dependencies:
    - mvn:package:test
  tags:
    - ai-pc
  cache:
    - key: cache-build-${CI_PIPELINE_ID}
      policy: pull
      paths:
        - insightFlow-cms-service/target/*.jar
        - insightFlow-cms-service/Dockerfile
  script:
    - export LATEST_VERSION="test-latest"
    - docker login "${REPOSITORY:-$REPOSITORY_DEFAULT}" -u "$docker_hub_user" -p "$docker_hub_pwd"
    - cd insightFlow-cms-service
    - docker build  -t $DOCKER_HUB_IMAGE_PATH_API:$IMAGE_VERSION  -f $DOCKER_FILE .
    - docker push $DOCKER_HUB_IMAGE_PATH_API:$IMAGE_VERSION
    - docker rmi -f $(docker images -q $DOCKER_HUB_IMAGE_PATH_API:$IMAGE_VERSION)

deploy:k8s:test:
  #  <<: *check_changes
  rules:
    - if: $BUILD_API == "true"
    - if: '$CI_COMMIT_REF_NAME == "test"'
  image:
    name: hub.intra.mlamp.cn/public/docker-jdk-mvn-jdk17-amd64:latest
    entrypoint:
      - /usr/local/bin/entrypoint.sh
  stage: deploy
  variables:
    GIT_STRATEGY: none
    K8S_ROBOT: ai-pc-ai-pc-robot
    NAMESPACE: ai-pc
    K8S_NAMESPACE: ai-pc
    ENV: test
  dependencies:
    - docker:build:test
  tags:
    - ai-pc
  script:
    - |
      . /usr/local/bin/ci_lib.sh
      init_env
      merge_object() {
        yq ea '. as $item ireduce ({}; . * $item )' $1 $2 >$3.a
        cp $3.a $3
      }
      merge_array() {
        path="$3" idPath="$4" yq eval-all '
        (
          (( (eval(strenv(path)) + eval(strenv(path)))  | .[] | {(eval(strenv(idPath))):  .}) as $item ireduce ({}; . * $item )) as $uniqueMap
          | ( $uniqueMap  | to_entries | .[]) as $item ireduce([]; . + $item.value)
        ) as $mergedArray
        | select(fi == 0) | (eval(strenv(path))) = $mergedArray
        ' $1 $2 >$5.a
        cp $5.a $5
      }
      k8s_deploy_api() {
        set -x
        IMAGE=$1
        TAG=$2
        P_NAME=$3
        if [[ -z $DEPLOYMENT ]];then
          DEPLOYMENT=$K8S_NAME_PREFIX$CI_PROJECT_NAME-$TAG-${ENV}-deployment
          POD=$K8S_NAME_PREFIX$CI_PROJECT_NAME-$TAG-${ENV}-pod
        else
          POD=$DEPLOYMENT
        fi
        if [[ $k8s_apply || $(kubectl get deployments | awk '{print $1}' | grep -Fxc "$DEPLOYMENT") -eq 0 ]];then
          if [[ $(kubectl get deployments | awk '{print $1}' | grep -Fxc "$DEPLOYMENT") -eq 1 ]];then
            K8S_DEPLOYMENT_REPLICAS=$(kubectl get deployments "$DEPLOYMENT" -o jsonpath='{.spec.replicas}')
            LIMITS_CPU=$(kubectl get deployment "$DEPLOYMENT" -o jsonpath='{.spec.template.spec.containers[0].resources.limits.cpu}')
            LIMITS_MEM=$(kubectl get deployment "$DEPLOYMENT" -o jsonpath='{.spec.template.spec.containers[0].resources.limits.memory}')
            REQUESTS_CPU=$(kubectl get deployment "$DEPLOYMENT" -o jsonpath='{.spec.template.spec.containers[0].resources.requests.cpu}')
            REQUESTS_MEM=$(kubectl get deployment "$DEPLOYMENT" -o jsonpath='{.spec.template.spec.containers[0].resources.requests.memory}')
          fi
          if [[ ! -f .k8s.$TAG.deployment.yml && -n $ENV && -n $NAMESPACE && -n $K8S_ROBOT && -n $IMAGE ]];then
            pattern="s#{PROJECT_NAME}#$P_NAME#g;s#{ENV}#$ENV#g;s#{NAMESPACE}#$NAMESPACE#g;s#{IMAGE}#$IMAGE#g;s#{REPLICAS}#$K8S_DEPLOYMENT_REPLICAS#g;s#{ROBOT_NAME}#$K8S_ROBOT#g;s#{APP_LOCAL_PORT}#${APP_LOCAL_PORT:-8080}#g;s#{K8S_PROBE_READINESS_PATH}#${K8S_PROBE_READINESS_PATH}#g;s#{K8S_PROBE_LIVENESS_PATH}#${K8S_PROBE_LIVENESS_PATH}#g;s#{APM_SERVER_URL}#${APM_SERVER_URL}#g;s#{APM_SERVICE_NAME}#${APM_SERVICE_NAME}#g;s#{APM_APPLICATION_PACKAGES}#${APM_APPLICATION_PACKAGES}#g;s#{PROMETHEUS_EXPORT_PATH}#${PROMETHEUS_EXPORT_PATH}#g;s#{K8S_RESOURCE_LABEL_GROUP}#${K8S_RESOURCE_LABEL_GROUP}#g;s#{LIMITS_CPU}#$LIMITS_CPU#g;s#{LIMITS_MEM}#$LIMITS_MEM#g;s#{REQUESTS_CPU}#$REQUESTS_CPU#g;s#{REQUESTS_MEM}#$REQUESTS_MEM#g;"
            get_ci_resource_file template-test/k8s_deployment_template.yml > .k8s.$TAG.deployment.yml
            sed -i "$pattern" .k8s.$TAG.deployment.yml
            cat .k8s.$TAG.deployment.yml
          fi
          if [[ -f .k8s.$TAG.deployment.yml ]];then
            kubectl -n $NAMESPACE apply -f .k8s.$TAG.deployment.yml
          fi
          set +x
        else
          kubectl -n $NAMESPACE set image deployment/$DEPLOYMENT $POD=$IMAGE
        fi
      }
      service_deploy_api() {
        set -x
        IMAGE=$1
        TAG=$2
        P_NAME=$3
        SRV_NAME=$P_NAME-${ENV}-srv
        if [[ $K8S_SERVICE_ENABLE = 1 && ($k8s_apply || $(kubectl get services | awk '{print $1}' | grep -Fxc "$SRV_NAME") -eq 0) ]];then
          if [[ ! -f .k8s.$TAG.srv.yml && -n $ENV && -n $NAMESPACE ]];then
            get_ci_resource_file template-test/k8s_service_template.yml > .k8s.$TAG.srv.yml
            pattern="s#{PROJECT_NAME}#$P_NAME#g;s#{ENV}#$ENV#g;s#{NAMESPACE}#$NAMESPACE#g;s#{APP_LOCAL_PORT}#${APP_LOCAL_PORT:-8080}#g;s#{K8S_RESOURCE_LABEL_GROUP}#${K8S_RESOURCE_LABEL_GROUP}#g;"
            sed -i "$pattern" .k8s.$TAG.srv.yml
          fi
          if [[ -f .k8s.$TAG.srv.yml ]];then
            cat .k8s.$TAG.srv.yml
            kubectl -n $NAMESPACE apply -f .k8s.$TAG.srv.yml
          fi
        fi
        set +x
      }
    - export KUBECONFIG="$ACK_XMING"
    - kubectl config get-contexts
    - k8s_deploy_api $DOCKER_HUB_IMAGE_PATH_API:$IMAGE_VERSION api $API_P_NAME
    - service_deploy_api $DOCKER_HUB_IMAGE_PATH_API:$IMAGE_VERSION api $API_P_NAME


reset_cache:
  stage: reset_cache
  variables:
    GIT_STRATEGY: none
  tags:
    - ai-pc
  rules:
    - if: $BUILD_API == "true"
    - if: '$CI_COMMIT_REF_NAME == "test"'
  cache:
    - key: cache-build-${CI_PIPELINE_ID}
      paths:
        - insightFlow-cms-service/target
  script:
    - rm -rf insightFlow-cms-service/target
    - rm -rf insight-flow-cms
    - mkdir -p insightFlow-cms-service/target/

