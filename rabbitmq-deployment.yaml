apiVersion: apps/v1
kind: Deployment
metadata:
  name: rabbitmq-deployment
  namespace: ai-pc
spec:
  replicas: 1
  selector:
    matchLabels:
      app: rabbitmq
  template:
    metadata:
      labels:
        app: rabbitmq
    spec:
      imagePullSecrets:
        - name: ai-pc-ai-pc-robot
      containers:
        - name: rabbitmq
          image: hub.intra.mlamp.cn/ai-pc/rabbitmq:3.12-management
          ports:
            - containerPort: 5672  # RabbitMQ main port
            - containerPort: 15672 # Management UI
          env:
            - name: RABBITMQ_DEFAULT_USER
              value: "admin"
            - name: RABBITMQ_DEFAULT_PASS
              value: "admin123"
          volumeMounts:
            - name: rabbitmq-storage
              mountPath: /var/lib/rabbitmq
      volumes:
        - name: rabbitmq-storage
          persistentVolumeClaim:
            claimName: rabbitmq-pvc

